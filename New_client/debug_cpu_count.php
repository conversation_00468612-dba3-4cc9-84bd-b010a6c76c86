<?php
// Debug CPU matching counts for 2x500GB SSD
require_once 'mysql.php';

echo "=== Debugging CPU Match Counts for 2x500GB SSD ===\n\n";

try {
    $cpu_id = 1; // Dual Intel Xeon E5-2630v3
    $country_id = 1; // Romania
    $city_id = 1; // Bucharest
    
    echo "Test parameters:\n";
    echo "- CPU ID: $cpu_id\n";
    echo "- Country ID: $country_id\n";
    echo "- City ID: $city_id\n\n";
    
    // Check dedicated servers with CPU match
    echo "=== Dedicated Servers with CPU Match ===\n";
    $cpu_ram_query = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM inventory_dedicated_servers
        WHERE status = 'Available'
        AND country_id = ?
        AND order_id IS NULL
        AND cpu = ?
    ");
    $cpu_ram_query->execute([$country_id, $cpu_id]);
    $cpu_ram_match_count = $cpu_ram_query->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Dedicated servers with CPU $cpu_id: $cpu_ram_match_count\n";
    
    // Check blade servers with CPU match
    echo "\n=== Blade Servers with CPU Match ===\n";
    $blade_cpu_query = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM blade_server_inventory bsi
        LEFT JOIN inventory_chassis ich ON bsi.chassis_id = ich.id
        WHERE bsi.status = 'Available'
        AND ich.city_id = ?
        AND bsi.order_id IS NULL
        AND bsi.cpu = ?
    ");
    $blade_cpu_query->execute([$city_id, $cpu_id]);
    $blade_cpu_count = $blade_cpu_query->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Blade servers with CPU $cpu_id: $blade_cpu_count\n";
    
    $total_cpu_match = $cpu_ram_match_count + $blade_cpu_count;
    echo "\n=== TOTAL ===\n";
    echo "Total CPU matches: $total_cpu_match\n";
    echo "This explains the '90 available' count!\n\n";
    
    // Show some sample servers from each type
    echo "=== Sample Dedicated Servers ===\n";
    $sample_dedicated = $pdo->prepare("
        SELECT id, label, cpu, status, country_id, order_id
        FROM inventory_dedicated_servers
        WHERE status = 'Available'
        AND country_id = ?
        AND order_id IS NULL
        AND cpu = ?
        LIMIT 5
    ");
    $sample_dedicated->execute([$country_id, $cpu_id]);
    while ($server = $sample_dedicated->fetch(PDO::FETCH_ASSOC)) {
        echo "Dedicated {$server['id']}: {$server['label']}, CPU: {$server['cpu']}, Status: {$server['status']}\n";
    }
    
    echo "\n=== Sample Blade Servers ===\n";
    $sample_blade = $pdo->prepare("
        SELECT bsi.id, bsi.label, bsi.cpu, bsi.status, ich.city_id, bsi.order_id
        FROM blade_server_inventory bsi
        LEFT JOIN inventory_chassis ich ON bsi.chassis_id = ich.id
        WHERE bsi.status = 'Available'
        AND ich.city_id = ?
        AND bsi.order_id IS NULL
        AND bsi.cpu = ?
        LIMIT 5
    ");
    $sample_blade->execute([$city_id, $cpu_id]);
    while ($server = $sample_blade->fetch(PDO::FETCH_ASSOC)) {
        echo "Blade {$server['id']}: {$server['label']}, CPU: {$server['cpu']}, Status: {$server['status']}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
