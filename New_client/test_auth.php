<?php
// Test script to verify the authentication system

// Include required files
require_once("mysql.php");
require_once("auth_handler.php");

// Create Auth instance
$auth = new Auth($pdo);

// Test function to simulate a request
function simulateRequest($endpoint, $data) {
    echo "Testing endpoint: $endpoint\n";
    echo "Request data: " . json_encode($data) . "\n";
    
    // Save the original input
    $originalInput = file_get_contents('php://input');
    
    // Create a stream
    $options = [
        'http' => [
            'method' => 'POST',
            'header' => 'Content-type: application/json',
            'content' => json_encode($data)
        ]
    ];
    $context = stream_context_create($options);
    
    // Open the file using the HTTP headers set above
    $file = fopen("http://localhost/New_client/api.php?f=$endpoint", 'r', false, $context);
    
    if ($file) {
        $response = stream_get_contents($file);
        fclose($file);
        echo "Response: $response\n";
    } else {
        echo "Error: Could not open the file.\n";
    }
    
    // Restore the original input
    file_put_contents('php://input', $originalInput);
    
    echo "\n";
}

// Test login
$loginData = [
    'username' => '<EMAIL>',
    'password' => 'password123'
];
simulateRequest('login', $loginData);

// Get the token from the response
// For testing purposes, you can hardcode a valid token here
$token = "YOUR_VALID_TOKEN_HERE";

// Test token verification
$verifyData = [
    'token' => $token
];
simulateRequest('login_check', $verifyData);

// Test an endpoint that uses auth_user()
simulateRequest('user_details', $verifyData);

echo "Authentication tests completed.\n";
