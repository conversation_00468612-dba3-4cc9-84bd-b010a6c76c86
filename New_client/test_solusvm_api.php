<?php
// Include the SolusVM API helper
require_once("solusvm_api.php");

// Set error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Function to print results in a readable format
function printResult($title, $data) {
    echo "\n=== $title ===\n";
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                echo "$key: " . json_encode($value) . "\n";
            } else {
                echo "$key: $value\n";
            }
        }
    } else {
        echo $data . "\n";
    }
    echo "\n";
}

// Get the VPS ID from the URL parameter
$solusvm_id = isset($_GET['id']) ? $_GET['id'] : 1973; // Default to 1973 if not specified

// Initialize SolusVM API with the same credentials used in the main application
$solus_api = new SolusVMAPI(
    'https://virt.zetservers.com/api/v2', // API URL
    '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
);

echo "Testing SolusVM API Connection...\n\n";

// Test 1: Get VPS Information
echo "Test 1: Get VPS Information for SolusVM ID: $solusvm_id\n";
$vps_info = $solus_api->getVpsInfo($solusvm_id);

if ($vps_info) {
    printResult("VPS Information", $vps_info);
} else {
    echo "Error: " . $solus_api->getLastError() . "\n\n";
    
    // Add more detailed error information
    echo "Detailed Error Information:\n";
    echo "Last Response: " . json_encode($solus_api->getLastResponse()) . "\n\n";
}

// Test 2: Get VPS Status
echo "Test 2: Get VPS Status for SolusVM ID: $solusvm_id\n";
$vps_status = $solus_api->getVpsStatus($solusvm_id);

if ($vps_status) {
    printResult("VPS Status", $vps_status);
} else {
    echo "Error: " . $solus_api->getLastError() . "\n\n";
}

// Test 3: List all VPS servers
echo "Test 3: List all VPS servers\n";
$all_vps = $solus_api->listVps();

if ($all_vps) {
    echo "Found " . count($all_vps) . " VPS servers\n";
    // Just show the first one as an example
    if (count($all_vps) > 0) {
        printResult("First VPS in List", $all_vps[0]);
    }
} else {
    echo "Error: " . $solus_api->getLastError() . "\n\n";
}

// Test 4: Get available OS templates
echo "Test 4: Get available OS templates\n";
$templates = $solus_api->getOsTemplates();

if ($templates) {
    echo "Found " . count($templates) . " OS templates\n";
    // Just show the first few as examples
    if (count($templates) > 0) {
        printResult("First few OS templates", array_slice($templates, 0, 3));
    }
} else {
    echo "Error: " . $solus_api->getLastError() . "\n\n";
}

// Test 5: Test a simple GET request to the API root
echo "Test 5: Test a simple GET request to the API root\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://virt.zetservers.com/api/v2');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if (curl_errno($ch)) {
    echo "cURL Error: " . curl_error($ch) . "\n";
} else {
    echo "HTTP Code: $httpCode\n";
    echo "Response: $response\n";
}

curl_close($ch);
?>
