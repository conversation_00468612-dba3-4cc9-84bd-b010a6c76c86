<?php
require_once "mysql.php";

// Test the referral voucher system
echo "=== REFERRAL VOUCHER TEST ===\n";

// Check if the function exists
if (!function_exists('generateRandom')) {
    // Define the function if it doesn't exist
    function generateRandom($length = 10) {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }
}

// Include the referral voucher function
function process_referral_voucher_test($pdo, $invoice_id) {
    try {
        echo "=== PROCESSING REFERRAL VOUCHER for invoice $invoice_id ===\n";
        
        // Get invoice details
        $invoiceQuery = "SELECT * FROM invoices WHERE id = :invoice_id";
        $invoiceStmt = $pdo->prepare($invoiceQuery);
        $invoiceStmt->bindValue(":invoice_id", $invoice_id);
        $invoiceStmt->execute();
        $invoice = $invoiceStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$invoice) {
            echo "❌ Invoice not found for ID: $invoice_id\n";
            return false;
        }
        
        $user_id = $invoice['user_id'];
        echo "✅ Invoice found - user_id: $user_id, invoice_id: $invoice_id\n";
        echo "📋 Invoice data: " . json_encode($invoice) . "\n";
        
        // Eligibility Check 1: Invoice must not have been paid using a voucher
        if (isset($invoice['voucher']) && $invoice['voucher'] > 0) {
            echo "❌ Skipped - Invoice was paid using a voucher (amount: {$invoice['voucher']})\n";
            return false;
        }
        echo "✅ Check 1 passed - No voucher used\n";
        
        // Eligibility Check 2: Invoice must not be for adding credit
        $description = strtolower($invoice['description'] ?? '');
        echo "📝 Invoice description: '$description'\n";
        if (strpos($description, 'credit') !== false || 
            strpos($description, 'balance') !== false ||
            strpos($description, 'account credit') !== false ||
            $invoice['type'] === 'Credit Purchase') {
            echo "❌ Skipped - Invoice is for credit addition\n";
            return false;
        }
        echo "✅ Check 2 passed - Not a credit purchase\n";
        
        // Get the user's referrer
        $referrerQuery = "SELECT referrer_id FROM users WHERE id = :user_id";
        $referrerStmt = $pdo->prepare($referrerQuery);
        $referrerStmt->bindValue(":user_id", $user_id);
        $referrerStmt->execute();
        $referrerData = $referrerStmt->fetch(PDO::FETCH_ASSOC);
        
        echo "👤 User referrer data: " . json_encode($referrerData) . "\n";
        
        if (!$referrerData || !$referrerData['referrer_id']) {
            echo "❌ Skipped - User has no referrer (referrer_id is null)\n";
            return false;
        }
        
        $referrer_id = $referrerData['referrer_id'];
        echo "✅ Check 3 passed - Found referrer ID: $referrer_id for user ID: $user_id\n";
        
        // Get transactions for this invoice
        $transactionQuery = "SELECT * FROM invoice_transactions WHERE invoice_id = :invoice_id ORDER BY date DESC LIMIT 1";
        $transactionStmt = $pdo->prepare($transactionQuery);
        $transactionStmt->bindValue(":invoice_id", $invoice_id);
        $transactionStmt->execute();
        $transaction = $transactionStmt->fetch(PDO::FETCH_ASSOC);
        
        echo "💳 Transaction data: " . json_encode($transaction) . "\n";
        
        $payment_amount = 0;
        $payment_processor = null;
        
        if ($transaction) {
            $payment_processor = strtolower($transaction['processor'] ?? $transaction['payment_method'] ?? '');
            $payment_amount = floatval($transaction['amount'] ?? 0);
            echo "✅ Found transaction - processor: '$payment_processor', amount: $payment_amount\n";
        } else {
            // Fallback to invoice amount
            $payment_amount = floatval($invoice['total'] ?? $invoice['subtotal'] ?? 0);
            echo "⚠️  No transaction found, using invoice amount: $payment_amount\n";
        }
        
        // Check if processor is eligible
        $eligible_processors = ['paypal', 'paddle', 'coingate', 'stripe'];
        echo "🔍 Checking processor '$payment_processor' against eligible: " . implode(', ', $eligible_processors) . "\n";
        
        if (!in_array($payment_processor, $eligible_processors)) {
            echo "❌ Skipped - Payment processor '$payment_processor' not eligible for referral rewards\n";
            return false;
        }
        echo "✅ Check 4 passed - Payment processor '$payment_processor' is eligible\n";
        
        // Calculate voucher value
        $voucher_value = round($payment_amount * 0.05, 2);
        
        if ($voucher_value <= 0) {
            echo "❌ Skipped - Calculated voucher value is 0 or negative: $voucher_value\n";
            return false;
        }
        
        echo "✅ Check 5 passed - Calculated voucher value: €$voucher_value (5% of €$payment_amount)\n";
        echo "🎉 ALL CHECKS PASSED! Creating referral voucher...\n";
        
        return true;
        
    } catch (Exception $e) {
        echo "❌ ERROR: " . $e->getMessage() . "\n";
        return false;
    }
}

// Test with recent invoices
echo "Checking recent paid invoices...\n\n";

$recentInvoicesQuery = "SELECT id, user_id, description, total, paid FROM invoices WHERE paid = 1 ORDER BY id DESC LIMIT 5";
$stmt = $pdo->prepare($recentInvoicesQuery);
$stmt->execute();
$invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($invoices)) {
    echo "No paid invoices found.\n";
} else {
    foreach ($invoices as $invoice) {
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "Testing Invoice ID: {$invoice['id']}\n";
        echo str_repeat("=", 50) . "\n";
        
        $result = process_referral_voucher_test($pdo, $invoice['id']);
        
        if ($result) {
            echo "✅ This invoice WOULD generate a referral voucher!\n";
        } else {
            echo "❌ This invoice would NOT generate a referral voucher.\n";
        }
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "🔍 CHECKING DATABASE SCHEMA...\n";

// Check if vouchers table has required columns
$voucherColumnsQuery = "SHOW COLUMNS FROM vouchers";
$stmt = $pdo->prepare($voucherColumnsQuery);
$stmt->execute();
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Vouchers table columns:\n";
foreach ($columns as $column) {
    echo "- {$column['Field']} ({$column['Type']})\n";
}

// Check if users have referrer_id set
$referrerCountQuery = "SELECT COUNT(*) as count FROM users WHERE referrer_id IS NOT NULL";
$stmt = $pdo->prepare($referrerCountQuery);
$stmt->execute();
$referrerCount = $stmt->fetch(PDO::FETCH_ASSOC);

echo "\nUsers with referrer_id set: {$referrerCount['count']}\n";

if ($referrerCount['count'] == 0) {
    echo "⚠️  WARNING: No users have referrer_id set! This is why no vouchers are generated.\n";
    echo "To test, you can set a referrer_id manually:\n";
    echo "UPDATE users SET referrer_id = 1 WHERE id = 2;\n";
}

?> 