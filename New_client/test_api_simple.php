<?php
// Simple test to check if delivery time is being set correctly
require_once 'mysql.php';

echo "=== Testing Delivery Time Logic ===\n\n";

// Test with a simple curl request to the API
$test_cases = [
    ['storage_id' => 1, 'name' => '240GB SSD'],
    ['storage_id' => 2, 'name' => '2x500GB SSD'],
];

foreach ($test_cases as $test) {
    echo "--- Testing {$test['name']} (ID: {$test['storage_id']}) ---\n";
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => 'http://localhost/api.php?f=dedicatedorder',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => http_build_query([
            'storage_id' => $test['storage_id'],
            'model_id' => 1,
            'location_id' => 1,
            'bandwidth_id' => 1
        ]),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/x-www-form-urlencoded'
        ]
    ]);
    
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    if ($response && $httpCode == 200) {
        $data = json_decode($response, true);
        
        if ($data && is_array($data) && count($data) > 0) {
            $config = $data[0];
            
            echo "✓ API Response received\n";
            if (isset($config['delivery'])) {
                echo "- Delivery: {$config['delivery']}\n";
            } else {
                echo "- Delivery: NOT SET\n";
            }
            
            // Check locations
            if (isset($config['location']) && is_array($config['location'])) {
                echo "- Locations: " . count($config['location']) . "\n";
                foreach ($config['location'] as $location) {
                    if (isset($location['delivery_time'])) {
                        echo "  - {$location['name']}: {$location['delivery_time']} ({$location['stockcolor']})\n";
                    }
                }
            }
        } else {
            echo "✗ Invalid JSON response\n";
            echo "Raw response: " . substr($response, 0, 100) . "...\n";
        }
    } else {
        echo "✗ Failed to get response (HTTP: $httpCode)\n";
    }
    echo "\n";
}

echo "=== Test Complete ===\n";
?>
