<?php
// Include database connection
require_once("mysql.php");

// Set error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Function to print results in a readable format
function printResult($title, $data) {
    echo "\n=== $title ===\n";
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                echo "$key: " . json_encode($value) . "\n";
            } else {
                echo "$key: $value\n";
            }
        }
    } else {
        echo $data . "\n";
    }
    echo "\n";
}

// Check both IDs
$ids_to_check = [84, 1973];
$user_id = 1; // Assuming user ID 1 for testing

foreach ($ids_to_check as $id) {
    echo "\n\n========== CHECKING ID: $id ==========\n\n";
    
    // Check if this ID exists as orders_items.id
    $check_oi_id = $pdo->prepare("
        SELECT oi.id, oi.type, oi.user_id, oi.server_id, oi.status
        FROM orders_items oi
        WHERE oi.id = :id
    ");
    $check_oi_id->bindValue(':id', $id);
    $check_oi_id->execute();
    
    if ($check_oi_id->rowCount() > 0) {
        $row = $check_oi_id->fetch(PDO::FETCH_ASSOC);
        echo "Found as orders_items.id:\n";
        echo "  ID: {$row['id']}\n";
        echo "  Type: " . (isset($row['type']) ? $row['type'] : 'NULL') . "\n";
        echo "  User ID: {$row['user_id']}\n";
        echo "  Server ID: " . (isset($row['server_id']) ? $row['server_id'] : 'NULL') . "\n";
        echo "  Status: " . (isset($row['status']) ? $row['status'] : 'NULL') . "\n";
    } else {
        echo "Not found as orders_items.id\n";
    }
    
    // Check if this ID exists as orders_items.server_id
    $check_server_id = $pdo->prepare("
        SELECT oi.id, oi.type, oi.user_id, oi.server_id, oi.status
        FROM orders_items oi
        WHERE oi.server_id = :id
    ");
    $check_server_id->bindValue(':id', $id);
    $check_server_id->execute();
    
    if ($check_server_id->rowCount() > 0) {
        $row = $check_server_id->fetch(PDO::FETCH_ASSOC);
        echo "\nFound as orders_items.server_id:\n";
        echo "  ID: {$row['id']}\n";
        echo "  Type: " . (isset($row['type']) ? $row['type'] : 'NULL') . "\n";
        echo "  User ID: {$row['user_id']}\n";
        echo "  Server ID: " . (isset($row['server_id']) ? $row['server_id'] : 'NULL') . "\n";
        echo "  Status: " . (isset($row['status']) ? $row['status'] : 'NULL') . "\n";
    } else {
        echo "\nNot found as orders_items.server_id\n";
    }
    
    // Now check if this would pass the API check
    $api_check = $pdo->prepare("
        SELECT oi.*, o.order_type
        FROM orders_items oi
        LEFT JOIN orders o ON oi.order_id = o.id
        WHERE oi.id = :id AND oi.user_id = :user_id
    ");
    $api_check->bindValue(':id', $id);
    $api_check->bindValue(':user_id', $user_id);
    $api_check->execute();
    
    echo "\nAPI check (oi.id = $id AND oi.user_id = $user_id): " . ($api_check->rowCount() > 0 ? "PASS" : "FAIL") . "\n";
    
    // Check if the URL is being constructed correctly in the frontend
    echo "\nCorrect URL should be: /vps-services/" . ($check_server_id->rowCount() > 0 ? $row['id'] : "???") . "\n";
}

// Now let's check what URL the frontend is actually using
echo "\n\n========== CHECKING FRONTEND URL CONSTRUCTION ==========\n\n";

// Get all VPS entries
$all_vps = $pdo->query("
    SELECT oi.id, oi.type, oi.user_id, oi.server_id, oi.status
    FROM orders_items oi
    WHERE oi.type LIKE '%vps%' OR oi.type LIKE '%Vps%'
");

if ($all_vps->rowCount() > 0) {
    echo "Found " . $all_vps->rowCount() . " VPS entries:\n\n";
    
    while ($vps = $all_vps->fetch(PDO::FETCH_ASSOC)) {
        echo "VPS Entry:\n";
        echo "  ID: {$vps['id']}\n";
        echo "  Type: " . (isset($vps['type']) ? $vps['type'] : 'NULL') . "\n";
        echo "  User ID: {$vps['user_id']}\n";
        echo "  Server ID: " . (isset($vps['server_id']) ? $vps['server_id'] : 'NULL') . "\n";
        echo "  Status: " . (isset($vps['status']) ? $vps['status'] : 'NULL') . "\n";
        echo "  Correct URL: /vps-services/{$vps['id']}\n\n";
    }
} else {
    echo "No VPS entries found in the database.\n";
}
?>
