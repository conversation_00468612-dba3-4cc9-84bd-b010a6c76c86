<?php
/**
 * Authentication Handler Class
 * Handles user authentication, registration, and token verification
 */
class Auth {
    private $pdo;

    /**
     * Constructor
     *
     * @param PDO $pdo Database connection
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Register a new user
     *
     * @param array $data User registration data
     * @return array Response with success/error status
     */
    public function register($data) {
        // Validate required fields
        if (!isset($data['username']) || !isset($data['password']) ||
            !isset($data['first_name']) || !isset($data['last_name']) ||
            !isset($data['address']) || !isset($data['city']) ||
            !isset($data['country'])) {
            return ['error' => 1, 'message' => 'Missing required fields'];
        }

        // Username field is actually email in this system
        $email = trim($data['username']);
        $password = $data['password'];
        $first_name = trim($data['first_name']);
        $last_name = trim($data['last_name']);
        $address = trim($data['address']);
        $city = trim($data['city']);
        $country = trim($data['country']);
        $company_name = isset($data['company_name']) ? trim($data['company_name']) : null;
        $vat_id = isset($data['vat_id']) ? trim($data['vat_id']) : null;
        $phone = isset($data['phone']) ? trim($data['phone']) : null;

        // Check for referral code
        $referrer_id = null;
        if (isset($data['ref_code']) && !empty($data['ref_code'])) {
            // Look up the referrer by the code
            $stmt = $this->pdo->prepare("SELECT user_id FROM affiliate_links WHERE code = ?");
            $stmt->execute([trim($data['ref_code'])]);

            if ($stmt->rowCount() > 0) {
                $referrer = $stmt->fetch(PDO::FETCH_ASSOC);
                $referrer_id = $referrer['user_id'];

                // Update click count
                $updateStmt = $this->pdo->prepare("
                    UPDATE affiliate_links
                    SET clicks = clicks + 1, last_clicked = NOW()
                    WHERE code = ?
                ");
                $updateStmt->execute([trim($data['ref_code'])]);
            }
        }

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return ['error' => 1, 'message' => 'Invalid email format'];
        }

        // Check if email already exists
        $stmt = $this->pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$email]);

        if ($stmt->rowCount() > 0) {
            return ['error' => 1, 'message' => 'Email already exists'];
        }

        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Generate token
        $token = bin2hex(random_bytes(32));

        // Insert new user
        $stmt = $this->pdo->prepare("
            INSERT INTO users (
                email, password, first_name, last_name,
                company_name, vat_id, phone, address,
                city, country, last_session, last_login,
                created, status, referrer_id
            ) VALUES (
                ?, ?, ?, ?,
                ?, ?, ?, ?,
                ?, ?, ?, NOW(),
                NOW(), 1, ?
            )
        ");

        try {
            $stmt->execute([
                $email, $hashedPassword, $first_name, $last_name,
                $company_name, $vat_id, $phone, $address,
                $city, $country, $token, $referrer_id
            ]);
            $userId = $this->pdo->lastInsertId();

            return [
                'error' => 0,
                'user_id' => $userId,
                'token' => $token
            ];
        } catch (PDOException $e) {
            return ['error' => 1, 'message' => 'Registration failed: ' . $e->getMessage()];
        }
    }

    /**
     * Login a user
     *
     * @param string $username Email address (used as username)
     * @param string $password Password
     * @return array Response with token or error
     */
    public function login($username, $password) {
        // Get user by email (username field from frontend is actually email)
        $stmt = $this->pdo->prepare("SELECT id, password FROM users WHERE email = ?");
        $stmt->execute([$username]);

        if ($stmt->rowCount() == 0) {
            return ['error' => 1, 'message' => 'Invalid email or password'];
        }

        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        // Verify password
        if (!password_verify($password, $user['password'])) {
            return ['error' => 1, 'message' => 'Invalid email or password'];
        }

        // Generate new token
        $token = bin2hex(random_bytes(32));

        // Update token in database
        $stmt = $this->pdo->prepare("UPDATE users SET last_session = ?, last_login = NOW() WHERE id = ?");
        $stmt->execute([$token, $user['id']]);

        return [
            'error' => 0,
            'token' => $token
        ];
    }

    /**
     * Verify a user token
     *
     * @param string $token User token
     * @return array|false User data or false if invalid
     */
    public function verifyToken($token) {
        if (!$token) {
            error_log("Token verification failed: No token provided");
            return false;
        }

        try {
            // Check for token with session expiration (30 days)
            $stmt = $this->pdo->prepare("
                SELECT id, email, first_name, last_name
                FROM users
                WHERE last_session = ?
                AND last_login > NOW() - INTERVAL 30 DAY
            ");
            $stmt->execute([$token]);

            if ($stmt->rowCount() == 0) {
                error_log("Token verification failed: Invalid or expired token for: " . substr($token, 0, 10) . "...");
                return false;
            }

            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            // Update last_login to extend the session
            $updateStmt = $this->pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
            $updateStmt->execute([$user['id']]);
            
            error_log("Token verification successful for user: " . $user['email']);
            return $user;
        } catch (PDOException $e) {
            error_log("Token verification error: " . $e->getMessage());
            return false;
        }
    }
}
