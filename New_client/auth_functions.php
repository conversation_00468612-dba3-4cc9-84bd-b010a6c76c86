<?php
require_once("mysql.php");

// Define allowed origins - update with your actual frontend domain
$allowed_origins = [
    'https://test.x-zoneit.ro',       // Main site
    'http://test.x-zoneit.ro',        // Main site (http)
    '82.152.132.28',
    'null',                           // Allow null origin for local file testing
    '*'                               // Allow all origins temporarily for debugging
];

// Debug: Log the request origin
error_log("Request origin: " . ($_SERVER['HTTP_ORIGIN'] ?? 'none'));


date_default_timezone_set('Europe/Bucharest');

function getCurrentTimestamp() {
  // Create timestamp in a consistent format
  return date('Y-m-d H:i:s');
}

function formatTimestampForDisplay($timestamp) {
  if (empty($timestamp)) return '';

  // First convert the timestamp to the correct timezone if needed
  $date = new DateTime($timestamp);
  $date->setTimezone(new DateTimeZone(date_default_timezone_get()));

  // Format consistently
  return $date->format('Y-m-d H:i:s');
}

// Get the origin from the request headers
$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';

// Set CORS headers - allow all origins temporarily for debugging
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, Origin");
header("Access-Control-Allow-Credentials: true");
header("Access-Control-Max-Age: 86400");

// Other security headers (relaxed for debugging)
header("X-Content-Type-Options: nosniff");
header("X-Frame-Options: SAMEORIGIN");
// Temporarily disable CSP for debugging
// header("Content-Security-Policy: default-src 'self'");
header("Strict-Transport-Security: max-age=31536000; includeSubDomains");

// Debug: Log the request method and headers
error_log("Request method: " . ($_SERVER['REQUEST_METHOD'] ?? 'none'));
error_log("Request headers: " . json_encode(getallheaders()));

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
  http_response_code(200);
  exit;
}

// Make sure all responses are in JSON format
header('Content-Type: application/json');

// Get client IP - updated to prioritize Cloudflare headers
function getClientIP() {
  // First check for Cloudflare IP header
  if (isset($_SERVER['HTTP_CF_CONNECTING_IP']) && !empty($_SERVER['HTTP_CF_CONNECTING_IP'])) {
    $ip = trim($_SERVER['HTTP_CF_CONNECTING_IP']);
    // Basic validation
    if (filter_var($ip, FILTER_VALIDATE_IP) !== false) {
      error_log("Using Cloudflare IP: " . $ip);
      return $ip;
    }
  }

  // Log Cloudflare headers for debugging
  error_log("CF Headers: " . json_encode(array_filter($_SERVER, function($key) {
    return strpos($key, 'HTTP_CF_') === 0;
  }, ARRAY_FILTER_USE_KEY)));
  
  // Fall back to regular IP detection
  $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];

  foreach ($ip_keys as $key) {
    if (array_key_exists($key, $_SERVER) === true) {
      foreach (explode(',', $_SERVER[$key]) as $ip) {
        $ip = trim($ip);
        // Validate IP format - removed the FILTER_FLAG_NO_PRIV_RANGE flag to allow private IPs behind Cloudflare
        if (filter_var($ip, FILTER_VALIDATE_IP) !== false) {
          error_log("Using fallback IP from {$key}: {$ip}");
          return $ip;
        }
      }
    }
  }

  $default_ip = $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
  error_log("Using default IP: " . $default_ip);
  return $default_ip; // Default fallback
}

$client_ip = getClientIP();
error_log("Final client IP determined: " . $client_ip);

// Generate cryptographically secure random string
function generateRandom($length = 32) {
  return bin2hex(random_bytes($length / 2));
}

// Authentication helper function
function getRequestData() {
  // Check if data is sent as JSON or as form data
  $input = file_get_contents('php://input');

  // Debug: Log the raw input
  error_log("Raw request input: " . $input);

  // Try to parse as JSON
  $data = json_decode($input, true);
  $jsonError = json_last_error();

  // Debug: Log JSON parsing result
  error_log("JSON parse result: " . ($jsonError === JSON_ERROR_NONE ? "Success" : "Error: " . json_last_error_msg()));

  if ($jsonError !== JSON_ERROR_NONE) {
    // Debug: Log POST data if JSON parsing failed
    error_log("Falling back to POST data: " . json_encode($_POST));
    return $_POST;
  }

  return $data;
}


// User authentication function - updated to handle tokens without strict IP binding
function auth_user() {
  global $pdo;
  global $client_ip;

  $data = getRequestData();
  $token = $data['token'] ?? '';

  if (empty($token)) {
    return respondWithError(5, 'User authentication required - no token provided');
  }

  // Additional security: token should be at least 32 chars
  if (strlen($token) < 32) {
    return respondWithError(5, 'User authentication required - invalid token format');
  }

  // Use a prepared statement to prevent SQL injection
  // Changed from 24 HOUR to 30 DAY for extended session
  // IMPORTANT: Removed IP check to allow for Cloudflare IP changes
  $stmt = $pdo->prepare("
    SELECT id, last_login, last_ip
    FROM `users`
    WHERE `last_session` = :token
    AND `last_login` > NOW() - INTERVAL 30 DAY
    LIMIT 1
  ");

  $stmt->bindValue(':token', $token);
  $stmt->execute();

  if ($stmt->rowCount() !== 1) {
    return respondWithError(5, 'User authentication required - invalid token');
  }

  $row = $stmt->fetch(PDO::FETCH_ASSOC);
  
  // Log IP mismatch but still allow authentication
  if ($row['last_ip'] !== $client_ip) {
    error_log("IP mismatch - Token IP: {$row['last_ip']}, Current IP: {$client_ip}");
    
    // Optional: Update the IP to the current one
    try {
      $update_ip = $pdo->prepare("UPDATE `users` SET `last_ip` = :ip WHERE `id` = :id");
      $update_ip->bindValue(':ip', $client_ip);
      $update_ip->bindValue(':id', $row['id']);
      $update_ip->execute();
    } catch (Exception $e) {
      error_log("Failed to update IP: " . $e->getMessage());
    }
  }
  
  return $row['id'];
}

// Helper function for consistent error responses
function respondWithError($code, $message = '') {
  $response = [
    'error' => $code,
    'message' => $message,
    'success' => false
  ];

  echo json_encode($response);
  exit;
}

// Helper function for success responses
function respondWithSuccess($data = []) {
  $response = array_merge(['error' => 0, 'success' => true], $data);
  echo json_encode($response);
  exit;
}

// One-time function to migrate existing MD5 hashes to password_hash (run ONCE)
function migratePasswordHashes() {
  global $pdo;

  // Select all existing users with their MD5 passwords
  $stmt = $pdo->query("SELECT id, email, password FROM `users`");

  while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    // Hash the existing MD5 password using PASSWORD_DEFAULT
    $newPasswordHash = password_hash($row['password'], PASSWORD_DEFAULT);

    // Update the password hash in the database
    $updateStmt = $pdo->prepare("UPDATE `users` SET password = :new_hash WHERE id = :id");
    $updateStmt->bindValue(':new_hash', $newPasswordHash);
    $updateStmt->bindValue(':id', $row['id']);
    $updateStmt->execute();
  }

  echo "Password migration complete.";
  exit;
}


function trackLoginAttempt($username, $ip_address, $success) {
  global $pdo;

  try {
      $stmt = $pdo->prepare("
          INSERT INTO `login_attempts`
          (ip_address, username, is_successful)
          VALUES (:ip, :username, :success)
      ");

      $stmt->execute([
          ':ip' => $ip_address,
          ':username' => $username,
          ':success' => $success ? 1 : 0
      ]);
  } catch (Exception $e) {
      error_log("Login attempt tracking error: " . $e->getMessage());
  }
}

// Enhanced Login Attempt Validation - modified to be less strict on IP
function validateLoginAttempts($username, $ip_address) {
  global $pdo;

  // Configuration
  $max_attempts = 5;
  $lockout_duration_minutes = 15;

  try {
      // Delete old attempts
      $cleanup_stmt = $pdo->prepare("
          DELETE FROM `login_attempts`
          WHERE attempt_time < NOW() - INTERVAL :duration MINUTE
      ");
      $cleanup_stmt->bindValue(':duration', $lockout_duration_minutes);
      $cleanup_stmt->execute();

      // Count recent failed attempts - check username only, not IP
      $check_stmt = $pdo->prepare("
          SELECT COUNT(*) AS failed_attempts
          FROM `login_attempts`
          WHERE username = :username
          AND is_successful = 0
          AND attempt_time > NOW() - INTERVAL :duration MINUTE
      ");

      $check_stmt->bindValue(':username', $username);
      $check_stmt->bindValue(':duration', $lockout_duration_minutes);
      $check_stmt->execute();

      $result = $check_stmt->fetch(PDO::FETCH_ASSOC);

      // Check if attempts exceed maximum
      if ($result['failed_attempts'] >= $max_attempts) {
          error_log("Login blocked - too many attempts. IP: $ip_address, Username: $username");
          return false;
      }

      return true;
  } catch (Exception $e) {
      error_log("Login attempts validation error: " . $e->getMessage());
      return false;
  }
}

// Parse request and route to the appropriate handler
$function = $_GET['f'] ?? '';

// User login endpoint

if ($function === 'user_login') {
  global $client_ip;

  // Debug: Log the user login attempt
  error_log("User login attempt from IP: $client_ip");

  // Validate input method and content
  $data = getRequestData();

  // Debug: Log the received data
  error_log("Login data received: " . json_encode($data));

  // Strict input validation
  if (!isset($data['username']) || !isset($data['password'])) {
      error_log("Login attempt with missing credentials");
      respondWithError(1, 'Invalid login credentials');
  }

  $username = trim($data['username']);
  $password = $data['password'];

  // Validate username format (basic email validation)
  if (!filter_var($username, FILTER_VALIDATE_EMAIL)) {
      error_log("Invalid email format: $username");
      respondWithError(1, 'Invalid email format');
  }

  // Check login attempts before processing
  if (!validateLoginAttempts($username, $client_ip)) {
      respondWithError(3, "Too many failed attempts. Please try again later.");
  }

  try {
      // Secure user lookup
      $stmt = $pdo->prepare("
          SELECT id, email, password
          FROM `users`
          WHERE LOWER(email) = LOWER(:username)
      ");

      $stmt->bindValue(':username', $username);
      $stmt->execute();

      $user = $stmt->fetch(PDO::FETCH_ASSOC);

      // Verify credentials
      if (!$user || !password_verify($password, $user['password'])) {
          // Log failed attempt
          trackLoginAttempt($username, $client_ip, false);

          error_log("Failed login attempt for username: $username");
          respondWithError(1, 'Invalid credentials');
      }

      // Successful login
      // Generate new session token
      $token = generateRandom(32);

      // Update user session
      $updateStmt = $pdo->prepare("
          UPDATE `users`
          SET `last_session` = :token,
              `last_ip` = :last_ip,
              `last_login` = NOW()
          WHERE `id` = :user_id
      ");

      $updateStmt->bindValue(':user_id', $user['id']);
      $updateStmt->bindValue(':last_ip', $client_ip);
      $updateStmt->bindValue(':token', $token);
      $updateStmt->execute();

      // Log successful attempt
      trackLoginAttempt($username, $client_ip, true);

      // Return success response
      respondWithSuccess([
          'token' => $token,
          'is_user' => true
      ]);

  } catch (Exception $e) {
      error_log("User login process error: " . $e->getMessage());
      respondWithError(4, 'Server error during login');
  }
}


// User Login Check endpoint
elseif ($function === 'user_login_check') {
  global $client_ip;
  $data = getRequestData();

  if (empty($data['token'])) {
    respondWithError(1, 'No token provided');
  }

  try {
    // Changed from 24 HOUR to 30 DAY for extended session
    // IMPORTANT: Removed IP check to allow for Cloudflare IP changes
    $stmt = $pdo->prepare("
      SELECT 1
      FROM `users`
      WHERE `last_session` = :token
      AND `last_login` > NOW() - INTERVAL 30 DAY
      LIMIT 1
    ");

    $stmt->bindValue(':token', $data['token']);
    $stmt->execute();


}
?>