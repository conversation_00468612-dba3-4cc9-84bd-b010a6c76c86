<?php
/**
 * Authentication Functions
 *
 * Handles user authentication, CORS, and security headers
 */

// Include the database connection
require_once("mysql.php");

// Define allowed origins - update with your actual frontend domain
$allowed_origins = [
    'https://x-zoneit.ro',         // Main site
    'http://x-zoneit.ro',          // Main site (http)
    'https://client.x-zoneit.ro',    // Test site
    'http://client.x-zoneit.ro',    // Test site
    'https://test.x-zoneit.ro',     // Test site (http)
    'http://localhost:3000',       // Local development
    'http://localhost:5173',       // Vite dev server
    'http://127.0.0.1:5173',       // Development via IP address
    'https://admin.x-zoneit.ro',   // Admin site
    'https://webapi.zetservers.com', // API domain
    'null'                         // Allow null origin for local file testing
];

// Debug: Log the request origin
error_log("Request origin: " . ($_SERVER['HTTP_ORIGIN'] ?? 'none'));

// Set timezone
date_default_timezone_set('Europe/Bucharest');

function getCurrentTimestamp() {
  // Create timestamp in a consistent format
  return date('Y-m-d H:i:s');
}

function formatTimestampForDisplay($timestamp) {
  if (empty($timestamp)) return '';

  // First convert the timestamp to the correct timezone if needed
  $date = new DateTime($timestamp);
  $date->setTimezone(new DateTimeZone(date_default_timezone_get()));

  // Format consistently
  return $date->format('Y-m-d H:i:s');
}

// Get the origin from the request headers
$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';

// Better CORS handling - check if the origin is in our allowed list
if (in_array($origin, $allowed_origins)) {
    // Set CORS headers for the specific origin
    header("Access-Control-Allow-Origin: $origin");
} else {
    // For development or testing - be more permissive
    // In production, you would want to remove this and only allow specific origins
    header("Access-Control-Allow-Origin: *");
    error_log("Warning: Request from unauthorized origin: $origin");
}

// Other CORS headers
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header("Access-Control-Allow-Credentials: true");
header("Access-Control-Max-Age: 86400");

// Security headers
header("X-Content-Type-Options: nosniff");
header("X-Frame-Options: SAMEORIGIN");
// Temporarily disable CSP for debugging
// header("Content-Security-Policy: default-src 'self'");
header("Strict-Transport-Security: max-age=31536000; includeSubDomains");

// Debug: Log the request method and headers
error_log("Request method: " . ($_SERVER['REQUEST_METHOD'] ?? 'none'));
error_log("Request headers: " . json_encode(getallheaders()));

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
  // Just exit with 200 OK for preflight requests
  http_response_code(200);
  exit;
}

// Make sure all responses are in JSON format
header('Content-Type: application/json');

// Get client IP - updated to prioritize Cloudflare headers
function getClientIP() {
  // First check for Cloudflare IP header
  if (isset($_SERVER['HTTP_CF_CONNECTING_IP']) && !empty($_SERVER['HTTP_CF_CONNECTING_IP'])) {
    $ip = trim($_SERVER['HTTP_CF_CONNECTING_IP']);
    // Basic validation
    if (filter_var($ip, FILTER_VALIDATE_IP) !== false) {
      error_log("Using Cloudflare IP: " . $ip);
      return $ip;
    }
  }

  // Log Cloudflare headers for debugging
  error_log("CF Headers: " . json_encode(array_filter($_SERVER, function($key) {
    return strpos($key, 'HTTP_CF_') === 0;
  }, ARRAY_FILTER_USE_KEY)));

  // Fall back to regular IP detection
  $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];

  foreach ($ip_keys as $key) {
    if (array_key_exists($key, $_SERVER) === true) {
      foreach (explode(',', $_SERVER[$key]) as $ip) {
        $ip = trim($ip);
        // Validate IP format - removed the FILTER_FLAG_NO_PRIV_RANGE flag to allow private IPs behind Cloudflare
        if (filter_var($ip, FILTER_VALIDATE_IP) !== false) {
          error_log("Using fallback IP from {$key}: {$ip}");
          return $ip;
        }
      }
    }
  }

  $default_ip = $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
  error_log("Using default IP: " . $default_ip);
  return $default_ip; // Default fallback
}

$client_ip = getClientIP();
error_log("Final client IP determined: " . $client_ip);

// Generate cryptographically secure random string
function generateRandom($length = 32) {
  return bin2hex(random_bytes($length / 2));
}

// Authentication helper function
function getRequestData() {
  // Check if data is sent as JSON or as form data
  $input = file_get_contents('php://input');

  // Debug: Log the raw input
  error_log("Raw request input: " . $input);

  // Try to parse as JSON
  $data = json_decode($input, true);
  $jsonError = json_last_error();

  // Debug: Log JSON parsing result
  error_log("JSON parse result: " . ($jsonError === JSON_ERROR_NONE ? "Success" : "Error: " . json_last_error_msg()));

  if ($jsonError !== JSON_ERROR_NONE) {
    // Debug: Log POST data if JSON parsing failed
    error_log("Falling back to POST data: " . json_encode($_POST));
    return $_POST;
  }

  return $data;
}

// Helper function for consistent error responses
function respondWithError($code, $message = '') {
  $response = [
    'error' => $code,
    'message' => $message,
    'success' => false
  ];

  echo json_encode($response);
  exit;
}

// Helper function for success responses
function respondWithSuccess($data = []) {
  $response = array_merge(['error' => 0, 'success' => true], $data);
  echo json_encode($response);
  exit;
}

function trackLoginAttempt($username, $ip_address, $success) {
  global $pdo;

  try {
      $stmt = $pdo->prepare("
          INSERT INTO `login_attempts`
          (ip_address, username, is_successful)
          VALUES (:ip, :username, :success)
      ");

      $stmt->execute([
          ':ip' => $ip_address,
          ':username' => $username,
          ':success' => $success ? 1 : 0
      ]);
  } catch (Exception $e) {
      error_log("Login attempt tracking error: " . $e->getMessage());
  }
}

// Enhanced Login Attempt Validation - modified to be less strict on IP
function validateLoginAttempts($username, $ip_address) {
  global $pdo;

  // Configuration
  $max_attempts = 5;
  $lockout_duration_minutes = 15;

  try {
      // Delete old attempts
      $cleanup_stmt = $pdo->prepare("
          DELETE FROM `login_attempts`
          WHERE attempt_time < NOW() - INTERVAL :duration MINUTE
      ");
      $cleanup_stmt->bindValue(':duration', $lockout_duration_minutes);
      $cleanup_stmt->execute();

      // Count recent failed attempts - check username only, not IP
      $check_stmt = $pdo->prepare("
          SELECT COUNT(*) AS failed_attempts
          FROM `login_attempts`
          WHERE username = :username
          AND is_successful = 0
          AND attempt_time > NOW() - INTERVAL :duration MINUTE
      ");

      $check_stmt->bindValue(':username', $username);
      $check_stmt->bindValue(':duration', $lockout_duration_minutes);
      $check_stmt->execute();

      $result = $check_stmt->fetch(PDO::FETCH_ASSOC);

      // Check if attempts exceed maximum
      if ($result['failed_attempts'] >= $max_attempts) {
          error_log("Login blocked - too many attempts. IP: $ip_address, Username: $username");
          return false;
      }

      return true;
  } catch (Exception $e) {
      error_log("Login attempts validation error: " . $e->getMessage());
      return false;
  }
}

/**
 * User Login function
 * 
 * Handles user authentication with rate limiting and session management
 */
function user_login() {
    global $pdo;
    global $client_ip;

    // Debug: Log the user login attempt
    error_log("User login attempt from IP: $client_ip");

    // Get the request data
    $data = getRequestData();

    // Debug: Log the received data
    error_log("Login data received: " . json_encode($data));

    // Strict input validation
    if (!isset($data['email']) || !isset($data['password'])) {
        // Try to fall back to username field if email is not specified (for compatibility)
        if (isset($data['username']) && !isset($data['email'])) {
            $data['email'] = $data['username'];
        } else {
            error_log("Login attempt with missing credentials");
            respondWithError(1, 'Email and password are required');
        }
    }

    $email = trim($data['email']);
    $password = $data['password'];

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        error_log("Invalid email format: $email");
        respondWithError(1, 'Invalid email format');
    }

    // Check login attempts before processing
    if (!validateLoginAttempts($email, $client_ip)) {
        respondWithError(3, "Too many failed attempts. Please try again later.");
    }

    try {
        // Secure user lookup
        $stmt = $pdo->prepare("
            SELECT id, email, password, first_name, last_name, status
            FROM `users`
            WHERE LOWER(email) = LOWER(:email)
        ");

        $stmt->bindValue(':email', $email);
        $stmt->execute();

        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        // Verify credentials and account status
        if (!$user) {
            // User doesn't exist
            trackLoginAttempt($email, $client_ip, false);
            error_log("Failed login attempt - user not found: $email");
            respondWithError(1, 'Invalid credentials');
        }

        // Check if account is active
        if ($user['status'] !== 1) {
            trackLoginAttempt($email, $client_ip, false);
            error_log("Login attempt on inactive account: $email");
            respondWithError(2, 'Account is inactive or suspended');
        }

        // Verify password
        if (!password_verify($password, $user['password'])) {
            // Invalid password
            trackLoginAttempt($email, $client_ip, false);
            error_log("Failed login attempt - invalid password for: $email");
            respondWithError(1, 'Invalid credentials');
        }

        // Successful login
        // Generate new session token
        $token = generateRandom(32);

        // Update user session
        $updateStmt = $pdo->prepare("
            UPDATE `users`
            SET `last_session` = :token,
                `last_ip` = :last_ip,
                `last_login` = NOW()
            WHERE `id` = :user_id
        ");

        $updateStmt->bindValue(':user_id', $user['id']);
        $updateStmt->bindValue(':last_ip', $client_ip);
        $updateStmt->bindValue(':token', $token);
        $updateStmt->execute();

        // Log successful attempt
        trackLoginAttempt($email, $client_ip, true);

        // Return success response with user info
        respondWithSuccess([
            'token' => $token,
            'user' => [
                'id' => $user['id'],
                'email' => $user['email'],
                'first_name' => $user['first_name'],
                'last_name' => $user['last_name']
            ]
        ]);

    } catch (Exception $e) {
        error_log("User login process error: " . $e->getMessage());
        respondWithError(4, 'Server error during login');
    }
}

/**
 * Verify User Token
 * 
 * Checks if a user token is valid and returns user information
 */
function user_verify_token() {
    global $pdo;
    global $client_ip;
    
    // Get the request data
    $data = getRequestData();

    if (empty($data['token'])) {
        respondWithError(1, 'No token provided');
    }

    try {
        // Token validity check - 30 day session
        $stmt = $pdo->prepare("
            SELECT id, email, first_name, last_name, company_name, vat_id, phone, address, city, country
            FROM `users`
            WHERE `last_session` = :token
            AND `status` = 1
            AND `last_login` > NOW() - INTERVAL 30 DAY
            LIMIT 1
        ");

        $stmt->bindValue(':token', $data['token']);
        $stmt->execute();

        if ($stmt->rowCount() === 1) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Optional: Update the last login time to extend the session
            $updateStmt = $pdo->prepare("
                UPDATE `users`
                SET `last_login` = NOW()
                WHERE `id` = :user_id
            ");
            $updateStmt->bindValue(':user_id', $user['id']);
            $updateStmt->execute();
            
            respondWithSuccess([
                'user' => $user
            ]);
        } else {
            respondWithError(1, 'Invalid or expired token');
        }

    } catch (Exception $e) {
        error_log("User token check error: " . $e->getMessage());
        respondWithError(4, 'Server error');
    }
}

/**
 * User Logout
 * 
 * Invalidates a user's session token
 */
function user_logout() {
    global $pdo;
    
    // Get the request data
    $data = getRequestData();

    if (empty($data['token'])) {
        respondWithError(1, 'No token provided');
    }

    try {
        // Clear the session token
        $stmt = $pdo->prepare("
            UPDATE `users`
            SET `last_session` = NULL
            WHERE `last_session` = :token
        ");

        $stmt->bindValue(':token', $data['token']);
        $stmt->execute();

        // Check if any rows were affected
        if ($stmt->rowCount() > 0) {
            respondWithSuccess(['message' => 'Logged out successfully']);
        } else {
            // Token wasn't found, but we'll still return success
            respondWithSuccess(['message' => 'Already logged out']);
        }

    } catch (Exception $e) {
        error_log("User logout error: " . $e->getMessage());
        respondWithError(4, 'Server error during logout');
    }
}

/**
 * User Register
 * 
 * Creates a new user account
 */
function user_register() {
    global $pdo;
    global $client_ip;
    
    // Get the request data
    $data = getRequestData();
    
    // Required fields
    $required_fields = ['email', 'password', 'first_name', 'last_name', 'address', 'city', 'country'];
    
    // Validate required fields
    foreach ($required_fields as $field) {
        if (empty($data[$field])) {
            respondWithError(1, "Missing required field: $field");
        }
    }
    
    // Validate email format
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        respondWithError(1, 'Invalid email format');
    }
    
    // Validate password strength (minimum 8 characters with mixed case and numbers)
    if (strlen($data['password']) < 8 || 
        !preg_match('/[A-Z]/', $data['password']) || 
        !preg_match('/[a-z]/', $data['password']) || 
        !preg_match('/[0-9]/', $data['password'])) {
        respondWithError(1, 'Password must be at least 8 characters and include uppercase, lowercase, and numbers');
    }
    
    try {
        // Check if email already exists
        $stmt = $pdo->prepare("SELECT 1 FROM `users` WHERE LOWER(email) = LOWER(:email)");
        $stmt->bindValue(':email', $data['email']);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            respondWithError(2, 'Email already registered');
        }
        
        // Create secure password hash
        $passwordHash = password_hash($data['password'], PASSWORD_DEFAULT);
        
        // Insert new user
        $stmt = $pdo->prepare("
            INSERT INTO `users` (
                email, password, first_name, last_name, 
                company_name, vat_id, phone, address, city, country,
                last_ip, status
            ) VALUES (
                :email, :password, :first_name, :last_name,
                :company_name, :vat_id, :phone, :address, :city, :country,
                :last_ip, 1
            )
        ");
        
        $stmt->execute([
            ':email' => $data['email'],
            ':password' => $passwordHash,
            ':first_name' => $data['first_name'],
            ':last_name' => $data['last_name'],
            ':company_name' => $data['company_name'] ?? null,
            ':vat_id' => $data['vat_id'] ?? null,
            ':phone' => $data['phone'] ?? null,
            ':address' => $data['address'],
            ':city' => $data['city'],
            ':country' => $data['country'],
            ':last_ip' => $client_ip
        ]);
        
        $user_id = $pdo->lastInsertId();
        
        // Generate initial session token
        $token = generateRandom(32);
        
        // Set initial session
        $updateStmt = $pdo->prepare("
            UPDATE `users`
            SET `last_session` = :token,
                `last_login` = NOW()
            WHERE `id` = :user_id
        ");
        
        $updateStmt->bindValue(':user_id', $user_id);
        $updateStmt->bindValue(':token', $token);
        $updateStmt->execute();
        
        // Return success with token and basic user info
        respondWithSuccess([
            'token' => $token,
            'user' => [
                'id' => $user_id,
                'email' => $data['email'],
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name']
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("User registration error: " . $e->getMessage());
        respondWithError(4, 'Server error during registration');
    }
}

/**
 * User Change Password
 * 
 * Allows a user to change their password
 */
function user_change_password() {
    global $pdo;
    
    // Get the request data
    $data = getRequestData();
    
    // Check required fields
    if (empty($data['token']) || empty($data['current_password']) || empty($data['new_password'])) {
        respondWithError(1, 'Token, current password, and new password are required');
    }
    
    // Validate password strength
    if (strlen($data['new_password']) < 8 || 
        !preg_match('/[A-Z]/', $data['new_password']) || 
        !preg_match('/[a-z]/', $data['new_password']) || 
        !preg_match('/[0-9]/', $data['new_password'])) {
        respondWithError(1, 'New password must be at least 8 characters and include uppercase, lowercase, and numbers');
    }
    
    try {
        // Get user by token
        $stmt = $pdo->prepare("
            SELECT id, password
            FROM `users`
            WHERE `last_session` = :token
            AND `status` = 1
            AND `last_login` > NOW() - INTERVAL 30 DAY
            LIMIT 1
        ");
        
        $stmt->bindValue(':token', $data['token']);
        $stmt->execute();
        
        if ($stmt->rowCount() !== 1) {
            respondWithError(5, 'Invalid or expired token');
        }
        
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Verify current password
        if (!password_verify($data['current_password'], $user['password'])) {
            respondWithError(2, 'Current password is incorrect');
        }
        
        // Hash new password
        $newPasswordHash = password_hash($data['new_password'], PASSWORD_DEFAULT);
        
        // Update password
        $updateStmt = $pdo->prepare("
            UPDATE `users`
            SET `password` = :new_password
            WHERE `id` = :user_id
        ");
        
        $updateStmt->bindValue(':new_password', $newPasswordHash);
        $updateStmt->bindValue(':user_id', $user['id']);
        $updateStmt->execute();
        
        respondWithSuccess(['message' => 'Password updated successfully']);
        
    } catch (Exception $e) {
        error_log("Password change error: " . $e->getMessage());
        respondWithError(4, 'Server error during password change');
    }
}

// Parse request and route to the appropriate handler
$function = $_GET['f'] ?? '';

// Handle API requests - user authentication only
if (in_array($function, ['user_login', 'user_register', 'user_verify_token', 'user_logout', 'user_change_password'])) {
    switch ($function) {
        case 'user_login':
            user_login();
            break;
            
        case 'user_register':
            user_register();
            break;
            
        case 'user_verify_token':
            user_verify_token();
            break;
            
        case 'user_logout':
            user_logout();
            break;
            
        case 'user_change_password':
            user_change_password();
            break;
            
        default:
            respondWithError(1, 'Unknown function');
    }
} else {
    respondWithError(1, 'Unknown function');
}
?>