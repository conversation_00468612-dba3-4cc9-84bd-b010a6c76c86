# VPS Plan Upgrade - Solus Integration

## Overview
This integration automatically resizes VPS servers in Solus when a VPS plan upgrade invoice is marked as paid.

## How It Works

### 1. Invoice Payment Flow
When a VPS plan upgrade invoice is paid, the system calls:
```php
process_paid_invoice_updates($pdo, $invoice_id)
```

### 2. VPS Plan Upgrade Processing  
For VPS Plan Upgrade invoices, the system executes:
```php
process_vps_plan_upgrade_update($pdo, $invoice, $items)
```

### 3. Database Update
The function updates the local database:
- Updates `cpu_id` and `config_id` in `orders_items` table
- Creates an entry in `upgrade_orders` table for tracking

### 4. Solus API Call
If a `server_id` exists in the `orders_items` record, the system calls:
```php
resize_vps_in_solus($server_id, $new_plan_id)
```

## API Integration Details

### Endpoint
```
POST https://virt.zetservers.com/api/v1/servers/{server_id}/resize
```

### Headers
```
Content-Type: application/json
Authorization: Bearer {JWT_TOKEN}
```

### Request Body
```json
{
    "plan_id": 2,
    "preserve_disk": true
}
```

### Authentication
Uses the existing JWT token from the system:
```
eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
```

## Function Reference

### `resize_vps_in_solus($server_id, $plan_id)`
**Parameters:**
- `$server_id` (int): The Solus server ID from `orders_items.server_id`
- `$plan_id` (int): The new plan ID from the upgrade invoice

**Returns:**
- `true` on successful API call (HTTP 2xx response)
- `false` on failure

**Logging:**
All API calls and responses are logged to `/tmp/api_debug.log`

## Error Handling
- If `server_id` is missing, the upgrade is processed in the database but Solus is not called
- API failures are logged but don't prevent the database update
- cURL errors and HTTP error responses are captured and logged

## Testing
Use `test_vps_resize_integration.php` to test the API integration:
```bash
php test_vps_resize_integration.php
```

## Integration Points
The integration is triggered automatically when:
1. A VPS Plan Upgrade invoice is marked as paid
2. The invoice type is exactly "VPS Plan Upgrade"
3. The invoice has valid `orders_items_id` and plan value
4. The corresponding `orders_items` record has a `server_id`

## Monitoring
Monitor the integration by checking:
- Error logs at `/tmp/api_debug.log`
- Database `upgrade_orders` table for tracking
- Solus control panel for actual server plan changes 