-- Create affiliate_links table
CREATE TABLE IF NOT EXISTS `affiliate_links` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `code` varchar(32) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `clicks` int NOT NULL DEFAULT '0',
  `last_clicked` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Add referrer_id column to users table if it doesn't exist
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `referrer_id` int DEFAULT NULL;
