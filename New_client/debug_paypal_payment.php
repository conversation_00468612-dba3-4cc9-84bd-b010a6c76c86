<?php
/**
 * Debug PayPal payment to identify the validation error
 */

require_once("./mysql.php");

// PayPal configuration
$paypal_client_id = 'AexATmG6J-fd_YGA3h-xCNBQxf8l1btDDfbfciytmiAemB1Dqc58E8L-1HiKogTEg0jkre8nItHCRv50';
$paypal_secret = 'EM-vDbIrr0Els7tRqezoR3v2Kk4-b1-4COXMxUNe5sq9vtdJ9kwrRokJWMkgjpor6y8tX-fJ7pA650KV';
$paypal_mode = 'sandbox';
$api_base = 'https://api-m.sandbox.paypal.com';

function getPayPalAccessToken($client_id, $secret, $api_base) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_base . '/v1/oauth2/token');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
    curl_setopt($ch, CURLOPT_USERPWD, $client_id . ':' . $secret);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json', 'Accept-Language: en_US']);

    $auth_response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code !== 200) {
        throw new Exception("Failed to get PayPal access token, HTTP code: $http_code");
    }
    
    $auth_data = json_decode($auth_response, true);
    return $auth_data['access_token'];
}

try {
    echo "=== PayPal Payment Debug ===\n\n";
    
    // Test parameters
    $invoice_id = 986;
    $user_id = 1;
    $invoice_amount = 34.51;
    
    echo "Testing PayPal payment for:\n";
    echo "- Invoice ID: $invoice_id\n";
    echo "- User ID: $user_id\n";
    echo "- Amount: €$invoice_amount\n\n";
    
    // Get PayPal payment method
    $paypalQuery = "SELECT * FROM payment_methods 
                    WHERE user_id = :user_id 
                    AND processor = 'paypal' 
                    AND status = 'active' 
                    AND is_default = 1 
                    ORDER BY created DESC 
                    LIMIT 1";
    $paypalStmt = $pdo->prepare($paypalQuery);
    $paypalStmt->bindValue(":user_id", $user_id);
    $paypalStmt->execute();
    $paypalMethod = $paypalStmt->fetch(PDO::FETCH_ASSOC);

    if (!$paypalMethod) {
        echo "❌ No default PayPal payment method found for user $user_id\n";
        exit(1);
    }

    echo "Found PayPal payment method:\n";
    echo "- ID: {$paypalMethod['id']}\n";
    echo "- Vault ID: {$paypalMethod['vault_id']}\n";
    echo "- Email: {$paypalMethod['payer_email']}\n";
    echo "- Status: {$paypalMethod['status']}\n\n";

    // Get access token
    echo "Getting PayPal access token...\n";
    $accessToken = getPayPalAccessToken($paypal_client_id, $paypal_secret, $api_base);
    echo "✅ Got access token\n\n";

    $vault_id = $paypalMethod['vault_id'];
    
    echo "Step 1: Verify vault token exists...\n";
    // First, let's verify the vault token exists and is valid
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_base . '/v3/vault/payment-tokens/' . $vault_id);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $accessToken
    ]);

    $vault_check_response = curl_exec($ch);
    $vault_check_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "Vault check HTTP code: $vault_check_http_code\n";
    echo "Vault check response: $vault_check_response\n\n";

    if ($vault_check_http_code !== 200) {
        echo "❌ Vault token is not valid or accessible\n";
        exit(1);
    }

    echo "✅ Vault token is valid\n\n";

    echo "Step 2: Create PayPal order with vault token...\n";
    
    // Create payment using vaulted payment token
    $payment_data = [
        'intent' => 'CAPTURE',
        'payment_source' => [
            'token' => [
                'id' => $vault_id,
                'type' => 'PAYMENT_METHOD_TOKEN'
            ]
        ],
        'purchase_units' => [
            [
                'amount' => [
                    'currency_code' => 'EUR',
                    'value' => number_format($invoice_amount, 2, '.', '')
                ],
                'description' => "Automatic renewal payment for invoice #$invoice_id",
                                 'invoice_id' => "invoice_{$invoice_id}_" . time() . "_" . rand(1000, 9999)
            ]
        ]
    ];

    echo "Payment data:\n";
    echo json_encode($payment_data, JSON_PRETTY_PRINT) . "\n\n";

    // Create order using vaulted token
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_base . '/v2/checkout/orders');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payment_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $accessToken,
        'PayPal-Request-Id: ' . uniqid('order_', true)
    ]);

    $order_response = curl_exec($ch);
    $order_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "Order creation HTTP code: $order_http_code\n";
    echo "Order response:\n";
    echo json_encode(json_decode($order_response, true), JSON_PRETTY_PRINT) . "\n\n";

    $order_result = json_decode($order_response, true);
    
    if ($order_http_code !== 201 || !isset($order_result['id'])) {
        echo "❌ PayPal order creation failed\n";
        if (isset($order_result['details'])) {
            echo "Error details:\n";
            foreach ($order_result['details'] as $detail) {
                echo "- Issue: {$detail['issue']}\n";
                echo "- Description: {$detail['description']}\n";
                if (isset($detail['field'])) {
                    echo "- Field: {$detail['field']}\n";
                }
                echo "\n";
            }
        }
        exit(1);
    }

    $order_id = $order_result['id'];
    echo "✅ Order created successfully: $order_id\n\n";
    
    echo "Step 3: Capture the payment...\n";
    
    // Capture the payment immediately
    $ch = curl_init();
    $capture_url = $api_base . "/v2/checkout/orders/$order_id/capture";
    curl_setopt($ch, CURLOPT_URL, $capture_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, '{}');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $accessToken,
        'PayPal-Request-Id: ' . uniqid('capture_', true)
    ]);

    $capture_response = curl_exec($ch);
    $capture_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "Capture HTTP code: $capture_http_code\n";
    echo "Capture response:\n";
    echo json_encode(json_decode($capture_response, true), JSON_PRETTY_PRINT) . "\n\n";

    $capture_result = json_decode($capture_response, true);
    
    if ($capture_http_code !== 201 || !isset($capture_result['purchase_units'][0]['payments']['captures'][0]['id'])) {
        echo "❌ PayPal capture failed\n";
        if (isset($capture_result['details'])) {
            echo "Error details:\n";
            foreach ($capture_result['details'] as $detail) {
                echo "- Issue: {$detail['issue']}\n";
                echo "- Description: {$detail['description']}\n";
                if (isset($detail['field'])) {
                    echo "- Field: {$detail['field']}\n";
                }
                echo "\n";
            }
        }
        exit(1);
    }

    $paypal_payment_id = $capture_result['purchase_units'][0]['payments']['captures'][0]['id'];
    
    echo "✅ Payment captured successfully!\n";
    echo "PayPal Payment ID: $paypal_payment_id\n";
    echo "🎉 PayPal payment test completed successfully!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?> 