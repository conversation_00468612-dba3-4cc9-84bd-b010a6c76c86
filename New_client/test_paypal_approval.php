<?php
/**
 * Test PayPal Approval and Vaulting Process
 * This script helps test the real PayPal API integration
 */

require_once("./mysql.php");

// Set error handling
error_reporting(E_ALL);
ini_set('display_errors', 1);

// PayPal configuration
$paypal_client_id = 'AexATmG6J-fd_YGA3h-xCNBQxf8l1btDDfbfciytmiAemB1Dqc58E8L-1HiKogTEg0jkre8nItHCRv50';
$paypal_secret = 'EM-vDbIrr0Els7tRqezoR3v2Kk4-b1-4COXMxUNe5sq9vtdJ9kwrRokJWMkgjpor6y8tX-fJ7pA650KV';
$paypal_mode = 'sandbox';
$api_base = 'https://api-m.sandbox.paypal.com';

function getPayPalAccessToken($client_id, $secret, $api_base) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_base . '/v1/oauth2/token');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
    curl_setopt($ch, CURLOPT_USERPWD, $client_id . ':' . $secret);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json', 'Accept-Language: en_US']);

    $auth_response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code !== 200) {
        throw new Exception("Failed to get PayPal access token, HTTP code: $http_code");
    }
    
    $auth_data = json_decode($auth_response, true);
    if (!isset($auth_data['access_token'])) {
        throw new Exception('No access token in PayPal response');
    }
    
    return $auth_data['access_token'];
}

try {
    echo "=== PayPal Integration Test ===\n\n";
    
    // Check current payment methods
    $stmt = $pdo->prepare("SELECT * FROM payment_methods WHERE user_id = 1 AND processor = 'paypal' ORDER BY created DESC LIMIT 1");
    $stmt->execute();
    $paypal_method = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$paypal_method) {
        echo "❌ No PayPal payment method found for user 1\n";
        echo "Please add a PayPal payment method through the frontend first.\n";
        exit(1);
    }
    
    echo "Found PayPal payment method:\n";
    echo "- ID: {$paypal_method['id']}\n";
    echo "- Status: {$paypal_method['status']}\n";
    echo "- Setup Token: {$paypal_method['setup_token']}\n";
    echo "- Vault ID: " . ($paypal_method['vault_id'] ?: 'None') . "\n";
    echo "- Is Default: {$paypal_method['is_default']}\n\n";
    
    if ($paypal_method['status'] === 'active' && !empty($paypal_method['vault_id'])) {
        echo "✅ PayPal payment method is already active and vaulted!\n";
        echo "You can test automatic payments now.\n";
        exit(0);
    }
    
    if (empty($paypal_method['setup_token'])) {
        echo "❌ No setup token found. Please create a new PayPal payment method.\n";
        exit(1);
    }
    
    $setup_token = $paypal_method['setup_token'];
    echo "🔄 Testing PayPal vaulting process with setup token: $setup_token\n\n";
    
    // Get access token
    echo "1. Getting PayPal access token...\n";
    $access_token = getPayPalAccessToken($paypal_client_id, $paypal_secret, $api_base);
    echo "✅ Got access token\n\n";
    
    // Check setup token status
    echo "2. Checking setup token status...\n";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_base . '/v3/vault/setup-tokens/' . $setup_token);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $access_token
    ]);

    $setup_response = curl_exec($ch);
    $setup_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "Setup token check HTTP code: $setup_http_code\n";
    
    if ($setup_http_code === 200) {
        $setup_details = json_decode($setup_response, true);
        echo "Setup token status: " . ($setup_details['status'] ?? 'unknown') . "\n";
        
        if (isset($setup_details['status']) && $setup_details['status'] === 'APPROVED') {
            echo "✅ Setup token is approved! Proceeding to vault...\n\n";
            
            // Create vault token
            echo "3. Creating PayPal vault token...\n";
            $vault_data = [
                'payment_source' => [
                    'token' => [
                        'id' => $setup_token,
                        'type' => 'SETUP_TOKEN'
                    ]
                ]
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_base . '/v3/vault/payment-tokens');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($vault_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $access_token,
                'PayPal-Request-Id: ' . uniqid('vault_', true)
            ]);

            $vault_response = curl_exec($ch);
            $vault_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            echo "Vault creation HTTP code: $vault_http_code\n";
            echo "Vault response: $vault_response\n\n";

            if ($vault_http_code === 201) {
                $vault_result = json_decode($vault_response, true);
                
                if (isset($vault_result['id'])) {
                    echo "✅ Successfully created vault token: {$vault_result['id']}\n";
                    
                    // Update database
                    $updateStmt = $pdo->prepare("UPDATE payment_methods 
                                               SET status = 'active', 
                                                   vault_id = :vault_id,
                                                   payment_method_id = :payment_method_id
                                               WHERE id = :id");
                    
                    $updateStmt->bindValue(":vault_id", $vault_result['id']);
                    $updateStmt->bindValue(":payment_method_id", $vault_result['id']);
                    $updateStmt->bindValue(":id", $paypal_method['id']);
                    
                    if ($updateStmt->execute()) {
                        echo "✅ Updated database with real vault ID\n";
                        echo "\n🎉 PayPal vaulting completed successfully!\n";
                        echo "You can now test automatic payments with the real PayPal API.\n";
                    } else {
                        echo "❌ Failed to update database\n";
                    }
                } else {
                    echo "❌ No vault ID in response\n";
                }
            } else {
                echo "❌ Failed to create vault token\n";
                $error_data = json_decode($vault_response, true);
                if (isset($error_data['details'])) {
                    foreach ($error_data['details'] as $detail) {
                        echo "Error: {$detail['description']}\n";
                    }
                }
            }
        } else {
            echo "❌ Setup token is not approved\n";
            echo "Status: " . ($setup_details['status'] ?? 'unknown') . "\n";
            echo "You need to approve the PayPal setup through the frontend first.\n";
        }
    } else {
        echo "❌ Failed to get setup token details\n";
        echo "Response: $setup_response\n";
        
        if ($setup_http_code === 404) {
            echo "Setup token not found or expired. Please create a new one.\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?> 