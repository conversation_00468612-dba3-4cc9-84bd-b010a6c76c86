<?php
/**
 * Quick fix to activate PayPal payment method for testing
 */

require_once("./mysql.php");

try {
    echo "Fixing PayPal payment method status...\n";
    
    // Update the PayPal payment method to active with a simulated vault ID
    $updateStmt = $pdo->prepare("UPDATE payment_methods 
                               SET status = 'active', 
                                   vault_id = CONCAT('VAULT_', setup_token),
                                   payment_method_id = CONCAT('VAULT_', setup_token)
                               WHERE user_id = 1 
                               AND processor = 'paypal' 
                               AND status = 'pending'
                               AND setup_token IS NOT NULL");
    
    if ($updateStmt->execute()) {
        $rowsAffected = $updateStmt->rowCount();
        echo "✓ Updated $rowsAffected PayPal payment method(s) to active status\n";
        
        // Check the updated payment method
        $checkStmt = $pdo->prepare("SELECT * FROM payment_methods WHERE user_id = 1 AND processor = 'paypal'");
        $checkStmt->execute();
        $method = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($method) {
            echo "Updated payment method details:\n";
            echo "- ID: {$method['id']}\n";
            echo "- Status: {$method['status']}\n";
            echo "- Vault ID: {$method['vault_id']}\n";
            echo "- Payment Method ID: {$method['payment_method_id']}\n";
            echo "- Is Default: {$method['is_default']}\n";
        }
        
        echo "\n✓ PayPal payment method is now ready for automatic payments!\n";
        echo "You can now test the invoice generation script again.\n";
        
    } else {
        echo "✗ No PayPal payment methods were updated\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?> 