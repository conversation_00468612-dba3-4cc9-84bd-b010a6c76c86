<?php
/**
 * Test script for checking SolusVM server status
 * 
 * This script makes a direct API call to the SolusVM API to check a server's status.
 * Usage: php check_status.php <server_id>
 */

// Check if server ID is provided
if ($argc < 2) {
    echo "Usage: php check_status.php <server_id>\n";
    exit(1);
}

// Get server ID from command line argument
$server_id = $argv[1];

// API configuration
$apiKey = '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
$baseUrl = 'https://virt.zetservers.com';

// Fetch the server list
echo "Checking status for server ID: $server_id\n";
$apiEndpoint = $baseUrl . '/api/v1/servers';
echo "API Endpoint: $apiEndpoint\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiEndpoint);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $apiKey,
    'Accept: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if (curl_errno($ch)) {
    echo "cURL Error: " . curl_error($ch) . "\n";
    exit(1);
}

curl_close($ch);

// Process API response
$apiData = json_decode($response, true);

// Check if we got a valid response
if ($httpCode != 200 || !$apiData || !isset($apiData['data']) || empty($apiData['data'])) {
    echo "SolusVM API Error: " . ($apiData['message'] ?? 'Unknown error') . " (HTTP Code: $httpCode)\n";
    exit(1);
}

// Find the server with the matching ID
$serverFound = false;
echo "Looking for server ID: $server_id in " . count($apiData['data']) . " servers\n";

foreach ($apiData['data'] as $server) {
    // Convert both IDs to strings for comparison to avoid type issues
    if ((string)$server['id'] === (string)$server_id) {
        $serverFound = true;
        $status = $server['status'] ?? 'unknown';
        
        echo "\n=== Server Details ===\n";
        echo "Server ID: " . $server['id'] . "\n";
        echo "Status: " . $status . "\n";
        
        // Map status to our standardized states
        $powerState = 'unknown';
        $status = strtolower($status);
        
        if (strpos($status, 'online') !== false || strpos($status, 'active') !== false || 
            $status === 'running' || $status === 'on' || $status === 'started') {
            $powerState = 'online';
        } else if (strpos($status, 'offline') !== false || $status === 'stopped' || $status === 'off') {
            $powerState = 'offline';
        } else if (strpos($status, 'restart') !== false || strpos($status, 'reboot') !== false) {
            $powerState = 'restarting';
        }
        
        echo "Mapped Power State: " . $powerState . "\n";
        
        // Display additional server information if available
        if (isset($server['hostname'])) {
            echo "Hostname: " . $server['hostname'] . "\n";
        }
        
        if (isset($server['ip_addresses']) && !empty($server['ip_addresses'])) {
            echo "IP Addresses:\n";
            foreach ($server['ip_addresses'] as $ip) {
                echo "  - " . $ip . "\n";
            }
        }
        
        if (isset($server['plan'])) {
            echo "Plan: " . $server['plan'] . "\n";
        }
        
        break;
    }
}

if (!$serverFound) {
    echo "Server with ID $server_id not found in API response\n";
    exit(1);
}

echo "\nStatus check completed.\n";
