<?php
// Include database connection
require_once("mysql.php");
require_once("auth_handler.php");

// Get data from request
$data = json_decode(file_get_contents('php://input'), true);
$token = $data['token'] ?? '';

// Initialize response with referral_count
$response = [
    'error' => 1,
    'message' => 'Authentication required',
    'referral_count' => 0  // CRITICAL FIX: Always include referral_count
];

// Authenticate user
if (empty($token)) {
    // CRITICAL FIX: Use the same guaranteed response structure for early exits
    $finalResponse = [
        'error' => 1,
        'message' => 'Authentication required',
        'referral_count' => 0,
        'service_count' => 0  // CRITICAL FIX: Include both property names for compatibility
    ];

    header('Content-Type: application/json');
    echo json_encode($finalResponse);
    exit;
}

// Create auth handler
$auth = new Auth($pdo);

// Verify token
$user = $auth->verifyToken($token);

if (!$user) {
    // CRITICAL FIX: Use the same guaranteed response structure for early exits
    $finalResponse = [
        'error' => 1,
        'message' => 'Invalid authentication token',
        'referral_count' => 0,
        'service_count' => 0  // CRITICAL FIX: Include both property names for compatibility
    ];

    header('Content-Type: application/json');
    echo json_encode($finalResponse);
    exit;
}

// Get user ID
$user_id = $user['id'];

try {
    // Enable error reporting for debugging
    error_log("Starting get_commission_rates.php execution");

    // Check if commission_rates table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'commission_rates'");
    error_log("Table check result: " . $tableCheck->rowCount());

    if ($tableCheck->rowCount() == 0) {
        error_log("Commission rates table does not exist, creating it");
        // Create commission_rates table if it doesn't exist
        $pdo->exec("
            CREATE TABLE `commission_rates` (
                `id` int NOT NULL AUTO_INCREMENT,
                `level` varchar(50) NOT NULL,
                `requirement` varchar(100) NOT NULL,
                `min_services` int NOT NULL,
                `rate` decimal(5,2) NOT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
        ");

        // Insert default commission rates
        $pdo->exec("
            INSERT INTO `commission_rates` (`level`, `requirement`, `min_services`, `rate`) VALUES
            ('Bronze', '5+ Active Referrals', 5, 5.00),
            ('Silver', '20+ Active Referrals', 20, 10.00),
            ('Gold', '50+ Active Referrals', 50, 15.00),
            ('Platinum', '100+ Active Referrals', 100, 20.00);
        ");
        error_log("Default commission rates inserted");
    }

    // Get commission rates from database
    error_log("Fetching commission rates from database");
    $ratesQuery = $pdo->query("SELECT * FROM commission_rates ORDER BY min_services ASC");
    $rates = $ratesQuery->fetchAll(PDO::FETCH_ASSOC);
    error_log("Found " . count($rates) . " commission rate records");

    // ========== ULTRA SIMPLE REFERRAL COUNT IMPLEMENTATION ==========
    // Initialize referral count to 0
    $referralCount = 0;

    error_log("Starting ultra simple referral count implementation for user ID: " . $user_id);

    try {
        // First ensure the referrer_id column exists
        $columnCheck = $pdo->query("SHOW COLUMNS FROM users LIKE 'referrer_id'");
        if ($columnCheck->rowCount() == 0) {
            // Column doesn't exist, create it
            $pdo->exec("ALTER TABLE users ADD COLUMN referrer_id int DEFAULT NULL");
            error_log("Added referrer_id column to users table");
            // No referrals yet since we just created the column
            $referralCount = 0;
        } else {
            // Use a direct, simple query to count referrals
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE referrer_id = ?");
            $stmt->execute([$user_id]);
            $referralCount = (int)$stmt->fetchColumn();

            error_log("Simple query found " . $referralCount . " referrals for user ID: " . $user_id);

            // Get the actual list for verification
            $listStmt = $pdo->prepare("SELECT id FROM users WHERE referrer_id = ?");
            $listStmt->execute([$user_id]);
            $referredUsers = $listStmt->fetchAll(PDO::FETCH_ASSOC);

            // Log the actual count and IDs for debugging
            $actualCount = count($referredUsers);
            error_log("Actual count from list: " . $actualCount);

            if ($actualCount != $referralCount) {
                error_log("WARNING: Count mismatch! Using actual count from list.");
                $referralCount = $actualCount;
            }

            // Log the IDs for debugging
            $idList = [];
            foreach ($referredUsers as $user) {
                $idList[] = $user['id'];
            }
            error_log("Referred user IDs: " . implode(', ', $idList ?: ['none']));
        }
    } catch (Exception $e) {
        error_log("Error counting referrals: " . $e->getMessage());
        $referralCount = 0;
    }

    // Final cast to ensure it's an integer
    $referralCount = (int)$referralCount;
    error_log("Final referral count: " . $referralCount);
    // ========== END ULTRA SIMPLE REFERRAL COUNT IMPLEMENTATION ==========

    // Determine user's level based on referral count
    $userLevel = 'Bronze'; // Default level
    foreach ($rates as $rate) {
        if ($referralCount >= $rate['min_services']) {
            $userLevel = $rate['level'];
        } else {
            break;
        }
    }
    error_log("User level determined as: " . $userLevel);

    // Format rates for response
    $formattedRates = [];
    foreach ($rates as $rate) {
        // Ensure min_services is properly cast to integer
        $min_services = isset($rate['min_services']) ? (int)$rate['min_services'] : 0;

        $formattedRates[] = [
            'level' => $rate['level'],
            'requirement' => $rate['requirement'],
            'min_services' => $min_services,
            'rate' => (float)$rate['rate']
        ];

        error_log("Formatted rate: level={$rate['level']}, min_services={$min_services}");
    }
    error_log("Formatted " . count($formattedRates) . " commission rates for response");

    // ========== BASIC RESPONSE CREATION ==========
    // Final cast to ensure referral count is an integer
    $referralCount = (int)$referralCount;

    // Create a clean response with all required fields
    $response = [
        'error' => 0,
        'rates' => $formattedRates,
        'user_level' => $userLevel,
        'referral_count' => $referralCount,
        'service_count' => $referralCount  // CRITICAL FIX: Include both property names for compatibility
    ];

    // Log the response for debugging
    error_log("CRITICAL: Final referral_count value: " . $referralCount . " (type: " . gettype($referralCount) . ")");
    // ========== END BASIC RESPONSE CREATION ==========

} catch (Exception $e) {
    // Log the error
    error_log("Error in get_commission_rates.php: " . $e->getMessage());

    // Create a simple error response with both count properties explicitly set to 0
    $response = [
        'error' => 1,
        'message' => 'Error retrieving commission rates',
        'referral_count' => 0,
        'service_count' => 0  // CRITICAL FIX: Include both property names for compatibility
    ];

    // Log the error response
    error_log("Error response created with referral_count = 0");
}

// ========== GUARANTEED RESPONSE WITH REFERRAL COUNT ==========
// CRITICAL FIX: Create a completely new response array with guaranteed structure
$finalResponse = [
    'error' => isset($response['error']) ? (int)$response['error'] : 0,
    'message' => isset($response['message']) ? $response['message'] : '',
    'referral_count' => 0,  // Default to 0
    'service_count' => 0    // CRITICAL FIX: Include both property names for compatibility
];

// Add the referral count if it exists in the original response
if (isset($response['referral_count'])) {
    $finalResponse['referral_count'] = (int)$response['referral_count'];
    $finalResponse['service_count'] = (int)$response['referral_count'];  // Use same value for both
} else if (isset($response['service_count'])) {
    // If only service_count exists, use it for both
    $finalResponse['referral_count'] = (int)$response['service_count'];
    $finalResponse['service_count'] = (int)$response['service_count'];
}

// Add other fields from the original response
if (isset($response['rates'])) {
    $finalResponse['rates'] = $response['rates'];
}
if (isset($response['user_level'])) {
    $finalResponse['user_level'] = $response['user_level'];
}

// Log the final response structure
error_log("FINAL RESPONSE STRUCTURE: " . print_r($finalResponse, true));
error_log("GUARANTEED referral_count = " . $finalResponse['referral_count']);

// Send the JSON response
header('Content-Type: application/json');
echo json_encode($finalResponse);
// ========== END DIRECT JSON RESPONSE ==========
