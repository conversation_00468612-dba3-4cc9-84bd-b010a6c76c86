<?php
// Include database connection
require_once("mysql.php");

// VPS ID to check
$vps_id = 1973;

// Function to print results in a readable format
function printResult($title, $data) {
    echo "\n=== $title ===\n";
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                echo "$key: " . json_encode($value) . "\n";
            } else {
                echo "$key: $value\n";
            }
        }
    } else {
        echo $data . "\n";
    }
    echo "\n";
}

try {
    // Check if the VPS exists in orders_items
    $stmt = $pdo->prepare("
        SELECT oi.*, o.order_type
        FROM orders_items oi
        LEFT JOIN orders o ON oi.order_id = o.id
        WHERE oi.id = :vps_id
    ");
    $stmt->bindValue(':vps_id', $vps_id);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        printResult("VPS Check", "VPS ID $vps_id does not exist in orders_items table");
        exit;
    }
    
    $vps_data = $stmt->fetch(PDO::FETCH_ASSOC);
    printResult("VPS Data", $vps_data);
    
    // Check if server_id exists
    if (empty($vps_data['server_id'])) {
        printResult("Server ID Check", "VPS ID $vps_id has no server_id (SolusVM ID)");
    } else {
        printResult("Server ID Check", "VPS ID $vps_id has server_id: " . $vps_data['server_id']);
    }
    
    // Check user ownership
    $user_id = $vps_data['user_id'];
    if (empty($user_id)) {
        printResult("User Check", "VPS ID $vps_id has no associated user_id");
    } else {
        // Get user details
        $user_stmt = $pdo->prepare("SELECT id, email FROM users WHERE id = :user_id");
        $user_stmt->bindValue(':user_id', $user_id);
        $user_stmt->execute();
        
        if ($user_stmt->rowCount() == 0) {
            printResult("User Check", "User ID $user_id does not exist in users table");
        } else {
            $user_data = $user_stmt->fetch(PDO::FETCH_ASSOC);
            printResult("User Check", "VPS ID $vps_id belongs to User ID: " . $user_data['id'] . " (Email: " . $user_data['email'] . ")");
        }
    }
    
    // Check type field
    if (isset($vps_data['type'])) {
        printResult("Type Check", "VPS ID $vps_id has type: " . $vps_data['type']);
    } else {
        printResult("Type Check", "VPS ID $vps_id has no type field");
    }
    
} catch (Exception $e) {
    printResult("Error", $e->getMessage());
}
