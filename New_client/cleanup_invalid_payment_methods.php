<?php
/**
 * Utility script to clean up invalid payment methods
 * This script checks stored Stripe payment methods against Stripe's API
 * and marks invalid ones as inactive
 */

// Include database connection
$scriptDir = dirname(__FILE__);
require_once($scriptDir . "/mysql.php");

// Include Stripe library if available
if (file_exists($scriptDir . '/vendor/autoload.php')) {
    require_once($scriptDir . '/vendor/autoload.php');
} elseif (file_exists('./vendor/autoload.php')) {
    require_once('./vendor/autoload.php');
}

// Set error handling
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

$log_prefix = "[" . date('Y-m-d H:i:s') . "] ";
echo $log_prefix . "Starting payment method cleanup\n";

// Statistics
$stats = [
    'total_stripe_methods' => 0,
    'invalid_methods_found' => 0,
    'methods_marked_inactive' => 0,
    'errors' => 0
];

try {
    // Initialize Stripe
    if (!class_exists('\Stripe\Stripe')) {
        echo "Error: Stripe library not available\n";
        exit(1);
    }
    
    if (!defined('STRIPE_SECRET_KEY')) {
        define('STRIPE_SECRET_KEY', 'sk_test_51RR8eZQR2z1KpIsl2jb8CYDtLmuDTbEJzq801T4D40Mga3uPVCc22lGHigIqrsQw8tl9bBA2J59EsAOAU29zmZlD00Zbz5zgqn');
    }
    
    \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
    echo $log_prefix . "Stripe initialized successfully\n";

    // Get all active Stripe payment methods
    $query = "SELECT * FROM payment_methods 
              WHERE processor = 'stripe' 
              AND status = 'active' 
              ORDER BY user_id, created DESC";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $payment_methods = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $stats['total_stripe_methods'] = count($payment_methods);
    echo $log_prefix . "Found {$stats['total_stripe_methods']} active Stripe payment methods to check\n";

    // Check each payment method
    foreach ($payment_methods as $method) {
        try {
            echo $log_prefix . "Checking payment method {$method['payment_method_id']} for user {$method['user_id']}\n";
            
            // Try to retrieve the payment method from Stripe
            $stripePaymentMethod = \Stripe\PaymentMethod::retrieve($method['payment_method_id']);
            
            echo $log_prefix . "✓ Payment method {$method['payment_method_id']} is valid\n";
            
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            echo $log_prefix . "✗ Payment method {$method['payment_method_id']} not found in Stripe: " . $e->getMessage() . "\n";
            $stats['invalid_methods_found']++;
            
            // Mark as inactive
            try {
                $updateStmt = $pdo->prepare("UPDATE payment_methods SET status = 'inactive' WHERE id = :id");
                $updateStmt->bindValue(":id", $method['id']);
                $updateStmt->execute();
                
                $stats['methods_marked_inactive']++;
                echo $log_prefix . "  → Marked payment method ID {$method['id']} as inactive\n";
                
                // If this was a default payment method, try to set another one as default
                if ($method['is_default'] == 1) {
                    $newDefaultStmt = $pdo->prepare("
                        UPDATE payment_methods 
                        SET is_default = 1 
                        WHERE user_id = :user_id 
                        AND processor = 'stripe' 
                        AND status = 'active' 
                        AND id != :excluded_id
                        ORDER BY created DESC 
                        LIMIT 1
                    ");
                    $newDefaultStmt->bindValue(":user_id", $method['user_id']);
                    $newDefaultStmt->bindValue(":excluded_id", $method['id']);
                    $newDefaultStmt->execute();
                    
                    if ($newDefaultStmt->rowCount() > 0) {
                        echo $log_prefix . "  → Set new default payment method for user {$method['user_id']}\n";
                    } else {
                        echo $log_prefix . "  → Warning: No alternative payment method found for user {$method['user_id']}\n";
                    }
                }
                
            } catch (Exception $updateError) {
                echo $log_prefix . "  → Error marking payment method as inactive: " . $updateError->getMessage() . "\n";
                $stats['errors']++;
            }
            
        } catch (Exception $e) {
            echo $log_prefix . "Error checking payment method {$method['payment_method_id']}: " . $e->getMessage() . "\n";
            $stats['errors']++;
        }
        
        // Small delay to avoid hitting rate limits
        usleep(100000); // 0.1 seconds
    }

    // Summary
    echo "\n" . $log_prefix . "=== CLEANUP SUMMARY ===\n";
    echo $log_prefix . "Total Stripe payment methods checked: {$stats['total_stripe_methods']}\n";
    echo $log_prefix . "Invalid methods found: {$stats['invalid_methods_found']}\n";
    echo $log_prefix . "Methods marked inactive: {$stats['methods_marked_inactive']}\n";
    echo $log_prefix . "Errors: {$stats['errors']}\n";
    
    if ($stats['invalid_methods_found'] > 0) {
        echo $log_prefix . "Cleanup completed. Invalid payment methods have been marked as inactive.\n";
        echo $log_prefix . "Users with invalid payment methods should add new ones for auto-renewal to work.\n";
    } else {
        echo $log_prefix . "All payment methods are valid. No cleanup needed.\n";
    }

} catch (Exception $e) {
    echo $log_prefix . "Fatal error: " . $e->getMessage() . "\n";
    exit(1);
}

exit(0); 