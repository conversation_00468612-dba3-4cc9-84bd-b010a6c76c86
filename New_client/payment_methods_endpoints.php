<?php
// Payment Method Management Endpoints for Automatic Renewals
// Add these endpoints to the end of your api.php file before the final closing brace

// Add Stripe payment method for automatic renewals
if ($_GET["f"] == "add_stripe_payment_method") {
    try {
        $data = json_decode(file_get_contents("php://input"), true);
        $user_id = auth_user();
        
        if (!$user_id) {
            $reply["error"] = "Authentication required";
            die(json_encode($reply));
        }

        // Validate required fields
        if (!isset($data["card_number"]) || !isset($data["card_name"]) || 
            !isset($data["expiry_month"]) || !isset($data["expiry_year"]) || !isset($data["cvv"])) {
            $reply["error"] = "Missing required card information";
            die(json_encode($reply));
        }

        // Create payment_methods table if it doesn't exist
        $pdo->exec("CREATE TABLE IF NOT EXISTS `payment_methods` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `processor` enum('stripe','paypal') NOT NULL,
            `payment_method_id` varchar(255) DEFAULT NULL,
            `customer_id` varchar(255) DEFAULT NULL,
            `last_four` varchar(4) DEFAULT NULL,
            `card_brand` varchar(50) DEFAULT NULL,
            `expiry_month` int(2) DEFAULT NULL,
            `expiry_year` int(4) DEFAULT NULL,
            `payer_email` varchar(255) DEFAULT NULL,
            `status` enum('active','inactive','expired') NOT NULL DEFAULT 'active',
            `is_default` tinyint(1) NOT NULL DEFAULT 0,
            `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `processor` (`processor`),
            KEY `is_default` (`is_default`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        // Get user details for Stripe customer creation
        $userStmt = $pdo->prepare("SELECT * FROM users WHERE id = :user_id");
        $userStmt->bindValue(":user_id", $user_id);
        $userStmt->execute();
        $user = $userStmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            $reply["error"] = "User not found";
            die(json_encode($reply));
        }

        // Simulate Stripe customer and payment method creation
        $simulated_customer_id = 'cus_' . uniqid();
        $simulated_payment_method_id = 'pm_' . uniqid();
        $last_four = substr($data["card_number"], -4);
        
        // Determine card brand from number
        $card_brand = 'card';
        if (preg_match('/^4/', $data["card_number"])) {
            $card_brand = 'visa';
        } elseif (preg_match('/^5[1-5]/', $data["card_number"])) {
            $card_brand = 'mastercard';
        } elseif (preg_match('/^3[47]/', $data["card_number"])) {
            $card_brand = 'amex';
        }

        // Check if this is the user's first payment method
        $countStmt = $pdo->prepare("SELECT COUNT(*) as count FROM payment_methods WHERE user_id = :user_id");
        $countStmt->bindValue(":user_id", $user_id);
        $countStmt->execute();
        $is_first = $countStmt->fetch(PDO::FETCH_ASSOC)['count'] == 0;

        // Insert payment method
        $insertStmt = $pdo->prepare("INSERT INTO payment_methods 
            (user_id, processor, payment_method_id, customer_id, last_four, card_brand, expiry_month, expiry_year, status, is_default) 
            VALUES (:user_id, 'stripe', :payment_method_id, :customer_id, :last_four, :card_brand, :expiry_month, :expiry_year, 'active', :is_default)");
        
        $insertStmt->bindValue(":user_id", $user_id);
        $insertStmt->bindValue(":payment_method_id", $simulated_payment_method_id);
        $insertStmt->bindValue(":customer_id", $simulated_customer_id);
        $insertStmt->bindValue(":last_four", $last_four);
        $insertStmt->bindValue(":card_brand", $card_brand);
        $insertStmt->bindValue(":expiry_month", intval($data["expiry_month"]));
        $insertStmt->bindValue(":expiry_year", intval($data["expiry_year"]));
        $insertStmt->bindValue(":is_default", $is_first ? 1 : 0);
        
        if ($insertStmt->execute()) {
            $reply["success"] = true;
            $reply["message"] = "Stripe payment method added successfully";
            $reply["payment_method_id"] = $pdo->lastInsertId();
        } else {
            $reply["error"] = "Failed to save payment method";
        }

    } catch (Exception $e) {
        error_log("Error adding Stripe payment method: " . $e->getMessage());
        $reply["error"] = "Failed to add payment method: " . $e->getMessage();
    }
    
    die(json_encode($reply));
}

// Add PayPal payment method for automatic renewals using Vaulting API
elseif ($_GET["f"] == "add_paypal_payment_method") {
    try {
        $data = json_decode(file_get_contents("php://input"), true);
        $user_id = auth_user();
        
        if (!$user_id) {
            $reply["error"] = "Authentication required";
            die(json_encode($reply));
        }

        // Create payment_methods table if it doesn't exist (updated for vaulting)
        $pdo->exec("CREATE TABLE IF NOT EXISTS `payment_methods` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `processor` enum('stripe','paypal') NOT NULL,
            `payment_method_id` varchar(255) DEFAULT NULL,
            `customer_id` varchar(255) DEFAULT NULL,
            `last_four` varchar(4) DEFAULT NULL,
            `card_brand` varchar(50) DEFAULT NULL,
            `expiry_month` int(2) DEFAULT NULL,
            `expiry_year` int(4) DEFAULT NULL,
            `payer_email` varchar(255) DEFAULT NULL,
            `payer_id` varchar(255) DEFAULT NULL,
            `vault_id` varchar(255) DEFAULT NULL,
            `setup_token` varchar(255) DEFAULT NULL,
            `status` enum('active','inactive','expired','pending') NOT NULL DEFAULT 'pending',
            `is_default` tinyint(1) NOT NULL DEFAULT 0,
            `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `processor` (`processor`),
            KEY `is_default` (`is_default`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        // PayPal configuration
        if (!defined('PAYPAL_CLIENT_ID')) {
            define('PAYPAL_CLIENT_ID', 'AexATmG6J-fd_YGA3h-xCNBQxf8l1btDDfbfciytmiAemB1Dqc58E8L-1HiKogTEg0jkre8nItHCRv50');
            define('PAYPAL_SECRET', 'EM-vDbIrr0Els7tRqezoR3v2Kk4-b1-4COXMxUNe5sq9vtdJ9kwrRokJWMkgjpor6y8tX-fJ7pA650KV');
            define('PAYPAL_SANDBOX', true);
        }

        // Get user details
        $userStmt = $pdo->prepare("SELECT * FROM users WHERE id = :user_id");
        $userStmt->bindValue(":user_id", $user_id);
        $userStmt->execute();
        $user = $userStmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            $reply["error"] = "User not found";
            die(json_encode($reply));
        }

        $api_base = PAYPAL_SANDBOX ? 'https://api-m.sandbox.paypal.com' : 'https://api-m.paypal.com';
        
        // Step 1: Get PayPal access token
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_base . '/v1/oauth2/token');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
        curl_setopt($ch, CURLOPT_USERPWD, PAYPAL_CLIENT_ID . ':' . PAYPAL_SECRET);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Accept: application/json', 'Accept-Language: en_US'));

        $auth_response = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);
        
        if ($curl_error) {
            throw new Exception('PayPal connection error: ' . $curl_error);
        }
        
        $auth_data = json_decode($auth_response, true);
        if (!isset($auth_data['access_token'])) {
            throw new Exception('Failed to get PayPal access token: ' . ($auth_data['error_description'] ?? 'Unknown error'));
        }

        $access_token = $auth_data['access_token'];

        // Step 2: Try vaulting first, fallback to billing agreements if insufficient permissions
        $use_vaulting = true;
        
        // Test vaulting permissions first
        $test_data = [
            'payment_source' => [
                'paypal' => [
                    'description' => 'Test permission check',
                    'usage_pattern' => 'IMMEDIATE',
                    'usage_type' => 'MERCHANT',
                    'customer_type' => 'CONSUMER'
                ]
            ]
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_base . '/v3/vault/setup-tokens');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $access_token,
            'PayPal-Request-Id: ' . uniqid('test_', true)
        ]);

        $test_response = curl_exec($ch);
        $test_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        // If vaulting not available, use billing agreements
        if ($test_http_code === 403 || $test_http_code === 401) {
            $use_vaulting = false;
            error_log("Vaulting permissions not available, using billing agreements fallback");
        }

        if ($use_vaulting) {
            // Use vaulting approach
            $setup_data = [
                'payment_source' => [
                    'paypal' => [
                        'description' => 'Payment method for automatic renewals',
                        'usage_pattern' => 'IMMEDIATE',
                        'usage_type' => 'MERCHANT',
                        'customer_type' => 'CONSUMER',
                        'experience_context' => [
                            'shipping_preference' => 'NO_SHIPPING',
                            'payment_method_preference' => 'IMMEDIATE_PAYMENT_REQUIRED',
                            'brand_name' => 'X-Zone IT Services',
                            'locale' => 'en-US',
                            'return_url' => $data['return_url'] ?? 'https://client.x-zoneit.ro/billing?paypal_success=1',
                            'cancel_url' => $data['cancel_url'] ?? 'https://client.x-zoneit.ro/billing?paypal_cancel=1'
                        ]
                    ]
                ]
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_base . '/v3/vault/setup-tokens');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($setup_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $access_token,
                'PayPal-Request-Id: ' . uniqid('setup_', true)
            ]);
        } else {
            // Fallback to billing agreements
            $agreement_data = [
                'name' => 'Auto-Renewal Billing Agreement',
                'description' => 'Billing agreement for automatic service renewals',
                'start_date' => date('c', strtotime('+1 hour')),
                'plan' => [
                    'id' => 'P-5ML4271244454362WXNWU5NQ' // Use a simple plan or create one
                ],
                'payer' => [
                    'payment_method' => 'paypal'
                ]
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_base . '/v1/payments/billing-agreements');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($agreement_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $access_token,
                'PayPal-Request-Id: ' . uniqid('setup_', true)
            ]);
        }

        $setup_response = curl_exec($ch);
        $setup_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($setup_http_code !== 201) {
            $error_data = json_decode($setup_response, true);
            throw new Exception('Failed to create PayPal setup token: ' . ($error_data['message'] ?? 'HTTP ' . $setup_http_code));
        }

        $setup_result = json_decode($setup_response, true);
        
        // Find the approve URL from the links
        $approval_url = null;
        if (isset($setup_result['links'])) {
            foreach ($setup_result['links'] as $link) {
                if ($link['rel'] === 'approve') {
                    $approval_url = $link['href'];
                    break;
                }
            }
        }

        if (!$approval_url) {
            throw new Exception('No approval URL found in PayPal response');
        }

        // Check if this is the user's first payment method
        $countStmt = $pdo->prepare("SELECT COUNT(*) as count FROM payment_methods WHERE user_id = :user_id");
        $countStmt->bindValue(":user_id", $user_id);
        $countStmt->execute();
        $is_first = $countStmt->fetch(PDO::FETCH_ASSOC)['count'] == 0;

        // Store the setup token temporarily (pending approval)
        $insertStmt = $pdo->prepare("INSERT INTO payment_methods 
            (user_id, processor, setup_token, payer_email, status, is_default) 
            VALUES (:user_id, 'paypal', :setup_token, :payer_email, 'pending', :is_default)");
        
        $insertStmt->bindValue(":user_id", $user_id);
        $insertStmt->bindValue(":setup_token", $setup_result['id']);
        $insertStmt->bindValue(":payer_email", $user['email']);
        $insertStmt->bindValue(":is_default", $is_first ? 1 : 0);
        
        if ($insertStmt->execute()) {
            $reply["success"] = true;
            $reply["message"] = "PayPal setup token created. Please approve to vault your payment method.";
            $reply["payment_method_id"] = $pdo->lastInsertId();
            $reply["approval_url"] = $approval_url;
            $reply["setup_token"] = $setup_result['id'];
        } else {
            $reply["error"] = "Failed to save PayPal setup token";
        }

    } catch (Exception $e) {
        error_log("Error adding PayPal payment method: " . $e->getMessage());
        $reply["error"] = "Failed to add PayPal payment method: " . $e->getMessage();
    }
    
    die(json_encode($reply));
}

// Get user's payment methods
elseif ($_GET["f"] == "get_payment_methods") {
    try {
        $user_id = auth_user();
        
        if (!$user_id) {
            $reply["error"] = "Authentication required";
            die(json_encode($reply));
        }

        // Get payment methods
        $stmt = $pdo->prepare("SELECT * FROM payment_methods WHERE user_id = :user_id ORDER BY is_default DESC, created DESC");
        $stmt->bindValue(":user_id", $user_id);
        $stmt->execute();
        $payment_methods = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($payment_methods)) {
            $reply["error"] = "No payment methods found";
            die(json_encode($reply));
        }

        $reply = $payment_methods;

    } catch (Exception $e) {
        error_log("Error getting payment methods: " . $e->getMessage());
        $reply["error"] = "Failed to get payment methods: " . $e->getMessage();
    }
    
    die(json_encode($reply));
}

// Set default payment method
elseif ($_GET["f"] == "set_default_payment_method") {
    try {
        $data = json_decode(file_get_contents("php://input"), true);
        $user_id = auth_user();
        
        if (!$user_id) {
            $reply["error"] = "Authentication required";
            die(json_encode($reply));
        }

        if (!isset($data["payment_method_id"])) {
            $reply["error"] = "Payment method ID required";
            die(json_encode($reply));
        }

        $payment_method_id = $data["payment_method_id"];

        // Begin transaction
        $pdo->beginTransaction();

        // Remove default flag from all user's payment methods
        $updateAllStmt = $pdo->prepare("UPDATE payment_methods SET is_default = 0 WHERE user_id = :user_id");
        $updateAllStmt->bindValue(":user_id", $user_id);
        $updateAllStmt->execute();

        // Set the selected payment method as default
        $updateDefaultStmt = $pdo->prepare("UPDATE payment_methods SET is_default = 1 WHERE id = :payment_method_id AND user_id = :user_id");
        $updateDefaultStmt->bindValue(":payment_method_id", $payment_method_id);
        $updateDefaultStmt->bindValue(":user_id", $user_id);
        
        if ($updateDefaultStmt->execute() && $updateDefaultStmt->rowCount() > 0) {
            $pdo->commit();
            $reply["success"] = true;
            $reply["message"] = "Default payment method updated successfully";
        } else {
            $pdo->rollBack();
            $reply["error"] = "Payment method not found or access denied";
        }

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Error setting default payment method: " . $e->getMessage());
        $reply["error"] = "Failed to set default payment method: " . $e->getMessage();
    }
    
    die(json_encode($reply));
}

// Delete payment method
elseif ($_GET["f"] == "delete_payment_method") {
    try {
        $data = json_decode(file_get_contents("php://input"), true);
        $user_id = auth_user();
        
        if (!$user_id) {
            $reply["error"] = "Authentication required";
            die(json_encode($reply));
        }

        if (!isset($data["payment_method_id"])) {
            $reply["error"] = "Payment method ID required";
            die(json_encode($reply));
        }

        $payment_method_id = $data["payment_method_id"];

        // Get the payment method to check ownership and get details for cleanup
        $getStmt = $pdo->prepare("SELECT * FROM payment_methods WHERE id = :payment_method_id AND user_id = :user_id");
        $getStmt->bindValue(":payment_method_id", $payment_method_id);
        $getStmt->bindValue(":user_id", $user_id);
        $getStmt->execute();
        $payment_method = $getStmt->fetch(PDO::FETCH_ASSOC);

        if (!$payment_method) {
            $reply["error"] = "Payment method not found or access denied";
            die(json_encode($reply));
        }

        // Begin transaction
        $pdo->beginTransaction();

        // Delete the payment method
        $deleteStmt = $pdo->prepare("DELETE FROM payment_methods WHERE id = :payment_method_id AND user_id = :user_id");
        $deleteStmt->bindValue(":payment_method_id", $payment_method_id);
        $deleteStmt->bindValue(":user_id", $user_id);
        
        if ($deleteStmt->execute()) {
            // If this was the default payment method, set another one as default
            if ($payment_method['is_default'] == 1) {
                $setNewDefaultStmt = $pdo->prepare("UPDATE payment_methods SET is_default = 1 WHERE user_id = :user_id ORDER BY created DESC LIMIT 1");
                $setNewDefaultStmt->bindValue(":user_id", $user_id);
                $setNewDefaultStmt->execute();
            }

            $pdo->commit();
            $reply["success"] = true;
            $reply["message"] = "Payment method deleted successfully";
        } else {
            $pdo->rollBack();
            $reply["error"] = "Failed to delete payment method";
        }

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Error deleting payment method: " . $e->getMessage());
        $reply["error"] = "Failed to delete payment method: " . $e->getMessage();
    }
    
    die(json_encode($reply));
}

// Confirm PayPal vaulting after user approval
elseif ($_GET["f"] == "confirm_paypal_vaulting") {
    try {
        $data = json_decode(file_get_contents("php://input"), true);
        $user_id = auth_user();
        
        if (!$user_id) {
            $reply["error"] = "Authentication required";
            die(json_encode($reply));
        }

        $setup_token = $data['token'] ?? $_GET['token'] ?? null;
        if (!$setup_token) {
            $reply["error"] = "Setup token required";
            die(json_encode($reply));
        }

        // PayPal configuration
        if (!defined('PAYPAL_CLIENT_ID')) {
            define('PAYPAL_CLIENT_ID', 'AexATmG6J-fd_YGA3h-xCNBQxf8l1btDDfbfciytmiAemB1Dqc58E8L-1HiKogTEg0jkre8nItHCRv50');
            define('PAYPAL_SECRET', 'EM-vDbIrr0Els7tRqezoR3v2Kk4-b1-4COXMxUNe5sq9vtdJ9kwrRokJWMkgjpor6y8tX-fJ7pA650KV');
            define('PAYPAL_SANDBOX', true);
        }

        $api_base = PAYPAL_SANDBOX ? 'https://api-m.sandbox.paypal.com' : 'https://api-m.paypal.com';
        
        // Get PayPal access token
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_base . '/v1/oauth2/token');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
        curl_setopt($ch, CURLOPT_USERPWD, PAYPAL_CLIENT_ID . ':' . PAYPAL_SECRET);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Accept: application/json', 'Accept-Language: en_US'));

        $auth_response = curl_exec($ch);
        curl_close($ch);
        
        $auth_data = json_decode($auth_response, true);
        if (!isset($auth_data['access_token'])) {
            throw new Exception('Failed to get PayPal access token');
        }

        $access_token = $auth_data['access_token'];

        // Create payment token from approved setup token
        $vault_data = [
            'payment_source' => [
                'token' => [
                    'id' => $setup_token,
                    'type' => 'SETUP_TOKEN'
                ]
            ]
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_base . '/v3/vault/payment-tokens');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($vault_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $access_token,
            'PayPal-Request-Id: ' . uniqid('vault_', true)
        ]);

        $vault_response = curl_exec($ch);
        $vault_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($vault_http_code !== 201 && $vault_http_code !== 200) {
            $error_data = json_decode($vault_response, true);
            throw new Exception('Failed to create PayPal payment token: ' . ($error_data['message'] ?? 'HTTP ' . $vault_http_code));
        }

        $vault_result = json_decode($vault_response, true);
        
        if (!isset($vault_result['id'])) {
            throw new Exception('No payment token ID found in PayPal response');
        }

        // Extract payer information from the correct API response structure
        $payer_email = null;
        $payer_id = null;
        
        if (isset($vault_result['payment_source']['paypal']['email_address'])) {
            $payer_email = $vault_result['payment_source']['paypal']['email_address'];
        }
        if (isset($vault_result['payment_source']['paypal']['payer_id'])) {
            $payer_id = $vault_result['payment_source']['paypal']['payer_id'];
        }

        // Update payment method with vault information
        $updateStmt = $pdo->prepare("UPDATE payment_methods 
                                   SET status = 'active', 
                                       vault_id = :vault_id,
                                       payment_method_id = :payment_method_id,
                                       payer_email = :payer_email,
                                       updated = NOW()
                                   WHERE user_id = :user_id 
                                   AND processor = 'paypal' 
                                   AND setup_token = :setup_token");
        
        $updateStmt->bindValue(":vault_id", $vault_result['id']);
        $updateStmt->bindValue(":payment_method_id", $vault_result['id']); // Use vault_id as payment_method_id
        $updateStmt->bindValue(":payer_email", $payer_email);
        $updateStmt->bindValue(":user_id", $user_id);
        $updateStmt->bindValue(":setup_token", $setup_token);
        
        if ($updateStmt->execute() && $updateStmt->rowCount() > 0) {
            $reply["success"] = true;
            $reply["message"] = "PayPal payment method vaulted successfully";
            $reply["vault_id"] = $vault_result['id'];
            $reply["payer_email"] = $payer_email;
        } else {
            $reply["error"] = "Failed to update PayPal payment method";
        }

    } catch (Exception $e) {
        error_log("Error confirming PayPal vaulting: " . $e->getMessage());
        $reply["error"] = "Failed to confirm PayPal vaulting: " . $e->getMessage();
    }
    
    die(json_encode($reply));
}

?>