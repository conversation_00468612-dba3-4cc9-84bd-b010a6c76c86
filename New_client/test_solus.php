<?php
// Test script to debug SolusVM server creation
// Run this directly to see the exact error

$apiKey = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
$baseUrl = "https://virt.zetservers.com";
$project_id = 3;

$url = $baseUrl . "/api/v1/projects/{$project_id}/servers";

echo "\n=== Testing with MINIMAL payload ===\n";

$minimal_data = [
    "name" => "test-minimal-" . time(),
    "plan_id" => 1,
    "location_id" => 2, // Try with Default location
    "os_image_version_id" => 17, // Try with Ubuntu 20.04
    "password" => "testpass123"
];

echo "Minimal payload:\n";
echo json_encode($minimal_data, JSON_PRETTY_PRINT) . "\n\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($minimal_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $apiKey,
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response2 = curl_exec($ch);
$http_code2 = curl_getinfo($ch, CURLINFO_HTTP_CODE);

curl_close($ch);

echo "HTTP Response Code: $http_code2\n";
echo "Response: $response2\n\n";

