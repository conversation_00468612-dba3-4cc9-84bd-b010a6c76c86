# Enable rewrite engine
RewriteEngine On

# Force JSON content type for API requests
<IfModule mod_headers.c>
    # Set JSON headers for API responses
    <FilesMatch "api\.php$">
        Header set Content-Type "application/json" 
        Header set Access-Control-Allow-Origin "*"
        Header set Access-Control-Allow-Headers "Content-Type"
    </FilesMatch>
</IfModule>

# Redirect requests to /zet/#/vps-services/1973 to the help page
RewriteCond %{REQUEST_URI} ^/zet/$
RewriteCond %{QUERY_STRING} ^#/vps-services/1973$
RewriteRule ^(.*)$ /vps_help.html [L]

# Content Security Policy for Paddle payments - split into multiple headers to avoid truncation
Header always set Content-Security-Policy "default-src 'self'"
Header always append Content-Security-Policy "; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.paddle.com"
Header always append Content-Security-Policy "; frame-src 'self' https://buy.paddle.com"
Header always append Content-Security-Policy "; connect-src 'self' https://checkout.paddle.com"
Header always append Content-Security-Policy "; img-src 'self' data: https:"
Header always append Content-Security-Policy "; style-src 'self' 'unsafe-inline'"

# For direct access to /vps-services/1973
RewriteRule ^vps-services/1973$ /vps_help.html [L]

# Handle other VPS service requests with SolusVM IDs
RewriteCond %{REQUEST_URI} ^/vps-services/(.*)$
RewriteRule ^vps-services/(.*)$ /vps_redirect.php?id=$1 [L]

AddType text/css .css