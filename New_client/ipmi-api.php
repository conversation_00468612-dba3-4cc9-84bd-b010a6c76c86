<?php

$ch = curl_init();

curl_setopt($ch, CURLOPT_URL,"https://***********/data/login?console");
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, "user=root&password=InterNext321!");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);

curl_close($ch);

$xml = simplexml_load_string($response) or die("Error: Cannot create object");
$auth_result = $xml->authResult;
$forwardUrl = $xml->forwardUrl;
$url = "https://***********/".$forwardUrl;

if($auth_result == 5)
	echo 5;
else{

echo $url;

$ch = curl_init();

curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);

echo $response;

}
?>