<?php
/**
 * SolusVM v1 API Connection Test
 * 
 * This script tests the connection to the SolusVM v1 API
 * based on the findings from our previous tests.
 */

// API configuration
$apiKey = '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
$baseUrl = 'https://virt.zetservers.com';

echo "=================================================\n";
echo "        SolusVM v1 API Connection Test           \n";
echo "=================================================\n\n";

// Define v1 API endpoints to test
$endpoints = [
    '/api/v1/user/info',
    '/api/v1/servers',
    '/api/v1/plans',
    '/api/v1/locations',
    '/api/v1/client/servers',
    '/api/v1/client/plans',
    '/api/v1/client/account',
    '/api/v1/admin/servers',
    '/api/v1/admin/users',
    '/api/v1/admin/plans'
];

// Try with Bearer token authentication
foreach ($endpoints as $endpoint) {
    testEndpoint($baseUrl . $endpoint, $apiKey);
}

// Also try the SolusVM v1 API format which might be different
echo "\n=================================================\n";
echo "Testing SolusVM v1 legacy API format...\n";
echo "=================================================\n\n";

$legacyEndpoints = [
    '/api/client/command.php?key=' . $apiKey . '&action=info',
    '/api/client/command.php?key=' . $apiKey . '&action=listnodes',
    '/api/client/command.php?key=' . $apiKey . '&action=listplans',
    '/api/admin/command.php?key=' . $apiKey . '&action=listnodes',
    '/api/admin/command.php?key=' . $apiKey . '&action=listusers'
];

foreach ($legacyEndpoints as $endpoint) {
    testLegacyEndpoint($baseUrl . $endpoint);
}

/**
 * Test an API endpoint with Bearer token authentication
 */
function testEndpoint($url, $apiKey) {
    echo "Testing endpoint: " . $url . "\n";
    echo "----------------------------------------\n";
    
    // Initialize cURL session
    $ch = curl_init();
    
    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $apiKey,
        'Accept: application/json'
    ]);
    
    // Execute cURL request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    // Split header and body
    $header = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    // Check for cURL errors
    if (curl_errno($ch)) {
        echo "❌ CONNECTION FAILED\n";
        echo "Error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")\n\n";
    } else {
        // Display status and content type
        echo "HTTP Status: $httpCode\n";
        echo "Content-Type: $contentType\n\n";
        
        // Check if response is JSON
        $data = json_decode($body, true);
        $isJson = json_last_error() === JSON_ERROR_NONE;
        
        if ($isJson) {
            echo "✅ RECEIVED JSON RESPONSE\n";
            echo "Response Data:\n";
            echo json_encode($data, JSON_PRETTY_PRINT) . "\n\n";
        } else {
            // Check if it's HTML
            if (stripos($contentType, 'html') !== false || stripos($body, '<!doctype html>') !== false) {
                echo "❌ RECEIVED HTML RESPONSE\n";
                echo "Response (first 200 chars):\n";
                echo substr($body, 0, 200) . "...\n\n";
            } else {
                echo "❌ RECEIVED NON-JSON RESPONSE\n";
                echo "Response (first 200 chars):\n";
                echo substr($body, 0, 200) . "...\n\n";
            }
        }
        
        // Display first few headers for debugging
        $headerLines = explode("\n", $header);
        $displayedHeaders = 0;
        foreach ($headerLines as $line) {
            if (trim($line) !== '' && $displayedHeaders < 5) {
                echo trim($line) . "\n";
                $displayedHeaders++;
            }
        }
        if ($displayedHeaders > 0) {
            echo "...\n";
        }
    }
    
    // Close cURL session
    curl_close($ch);
    
    echo "\n";
}

/**
 * Test a legacy API endpoint
 */
function testLegacyEndpoint($url) {
    echo "Testing legacy endpoint: " . $url . "\n";
    echo "----------------------------------------\n";
    
    // Initialize cURL session
    $ch = curl_init();
    
    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    // Execute cURL request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    // Split header and body
    $header = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    // Check for cURL errors
    if (curl_errno($ch)) {
        echo "❌ CONNECTION FAILED\n";
        echo "Error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")\n\n";
    } else {
        // Display status and content type
        echo "HTTP Status: $httpCode\n";
        echo "Content-Type: $contentType\n\n";
        
        // Check if response is XML or JSON
        $isXml = stripos($contentType, 'xml') !== false || stripos($body, '<?xml') !== false;
        $data = json_decode($body, true);
        $isJson = json_last_error() === JSON_ERROR_NONE;
        
        if ($isXml) {
            echo "✅ RECEIVED XML RESPONSE\n";
            echo "Response (first 300 chars):\n";
            echo substr($body, 0, 300) . "...\n\n";
        } elseif ($isJson) {
            echo "✅ RECEIVED JSON RESPONSE\n";
            echo "Response Data:\n";
            echo json_encode($data, JSON_PRETTY_PRINT) . "\n\n";
        } else {
            // Check if it's HTML
            if (stripos($contentType, 'html') !== false || stripos($body, '<!doctype html>') !== false) {
                echo "❌ RECEIVED HTML RESPONSE\n";
                echo "Response (first 200 chars):\n";
                echo substr($body, 0, 200) . "...\n\n";
            } else {
                echo "❌ RECEIVED UNKNOWN RESPONSE\n";
                echo "Response (first 200 chars):\n";
                echo substr($body, 0, 200) . "...\n\n";
            }
        }
        
        // Display first few headers for debugging
        $headerLines = explode("\n", $header);
        $displayedHeaders = 0;
        foreach ($headerLines as $line) {
            if (trim($line) !== '' && $displayedHeaders < 5) {
                echo trim($line) . "\n";
                $displayedHeaders++;
            }
        }
        if ($displayedHeaders > 0) {
            echo "...\n";
        }
    }
    
    // Close cURL session
    curl_close($ch);
    
    echo "\n";
}
