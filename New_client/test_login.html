<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        #response {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Test Login</h1>
    
    <div class="form-group">
        <label for="username">Email:</label>
        <input type="email" id="username" name="username" value="<EMAIL>">
    </div>
    
    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" value="password123">
    </div>
    
    <button onclick="login()">Login</button>
    
    <div id="response"></div>
    
    <h2>Test API with Token</h2>
    
    <div class="form-group">
        <label for="token">Token:</label>
        <input type="text" id="token" name="token">
    </div>
    
    <div class="form-group">
        <label for="endpoint">Endpoint:</label>
        <select id="endpoint">
            <option value="login_check">login_check</option>
            <option value="user_details">user_details</option>
            <option value="servers">servers</option>
        </select>
    </div>
    
    <button onclick="testEndpoint()">Test Endpoint</button>
    
    <script>
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const responseDiv = document.getElementById('response');
            
            try {
                const response = await fetch('api.php?f=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
                
                if (data.token) {
                    document.getElementById('token').value = data.token;
                }
            } catch (error) {
                responseDiv.textContent = 'Error: ' + error.message;
            }
        }
        
        async function testEndpoint() {
            const token = document.getElementById('token').value;
            const endpoint = document.getElementById('endpoint').value;
            const responseDiv = document.getElementById('response');
            
            if (!token) {
                responseDiv.textContent = 'Please enter a token';
                return;
            }
            
            try {
                const response = await fetch(`api.php?f=${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ token })
                });
                
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseDiv.textContent = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
