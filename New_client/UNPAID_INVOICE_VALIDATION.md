# Unpaid Invoice Validation for Service Upgrades

## Overview

This implementation prevents creating new upgrade invoices for a service that already has unpaid invoices. This is a common business practice to ensure customers pay outstanding invoices before requesting additional services or upgrades.

## Implementation Details

### New Function: `check_service_unpaid_invoices()`

**Location:** `api.php` (around line 3030)

**Purpose:** Centralized function to check if a specific service has any unpaid invoices before allowing upgrade invoice creation.

**Parameters:**
- `$pdo` - Database connection
- `$user_id` - User ID making the request
- `$service_identifier` - Service identifier (server_id, orders_items_id, or order_id)
- `$service_type` - Type of identifier ('server_id', 'orders_items_id', 'order_id')
- `$upgrade_type` - Type of upgrade being requested (for error messages)

**Returns:**
- `null` - If service not found or error occurred (fail open for backward compatibility)
- `Array` with `has_unpaid => false` - If no unpaid invoices found
- `Array` with `has_unpaid => true` and detailed message - If unpaid invoices exist

### Integration Points

The validation is integrated into several invoice generation endpoints:

1. **General Invoice Generation** (`f=generate_invoice`)
   - Checks for upgrade types: Bandwidth, Subnet, Storage, VPS Plan, Additional IPs
   - Uses specific service validation for upgrades
   - Falls back to category-based validation for other invoice types

2. **Subnet Upgrade Invoice** (`f=generate_subnet_upgrade_invoice`)
   - Updated to use the centralized function
   - Provides specific error messages for subnet upgrades

3. **Server Renewal Invoice** (`f=generate_server_renewal_invoice`)
   - Simplified to use the centralized function
   - Covers all server renewal scenarios

## Upgrade Types Covered

The following upgrade types are validated:
- **Bandwidth Upgrade**
- **Subnet Upgrade** 
- **Storage Upgrade**
- **VPS Plan Upgrade**
- **Additional IPs Upgrade**

## Error Messages

When an unpaid invoice is found, users receive detailed error messages such as:

```
Cannot create VPS Plan Upgrade invoice. This service has 2 unpaid invoice(s): 
#12345 (VPS Plan Upgrade - €25.00), #12340 (Storage Upgrade - €15.00). 
Please pay all outstanding invoices before requesting upgrades.
```

## Database Query Logic

The function checks for unpaid invoices using multiple approaches:

1. **Direct order_id match** - `i.order_id = :orders_items_id`
2. **Invoice items match** - `ii.orders_items_id = :orders_items_id`
3. **Description pattern match** - For upgrade invoices mentioning the service

### Query Conditions

```sql
WHERE i.user_id = :user_id 
AND (i.status = 'Unpaid' OR (i.paid = 0 AND i.status != 'Cancelled'))
AND (
    i.order_id = :orders_items_id 
    OR ii.orders_items_id = :orders_items_id
    OR (i.description LIKE CONCAT('%', :service_identifier, '%') AND i.type LIKE '%Upgrade%')
)
```

## Service Identifier Resolution

The function handles different ways services can be identified:

- **server_id**: Looks up corresponding `orders_items.id`
- **orders_items_id**: Uses directly
- **order_id**: Looks up corresponding `orders_items.id`

## Backward Compatibility

- **Fail Open**: If service lookup fails, validation is skipped (allows upgrade)
- **Error Handling**: Database errors don't block upgrades
- **Gradual Rollout**: Can be enabled/disabled per upgrade type

## Testing Scenarios

### Test Case 1: Clean Service (Should Allow)
- Service has no unpaid invoices
- Upgrade invoice should be created successfully

### Test Case 2: Unpaid Invoice Exists (Should Block)
- Service has 1+ unpaid invoices
- Error message should list specific unpaid invoices
- Upgrade invoice creation should be blocked

### Test Case 3: Service Not Found (Should Allow)
- Service identifier doesn't exist
- Should allow upgrade (backward compatibility)

### Test Case 4: Paid Invoices Only (Should Allow)
- Service has paid invoices but no unpaid ones
- Upgrade invoice should be created successfully

## Monitoring & Logging

The function provides detailed logging:
- Service identifier resolution
- Unpaid invoice searches
- Found unpaid invoices with details
- Error conditions

## Future Enhancements

1. **Configuration Options**: Allow per-customer or per-service-type exemptions
2. **Grace Period**: Allow upgrades if unpaid invoices are within grace period
3. **Partial Payments**: Consider partially paid invoices
4. **Admin Override**: Allow admins to bypass validation
5. **Customer Communication**: Automatic notifications about blocking reasons 