<?php
require_once 'config.php';

// Initialize database connection
$pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

try {
    echo "=== Debug Storage Inventory ===\n";
    
    // Check dedicated_storages table
    echo "\n=== Dedicated Storage Options ===\n";
    $storage_query = $pdo->prepare("SELECT * FROM dedicated_storages ORDER BY id");
    $storage_query->execute();
    while ($storage = $storage_query->fetch(PDO::FETCH_ASSOC)) {
        echo "Storage ID: {$storage['id']}, Name: {$storage['name']}, Price: {$storage['price']}\n";
    }
    
    // Check storage table
    echo "\n=== Storage Table ===\n";
    $storage_table_query = $pdo->prepare("SELECT * FROM storage ORDER BY id");
    $storage_table_query->execute();
    while ($storage = $storage_table_query->fetch(PDO::FETCH_ASSOC)) {
        echo "Storage ID: {$storage['id']}, Name: {$storage['name']}\n";
    }
    
    // Check sample inventory servers
    echo "\n=== Sample Inventory Dedicated Servers (Available) ===\n";
    $inventory_query = $pdo->prepare("
        SELECT id, label, cpu, status, country_id, order_id,
               bay1, bay2, bay3, bay4, bay5, bay6, bay7, bay8, bay9, bay10,
               port1_speed, port2_speed
        FROM inventory_dedicated_servers 
        WHERE status = 'Available' 
        AND order_id IS NULL
        LIMIT 10
    ");
    $inventory_query->execute();
    
    while ($server = $inventory_query->fetch(PDO::FETCH_ASSOC)) {
        echo "Server ID: {$server['id']}, Label: {$server['label']}, CPU: {$server['cpu']}, Country: {$server['country_id']}\n";
        echo "  Bays: ";
        for ($i = 1; $i <= 10; $i++) {
            $bay = $server["bay$i"];
            if ($bay) {
                echo "bay$i=$bay ";
            }
        }
        echo "\n";
        echo "  Port speeds: port1={$server['port1_speed']}, port2={$server['port2_speed']}\n";
        echo "  Total port speed: " . (($server['port1_speed'] ?: 0) + ($server['port2_speed'] ?: 0)) . "\n\n";
    }
    
    // Test the exact query from the API
    echo "\n=== Testing API Query Logic ===\n";
    
    // Test parameters from the logs
    $cpu_id = 1; // Dual Intel Xeon E5-2630v3
    $country_id = 1; // Romania (Bucharest)
    $storage_id_240gb = 1; // 240GB SSD
    $storage_id_2x500gb = 2; // 2x500GB SSD
    $required_bandwidth_speed = 1000; // 1 Gbps
    
    echo "Testing for CPU ID: $cpu_id, Country ID: $country_id\n";
    
    // Test 240GB SSD (storage_id = 1)
    echo "\n--- Testing 240GB SSD (storage_id = 1) ---\n";
    $test_query_240 = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM (
            SELECT id,
            (CASE WHEN bay1 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay2 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay3 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay4 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay5 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay6 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay7 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay8 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay9 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay10 = ? THEN 1 ELSE 0 END) as matching_bays,
            (COALESCE(port1_speed, 0) + COALESCE(port2_speed, 0)) as total_port_speed
            FROM inventory_dedicated_servers
            WHERE status = 'Available'
            AND country_id = ?
            AND order_id IS NULL
            AND cpu = ?
        ) subquery
        WHERE matching_bays = ? AND total_port_speed >= ?
    ");
    
    $params_240 = array_fill(0, 10, $storage_id_240gb);
    $params_240[] = $country_id;
    $params_240[] = $cpu_id;
    $params_240[] = 1; // required_qty for 240GB SSD
    $params_240[] = $required_bandwidth_speed;
    
    $test_query_240->execute($params_240);
    $result_240 = $test_query_240->fetch(PDO::FETCH_ASSOC);
    echo "240GB SSD matches: {$result_240['count']}\n";
    
    // Test 2x500GB SSD (storage_id = 2)
    echo "\n--- Testing 2x500GB SSD (storage_id = 2) ---\n";
    $test_query_2x500 = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM (
            SELECT id,
            (CASE WHEN bay1 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay2 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay3 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay4 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay5 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay6 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay7 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay8 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay9 = ? THEN 1 ELSE 0 END) +
            (CASE WHEN bay10 = ? THEN 1 ELSE 0 END) as matching_bays,
            (COALESCE(port1_speed, 0) + COALESCE(port2_speed, 0)) as total_port_speed
            FROM inventory_dedicated_servers
            WHERE status = 'Available'
            AND country_id = ?
            AND order_id IS NULL
            AND cpu = ?
        ) subquery
        WHERE matching_bays = ? AND total_port_speed >= ?
    ");
    
    $params_2x500 = array_fill(0, 10, $storage_id_2x500gb);
    $params_2x500[] = $country_id;
    $params_2x500[] = $cpu_id;
    $params_2x500[] = 2; // required_qty for 2x500GB SSD
    $params_2x500[] = $required_bandwidth_speed;
    
    $test_query_2x500->execute($params_2x500);
    $result_2x500 = $test_query_2x500->fetch(PDO::FETCH_ASSOC);
    echo "2x500GB SSD matches: {$result_2x500['count']}\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
