// DismissibleAlert.js
import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const DismissibleAlert = ({ serviceName }) => {
  // Use only useState without localStorage/sessionStorage
  // This way the alert will reset on page refresh
  const [isVisible, setIsVisible] = useState(true);

  const handleDismiss = () => {
    setIsVisible(false);
    // No storage used, so it will reappear on refresh
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="alert alert-success" role="alert">
      <div className="d-flex justify-content-between align-items-center">
        <div>
          <i className="fa fa-check-circle-o me-2" aria-hidden="true"></i> 
          <strong>Well done!</strong> Activate another {serviceName} service to get <b>15% discount</b> on all {serviceName} services &nbsp;
        </div>
        <div className="d-flex align-items-center">
          <Link to="/reseller" className="me-3">
            <button className="btn btn-default btn-sm">Check Discounts</button>
          </Link>
          <button
            type="button"
            className="btn-close"
            aria-label="Close"
            onClick={handleDismiss}
            style={{ fontSize: '0.8rem' }}
          ></button>
        </div>
      </div>
    </div>
  );
};

export default DismissibleAlert;