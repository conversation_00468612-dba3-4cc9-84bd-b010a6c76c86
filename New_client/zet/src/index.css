   .better-radio {
          display: none;
        }
        
        .better-radio-group label {
          cursor: pointer;
          transition: all 0.3s ease;
          border-top: none;
          border-left: none;
          border-right: none;
          margin-bottom: 0;
        }
        
        .better-radio-group label:hover {
          background-color: rgba(59, 130, 246, 0.05);
        }
        
        .better-radio-group label.selected {
          background-color: rgba(59, 130, 246, 0.1);
          border-left: 3px solid #3b82f6;
          font-weight: bold;
        }
        
        .card-header {
          background-color: #f8fafc;
          border-bottom: 1px solid rgba(0, 0, 0, 0.125);
          padding: 0.75rem 1.25rem;
        }
        
        .flag {
          width: 24px;
          height: 16px;
          display: inline-block;
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
          vertical-align: middle;
        }
        

        .modal-backdrop, .fixed-backdrop {

          z-index: 1 !important;
        }

        .modal-backdrop, .fixed-backdrop {
          z-index: 1040 !important;
        }
        
        /* Ensure modal content has higher z-index than backdrop */
        .modal-content {
          z-index: 1050 !important;
        }
        
        /* Match z-index for both modal implementations */
        .server-details-modal {
          z-index: 9999 !important;
        }
        

        .smaller{
          height: auto;
          display: flex;
          flex-direction: column;
        }
  
        