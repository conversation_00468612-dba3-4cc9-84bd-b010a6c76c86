import React, { useState, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { Link } from "react-router-dom";

const DiscountAlert = () => {
  const [activeDedicatedServers, setActiveDedicatedServers] = useState(0);
  const [discountTiers, setDiscountTiers] = useState([]);
  const [currentDiscount, setCurrentDiscount] = useState(0);
  const [nextTier, setNextTier] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showDiscountModal, setShowDiscountModal] = useState(false);
  
  function getToken() {
    const userToken = sessionStorage.getItem('token');
    return userToken;
  }
  
  const token = getToken();
  
  useEffect(() => {
    fetchDedicatedServersCount();
  }, []);
  
  function fetchDedicatedServersCount() {
    fetch("/api.php?f=dedicated_servers_count", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
    .then(response => {
      // First check if the response is ok
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      // Log response for debugging
      response.clone().text().then(text => {
        if (text.trim() === '' || text.includes('<!DOCTYPE html>') || text.includes('<html>')) {
          console.error("Received HTML instead of JSON:", text.substring(0, 200) + '...');
        }
      });
      return response.text(); // Get response as text first
    })
    .then(text => {
      // Try to parse the response as JSON
      try {
        if (!text || text.trim() === '') {
          console.error("Empty response from server");
          throw new Error("Empty response from server");
        }
        
        // Log the first 200 characters of the response for debugging
        console.log("API Response (first 200 chars):", text.substring(0, 200) + (text.length > 200 ? '...' : ''));
        
        return JSON.parse(text);
      } catch (e) {
        console.error("Error parsing JSON response:", e);
        console.error("Raw response text:", text.substring(0, 500) + (text.length > 500 ? '...' : ''));
        throw new Error("Invalid JSON response from server");
      }
    })
    .then(data => {
      if (data.error) {
        console.error("Error fetching dedicated server count:", data.error);
      } else {
        console.log("Parsed discount data:", data);
        setActiveDedicatedServers(data.count || 0);
        setDiscountTiers(Array.isArray(data.tiers) ? data.tiers : []);
        
        // Only proceed if we have valid tiers data
        if (Array.isArray(data.tiers) && data.tiers.length > 0) {
          // Find current discount tier and next tier
          const sortedTiers = [...data.tiers].sort((a, b) => a.active_servers - b.active_servers);
          
          // Find current discount tier
          const currentTier = sortedTiers
            .filter(tier => data.count >= tier.active_servers)
            .pop();
          
          // Find next tier
          const nextPossibleTier = sortedTiers
            .find(tier => data.count < tier.active_servers);
          
          if (currentTier) {
            setCurrentDiscount(currentTier.discount);
          } else {
            setCurrentDiscount(0);
          }
          
          if (nextPossibleTier) {
            setNextTier({
              servers: nextPossibleTier.active_servers,
              discount: nextPossibleTier.discount,
              remaining: nextPossibleTier.active_servers - data.count
            });
          } else {
            setNextTier(null);
          }
        } else {
          // No tiers data available, set defaults
          console.log("No discount tiers found, using defaults");
          setCurrentDiscount(0);
          
          // Default tier information if no tiers are available
          setNextTier({
            servers: 5,
            discount: 5,
            remaining: 5
          });
        }
      }
      setLoading(false);
    })
    .catch(error => {
      console.error("Error fetching dedicated server data:", error);
      // Set default values in case of error
      console.log("Setting default values due to error");
      setActiveDedicatedServers(0);
      setCurrentDiscount(0);
      setNextTier({
        servers: 5,
        discount: 5,
        remaining: 5
      });
      
      // Use default discount tiers if none were fetched
      if (discountTiers.length === 0) {
        setDiscountTiers([
          {id: 1, active_servers: 5, discount: 5},
          {id: 2, active_servers: 10, discount: 7},
          {id: 3, active_servers: 25, discount: 10},
          {id: 4, active_servers: 50, discount: 20}
        ]);
      }
      setLoading(false);
    });
  }
  
  // Render the discount message
  const renderDiscountMessage = () => {
    if (loading) {
      return (
        <div className="alert alert-info" role="alert">
          <i className="fa fa-circle-o-notch fa-spin me-2" aria-hidden="true"></i> Loading server information...
        </div>
      );
    }

    if (activeDedicatedServers === 0) {
      // User has no active dedicated servers
      const firstTierServers = nextTier ? nextTier.servers : 5;
      const firstTierDiscount = nextTier ? nextTier.discount : 5;
      
      return (
        <div className="alert alert-success" role="alert">
          <i className="fa fa-info-circle me-2" aria-hidden="true"></i> You have no active dedicated servers. You need <b>{firstTierServers}</b> servers to reach the first discount tier of <b>{firstTierDiscount}%</b>. &nbsp; 
          <button 
            className="btn btn-default btn-sm" 
            onClick={() => setShowDiscountModal(true)}
          >
            View Discount Tiers
          </button>
        </div>
      );
    } else if (nextTier) {
      // User has servers but can reach a higher tier
      return (
        <div className="alert alert-success" role="alert">
          <i className="fa fa-info-circle me-2" aria-hidden="true"></i> You need <b>{nextTier.remaining}</b> more servers to reach the new discount tier of <b>{nextTier.discount}%</b>. &nbsp; 
          <button 
            className="btn btn-default btn-sm" 
            onClick={() => setShowDiscountModal(true)}
          >
            View Discount Tiers
          </button>
        </div>
      );
    } else {
      // User is at the highest tier
      return (
        <div className="alert alert-success" role="alert">
          <i className="fa fa-check-circle-o me-2" aria-hidden="true"></i> You have reached the highest discount tier. &nbsp; 
          <button 
            className="btn btn-default btn-sm" 
            onClick={() => setShowDiscountModal(true)}
          >
            View Discount Tiers
          </button>
        </div>
      );
    }
  };
  
  // Discount Tiers Modal
  const renderDiscountTiersModal = () => {
    const sortedTiers = [...discountTiers].sort((a, b) => a.active_servers - b.active_servers);

    return (
      <Dialog 
        header="Volume Discount Tiers" 
        visible={showDiscountModal} 
        style={{ width: '50vw' }} 
        onHide={() => setShowDiscountModal(false)}
        breakpoints={{'960px': '75vw', '640px': '90vw'}}
      >
        <div className="p-3">
          <p className="lead">
            Activate more dedicated servers to qualify for greater discounts on all your services.
          </p>
          
          <div className="table-responsive">
            <table className="table table-striped table-bordered">
              <thead>
                <tr style={{ backgroundColor: '#042a77' }}>
                  <th style={{ color: '#ffffff', fontWeight: 'bold' }}>Active Dedicated Servers</th>
                  <th style={{ color: '#ffffff', fontWeight: 'bold' }}>Discount Rate</th>
                </tr>
              </thead>
              <tbody>
                {sortedTiers.map((tier) => (
                  <tr key={tier.id} className={activeDedicatedServers >= tier.active_servers ? 'table-success' : ''}>
                    <td>{tier.active_servers}+</td>
                    <td>{tier.discount}%</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="mt-4">
            <h5>Your Status</h5>
            <p>
              You currently have <b>{activeDedicatedServers}</b> active dedicated servers.
              {currentDiscount > 0 ? (
                <span> You qualify for a <b>{currentDiscount}%</b> discount.</span>
              ) : (
                <span> You don't qualify for any discount yet.</span>
              )}
            </p>
            
            <div className="mt-3 d-flex justify-content-end">
               <Link to="/dedicatedorder" className="btn btn-primary">
                <i className="fa fa-plus me-2"></i>
                Add New Dedicated Server
              </Link>
            </div>
          </div>
        </div>
      </Dialog>
    );
  };

  return (
    <>
      {renderDiscountMessage()}
      {renderDiscountTiersModal()}
    </>
  );
};

export default DiscountAlert; 