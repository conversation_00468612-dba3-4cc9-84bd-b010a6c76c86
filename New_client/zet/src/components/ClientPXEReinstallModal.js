import { useState, useEffect } from 'react';
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
  MDBBtn
} from 'mdb-react-ui-kit';

const ClientPXEReinstallModal = ({
  show,
  onHide,
  server,
  serverType = 'dedicated',
  onReinstallComplete,
  onStatusChange
}) => {
  const [operatingSystems, setOperatingSystems] = useState([]);
  const [selectedOS, setSelectedOS] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isInstalling, setIsInstalling] = useState(false);
  const [installProgress, setInstallProgress] = useState('');
  const [customRootPassword, setCustomRootPassword] = useState('');
  const [useCustomPassword, setUseCustomPassword] = useState(false);
  const [installationInProgress, setInstallationInProgress] = useState(false);
  const [installationStatus, setInstallationStatus] = useState(null);
  const [checkingStatus, setCheckingStatus] = useState(false);
  const [initialStatusCheck, setInitialStatusCheck] = useState(true);

  // Debug state changes
  useEffect(() => {
    console.log('🔄 installationInProgress changed:', installationInProgress);
    if (installationInProgress && installationStatus) {
      console.log('📊 Installation status details:', installationStatus);
    }
  }, [installationInProgress, installationStatus]);

  // Fetch available operating systems and check installation status
  useEffect(() => {
    if (show && server?.id) {
      console.log('🔄 Modal opened, fetching OS list and checking installation status...');
      setInitialStatusCheck(true);
      fetchOperatingSystems();
      checkInstallationStatus();

      // Set up periodic status check every 15 seconds, but only when modal is open
      const interval = setInterval(() => {
        if (show && !isInstalling && !loading) {
          console.log('⏰ Periodic status check...');
          checkInstallationStatus();
        }
      }, 15000);

      return () => {
        console.log('🧹 Cleaning up periodic status check');
        clearInterval(interval);
      };
    } else if (!show) {
      // Reset states when modal is closed
      setInstallationInProgress(false);
      setInstallationStatus(null);
      setCheckingStatus(false);
      setInitialStatusCheck(true);
    }
  }, [show, server?.id, isInstalling, loading]);

  const checkInstallationStatus = async () => {
    // Don't check status if modal is not shown or server info is missing
    if (!show || !server?.id) {
      return false;
    }

    try {
      setCheckingStatus(true);
      const token = sessionStorage.getItem('token');

      console.log('🔍 Checking installation status for server:', server.id);

      const response = await fetch('/api.php?f=client_pxe_status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          server_id: server.id,
          server_type: serverType
        })
      });

      console.log('📡 Status check response HTTP code:', response.status);

      if (!response.ok) {
        console.warn('⚠️ Installation status check failed with HTTP error:', response.status);
        const errorText = await response.text();
        console.warn('Error response body:', errorText);
        // Only update state if modal is still open
        if (show) {
          setInstallationInProgress(false);
          if (onStatusChange) onStatusChange(false);
          setInstallationStatus(null);
        }
        return false;
      }

      const data = await response.json();
      console.log('📦 Installation status response data:', data);

      // Only update state if modal is still open
      if (show) {
        if (data.has_installation && data.is_active) {
          console.log('🚫 Installation in progress detected!');
          // Only update if state actually changed to prevent unnecessary re-renders
          if (!installationInProgress) {
            setInstallationInProgress(true);
            if (onStatusChange) onStatusChange(true);
          }
          setInstallationStatus(data);
          return true;
        } else {
          console.log('✅ No installation in progress');
          // Only update if state actually changed to prevent unnecessary re-renders
          if (installationInProgress) {
            setInstallationInProgress(false);
            if (onStatusChange) onStatusChange(false);
          }
          setInstallationStatus(null);
          return false;
        }
      }
      return false;
    } catch (err) {
      console.error('❌ Error checking installation status:', err);
      // Only update state if modal is still open
      if (show) {
        if (installationInProgress) {
          setInstallationInProgress(false);
          if (onStatusChange) onStatusChange(false);
        }
        setInstallationStatus(null);
      }
      return false;
    } finally {
      if (show) {
        setCheckingStatus(false);
        setInitialStatusCheck(false);
      }
    }
  };

  const fetchOperatingSystems = async () => {
    try {
      setLoading(true);
      setError('');

      const token = sessionStorage.getItem('token');
      console.log('Token for OS fetch:', token ? `${token.substring(0, 10)}...` : 'null');

      // Use the same endpoint as the admin version to get from reinstallable_os table
      const response = await fetch('/api.php?f=client_reinstallable_os', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      if (!response.ok) {
        console.error('HTTP Response not OK:', response.status, response.statusText);
        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
      }

      const responseText = await response.text();
      console.log('Raw API response:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        console.error('Response text:', responseText);
        throw new Error('Invalid JSON response from server');
      }

      if (data.error) {
        console.error('API Error:', data);
        throw new Error(data.message || data.error || 'Unknown error');
      }

      console.log('Parsed data:', data);
      console.log('Is array?', Array.isArray(data));

      if (Array.isArray(data)) {
        console.log('OS data length:', data.length);
        setOperatingSystems(data);
        if (data.length > 0) {
          setSelectedOS(data[0].id.toString());
        }
      } else {
        console.error('Data is not an array:', typeof data, data);
        throw new Error('Invalid response format - expected array');
      }

    } catch (err) {
      console.error('Error fetching operating systems:', err);
      setError(`Failed to load operating systems: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleStartReinstall = async () => {
    try {
      if (!selectedOS) {
        setError('Please select an operating system');
        return;
      }

      setError('');

      // Double-check installation status before proceeding
      console.log('🔄 Double-checking installation status before starting new installation...');
      const isInstallationRunning = await checkInstallationStatus();

      if (isInstallationRunning) {
        const errorMsg = 'Cannot start installation: Another installation is already in progress for this server';
        setError(errorMsg);
        console.log('🚫 Installation blocked - another installation is in progress');
        console.log('Current installation status:', installationStatus);
        return;
      }

      console.log('✅ No installation in progress, proceeding with new installation...');

      setLoading(true);
      setIsInstalling(true);
      setInstallProgress('Initializing PXE reinstall...');

      const token = sessionStorage.getItem('token');

      const requestData = {
        token: token,
        server_id: server.id,
        server_type: serverType,
        os_id: selectedOS,
        custom_root_password: useCustomPassword && customRootPassword.trim() ? customRootPassword : null
      };

      console.log('🚀 Starting PXE reinstall with configuration:', requestData);

      const response = await fetch('/api.php?f=client_pxe_reinstall', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      console.log('📡 Installation execution response HTTP code:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Installation execution failed with HTTP error:', response.status, errorText);
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      console.log('📦 Installation execution result:', result);

      if (result.success) {
        console.log('✅ PXE reinstall initiated successfully!');
        setInstallProgress('PXE reinstall initiated successfully! The server will now boot from network and begin OS installation.');

        // Show success for a moment, then close
        setTimeout(() => {
          if (onReinstallComplete) {
            onReinstallComplete();
          }
          handleClose();
        }, 3000);
      } else {
        console.error('❌ PXE reinstall failed:', result.error);
        throw new Error(result.error || 'PXE reinstall failed');
      }

    } catch (err) {
      console.error('❌ Error starting PXE reinstall:', err);
      setError(`Failed to start reinstall: ${err.message}`);
      setIsInstalling(false);

      // After error, immediately check status to update UI
      setTimeout(() => {
        checkInstallationStatus();
      }, 1000);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedOS('');
    setError('');
    setInstallProgress('');
    setCustomRootPassword('');
    setUseCustomPassword(false);
    setIsInstalling(false);
    setInstallationInProgress(false);
    setInstallationStatus(null);
    setCheckingStatus(false);
    onHide();
  };

  return (
    <MDBModal show={show} tabIndex='-1'>
      <MDBModalDialog>
        <MDBModalContent>
          <MDBModalHeader>
            <MDBModalTitle>Reinstall Operating System</MDBModalTitle>
            <button type="button" className="close" onClick={handleClose}>
              <span aria-hidden="true">&times;</span>
            </button>
          </MDBModalHeader>

          <MDBModalBody>
            {/* Server Info */}


            {/* Initial Loading State */}
            {checkingStatus && initialStatusCheck && (
              <div className="mb-4 p-3 bg-info bg-opacity-10 border border-info rounded">
                <div className="d-flex align-items-center justify-content-center">
                  <div className="spinner-border spinner-border-sm me-2 text-info" role="status"></div>
                  <span className="small text-info">Loading...</span>
                </div>
              </div>
            )}

            {/* Periodic Status Check Indicator */}
            {checkingStatus && !initialStatusCheck && !installationInProgress && !isInstalling && (
              <div className="mb-2 p-2 bg-info bg-opacity-10 border border-info rounded">
                <div className="d-flex align-items-center justify-content-center">
                  <div className="spinner-border spinner-border-sm me-2 text-info" role="status"></div>
                  <span className="small text-info">Checking installation status...</span>
                </div>
              </div>
            )}

            {/* Installation Already in Progress */}
            {installationInProgress && !initialStatusCheck && (
              /* Installation Already in Progress */
              <div>
                <div className="mb-4 p-3 bg-warning bg-opacity-10 border border-warning rounded">
                  <div className="d-flex align-items-start">
                    <i className="fas fa-clock me-2 text-warning mt-1"></i>
                    <div className="small text-warning">
                      <div className="fw-bold mb-1">Installation Already in Progress</div>
                      <div>
                        Another PXE installation is currently running on this server.
                        {installationStatus?.os_template && ` Installing: ${installationStatus.os_template}`}
                        {installationStatus?.initiated_at && ` • Started: ${new Date(installationStatus.initiated_at).toLocaleString()}`}
                      </div>
                      <div className="mt-2">
                        Please wait for the current installation to complete before starting a new one.
                        You can monitor the progress through the server's IPMI console.
                      </div>
                    </div>
                  </div>
                </div>

                {/* Refresh Status Button */}
                <div className="mb-4 text-center">
                  <button
                    type="button"
                    onClick={checkInstallationStatus}
                    className="btn btn-primary btn-sm d-flex align-items-center mx-auto"
                    disabled={checkingStatus}
                  >
                    {checkingStatus ? (
                      <>
                        <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                        Checking...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-sync-alt me-2"></i>
                        Refresh Status
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}

            {/* OS Selection Form - Show when not in initial loading, not installing, and no installation in progress */}
            {!initialStatusCheck && !isInstalling && !installationInProgress && (
              <>
                {/* OS Selection */}
                <div className="mb-4">
                  <label className="form-label">
                    Select Operating System <span className="text-danger">*</span>
                  </label>
                  {loading ? (
                    <div className="text-center py-4">
                      <div className="spinner-border text-primary mb-3" role="status"></div>
                      <p>Loading operating systems...</p>
                    </div>
                  ) : (
                    <select
                      className="form-control form-select"
                      value={selectedOS}
                      onChange={(e) => setSelectedOS(e.target.value)}
                      disabled={loading}
                    >
                      <option value="">Select an operating system</option>
                      {operatingSystems.map(os => (
                        <option key={os.id} value={os.id.toString()}>
                          {os.name}
                        </option>
                      ))}
                    </select>
                  )}
                </div>

                {/* Custom Root Password Option */}
                <div className="mb-4">
                  <div className="form-check">
                    <input
                      type="checkbox"
                      className="form-check-input"
                      id="useCustomPassword"
                      checked={useCustomPassword}
                  onChange={(e) => setUseCustomPassword(e.target.checked)}
                                  style={{ 
        marginTop: '15px',
        marginLeft: '2px'
      }} />
                    <label className="form-check-label" htmlFor="useCustomPassword">
                      Use custom root password
                    </label>
                  </div>

                  {useCustomPassword && (
                    <input
                      type="password"
                      value={customRootPassword}
                      onChange={(e) => setCustomRootPassword(e.target.value)}
                      placeholder="Enter custom root password"
                      className="form-control mt-2"
                    />
                  )}
                </div>

 

            {/* Warning */}
            <div className="alert alert-warning">
              <strong>Warning:</strong> This will completely wipe your server and install a fresh operating system.
              All data will be permanently lost. Make sure you have backups of any important data.
            </div>
              </>
            )}

            {/* Installation Confirmation - Show when installation is starting/started */}
            {isInstalling && (
              <div>
                <div className="text-center mb-4">
                  <div className="fw-bold text-dark">Installation Started</div>
                  <div className="small text-muted mt-1">
                    {installProgress}
                  </div>
                </div>

                <div className="p-3 bg-success bg-opacity-10 border border-success rounded">
                  <div className="d-flex align-items-start">
                    <i className="fas fa-check-circle me-2 text-success mt-1"></i>
                    <div className="small text-success">
                      Installation process has been initiated. You can monitor the progress
                      through the server's IPMI console.
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="mb-4 p-3 bg-danger bg-opacity-10 border border-danger rounded">
                <div className="d-flex align-items-start">
                  <i className="fas fa-exclamation-triangle me-2 text-danger mt-1"></i>
                  <div className="small text-danger">{error}</div>
                </div>
              </div>
            )}

          </MDBModalBody>

          <MDBModalFooter>
            {installationInProgress ? (
              /* Only show close button when installation is already in progress */
              <MDBBtn color='primary' onClick={handleClose}>
                Close
              </MDBBtn>
            ) : !isInstalling ? (
              <>
                <MDBBtn
                  color='secondary'
                  onClick={handleClose}
                  disabled={loading}
                >
                  Cancel
                </MDBBtn>
                <MDBBtn
                  color='danger'
                  onClick={handleStartReinstall}
                  disabled={loading || !selectedOS || (!server?.ipmi_ip)}
                >
                  {loading ? (
                    <>
                      <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                      Starting...
                    </>
                  ) : (
                    'Start Reinstall'
                  )}
                </MDBBtn>
              </>
            ) : (
              !loading && (
                <MDBBtn color='primary' onClick={handleClose}>
                  Close
                </MDBBtn>
              )
            )}
          </MDBModalFooter>
        </MDBModalContent>
      </MDBModalDialog>
    </MDBModal>
  );
};

export default ClientPXEReinstallModal;
