import React, { useState, useEffect, useCallback } from "react";
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
} from 'mdb-react-ui-kit';

/**
 * RenewServerModal Component
 *
 * A reusable modal component for renewing server services.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Controls whether the modal is displayed
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Object} props.server - Server data object
 * @param {Function} props.onSuccess - Function to call after successful renewal
 */
const RenewServerModal = ({ isOpen, onClose, server, onSuccess }) => {
  // State for form data and UI
  const [selectedBillingPeriod, setSelectedBillingPeriod] = useState('1');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [debugInfo, setDebugInfo] = useState({});
  const [hasUnpaidInvoices, setHasUnpaidInvoices] = useState(false);

  // Calculate renewal price based on server price and billing period
  const calculateRenewalPrice = useCallback(() => {
    if (!server) return 0;

    // Try different price fields that might be available in the server object
    const priceField = server.monthly_price || server.requirement_price || server.recurring_price || 0;
    const basePrice = parseFloat(priceField);
    const period = parseInt(selectedBillingPeriod, 10);

    return basePrice * period;
  }, [server, selectedBillingPeriod]);

  // Check for unpaid invoices when modal opens
  const checkUnpaidInvoices = useCallback(async () => {
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('/api.php?f=bills', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();

      // Check if there are any unpaid invoices for this server
      // Get the order ID from different possible fields
      const orderId = server.order_id || server.id;
      console.log("Checking for unpaid invoices for order ID:", orderId);

      const unpaidInvoices = data.filter(invoice => {
        const isUnpaid = invoice.status === 'Unpaid';
        const matchesOrderId = invoice.order_id && invoice.order_id.toString() === orderId.toString();

        console.log(`Invoice ${invoice.id}: unpaid=${isUnpaid}, matches=${matchesOrderId}`);
        return isUnpaid && matchesOrderId;
      });

      setHasUnpaidInvoices(unpaidInvoices.length > 0);
    } catch (error) {
      console.error("Error checking unpaid invoices:", error);
      setHasUnpaidInvoices(false); // Assume no unpaid invoices if check fails
    }
  }, [server]);

  // Handle server renewal submission
  const handleRenewal = async () => {
    setError('');
    setDebugInfo({});

    if (!server) {
      setError('Server information is missing');
      return;
    }

    // Get the server ID - could be in different fields depending on the server type
    const serverId = server.id || server.server_id;
    if (!serverId) {
      setError('Server ID is missing');
      console.error('Server object is missing ID field:', server);
      return;
    }

    try {
      setIsSubmitting(true);
      setDebugInfo({
        stage: 'starting_request',
        server_id: serverId,
        billing_period: selectedBillingPeriod
      });

      const token = sessionStorage.getItem('token');

      // Calculate the renewal amount
      const renewalAmount = calculateRenewalPrice();

      // Generate invoice for the renewal using the dedicated server renewal endpoint
      const response = await fetch('/api.php?f=generate_server_renewal_invoice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          server_id: serverId,
          amount: renewalAmount,
          billing_period: selectedBillingPeriod
        })
      });

      const responseText = await response.text();
      console.log("Raw renewal response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        console.error("Failed to parse renewal response:", e);
        throw new Error("Invalid server response");
      }

      console.log("Processing renewal API response:", data);
      setDebugInfo(prev => ({ ...prev, stage: 'processing_renewal_response', finalResponse: data }));

      if (data.success === true) {
        console.log("Server renewal invoice generated successfully");

        // The billing cycle is already updated by the API if it changed

        // Format success data
        const formattedData = {
          ...data,
          invoice_id: data.invoice_id,
          prorated_amount: data.details.subtotal,
          vat_rate: data.details.tax_rate,
          vat_amount: data.details.tax,
          total_amount: data.details.total,
          message: data.message || "Server renewal invoice has been generated. Your server will be renewed after payment is processed."
        };

        if (typeof onSuccess === 'function') {
          onSuccess(formattedData);
        }
        onClose();
      } else {
        console.error("Renewal API returned error:", data.error || "Unknown error");
        setError(data.error || 'Failed to generate renewal invoice');
      }
    } catch (error) {
      console.error("Error renewing server:", error);
      setError('Connection error - please try again');
      setDebugInfo(prev => ({
        ...prev,
        fatalError: error.message,
        fatalErrorStack: error.stack
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check for unpaid invoices when modal opens
  useEffect(() => {
    if (isOpen && server) {
      console.log("RenewServerModal opened with server data:", server);

      // Log all server properties for debugging
      console.log("Server ID:", server.id || server.server_id);
      console.log("Order ID:", server.order_id);
      console.log("Payment period:", server.payment_period);
      console.log("Monthly price:", server.monthly_price || server.requirement_price || server.recurring_price);

      setError('');
      setDebugInfo({});
      setSelectedBillingPeriod(server.payment_period || '1');
      checkUnpaidInvoices();
    }
  }, [isOpen, server, checkUnpaidInvoices]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setError('');
      setDebugInfo({});
    }
  }, [isOpen]);

  return (
    <MDBModal show={isOpen} tabIndex='-1'>
      <MDBModalDialog>
        <MDBModalContent>
          <MDBModalHeader>
            <MDBModalTitle>Renew Server</MDBModalTitle>
            <button type="button" className="close" onClick={onClose}>
              <span aria-hidden="true">&times;</span>
            </button>
          </MDBModalHeader>
          <MDBModalBody>
            {hasUnpaidInvoices ? (
              <div className="alert alert-warning">
                <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
                <strong>Warning:</strong> You have pending invoices for this order. Please pay those invoices before generating a new renewal invoice.
              </div>
            ) : (
              <div className="form-group">
                <label className="form-label mb-2">Billing Period:</label>
                <select
                  name="billing_period"
                  className="form-control custom-select mb-3"
                  value={selectedBillingPeriod}
                  onChange={(e) => setSelectedBillingPeriod(e.target.value)}
                >
                  {(() => {
                    // Get the base price (for 1 month)
                    const basePrice = server?.monthly_price || server?.requirement_price || server?.recurring_price || 0;
                    const price1Month = parseFloat(basePrice).toFixed(2);
                    const price3Months = (parseFloat(basePrice) * 3).toFixed(2);
                    const price6Months = (parseFloat(basePrice) * 6).toFixed(2);
                    const price12Months = (parseFloat(basePrice) * 12).toFixed(2);

                    return (
                      <>
                        <option value="1">1 Month (€{price1Month})</option>
                        <option value="3">3 Months (€{price3Months})</option>
                        <option value="6">6 Months (€{price6Months})</option>
                        <option value="12">12 Months (€{price12Months})</option>
                      </>
                    );
                  })()}
                </select>

                {error && (
                  <div className="alert alert-danger mb-3">
                    <i className="fa fa-exclamation-circle me-2" style={{paddingRight: '0.5rem'}}></i>
                    {error}
                  </div>
                )}

                <div className="alert alert-info mb-2 py-2">
                  <i className="fa fa-info-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                  <strong>Renewal Process:</strong> Clicking "Generate Renewal Invoice" will create an invoice for the selected period.
                  Your server will be renewed after the invoice is paid.
                </div>
              </div>
            )}
          </MDBModalBody>
          <MDBModalFooter>
            <button
              className="btn btn-secondary"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              className="btn btn-success ms-2"
              onClick={handleRenewal}
              disabled={isSubmitting || hasUnpaidInvoices}
            >
              {isSubmitting ? (
                <>
                  <span className="spinner-border spinner-border-sm me-1" role="status"></span>
                  Processing...
                </>
              ) : (
                <>Generate Renewal Invoice</>
              )}
            </button>
          </MDBModalFooter>
        </MDBModalContent>
      </MDBModalDialog>
    </MDBModal>
  );
};

export default RenewServerModal;
