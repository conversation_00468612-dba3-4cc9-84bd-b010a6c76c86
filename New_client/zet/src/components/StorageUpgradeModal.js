import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
} from 'mdb-react-ui-kit';

/**
 * StorageUpgradeModal Component
 *
 * A reusable modal component for upgrading server storage plans.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Controls whether the modal is displayed
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Object} props.server - Server data object (must include storage_id or storage)
 * @param {Function} props.onSuccess - Function to call after successful storage upgrade
 */
const StorageUpgradeModal = ({ isOpen, onClose, server, onSuccess }) => {
  // State for storage plans and selection
  const [storagePlans, setStoragePlans] = useState([]);
  const [selectedStorageId, setSelectedStorageId] = useState(null);
  const [currentStorageId, setCurrentStorageId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [debugInfo, setDebugInfo] = useState({});
  
  // VAT-related state
  const [vatInfo, setVatInfo] = useState({
    vat_rate: 0,
    vat_exempt: false,
    country: null,
    vat_id: null
  });
  const [isLoadingVat, setIsLoadingVat] = useState(false);

  // Fetch VAT information
  const fetchVatInfo = useCallback(async () => {
    setIsLoadingVat(true);
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('/api.php?f=get_vat_rate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log("Raw VAT rate response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log("Parsed VAT data:", data);
      } catch (e) {
        console.error("Failed to parse VAT response:", e);
        return;
      }

      if (data && data.success) {
        setVatInfo({
          vat_rate: parseFloat(data.vat_rate) || 0,
          vat_exempt: data.vat_exempt || false,
          country: data.country || null,
          vat_id: data.vat_id || null
        });
      }
    } catch (error) {
      console.error("Error fetching VAT info:", error);
    } finally {
      setIsLoadingVat(false);
    }
  }, []);

  // Calculate prorated amount for the upgrade
  const calculateProratedAmount = useCallback(() => {
    if (!selectedStorageId || !currentStorageId ||
        selectedStorageId === currentStorageId ||
        !storagePlans || !storagePlans.length) {
      return 0;
    }

    const selectedId = parseInt(selectedStorageId, 10);
    const currentId = parseInt(currentStorageId, 10);
    
    const currentPlan = storagePlans.find(plan => parseInt(plan.id, 10) === currentId);
    const selectedPlan = storagePlans.find(plan => parseInt(plan.id, 10) === selectedId);

    if (!currentPlan || !selectedPlan) {
      console.log(`Storage plans not found - currentId: ${currentId}, selectedId: ${selectedId}`);
      return 0;
    }

    const currentPrice = parseFloat(currentPlan.price || 0);
    const newPrice = parseFloat(selectedPlan.price || 0);
    const priceDifference = newPrice - currentPrice;

    if (priceDifference <= 0) {
      return 0;
    }

    const today = new Date();
    const dueDate = server?.expires ? new Date(server.expires) : null;

    if (!dueDate) {
      return priceDifference;
    }

    const daysRemaining = Math.max(0, Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24)));
    const proratedAmount = (priceDifference / 30) * daysRemaining;

    return Math.round(proratedAmount * 100) / 100;
  }, [selectedStorageId, currentStorageId, storagePlans, server]);

  // Helper function to extract storage size from storage name
  const extractStorageSize = (storageName) => {
    if (!storageName) return null;
    
    // Match patterns like "500GB", "1TB", "2TB", etc.
    const match = storageName.match(/(\d+(?:\.\d+)?)\s*(GB|TB|MB)/i);
    if (match) {
      const size = parseFloat(match[1]);
      const unit = match[2].toUpperCase();
      
      // Convert to GB for comparison
      switch (unit) {
        case 'TB':
          return size * 1024;
        case 'GB':
          return size;
        case 'MB':
          return size / 1024;
        default:
          return size;
      }
    }
    return null;
  };

  // Helper function to determine if one storage is an upgrade from another
  const isStorageUpgrade = (currentPlan, targetPlan) => {
    if (!currentPlan || !targetPlan) return false;
    
    const currentSize = extractStorageSize(currentPlan.name);
    const targetSize = extractStorageSize(targetPlan.name);
    
    // If we can compare sizes, use that
    if (currentSize !== null && targetSize !== null) {
      return targetSize > currentSize;
    }
    
    // Fallback to price comparison
    const currentPrice = parseFloat(currentPlan.price || 0);
    const targetPrice = parseFloat(targetPlan.price || 0);
    
    return targetPrice > currentPrice;
  };

  // Enhanced fetchStoragePlans function with improved detection
  const fetchStoragePlans = useCallback(async () => {
    if (!server || !server.id) return;

    setIsLoading(true);
    setError('');
    
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('/api.php?f=get_storage_plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          token,
          server_id: server.id
        })
      });

      const responseText = await response.text();
      console.log("Raw storage plans response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log("Parsed storage plans:", data);
      } catch (e) {
        console.error("Failed to parse storage plans response:", e);
        setError('Invalid response from server');
        return;
      }

      // Handle the response structure based on your PHP API
      if (data.success === false) {
        setError(data.error || 'Failed to load storage plans');
        return;
      }

      // Handle the storage plans array
      let plans = [];
      if (data && data.plans && Array.isArray(data.plans)) {
        plans = data.plans;
      } else if (data && Array.isArray(data)) {
        plans = data;
      }

      if (plans.length > 0) {
        const plansWithNumericIds = plans.map(plan => ({
          ...plan,
          id: parseInt(plan.id, 10),
          price: parseFloat(plan.price || 0)
        }));

        // Sort plans by storage size for easier comparison
        plansWithNumericIds.sort((a, b) => {
          const sizeA = extractStorageSize(a.name);
          const sizeB = extractStorageSize(b.name);
          
          if (sizeA !== null && sizeB !== null) {
            return sizeA - sizeB;
          }
          
          // Fallback to price sorting
          return a.price - b.price;
        });
        
        setStoragePlans(plansWithNumericIds);

        // Enhanced storage detection logic
        console.log("=== ENHANCED STORAGE DETECTION DEBUG ===");
        console.log("Full server object:", server);
        console.log("API response current_storage_id:", data.current_storage_id);
        console.log("API response current_storage_name:", data.current_storage_name);
        console.log("Available storage plans:", plansWithNumericIds.map(p => ({ 
          id: p.id, 
          name: p.name, 
          price: p.price,
          is_current: p.is_current 
        })));

        let currentId = null;
        let detectionMethod = "none";

        // Method 1: Use current_storage_id from API response (most reliable)
        if (data.current_storage_id !== null && data.current_storage_id !== undefined) {
          const apiStorageId = parseInt(data.current_storage_id, 10);
          
          console.log("Method 1 - API provided storage_id:", apiStorageId);
          
          const matchingPlan = plansWithNumericIds.find(plan => plan.id === apiStorageId);
          if (matchingPlan && !isNaN(apiStorageId)) {
            currentId = apiStorageId;
            console.log("✅ Method 1 SUCCESS - Using API provided storage ID:", currentId, "Plan:", matchingPlan.name);
            detectionMethod = "api_storage_id";
          } else {
            console.warn("❌ Method 1 FAILED - API storage_id doesn't match any available plans or is invalid");
          }
        } else {
          console.log("Method 1 SKIPPED - No current_storage_id from API");
        }

        // Method 2: Check if any plan is marked as current by the API
        if (currentId === null) {
          console.log("Method 2 - Looking for plan marked as current");
          const currentPlan = plansWithNumericIds.find(plan => plan.is_current === true);
          
          if (currentPlan) {
            currentId = currentPlan.id;
            console.log("✅ Method 2 SUCCESS - Found plan marked as current:", currentId, "Plan:", currentPlan.name);
            detectionMethod = "api_is_current_flag";
          } else {
            console.log("Method 2 SKIPPED - No plan marked as current");
          }
        }

        // Method 3: Try to match by storage name from API
        if (currentId === null && data.current_storage_name) {
          console.log("Method 3 - Matching by API storage name:", data.current_storage_name);
          
          // Try exact match first
          let matchingPlan = plansWithNumericIds.find(plan =>
            plan.name && plan.name.toLowerCase() === data.current_storage_name.toLowerCase()
          );

          // Try partial match if exact match fails
          if (!matchingPlan) {
            matchingPlan = plansWithNumericIds.find(plan =>
              plan.name && (
                plan.name.toLowerCase().includes(data.current_storage_name.toLowerCase()) ||
                data.current_storage_name.toLowerCase().includes(plan.name.toLowerCase())
              )
            );
          }

          if (matchingPlan) {
            currentId = matchingPlan.id;
            console.log("✅ Method 3 SUCCESS - Found matching plan by name:", currentId, "Plan:", matchingPlan.name);
            detectionMethod = "api_storage_name";
          } else {
            console.warn("❌ Method 3 FAILED - No plan matches API storage name");
          }
        } else {
          console.log("Method 3 SKIPPED - No current_storage_name from API");
        }

        // Method 4: Fallback to server object properties (legacy support)
        if (currentId === null) {
          console.log("Method 4 - Fallback to server object properties");
          
          // Try server.storage_id first
          if (server.storage_id !== null && server.storage_id !== undefined) {
            const serverStorageId = parseInt(server.storage_id, 10);
            const matchingPlan = plansWithNumericIds.find(plan => plan.id === serverStorageId);
            
            if (matchingPlan && !isNaN(serverStorageId)) {
              currentId = serverStorageId;
              console.log("✅ Method 4a SUCCESS - Using server.storage_id:", currentId);
              detectionMethod = "server_storage_id";
            }
          }

          // Try server.storage name matching
          if (currentId === null && server.storage) {
            console.log("Method 4b - Trying server.storage:", server.storage);
            
            const matchingPlan = plansWithNumericIds.find(plan =>
              plan.name && plan.name.toLowerCase().includes(server.storage.toLowerCase())
            );

            if (matchingPlan) {
              currentId = matchingPlan.id;
              console.log("✅ Method 4b SUCCESS - Found matching plan by server.storage:", currentId);
              detectionMethod = "server_storage_name";
            }
          }

          // Try server.disks field as last resort
          if (currentId === null && server.disks) {
            console.log("Method 4c - Trying server.disks:", server.disks);
            
            const matchingPlan = plansWithNumericIds.find(plan =>
              plan.name && (
                plan.name.toLowerCase().includes(server.disks.toLowerCase()) ||
                server.disks.toLowerCase().includes(plan.name.toLowerCase())
              )
            );

            if (matchingPlan) {
              currentId = matchingPlan.id;
              console.log("✅ Method 4c SUCCESS - Found matching plan by server.disks:", currentId);
              detectionMethod = "server_disks";
            }
          }
        }

        // Final fallback: Use the cheapest plan (first in sorted list)
        if (currentId === null) {
          console.log("❌ ALL METHODS FAILED - Using fallback to first plan");
          currentId = plansWithNumericIds[0]?.id || null;
          detectionMethod = "fallback_first_plan";
        }

        // Set the detected current storage ID
        setCurrentStorageId(currentId);
        setSelectedStorageId(currentId);

        console.log("=== FINAL RESULTS ===");
        console.log("Detection method used:", detectionMethod);
        console.log("Final current storage ID:", currentId);
        console.log("Selected storage ID:", currentId);
        if (currentId) {
          const finalPlan = plansWithNumericIds.find(p => p.id === currentId);
          console.log("Current storage plan:", finalPlan ? finalPlan.name : "NOT FOUND");
        }
        console.log("=== END DEBUG ===");
        
        // Store debug info for troubleshooting
        setDebugInfo({
          api_current_storage_id: data.current_storage_id,
          api_current_storage_name: data.current_storage_name,
          server_storage_id: server.storage_id,
          server_storage: server.storage,
          server_disks: server.disks,
          final_current_id: currentId,
          total_plans: plansWithNumericIds.length
        });
        
      } else {
        setError('No storage plans available for this server');
      }
    } catch (error) {
      console.error("Error fetching storage plans:", error);
      setError('Failed to load storage plans. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [server]);

  // Handle storage change submission
  const handleStorageChange = async () => {
    setError('');
    setDebugInfo({});

    if (!selectedStorageId) {
      setError('Please select a storage plan');
      return;
    }

    if (!currentStorageId) {
      setError('Current storage information is missing');
      return;
    }

    const currentId = parseInt(currentStorageId, 10);
    const selectedId = parseInt(selectedStorageId, 10);

    if (currentId === selectedId) {
      setError('Please select a different storage plan');
      return;
    }

    if (!storagePlans || !storagePlans.length) {
      setError('Storage plan information is unavailable');
      return;
    }

    const currentPlan = storagePlans.find(plan => parseInt(plan.id, 10) === currentId);
    const selectedPlan = storagePlans.find(plan => parseInt(plan.id, 10) === selectedId);

    if (!currentPlan || !selectedPlan) {
      console.error("Plan not found - Current:", !currentPlan, "Selected:", !selectedPlan);
      setError('Invalid storage plan selection');
      return;
    }

    // Validate that it's an upgrade
    if (!isStorageUpgrade(currentPlan, selectedPlan)) {
      setError('You can only upgrade to a larger storage plan');
      return;
    }

    try {
      setIsSubmitting(true);

      const token = sessionStorage.getItem('token');

      const generateStorageUpgradeInvoice = async () => {
        try {
          console.log("Generating storage upgrade invoice");

          const proratedAmount = calculateProratedAmount();
          
          const description = `Storage upgrade from ${currentPlan.name} to ${selectedPlan.name} for server ID ${server.id}`;
          
          const vatRate = vatInfo.vat_exempt ? 0 : (vatInfo.vat_rate || 0);
          const vatAmount = proratedAmount * (vatRate / 100);
          
          const invoicePayload = {
            token: sessionStorage.getItem('token'),
            amount: proratedAmount,
            order_id: server.order_id || server.id,
            type: "Storage Upgrade",
            description: description,
            vat_rate: vatRate,
            vat_amount: vatAmount,
            server_expires: server.expires,
            metadata: JSON.stringify({
              action: 'storage_upgrade',
              server_id: server.id,
              current_storage_id: currentId,
              new_storage_id: selectedId,
              server_expires: server.expires,
              hostname: server.hostname || server.server_hostname || server.label || null,
              server_label: server.label || null,
              main_ip: server.main_ip || null,
              location: server.locationname || server.datacenter || null,
              storage: server.storage || null
            })
          };
          
          console.log("Storage invoice generation payload:", invoicePayload);
          
          const response = await fetch('/api.php?f=generate_invoice', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(invoicePayload)
          });
          
          const responseText = await response.text();
          console.log("Storage invoice generation response:", responseText);
          
          const data = JSON.parse(responseText);
          
          if (data.success) {
            const enhancedResponse = {
              ...data,
              current_plan: currentPlan,
              new_plan: selectedPlan,
              prorated_amount: proratedAmount,
              vat_rate: vatRate,
              vat_amount: data.details?.tax || vatAmount,
              total_amount: data.details?.total || (proratedAmount + vatAmount),
              message: `Storage upgrade invoice generated successfully. Invoice #${data.invoice_number} has been created for ${selectedPlan.name}.`
            };
            
            return enhancedResponse;
          }
          
          return data;
        } catch (error) {
          console.error("Error generating storage upgrade invoice:", error);
          
          return {
            success: false,
            error: "Error generating storage upgrade invoice: " + error.message
          };
        }
      };

      if (!server || !server.id) {
        setError('Server information is missing');
        return;
      }

      const data = await generateStorageUpgradeInvoice();

      if (!data) {
        console.error("No data returned from API");
        setError('No response from server');
        return;
      }

      console.log("Processing API response:", data);

      if (data.success === true) {
        console.log("Storage upgrade invoice generated successfully");

        const formattedData = {
          ...data,
          message: data.message || "Storage upgrade request submitted. An invoice has been generated. Your storage will be upgraded after payment is processed."
        };

        if (typeof onSuccess === 'function') {
          console.log("Calling onSuccess callback with data:", formattedData);
          onSuccess(formattedData);
        }

        onClose();
      } else {
        console.error("API returned error:", data.error || "Unknown error");

        if (data.error && data.error.includes("unpaid")) {
          setError(data.error);
        } else if (data.error && data.error.includes("Invalid")) {
          setError(
            "Failed to generate invoice. " +
            "Please contact support and mention server ID: " + server.id
          );
        } else if (data.error && data.error.includes("permission")) {
          setError(
            "You don't have permission to generate an invoice for this server. " +
            "Please contact support for assistance."
          );
        } else if (data.error && data.error.includes("not found")) {
          setError(
            "The server information could not be found. " +
            "Please contact support and mention server ID: " + server.id
          );
        } else {
          setError(data.error || 'Failed to generate storage upgrade invoice');
        }
      }
    } catch (error) {
      console.error("Error changing storage:", error);
      setError('Connection error - please try again');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Load storage plans and VAT info when the modal opens
  useEffect(() => {
    if (isOpen && server) {
      console.log("Storage modal opened with server data:", server);
      console.log("Server ID:", server.id);
      console.log("Server storage_id:", server.storage_id);
      console.log("Server storage:", server.storage);

      setError('');
      setDebugInfo({});
      setIsLoading(true);

      fetchStoragePlans();
      fetchVatInfo();
    }
  }, [isOpen, server, fetchStoragePlans, fetchVatInfo]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setError('');
      setSelectedStorageId(null);
      setCurrentStorageId(null);
      setStoragePlans([]);
      setDebugInfo({});
      setVatInfo({
        vat_rate: 0,
        vat_exempt: false,
        country: null,
        vat_id: null
      });
    }
  }, [isOpen]);

  // Helper function to get upgrade plans only
  const getUpgradePlans = () => {
    if (!currentStorageId || !storagePlans.length) return [];

    const currentPlan = storagePlans.find(plan => plan.id === currentStorageId);
    if (!currentPlan) return storagePlans;

    return storagePlans.filter(plan => {
      if (plan.id === currentStorageId) return false; // Exclude current plan
      return isStorageUpgrade(currentPlan, plan); // Only include upgrades
    });
  };

  return (
    <MDBModal show={isOpen} tabIndex='-1'>
      <MDBModalDialog>
        <MDBModalContent>
          <MDBModalHeader>
            <MDBModalTitle>Storage Plan Upgrade</MDBModalTitle>
            <button type="button" className="close" onClick={onClose}>
              <span aria-hidden="true">&times;</span>
            </button>
          </MDBModalHeader>

          <MDBModalBody>
            {(isLoading || isLoadingVat) ? (
              <div className="text-center py-4">
                <div className="spinner-border text-primary mb-3" role="status"></div>
                <p>Loading storage plans and tax information...</p>
              </div>
            ) : (
              <div className="form-group">
                {/* Current Storage Display */}


                {/* Storage not detected warning */}
                {!currentStorageId && storagePlans.length > 0 && (
                  <div className="alert alert-warning mb-3">
                    <i className="fa fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> Could not automatically detect your current storage plan. 
                    Please contact support for assistance.
                    {debugInfo && Object.keys(debugInfo).length > 0 && (
                      <details className="mt-2">
                        <summary style={{ cursor: 'pointer' }}>View Debug Information</summary>
                        <pre className="mt-2 p-2 bg-light rounded" style={{ fontSize: '11px' }}>
                          {JSON.stringify(debugInfo, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                )}

                <label className="form-label mb-1">Select New Storage Plan:</label>
                <select
                  name="storage"
                  className="form-control custom-select mb-2"
                  value={selectedStorageId || ''}
                  onChange={(e) => {
                    const newStorageId = parseInt(e.target.value, 10);
                    if (!isNaN(newStorageId)) {
                      setSelectedStorageId(newStorageId);
                      setError('');
                    }
                  }}
                  disabled={isSubmitting || storagePlans.length === 0}
                >
                  <option value="" disabled>
                    {storagePlans.length === 0 ? 'No storage plans available' : 'Select a storage plan'}
                  </option>
                  {storagePlans.map(plan => {
                    const planId = parseInt(plan?.id || 0, 10);
                    const currentId = parseInt(currentStorageId || 0, 10);

                    const currentPlan = storagePlans.find(p => {
                      const pId = parseInt(p?.id || 0, 10);
                      return pId === currentId;
                    });

                    let isUpgrade = false;
                    let upgradeText = '';
                    
                    if (currentPlan) {
                      isUpgrade = isStorageUpgrade(currentPlan, plan);
                      
                      if (isUpgrade) {
                        const priceDiff = parseFloat(plan?.price || 0) - parseFloat(currentPlan?.price || 0);
                        upgradeText = ` (+€${priceDiff.toFixed(2)}/mo)`;
                      }
                    }

                    const isDisabled = planId === currentId || !isUpgrade;
                    const isCurrent = planId === currentId;

                    return (
                      <option
                        key={planId}
                        value={planId}
                        disabled={isDisabled}
                        style={{
                          fontWeight: isCurrent ? 'bold' : 'normal',
                          color: isCurrent ? '#007bff' : isUpgrade ? '#28a745' : '#6c757d'
                        }}
                      >
                        {plan?.name || `Plan ${planId}`}
                        {isCurrent ? ' (Current Plan)' :
                          isUpgrade ? upgradeText :
                          ' (Downgrade not allowed)'}
                      </option>
                    );
                  })}
                </select>



                {/* No storage plans available */}
                {storagePlans.length === 0 && !isLoading && (
                  <div className="alert alert-warning mb-2">
                    <i className="fa fa-exclamation-triangle me-1" style={{paddingRight: '0.5rem'}}></i>
                    No storage plans are available. Please contact support for assistance.
                  </div>
                )}

                {/* No upgrade plans available */}
                {currentStorageId && storagePlans.length > 0 && getUpgradePlans().length === 0 && (
                  <div className="alert alert-info mb-2">
                    <i className="fa fa-info-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                    You are already on the highest available storage plan. No upgrades are available.
                  </div>
                )}

                {/* Error display */}
                {error && (
                  <div className="alert alert-danger mb-2">
                    <i className="fa fa-exclamation-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                    {error}
                  </div>
                )}

                {/* Price Summary */}
                {selectedStorageId && selectedStorageId !== currentStorageId && (
                  <div className="card mb-2">
                    <div className="card-header bg-light py-1"><h5 className="mb-0">Price Summary</h5></div>
                    <div className="card-body py-2">
                      {(() => {
                        const currentPlan = storagePlans.find(plan =>
                          parseInt(plan?.id || 0, 10) === parseInt(currentStorageId || 0, 10)
                        );
                        const selectedPlan = storagePlans.find(plan =>
                          parseInt(plan?.id || 0, 10) === parseInt(selectedStorageId || 0, 10)
                        );

                        if (!currentPlan || !selectedPlan) {
                          return <p>Unable to calculate price difference.</p>;
                        }

                        const currentStoragePrice = parseFloat(currentPlan.price || 0);
                        const newStoragePrice = parseFloat(selectedPlan.price || 0);
                        
                        const currentTotalPrice = parseFloat(server.monthly_price || server.price || 0);
                        const baseServerPrice = Math.max(0, currentTotalPrice - currentStoragePrice);
                        
                        const newTotalPrice = baseServerPrice + newStoragePrice;
                        const totalPriceDifference = newTotalPrice - currentTotalPrice;

                        const proratedAmount = calculateProratedAmount();
                        
                        const vatRate = vatInfo.vat_exempt ? 0 : (vatInfo.vat_rate || 0);
                        const vatAmount = proratedAmount * (vatRate / 100);

                        return (
                          <>
                            <div className="row mb-3">
                              <div className="col-md-6">
                                <h6 className="text-muted mb-2">Current Service</h6>
                                <div className="card">
                                  <div className="card-body p-2">
                                    <div><strong>Total Monthly Cost</strong></div>
                                    <div className="small text-muted mb-2">
                                      <div>Base Server: €{baseServerPrice.toFixed(2)}</div>
                                      <div>Storage ({currentPlan.name}): €{currentStoragePrice.toFixed(2)}</div>
                                    </div>
                                    <div className="h6 mb-0 text-primary">€{currentTotalPrice.toFixed(2)}/month</div>
                                  </div>
                                </div>
                              </div>
                              <div className="col-md-6">
                                <h6 className="text-muted mb-2">After Upgrade</h6>
                                <div className="card border-success">
                                  <div className="card-body p-2">
                                    <div><strong>Total Monthly Cost</strong></div>
                                    <div className="small text-muted mb-2">
                                      <div>Base Server: €{baseServerPrice.toFixed(2)}</div>
                                      <div>Storage ({selectedPlan.name}): €{newStoragePrice.toFixed(2)}</div>
                                    </div>
                                    <div className="h6 mb-0 text-success">€{newTotalPrice.toFixed(2)}/month</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            <hr className="my-2" />
                            
                            <div className="d-flex justify-content-between mb-1">
                              <div>Monthly Price Increase:</div>
                              <div>€{totalPriceDifference.toFixed(2)}</div>
                            </div>
                            <div className="d-flex justify-content-between mb-1">
                              <div>Prorated Amount:</div>
                              <div>€{proratedAmount.toFixed(2)}</div>
                            </div>
                            {!vatInfo.vat_exempt && vatRate > 0 && (
                              <div className="d-flex justify-content-between mb-1">
                                <div>VAT ({vatRate.toFixed(1)}%{vatInfo.country ? ` - ${vatInfo.country}` : ''}):</div>
                                <div>€{vatAmount.toFixed(2)}</div>
                              </div>
                            )}
                            {vatInfo.vat_exempt && (
                              <div className="d-flex justify-content-between mb-1">
                                <div>VAT:</div>
                                <div>Exempt</div>
                              </div>
                            )}
                            <div className="d-flex justify-content-between mb-1">
                              <div><strong>Total Amount Due Now:</strong></div>
                              <div><strong>€{(proratedAmount + vatAmount).toFixed(2)}</strong></div>
                            </div>
                            <div className="small text-muted">
                              Prorated to next billing date ({server?.expires || 'N/A'}).
                              New monthly cost will be €{newTotalPrice.toFixed(2)} starting from next billing cycle.
                              {vatInfo.country && ` VAT based on ${vatInfo.country}.`}
                              {vatInfo.vat_exempt && ' VAT exempt status applied.'}
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  </div>
                )}

                <div className="alert alert-info mb-2 py-2">
                  <i className="fa fa-info-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                  <strong>Billing Process:</strong> Clicking "Generate Upgrade Invoice" will create an invoice for the prorated amount.
                  Your storage will be upgraded after the invoice is paid.
                </div>

                <div className="alert alert-warning mb-2 py-2">
                  <i className="fa fa-exclamation-triangle me-1" style={{paddingRight: '0.5rem'}}></i>
                  <strong>Note:</strong> You can only upgrade to larger storage plans. Downgrades are not allowed.
                  Your current data will be preserved during the upgrade process.
                </div>
              </div>
            )}
          </MDBModalBody>

          <MDBModalFooter>
            <button
              className="btn btn-secondary"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              className="btn btn-success ms-2"
              onClick={handleStorageChange}
              disabled={
                isLoading ||
                isLoadingVat ||
                isSubmitting ||
                !selectedStorageId ||
                selectedStorageId === currentStorageId ||
                !storagePlans ||
                storagePlans.length === 0 ||
                getUpgradePlans().length === 0
              }
            >
              {isSubmitting ? (
                <>
                  <span className="spinner-border spinner-border-sm me-1" role="status"></span>
                  Processing...
                </>
              ) : (
                <>Generate Upgrade Invoice</>
              )}
            </button>
          </MDBModalFooter>
        </MDBModalContent>
      </MDBModalDialog>
    </MDBModal>
  );
};

export default StorageUpgradeModal;