import React, { useState, useEffect, useCallback } from "react";
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
} from 'mdb-react-ui-kit';

/**
 * VpsIpUpgradeModal Component
 *
 * A reusable modal component for adding additional IP addresses to VPS servers.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Controls whether the modal is displayed
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Object} props.server - Server data object
 * @param {Function} props.onSuccess - Function to call after successful IP upgrade
 */
const VpsIpUpgradeModal = ({ isOpen, onClose, server, onSuccess }) => {
  // State for IP packages and selection
  const [ipPackages, setIpPackages] = useState([]);
  const [selectedIpPackageId, setSelectedIpPackageId] = useState(null);
  const [currentIpPackage, setCurrentIpPackage] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [debugInfo, setDebugInfo] = useState({});
  
  // VAT-related state
  const [vatInfo, setVatInfo] = useState({
    vat_rate: 0,
    vat_exempt: false,
    country: null,
    vat_id: null
  });
  const [isLoadingVat, setIsLoadingVat] = useState(false);

  // Get base service price (current service price minus current IP package price)
  const getBaseServicePrice = () => {
    const currentTotalPrice = parseFloat(server?.price || server?.monthly_price || server?.cost || 0);
    const currentIpPackagePrice = currentIpPackage ? parseFloat(currentIpPackage.price || 0) : 0;
    return currentTotalPrice - currentIpPackagePrice;
  };

  // Fetch current IP package from database (orders_items -> subnet_id -> vps_ips)
  const fetchCurrentIpPackage = useCallback(async () => {
    if (!server || !server.id) return;

    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('/api.php?f=get_current_ip_package', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          token,
          server_id: server.id
        })
      });

      const responseText = await response.text();
      console.log("Raw current IP package response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log("Parsed current IP package data:", data);
      } catch (e) {
        console.error("Failed to parse current IP package response:", e);
        return;
      }

      if (data && data.success) {
        if (data.current_package) {
          setCurrentIpPackage(data.current_package);
          console.log("Current IP package set:", data.current_package);
        } else {
          setCurrentIpPackage(null);
          console.log("No current IP package found");
        }
      } else {
        console.error("Error fetching current IP package:", data.error || "Unknown error");
      }
    } catch (error) {
      console.error("Error fetching current IP package:", error);
    }
  }, [server]);

  // Fetch VAT information
  const fetchVatInfo = useCallback(async () => {
    setIsLoadingVat(true);
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('/api.php?f=get_vat_rate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log("Raw VAT rate response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log("Parsed VAT data:", data);
      } catch (e) {
        console.error("Failed to parse VAT response:", e);
        return;
      }

      if (data && data.success) {
        setVatInfo({
          vat_rate: parseFloat(data.vat_rate) || 0,
          vat_exempt: data.vat_exempt || false,
          country: data.country || null,
          vat_id: data.vat_id || null
        });
        console.log("VAT info set:", {
          vat_rate: parseFloat(data.vat_rate) || 0,
          vat_exempt: data.vat_exempt || false,
          country: data.country || null
        });
      }
    } catch (error) {
      console.error("Error fetching VAT info:", error);
    } finally {
      setIsLoadingVat(false);
    }
  }, []);

  // Calculate prorated amount for the IP upgrade
  const calculateProratedAmount = useCallback(() => {
    // Skip calculation if no package selected
    if (!selectedIpPackageId || !ipPackages || !ipPackages.length) {
      return 0;
    }

    // Find selected IP package
    const selectedPackage = ipPackages.find(pkg => 
      parseInt(pkg.id, 10) === parseInt(selectedIpPackageId, 10)
    );

    if (!selectedPackage) {
      console.log(`IP package not found for ID: ${selectedIpPackageId}`);
      return 0;
    }

    // Calculate price difference if there's a current package
    let priceDifference = parseFloat(selectedPackage.price || 0);
    
    if (currentIpPackage) {
      const currentPrice = parseFloat(currentIpPackage.price || 0);
      const newPrice = parseFloat(selectedPackage.price || 0);
      priceDifference = newPrice - currentPrice;
      
      // Don't allow downgrades
      if (priceDifference <= 0) {
        return 0;
      }
    }

    // Calculate remaining days until due date
    const today = new Date();
    const dueDate = server?.next_renewal ? new Date(server.next_renewal) : null;

    if (!dueDate) {
      return priceDifference; // If no due date, charge full amount
    }

    const daysRemaining = Math.max(0, Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24)));

    // Calculate prorated amount: price_difference / 30 * days_remaining
    const proratedAmount = (priceDifference / 30) * daysRemaining;

    return Math.round(proratedAmount * 100) / 100; // Round to 2 decimal places
  }, [selectedIpPackageId, currentIpPackage, ipPackages, server]);

  // Fetch IP packages from API with server_id for current package detection
  const fetchIpPackages = useCallback(async () => {
    if (!server || !server.id) return;

    setIsLoading(true);
    setError('');
    
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch('/api.php?f=get_vps_ip_packages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          token,
          server_id: server.id  // Pass server_id for current package detection
        })
      });

      const responseText = await response.text();
      console.log("Raw IP packages response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log("Parsed IP packages:", data);
      } catch (e) {
        console.error("Failed to parse IP packages response:", e);
        setError('Invalid response from server');
        return;
      }

      // Handle error response
      if (data && data.error) {
        setError(data.message || 'Failed to load IP packages');
        return;
      }

      if (data && Array.isArray(data)) {
        // Convert all IDs to numbers for consistency
        const packagesWithNumericIds = data.map(pkg => ({
          ...pkg,
          id: parseInt(pkg.id, 10),
          price: parseFloat(pkg.price || 0),
          ip_count: parseInt(pkg.ip_count || 1, 10),
          is_current: pkg.is_current || false
        }));

        // Sort packages by price for easier comparison
        packagesWithNumericIds.sort((a, b) => a.price - b.price);
        
        // Remove duplicates based on package ID
        const uniquePackages = packagesWithNumericIds.filter((pkg, index, array) => 
          array.findIndex(p => p.id === pkg.id) === index
        );
        
        setIpPackages(uniquePackages);

        // Set default selection to first package that's not current (for upgrades)
        if (currentIpPackage) {
          const upgradeOptions = uniquePackages.filter(pkg => 
            pkg.id !== currentIpPackage.id && pkg.price > currentIpPackage.price
          );
          
          if (upgradeOptions.length > 0) {
            setSelectedIpPackageId(upgradeOptions[0].id);
          } else {
            setSelectedIpPackageId(null);
          }
        } else {
          if (uniquePackages.length > 0) {
            setSelectedIpPackageId(uniquePackages[0].id);
          }
        }

        // Store debug info
        setDebugInfo({
          total_packages: uniquePackages.length,
          original_packages: packagesWithNumericIds.length,
          current_package_id: currentIpPackage?.id || null,
          current_package_name: currentIpPackage?.label || null,
          detection_method: currentIpPackage ? 'database_lookup' : 'none'
        });

      } else {
        setError('No IP packages available');
      }
    } catch (error) {
      console.error("Error fetching IP packages:", error);
      setError('Failed to load IP packages. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [server, currentIpPackage]);

  // Handle IP upgrade submission
  const handleIpUpgrade = async () => {
    // Clear previous errors
    setError('');
    setDebugInfo({});

    // Basic validation
    if (!selectedIpPackageId) {
      setError('Please select an IP package');
      return;
    }

    // Safety check for ipPackages
    if (!ipPackages || !ipPackages.length) {
      setError('IP package information is unavailable');
      return;
    }

    // Find the selected package
    const selectedPackage = ipPackages.find(pkg => 
      parseInt(pkg.id, 10) === parseInt(selectedIpPackageId, 10)
    );

    if (!selectedPackage) {
      console.error("IP package not found for ID:", selectedIpPackageId);
      setError('Invalid IP package selection');
      return;
    }

    // Validate that this is a valid upgrade if there's a current package
    if (currentIpPackage) {
      if (selectedIpPackageId === currentIpPackage.id) {
        setError('You have already selected your current package. Please choose a different package to upgrade.');
        return;
      }
      
      if (parseFloat(selectedPackage.price) <= parseFloat(currentIpPackage.price)) {
        setError('You can only upgrade to a higher-tier IP package. Downgrades are not allowed.');
        return;
      }
    }

    try {
      setIsSubmitting(true);
      setDebugInfo({
        stage: 'starting_request',
        selectedPackage,
        currentIpPackage,
        server_id: server.id
      });

      const token = sessionStorage.getItem('token');

      // Direct invoice generation approach
      const generateIpUpgradeInvoice = async () => {
        try {
          console.log("Using direct invoice generation approach for IP upgrade");
          setDebugInfo(prev => ({ ...prev, stage: 'direct_invoice_generation' }));

          // Calculate prorated amount
          const proratedAmount = calculateProratedAmount();
          setDebugInfo(prev => ({ ...prev, proratedAmount }));
          
          // Get current and new IP counts from database packages
          const currentIpCount = currentIpPackage ? currentIpPackage.ip_count : 1;
          const newIpCount = selectedPackage.ip_count;
          const additionalIpsFromPackage = newIpCount - currentIpCount;
          
          // Create a detailed description for better tracking
          const upgradeType = currentIpPackage ? "upgrade" : "assignment";
          const description = `IP ${upgradeType}: ${selectedPackage.label} for server ${server.hostname || server.name || server.id}`;
          
          // Use VAT info from state
          const vatRate = vatInfo.vat_exempt ? 0 : (vatInfo.vat_rate || 0);
          const vatAmount = proratedAmount * (vatRate / 100);
          
          // Create invoice payload with all required fields INCLUDING new_ip_count
          const invoicePayload = {
            token: sessionStorage.getItem('token'),
            amount: proratedAmount,
            order_id: server.order_id || server.id,
            type: "Additional IPs Upgrade",
            description: description,
            vat_rate: vatRate,
            vat_amount: vatAmount,
            server_expires: server.next_renewal,
            server_hostname: server.hostname || server.name || null,
            metadata: JSON.stringify({
              action: 'vps_ip_upgrade',
              server_id: server.id,
              ip_package_id: selectedIpPackageId,
              new_additional_ips_id: selectedIpPackageId,
              current_ip_package_id: currentIpPackage?.id || null,
              current_ip_count: currentIpCount, // From database
              new_ip_count: newIpCount, // From database
              additional_ips: additionalIpsFromPackage,
              server_expires: server.next_renewal,
              server_hostname: server.hostname || server.name || null,
              ip_package_label: selectedPackage.label,
              current_ip_package_label: currentIpPackage?.label || null,
              upgrade_type: upgradeType,
              subnet_id: currentIpPackage?.subnet_id || null
            })
          };
          
          console.log("IP upgrade invoice generation payload:", invoicePayload);
          setDebugInfo(prev => ({ ...prev, invoicePayload }));
          
          // Generate the invoice
          const response = await fetch('/api.php?f=generate_invoice', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(invoicePayload)
          });
          
          const responseText = await response.text();
          console.log("IP upgrade invoice generation response:", responseText);
          setDebugInfo(prev => ({ ...prev, rawResponse: responseText }));
          
          // Parse and process the response
          const data = JSON.parse(responseText);
          setDebugInfo(prev => ({ ...prev, parsedResponse: data }));
          
          if (data.success) {
            // Enhance the response with the IP package information
            const enhancedResponse = {
              ...data,
              ip_package: selectedPackage,
              current_ip_count: currentIpCount,
              new_ip_count: newIpCount,
              additional_ips: additionalIpsFromPackage,
              current_ip_package: currentIpPackage,
              prorated_amount: proratedAmount,
              vat_rate: vatRate,
              vat_amount: data.details?.tax || vatAmount,
              total_amount: data.details?.total || (proratedAmount + vatAmount),
              message: `IP address ${upgradeType} invoice generated successfully. Invoice #${data.invoice_number} has been created for ${selectedPackage.label}.`
            };
            
            console.log("Enhanced IP upgrade success response:", enhancedResponse);
            return enhancedResponse;
          }
          
          // Return the original error response if not successful
          return data;
        } catch (error) {
          console.error("Error generating IP upgrade invoice:", error);
          setDebugInfo(prev => ({ 
            ...prev, 
            error: error.message,
            errorStack: error.stack
          }));
          
          return {
            success: false,
            error: "Error generating IP upgrade invoice: " + error.message
          };
        }
      };

      // Check for server
      if (!server) {
        setError('Server information is missing');
        return;
      }

      // Check if we have server.id
      if (!server.id) {
        setError('Server ID information is missing');
        return;
      }

      // Generate the IP upgrade invoice
      const data = await generateIpUpgradeInvoice();

      // Check if the response is valid
      if (!data) {
        console.error("No data returned from API");
        setError('No response from server');
        return;
      }

      console.log("Processing IP upgrade API response:", data);
      setDebugInfo(prev => ({ ...prev, stage: 'processing_response', finalResponse: data }));

      if (data.success === true) {
        console.log("IP upgrade invoice generated successfully");

        // Format the data for the success callback
        const formattedData = {
          ...data,
          message: data.message || "IP address upgrade request submitted. An invoice has been generated. Additional IP addresses will be assigned after payment is processed."
        };

        // Call the success callback
        if (typeof onSuccess === 'function') {
          console.log("Calling onSuccess callback with IP upgrade data:", formattedData);
          onSuccess(formattedData);
        }

        // Close the modal
        onClose();
      } else {
        console.error("API returned error:", data.error || "Unknown error");

        // Provide more specific error messages based on common issues
        if (data.error && data.error.includes("unpaid")) {
          setError(data.error);
        } else if (data.error && data.error.includes("Invalid")) {
          setError(
            "Failed to generate invoice. " +
            "Please contact support and mention server ID: " + server.id
          );
        } else if (data.error && data.error.includes("permission")) {
          setError(
            "You don't have permission to generate an invoice for this server. " +
            "Please contact support for assistance."
          );
        } else if (data.error && data.error.includes("not found")) {
          setError(
            "The server information could not be found. " +
            "Please contact support and mention server ID: " + server.id
          );
        } else {
          setError(data.error || 'Failed to generate IP upgrade invoice');
        }
      }
    } catch (error) {
      console.error("Error upgrading IP addresses:", error);
      setError('Connection error - please try again');
      setDebugInfo(prev => ({ 
        ...prev, 
        fatalError: error.message,
        fatalErrorStack: error.stack
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Load IP packages and VAT info when the modal opens
  useEffect(() => {
    if (isOpen && server) {
      console.log("IP upgrade modal opened with server data:", server);

      // Reset any previous errors
      setError('');
      setDebugInfo({});

      // Reset loading state
      setIsLoading(true);

      // Fetch current IP package first, then other data
      const loadData = async () => {
        await fetchCurrentIpPackage();
        await fetchVatInfo();
      };
      
      loadData();
    }
  }, [isOpen, server, fetchCurrentIpPackage, fetchVatInfo]);

  // Fetch IP packages after current package is loaded
  useEffect(() => {
    if (isOpen && server && (currentIpPackage !== undefined)) {
      fetchIpPackages();
    }
  }, [isOpen, server, currentIpPackage, fetchIpPackages]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setError('');
      setSelectedIpPackageId(null);
      setCurrentIpPackage(null);
      setDebugInfo({});
      // Reset VAT info when modal closes
      setVatInfo({
        vat_rate: 0,
        vat_exempt: false,
        country: null,
        vat_id: null
      });
    }
  }, [isOpen]);

  // Get only upgrade packages (packages that cost more than current)
  const getUpgradePackages = () => {
    if (!currentIpPackage || !ipPackages.length) {
      return ipPackages; // If no current package, all packages are available
    }

    return ipPackages.filter(pkg => 
      pkg.id !== currentIpPackage.id && parseFloat(pkg.price) > parseFloat(currentIpPackage.price)
    );
  };

  // Check if selected package is a valid upgrade
  const isValidUpgrade = () => {
    if (!selectedIpPackageId || !ipPackages.length) return false;
    
    const selectedPackage = ipPackages.find(pkg => pkg.id === selectedIpPackageId);
    if (!selectedPackage) return false;
    
    // If no current package, any selection is valid
    if (!currentIpPackage) return true;
    
    // If there is a current package, ensure selected is not current and has higher price
    if (selectedIpPackageId === currentIpPackage.id) return false;
    
    return parseFloat(selectedPackage.price) > parseFloat(currentIpPackage.price);
  };

  return (
    <MDBModal show={isOpen} tabIndex='-1'>
      <MDBModalDialog>
        <MDBModalContent>
          <MDBModalHeader>
            <MDBModalTitle>
              {currentIpPackage ? 'Upgrade IP Package' : 'Add IP Addresses'}
            </MDBModalTitle>
            <button type="button" className="close" onClick={onClose}>
              <span aria-hidden="true">&times;</span>
            </button>
          </MDBModalHeader>

          <MDBModalBody>
            {(isLoading || isLoadingVat) ? (
              <div className="text-center py-4">
                <div className="spinner-border text-primary mb-3" role="status"></div>
                <p>Loading IP packages and tax information...</p>
              </div>
            ) : (
              <div className="form-group">

                <label className="form-label mb-1">
                  Select {currentIpPackage ? 'New' : 'Additional'} IP Package:
                </label>

                <select
                  name="ip_package"
                  className="form-control custom-select mb-2"
                  value={selectedIpPackageId || ''}
                  onChange={(e) => {
                    console.log("Selected IP package value from dropdown:", e.target.value);
                    const newPackageId = parseInt(e.target.value, 10);
                    console.log("Parsed IP package ID:", newPackageId);

                    if (!isNaN(newPackageId)) {
                      console.log("Setting selected IP package ID to:", newPackageId);
                      setSelectedIpPackageId(newPackageId);
                      setError('');
                    }
                  }}
                  disabled={isSubmitting}
                >
                  <option value="" disabled>
                    {ipPackages.length === 0 ? 'No IP packages available' : `Select ${currentIpPackage ? 'upgrade' : 'an IP'} package`}
                  </option>
                  {ipPackages.map(pkg => {
                    const packageId = parseInt(pkg?.id || 0, 10);
                    const isCurrent = currentIpPackage && packageId === currentIpPackage.id;
                    
                    // Determine if this is a valid selection
                    let isValidUpgrade = true;
                    let disabledReason = '';
                    
                    if (isCurrent) {
                      isValidUpgrade = false;
                      disabledReason = ' (Current Package)';
                    } else if (currentIpPackage && parseFloat(pkg?.price || 0) <= parseFloat(currentIpPackage?.price || 0)) {
                      isValidUpgrade = false;
                      disabledReason = ' (Downgrade - Not Allowed)';
                    }
                    
                    // Calculate price display
                    let priceText = '';
                    if (currentIpPackage && !isCurrent) {
                      const priceDiff = parseFloat(pkg?.price || 0) - parseFloat(currentIpPackage?.price || 0);
                      if (priceDiff > 0) {
                        priceText = ` (+€${priceDiff.toFixed(2)}/mo)`;
                      } else if (priceDiff < 0) {
                        priceText = ` (-€${Math.abs(priceDiff).toFixed(2)}/mo)`;
                      } else {
                        priceText = ` (€${parseFloat(pkg?.price || 0).toFixed(2)}/mo)`;
                      }
                    } else {
                      priceText = ` (€${parseFloat(pkg?.price || 0).toFixed(2)}/mo)`;
                    }

                    console.log(`Rendering IP package option: ID=${packageId}, Label=${pkg?.label}, Is Current=${isCurrent}, Valid Upgrade=${isValidUpgrade}`);

                    return (
                      <option
                        key={packageId || `pkg-${Math.random()}`}
                        value={packageId}
                        disabled={!isValidUpgrade}
                        style={{
                          color: isCurrent ? '#007bff' : 
                                isValidUpgrade ? '#28a745' : 
                                '#6c757d',
                          fontWeight: isCurrent ? 'bold' : 'normal'
                        }}
                      >
                        {pkg?.label || `Package ${packageId}`}{priceText}{disabledReason}
                      </option>
                    );
                  })}
                </select>

                {/* No upgrade packages available */}
                {currentIpPackage && getUpgradePackages().length === 0 && (
                  <div className="alert alert-info mb-2">
                    <i className="fa fa-info-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                    You are already on the highest available IP package. All packages are shown below for reference.
                  </div>
                )}

                {error && (
                  <div className="alert alert-danger mb-2">
                    <i className="fa fa-exclamation-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                    {error}
                  </div>
                )}

                {selectedIpPackageId && (
                  <div className="card mb-2">
                    <div className="card-header bg-light py-1"><h5 className="mb-0">IP Configuration & Price Summary</h5></div>
                    <div className="card-body py-2">
                      {(() => {
                        // Find selected package
                        const selectedPackage = ipPackages.find(pkg =>
                          parseInt(pkg?.id || 0, 10) === parseInt(selectedIpPackageId || 0, 10)
                        );

                        if (!selectedPackage) {
                          return <p>Unable to find selected IP package.</p>;
                        }

                        // Calculate prorated amount
                        const proratedAmount = calculateProratedAmount();
                        
                        // Get IP counts from database packages
                        const currentIpCount = currentIpPackage ? currentIpPackage.ip_count : 1;
                        const newIpCount = selectedPackage.ip_count;
                        
                        // Calculate current and new total monthly costs
                        const baseServicePrice = getBaseServicePrice();
                        const currentTotalMonthlyCost = parseFloat(server?.price || server?.monthly_price || server?.cost || 0);
                        const newTotalMonthlyCost = baseServicePrice + parseFloat(selectedPackage.price);
                        
                        // Use VAT info from state
                        const vatRate = vatInfo.vat_exempt ? 0 : (vatInfo.vat_rate || 0);
                        const vatAmount = proratedAmount * (vatRate / 100);

                        return (
                          <>
                            <div className="row mb-3">
                              <div className="col-md-6">
                                <h6 className="text-muted mb-2">Current Configuration</h6>
                                <div className="card">
                                  <div className="card-body p-2">
                                    <div><strong>VPS & IP Package</strong></div>
                                    <div className="small text-muted mb-2">
                                      <div>Base VPS: €{baseServicePrice.toFixed(2)}/month</div>
                                      {currentIpPackage ? (
                                        <>
                                          <div>IP Package: {currentIpPackage.label}</div>
                                          <div>IP Cost: €{parseFloat(currentIpPackage.price).toFixed(2)}/month</div>
                                          <div>IP Count: {currentIpPackage.ip_count}</div>
                                        </>
                                      ) : (
                                        <>
                                          <div>IP Package: None assigned</div>
                                          <div>IP Count: 1 (Main IP only)</div>
                                        </>
                                      )}
                                    </div>
                                    <div className="h6 mb-0 text-primary">
                                      €{currentTotalMonthlyCost.toFixed(2)}/month total
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="col-md-6">
                                <h6 className="text-muted mb-2">After {currentIpPackage ? 'Upgrade' : 'Assignment'}</h6>
                                <div className="card border-success">
                                  <div className="card-body p-2">
                                    <div><strong>VPS & IP Package</strong></div>
                                    <div className="small text-muted mb-2">
                                      <div>Base VPS: €{baseServicePrice.toFixed(2)}/month</div>
                                      <div>IP Package: {selectedPackage.label}</div>
                                      <div>IP Cost: €{parseFloat(selectedPackage.price).toFixed(2)}/month</div>
                                      <div>IP Count: {selectedPackage.ip_count}</div>
                                    </div>
                                    <div className="h6 mb-0 text-success">
                                      €{newTotalMonthlyCost.toFixed(2)}/month total
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            <hr className="my-2" />

                            {currentIpPackage && (
                              <div className="d-flex justify-content-between mb-1">
                                <div>Monthly Increase:</div>
                                <div>€{(parseFloat(selectedPackage.price) - parseFloat(currentIpPackage.price)).toFixed(2)}</div>
                              </div>
                            )}
                            <div className="d-flex justify-content-between mb-1">
                              <div>Prorated Amount:</div>
                              <div>€{proratedAmount.toFixed(2)}</div>
                            </div>
                            {!vatInfo.vat_exempt && vatRate > 0 && (
                              <div className="d-flex justify-content-between mb-1">
                                <div>VAT ({vatRate.toFixed(1)}%{vatInfo.country ? ` - ${vatInfo.country}` : ''}):</div>
                                <div>€{vatAmount.toFixed(2)}</div>
                              </div>
                            )}
                            {vatInfo.vat_exempt && (
                              <div className="d-flex justify-content-between mb-1">
                                <div>VAT:</div>
                                <div>Exempt</div>
                              </div>
                            )}
                            <div className="d-flex justify-content-between mb-1">
                              <div><strong>Total Amount Due Now:</strong></div>
                              <div><strong>€{(proratedAmount + vatAmount).toFixed(2)}</strong></div>
                            </div>
                            <div className="small text-muted">
                              Prorated to next billing date ({server?.next_renewal || 'N/A'}).
                              {currentIpPackage ? 
                                `New total monthly cost will be €${newTotalMonthlyCost.toFixed(2)} (Base VPS: €${baseServicePrice.toFixed(2)} + IP Package: €${parseFloat(selectedPackage.price).toFixed(2)}) starting from next billing cycle.` :
                                `Starting from next billing cycle, total monthly cost will be €${newTotalMonthlyCost.toFixed(2)} (Base VPS: €${baseServicePrice.toFixed(2)} + IP Package: €${parseFloat(selectedPackage.price).toFixed(2)}).`
                              }
                              {vatInfo.country && ` VAT based on ${vatInfo.country}.`}
                              {vatInfo.vat_exempt && ' VAT exempt status applied.'}
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  </div>
                )}

                <div className="alert alert-info mb-2 py-2">
                  <i className="fa fa-info-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                  <strong>Process:</strong> Clicking "Generate IP {currentIpPackage ? 'Upgrade' : 'Assignment'} Invoice" will create an invoice for the prorated amount.
                  Additional IP addresses will be assigned to your server after the invoice is paid.
                </div>

                <div className="alert alert-warning mb-2 py-2">
                  <i className="fa fa-exclamation-triangle me-1" style={{paddingRight: '0.5rem'}}></i>
                  <strong>Note:</strong> IP addresses are billed monthly. The price shown is prorated to your next billing date.
                  New IP addresses will be automatically configured on your server after payment.
                </div>
              </div>
            )}
          </MDBModalBody>

          <MDBModalFooter>
            <button
              className="btn btn-secondary"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              className="btn btn-success ms-2"
              onClick={handleIpUpgrade}
              disabled={
                isLoading ||
                isLoadingVat ||
                isSubmitting ||
                !selectedIpPackageId ||
                !ipPackages ||
                ipPackages.length === 0 ||
                !isValidUpgrade()
              }
            >
              {isSubmitting ? (
                <>
                  <span className="spinner-border spinner-border-sm me-1" role="status"></span>
                  Processing...
                </>
              ) : (
                <>Generate IP {currentIpPackage ? 'Upgrade' : 'Assignment'} Invoice</>
              )}
            </button>
          </MDBModalFooter>
        </MDBModalContent>
      </MDBModalDialog>
    </MDBModal>
  );
};

export default VpsIpUpgradeModal;