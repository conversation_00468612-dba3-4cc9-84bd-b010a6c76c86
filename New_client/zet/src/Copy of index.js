import ReactDOM from "react-dom/client";
import React, { useEffect, useState } from "react"
import { BrowserRouter as Router, Routes, Link, Route } from "react-router-dom";
import Login from "./pages/Login";
import Logout from "./pages/Logout";
import Register from "./pages/Register";
import LoginRedirect from "./pages/LoginRedirect";
import Layout from "./pages/Layout";
import Home from "./pages/Home";
import DedicatedOrder from "./pages/DedicatedOrder";
import CloudOrder from "./pages/CloudOrder";
import ColocationOrder from "./pages/ColocationOrder";
import ColocationAssetsOrder from "./pages/ColocationAssetsOrder";
import IPTransitOrder from "./pages/IPTransitOrder";
import Billing from "./pages/Billing";
import InvoiceDetails from "./pages/InvoiceDetails";  // Import the new component
import TransactionDetails from "./pages/TransactionDetails";  // Import the new component
import Support from "./pages/Support";
import Ticket from "./pages/Ticket";
import NewTicket from "./pages/NewTicket";
import Account from "./pages/Account";
import NoPage from "./pages/NoPage";
import Subnets from "./pages/Subnets";
import SubnetDetails from "./pages/SubnetDetails";
import Reseller from "./pages/Reseller.js";
import Faq from "./pages/Faq.js";
import ServerDetailsView from "./pages/ServerDetailsView.js"
import CloudServerDetailsView from './pages/CloudServerDetailsView';
import IPTransitDetailsView from './pages/IPTransitDetailsView.js';
import ColocationDetailsView from './pages/ColocationDetailsView.js';
import ColocationAssetsDetailsView from './pages/ColocationAssetsDetailsView.js';
import './index.css';
import LoginChecker from "./pages/LoginChecker.js";

// Create a new file called DismissibleAlert.js and import it here
// import DismissibleAlert from './DismissibleAlert';

function setToken(userToken) {
  sessionStorage.setItem('token', userToken)
  window.location.reload(false);
}

function getToken() {
  const userToken = sessionStorage.getItem('token');
  return userToken
}

const delay = ms => new Promise(
  resolve => setTimeout(resolve, ms)
);

async function parseLoginCheck() {
    const token = sessionStorage.getItem('token')
    const prepared_data = {'token' : token}
    return fetch('/New_client/api.php?f=login_check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(prepared_data)
  })
    .then(data => data.json())
}

async function loginCheck() {
    while(true){
    await delay(60000);
    const response = await parseLoginCheck()
    	if(response['error'] == '1'){
        	sessionStorage.clear()
  		window.location.reload(false);
    	}
   }
}

export default function App() {
  const token = getToken();

  if(!token) {
    return (
      <Router>
        <Routes>
          <Route path="/register" element={<Register setToken={setToken} />}>
	  </Route>
          <Route path="/*" element={<Login setToken={setToken}/>}>
          </Route>
        </Routes>
      </Router>
    );
  }

  loginCheck()

  return (
    <Router>
      <Routes>
        <Route path="/logout" element={<Logout />}>
	</Route>
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="/dedicatedorder" element={<DedicatedOrder />} />
          <Route path="/register" element={<LoginRedirect />} />
          <Route path="/login" element={<LoginRedirect />} />
          <Route path="/cloudorder" element={<CloudOrder />} />
          <Route path="/colocationorder" element={<ColocationOrder />} />
          <Route path="/colocationassetsorder" element={<ColocationAssetsOrder />} />
          <Route path="/iptransitorder" element={<IPTransitOrder />} />
          <Route path="/subnets" element={<Subnets />}/>
          <Route path="/subnet/:id" element={<SubnetDetails />} />
          <Route path="/reseller" element={<Reseller />}/>
          <Route path="/reseller/:section" element={<Reseller />} />
          <Route path="/faq" element={<Faq />}/>
          <Route path="billing/:section" element={<Billing />} />
          <Route path="billing/invoices/:id" element={<InvoiceDetails />} />  {/* New route for invoice details */}
          <Route path="billing/history/:id" element={<TransactionDetails />} />  {/* New route for transaction details */}
          <Route path="/support" element={<Support />}/>
          <Route path="/support/newcase" element={<NewTicket />}/>
          <Route path="/support/:id" element={<Ticket />}/>
          <Route path="/account" element={<Account />}/>
          <Route path="/account/:id" element={<Account />}/>
          <Route path="/*" element={<NoPage />} />
          <Route path="/services/:id" element={<ServerDetailsView />} />
          <Route path="/cloud-services/:id" element={<CloudServerDetailsView />} />
          <Route path="/ip-transit-services/:id" element={<IPTransitDetailsView />} />
          <Route path="/colocation-services/:id" element={<ColocationDetailsView />} />
          <Route path="/colocation-assets-services/:id" element={<ColocationAssetsDetailsView />} />
          <Route path="/logincheck" element={<LoginChecker />} />
        </Route>
      </Routes>
    </Router>
  );
};

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);