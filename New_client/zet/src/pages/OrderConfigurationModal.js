import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, FormGroup, Label, Input, Row, Col } from 'reactstrap';

const OrderConfigurationModal = ({ 
  isOpen, 
  toggle, 
  onContinue, 
  orderType, 
  subnetOptions = [], 
  selectedSubnet,
  serversNumber,
  onSubnetChange,
  onServersNumberChange
}) => {
  // Local state to track selections
  const [localSubnet, setLocalSubnet] = useState(selectedSubnet);
  const [localServersNumber, setLocalServersNumber] = useState(serversNumber || 1);
  
  // Update local state when props change
  useEffect(() => {
    setLocalSubnet(selectedSubnet);
    setLocalServersNumber(serversNumber || 1);
  }, [selectedSubnet, serversNumber, isOpen]);
  
  // Handle subnet change
  const handleSubnetChange = (e) => {
    const selectedId = parseInt(e.target.value);
    const newSubnet = subnetOptions.find(subnet => subnet.id === selectedId);
    setLocalSubnet(newSubnet);
  };
  
  // Handle server number change
  const handleServersNumberChange = (e) => {
    setLocalServersNumber(parseInt(e.target.value));
  };
  
  // Handle continue button click
  const handleContinue = () => {
    // Pass the selections back to the parent component
    if (onSubnetChange && localSubnet) {
      onSubnetChange(localSubnet);
    }
    
    if (onServersNumberChange) {
      onServersNumberChange(localServersNumber);
    }
    
    // Call the continue callback
    onContinue();
  };
  
  // Generate number of servers options
  const serverNumberOptions = [1, 2, 3, 4, 5, 10, 20, 50, 100];
  
  return (
    <Modal isOpen={isOpen} toggle={toggle} size="lg">
      <ModalHeader toggle={toggle}>
        {orderType === 'dedicated' ? 'Configure Dedicated Server Order' : 
         orderType === 'cloud' ? 'Configure Cloud Server Order' : 'Configure Order'}
      </ModalHeader>
      
      <ModalBody>
        <div className="p-3">
          <h5 className="mb-4">Additional Configuration Options</h5>
          
          <Row>
            {/* Show subnet selection for dedicated servers */}
            {orderType === 'dedicated' && subnetOptions.length > 0 && (
              <Col md={6} className="mb-4">
                <FormGroup>
                  <Label for="subnetSelect" className="fw-bold">
                    <i className="fa fa-network-wired text-primary me-2"></i>
                    IP Subnet
                  </Label>
                  <Input 
                    type="select" 
                    id="subnetSelect" 
                    className="form-select"
                    value={localSubnet?.id || ''}
                    onChange={handleSubnetChange}
                  >
                    {subnetOptions.map(subnet => (
                      <option key={subnet.id} value={subnet.id}>
                        {subnet.name} {subnet.price > 0 ? `(+€${subnet.price}/mo)` : '(Included)'}
                      </option>
                    ))}
                  </Input>
                  <small className="text-muted">
                    Choose the IP subnet range you need for your server(s).
                  </small>
                </FormGroup>
              </Col>
            )}
            
            {/* Show server number selection for all order types */}
            <Col md={orderType === 'dedicated' ? 6 : 12} className="mb-4">
              <FormGroup>
                <Label for="serversNumberSelect" className="fw-bold">
                  <i className="fa fa-server text-primary me-2"></i>
                  Number of Servers
                </Label>
                <Input 
                  type="select" 
                  id="serversNumberSelect" 
                  className="form-select"
                  value={localServersNumber}
                  onChange={handleServersNumberChange}
                >
                  {serverNumberOptions.map(num => (
                    <option key={num} value={num}>
                      {num} {num === 1 ? 'Server' : 'Servers'}
                    </option>
                  ))}
                </Input>
                <small className="text-muted">
                  Select the number of identical servers you want to provision.
                </small>
              </FormGroup>
            </Col>
          </Row>
          
          <div className="alert alert-info">
            <i className="fa fa-info-circle me-2"></i>
            {orderType === 'dedicated' 
              ? 'Multiple servers will be configured identically with the options you selected.' 
              : 'Your servers will be deployed with the configuration options you selected.'}
          </div>
        </div>
      </ModalBody>
      
      <ModalFooter>
        <Button color="secondary" onClick={toggle}>Back</Button>
        <Button color="primary" onClick={handleContinue}>
          Continue to Order Summary
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export default OrderConfigurationModal;