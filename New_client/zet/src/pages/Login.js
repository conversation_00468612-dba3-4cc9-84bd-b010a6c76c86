import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';

// Standalone component for animated background
const BackgroundAnimation = () => {
  const [elements, setElements] = useState([]);

  // Run immediately on mount with no delays
  useEffect(() => {
    const newElements = [];
    const count = 25; // More bubbles

    for (let i = 0; i < count; i++) {
      // Change bubble positioning to focus on the sides and bottom
      let posX, posY;

      // Create a different bubble distribution pattern
      // 60% chance to be on the sides, 40% chance to be on the bottom
      if (Math.random() < 0.6) {
        // Position on left or right side
        posX = Math.random() < 0.5 ? Math.random() * 20 : 80 + Math.random() * 20;
        posY = Math.random() * 100; // Any height
      } else {
        // Position on bottom
        posX = Math.random() * 100; // Any width
        posY = 70 + Math.random() * 30; // Bottom 30% of screen
      }

      newElements.push({
        id: i,
        size: Math.random() * 100 + 60, // Even bigger bubbles (60-160px)
        posX: posX,
        posY: posY,
        opacity: Math.random() * 0.15 + 0.05,
        duration: Math.random() * 8 + 6, // Faster animation
      });
    }

    setElements(newElements);
  }, []);

  return (
    <div className="background-animation">
      {elements.map((el) => (
        <div
          key={el.id}
          className="bg-bubble"
          style={{
            width: `${el.size}px`,
            height: `${el.size}px`,
            left: `${el.posX}%`,
            top: `${el.posY}%`,
            opacity: el.opacity,
            animationDuration: `${el.duration}s`,
            animationPlayState: 'running',
            animationDelay: '0s'
          }}
        />
      ))}
    </div>
  );
};

// Main Login Component
const Login = ({ setToken }) => {
  const [username, setUserName] = useState();
  const [password, setPassword] = useState();
  const [response_errors, setResponseErrors] = useState("All login trials are logged");
  const [response_class, setResponseClass] = useState("text-muted");
  const location = useLocation();
  const navigate = useNavigate();

  async function loginUser(credentials) {
    return fetch('/api.php?f=login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(credentials)
    })
    .then(data => data.json())
  }

  const handleSubmit = async e => {
    e.preventDefault();

    if(!username || !password){
      setResponseErrors("Please complete both email and password")
      setResponseClass("text-muted text-red")
      return
    }
    document.getElementById("login_button").innerHTML = "Logging";
    document.getElementById("login_button").disabled = true;
    const reply = await loginUser({
      username,
      password
    });

    if(reply['error'] == 0){
      // Store token in localStorage for persistence across tabs
      localStorage.setItem('token', reply['token']);
      setToken(reply['token']);

      // Check if there's a returnUrl in the location state
      const returnUrl = location.state?.returnUrl;
      if (returnUrl) {
        // Navigate to the return URL after successful login
        navigate(returnUrl);
      }
    }else if(reply['error'] == 1){
      setResponseErrors("Invalid email or password")
      setResponseClass("text-muted text-red")
      document.getElementById("login_button").innerHTML = "Login";
      document.getElementById("login_button").disabled = false;
    }
  }

  return (
    <>
      {/* Global styles for background animation */}
      <style dangerouslySetInnerHTML={{
        __html: `
        html, body {
          margin: 0;
          padding: 0;
          height: 100%;
          width: 100%;
          overflow-x: hidden;
        }

        /* Background animation container */
        .background-animation {
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          overflow: hidden;
          background: linear-gradient(135deg, #f5f7ff 0%, #ffffff 100%);
          z-index: -100;
        }

        /* Background elements */
        /* Bubble element */
        .bg-bubble {
          position: absolute;
          background: linear-gradient(135deg, rgba(224, 231, 255, 0.6), rgba(67, 83, 255, 0.4));
          border-radius: 50%;
          animation: floatBubble ease-in-out infinite;
          animation-play-state: running !important; /* Force animation to run immediately */
          box-shadow: 0 0 40px rgba(67, 83, 255, 0.2);
        }

        /* Animation keyframes - starting immediately */
        @keyframes floatBubble {
          0%, 100% {
            transform: translateY(0) translateX(0);
            opacity: 0.15;
            animation-timing-function: ease-in-out;
          }
          50% {
            transform: translateY(-40px) translateX(20px);
            opacity: 0.25;
            animation-timing-function: ease-in-out;
          }
        }
      `
      }} />

      {/* Render background animation */}
      <BackgroundAnimation />

      {/* Login content */}
      <div className="h-100vh app">
        <div className="page">
          <div className="page-single">
            <div className="p-5">
              <div className="row">
                <div className="col mx-auto">
                  <div className="row justify-content-center">
                    <div className="col-lg-9 col-xl-8 d-flex align-items-stretch">
                      <div className="card-group mb-0">
                        <div className="card p-4 page-content">
                          <div className="card-body page-single-content">
                            <div className="w-100">
                              <div>
                                <h1 className="mb-2">Login</h1>
                                <p className={response_class}>{response_errors}</p>
                              </div>
                              <div>
                                <div className="input-group mb-3">
                                  <span className="input-group-addon" style={{border:0}}>
                                    <svg className="svg-icon" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
                                      <path d="M0 0h24v24H0V0z" fill="none"/>
                                      <path d="M12 16c-2.69 0-5.77 1.28-6 2h12c-.2-.71-3.3-2-6-2z" opacity=".3"/>
                                      <circle cx="12" cy="8" opacity=".3" r="2"/>
                                      <path d="M12 14c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4zm-6 4c.22-.72 3.31-2 6-2 2.7 0 5.8 1.29 6 2H6zm6-6c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0-6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2z"/>
                                    </svg>
                                  </span>
                                  <input type="text" className="form-control" name="username" placeholder="Email" onChange={e => setUserName(e.target.value)}/>
                                </div>
                                <div className="input-group mb-4">
                                  <span className="input-group-addon" style={{border:0}}>
                                    <svg className="svg-icon" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
                                      <g fill="none">
                                        <path d="M0 0h24v24H0V0z"/>
                                        <path d="M0 0h24v24H0V0z" opacity=".87"/>
                                      </g>
                                      <path d="M6 20h12V10H6v10zm6-7c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2z" opacity=".3"/>
                                      <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6zm9 14H6V10h12v10zm-6-3c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z"/>
                                    </svg>
                                  </span>
                                  <input type="password" className="form-control" name="password" placeholder="Password" onChange={e => setPassword(e.target.value)}/>
                                </div>
                                <div className="row">
                                  <div className="col-12">
                                    <button type="button" className="btn btn-lg btn-primary btn-block" id="login_button" onClick={handleSubmit}>Login</button>
                                  </div>
                                  <div className="col-6 text-left">
                                    <a href="/forgon-password">Forgot password?</a>
                                  </div>
                                  <div className="col-6 text-right">
                                    <a href="/register">New here? Register</a>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="card text-white py-5 d-md-down-none page-content mt-0">
                          <div className="card-body text-center justify-content-center page-single-content">
                            <img src="/assets/images/brand/favicon.png" alt="img"/>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
};

Login.propTypes = {
  setToken: PropTypes.func.isRequired
};

export default Login;