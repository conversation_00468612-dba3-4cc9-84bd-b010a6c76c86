import React, { useState, useEffect, useCallback } from "react";
import { Outlet, Link, useParams, useNavigate, useLocation } from "react-router-dom";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, AreaChart, Area } from 'recharts';
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
} from 'mdb-react-ui-kit';

const ColocationDetailsView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [colocation, setColocation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [modalShow, setModalShow] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalBody, setModalBody] = useState('');
  const [modalFooter, setModalFooter] = useState('');
  
  // Sample bandwidth data for the graph
  const bandwidthData = [
    { time: '00:00', inbound: 2.3, outbound: 1.8 },
    { time: '02:00', inbound: 1.8, outbound: 1.2 },
    { time: '04:00', inbound: 1.2, outbound: 0.8 },
    { time: '06:00', inbound: 1.5, outbound: 0.9 },
    { time: '08:00', inbound: 3.5, outbound: 2.1 },
    { time: '10:00', inbound: 5.2, outbound: 3.8 },
    { time: '12:00', inbound: 4.8, outbound: 3.2 },
    { time: '14:00', inbound: 5.5, outbound: 4.1 },
    { time: '16:00', inbound: 6.2, outbound: 4.8 },
    { time: '18:00', inbound: 7.1, outbound: 5.2 },
    { time: '20:00', inbound: 4.5, outbound: 3.8 },
    { time: '22:00', inbound: 3.2, outbound: 2.5 },
  ];

  // Sample colocation data - in a real application, this would come from an API
  const sampleColocation = {
    id: id,
    name: "COLO-RACK1",
    status: "Active",
    rackSize: "Full Rack (42U)",
    power: "20A, 208V, 4.2kW",
    bandwidth: "10 Gbps Unmetered",
    ipAddresses: ["**********", "**********", "**********", "**********"],
    subnet: "**********/29",
    location: {
      flag: "de",
      name: "Frankfurt, Germany",
      datacenter: "Equinix FR1"
    },
    accessDetails: {
      accessCode: "A8403",
      remoteHands: "Available 24/7",
      accessHours: "24/7"
    },
    network: {
      primaryIp: "**********",
      subnet: "**********/29",
      gateway: "**********",
      bandwidth: {
        speed: "10 Gbps",
        monthly: "Unlimited",
        used: "12.3 TB",
        remaining: "Unlimited"
      }
    },
    billing: {
      price: "€499.00",
      billingCycle: "Monthly",
      nextRenewal: "April 15, 2025",
      autoRenew: true
    },
    uptime: {
      current: "43 days, 7 hours",
      percentage: "99.98%"
    }
  };

  useEffect(() => {
    // Simulate API call to fetch colocation details
    const fetchColocationDetails = () => {
      setLoading(true);
      // For demo purposes, use sample data
      setTimeout(() => {
        setColocation(sampleColocation);
        setLoading(false);
      }, 300);
    };

    fetchColocationDetails();
  }, [id]);

  // Modal content templates
  const shutdownPowerModal = {
    title: <>Shutdown Power</>,
    body: <>
      <div className="form-group">
        <div className="alert alert-warning mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          This action will shut down power to your rack. All equipment will power off immediately.
        </div>
        <div className="alert alert-danger mt-3 mb-0">
          <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
          <strong>WARNING:</strong> This action leads to immediate downtime of all your equipment.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-warning" onClick={() => setModalShow(false)}>Shutdown Power</button></>
  };

  const networkModal = {
    title: <>Network Configuration</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Select Bandwidth Plan:</label>
        <select 
          name="bandwidth" 
          className="form-control custom-select mb-3"
          onChange={(e) => {
            const alertMessage = document.getElementById('alert-message');
            if (e.target.value === '20') {
              alertMessage.textContent = "Sometimes the bandwidth upgrade will take a few days as the secondary port needs to be physically connected.";
            } else {
              alertMessage.textContent = "Changes are processed automatically. Your new bandwidth plan will take effect in up to 10 minutes. No downtime is expected.";
            }
          }}
        >
          <option value="1">1 Gbps Unmetered (+€0.00/mo)</option>
          <option value="2">2 Gbps Unmetered (+€80.00/mo)</option>
          <option value="5">5 Gbps Unmetered (+€150.00/mo)</option>
          <option value="10" selected>10 Gbps Unmetered (+€0.00/mo)</option>
          <option value="20">20 Gbps Unmetered (+€250.00/mo)</option>
        </select>
        
        <div id="bandwidth-alert">
          <div className="alert alert-info mb-0">
            <i className="fa fa-info-circle me-2"></i>
            <span id="alert-message">
              Changes are processed automatically. Your new bandwidth plan will take effect in up to 10 minutes. No downtime is expected.
            </span>
          </div>
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-success" onClick={() => setModalShow(false)}>Update Bandwidth</button></>
  };

  const createIpConfigModal = useCallback(() => {
    // If colocation isn't loaded yet, return the default modal
    if (!colocation) return {
      title: <>IP Configuration</>,

      footer: <><button className="btn btn-success" onClick={() => setModalShow(false)}>Add IP Resources</button></>
    };
    
    // Extract the current CIDR from colocation data
    const currentSubnet = colocation.network.subnet;
    const currentCIDRMatch = currentSubnet.match(/\/(\d+)/);
    const currentCIDR = currentCIDRMatch ? parseInt(currentCIDRMatch[1]) : 29; // Default to 29 if not found
    
    // Function to check if a subnet is an upgrade
    const isSubnetUpgrade = (selectedCIDR, currentCIDR) => {
      // A smaller CIDR number means a larger subnet
      // e.g., /28 (16 IPs) is larger than /29 (8 IPs)
      return selectedCIDR < currentCIDR;
    };
    
    return {
      title: <>IP Configuration</>,
      body: <>
        <div className="form-group">
          <label className="form-label mb-2">Select Upgrade Option:</label>
          <select name="ip_option" className="form-control custom-select mb-3">
            <option value="31" disabled={!isSubnetUpgrade(31, currentCIDR)}>Additional IPv4 Address (+€3.00/mo)</option>
            <option value="29" disabled={!isSubnetUpgrade(29, currentCIDR)}>
              /29 IPv4 Subnet - 5 IPs (+€15.00/mo)
              {!isSubnetUpgrade(29, currentCIDR) && " - Not an upgrade"}
            </option>
            <option value="28" disabled={!isSubnetUpgrade(28, currentCIDR)}>
              /28 IPv4 Subnet - 13 IPs (+€30.00/mo)
              {!isSubnetUpgrade(28, currentCIDR) && " - Not an upgrade"}
            </option>
            <option value="27" disabled={!isSubnetUpgrade(27, currentCIDR)}>
              /27 IPv4 Subnet - 29 IPs (+€60.00/mo)
              {!isSubnetUpgrade(27, currentCIDR) && " - Not an upgrade"}
            </option>
            <option value="26" disabled={!isSubnetUpgrade(26, currentCIDR)}>
              /26 IPv4 Subnet - 63 IPs (+€120.00/mo)
              {!isSubnetUpgrade(26, currentCIDR) && " - Not an upgrade"}
            </option>
          </select>
          
          <div className="alert alert-info mb-3">
            <i className="fa fa-info-circle me-2"></i>
            You can only select larger subnet sizes than your current one ({currentSubnet}).
            IP allocation is usually processed within 10 minutes. Configuration instructions will be provided in your client area.
          </div>
          
          <div className="alert alert-danger mb-0">
            <i className="fa fa-exclamation-triangle me-2" ></i>
            <strong>WARNING:</strong> This will change your assigned IP addresses. Make sure to update your configurations.
          </div>
        </div>
      </>,
      footer: <><button className="btn btn-success" onClick={() => setModalShow(false)}>Add IP Resources</button></>
    };
  }, [colocation]);

  const terminateModal = {
    title: <>Terminate Colocation Service</>,
    body: <>
      <div className="form-group">
        <div className="alert alert-danger mb-3">
          <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
          <strong>WARNING:</strong> Terminating the colocation service will end your contract. You will need to remove your equipment within 5 business days of termination.
        </div>
        <p>Please confirm that you want to terminate colocation service <strong>{colocation?.name}</strong>.</p>
        
        {/* Termination timing options */}
        <div className="mb-3">
          <label className="form-label">When would you like to terminate the service?</label>
          <div className="form-check mb-2">
            <input 
              type="radio" 
              className="form-check-input" 
              id="terminateEndOfBilling" 
              name="terminationTiming"
              value="end-of-billing" 
              style={{ 
                marginRight: '5px',
                marginLeft: '0',
                marginTop: '0',
                marginBottom: '0',
                padding: '0',
                position: 'relative',
                float: 'none'
              }}
              defaultChecked 
            />
            <label className="form-check-label" htmlFor="terminateEndOfBilling">
              Terminate at the end of billing period {colocation?.billing?.nextRenewal && `(${colocation.billing.nextRenewal})`}
            </label>
          </div>
          <div className="form-check">
            <input 
              type="radio" 
              className="form-check-input" 
              id="terminateImmediately" 
              name="terminationTiming"
              value="immediately"
              style={{ 
                marginRight: '5px',
                marginLeft: '0',
                marginTop: '0',
                marginBottom: '0',
                padding: '0',
                position: 'relative',
                float: 'none'
              }}
            />
            <label className="form-check-label" htmlFor="terminateImmediately">
              Terminate immediately (additional fees may apply)
            </label>
          </div>
        </div>
        
        {/* Reason for termination */}
        <div className="mb-3">
          <label htmlFor="terminationReason" className="form-label">Reason for termination <span className="text-danger">*</span></label>
          <textarea 
            className="form-control" 
            id="terminationReason" 
            rows="3" 
            placeholder="Please tell us why you're terminating this service..."
            required
          ></textarea>
          <div className="invalid-feedback" id="reasonFeedback" style={{display: 'none'}}>
            Please provide a reason for termination.
          </div>
        </div>
        
        <div className="form-check mb-3">
          <input 
            type="checkbox" 
            className="form-check-input" 
            id="confirmTermination" 
            style={{ 
              marginRight: '5px',
              marginLeft: '0',
              marginTop: '0',
              marginBottom: '0',
              padding: '0',
              position: 'relative',
              float: 'none'
            }} 
          />
          <label className="form-check-label" htmlFor="confirmTermination">
            I understand that I need to remove all my equipment within 5 business days after termination.
          </label>
        </div>
      </div>
    </>,
    footer: <>
      <button className="btn btn-danger" onClick={() => setModalShow(false)}>Cancel</button>
      <button 
        className="btn btn-secondary ms-2" 
        onClick={() => setModalShow(false)}
      >
        Terminate Service
      </button>
    </>
  };

  const remoteHandsModal = {
    title: <>Request Remote Hands</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Service Type:</label>
        <select name="service_type" className="form-control custom-select mb-3">
          <option value="reboot">Server Reboot</option>
          <option value="cable">Cable Connection</option>
          <option value="visual">Visual Inspection</option>
          <option value="hardware">Hardware Installation/Replacement</option>
          <option value="other">Other (Specify in Description)</option>
        </select>
        
        <label className="form-label mb-2">Priority:</label>
        <select name="priority" className="form-control custom-select mb-3">
          <option value="normal">Normal (Response within 4 hours)</option>
          <option value="urgent">Urgent (Response within 1 hour, +€50.00)</option>
          <option value="emergency">Emergency (Response within 15 minutes, +€150.00)</option>
        </select>
        
        <label className="form-label mb-2">Description:</label>
        <textarea 
          className="form-control mb-3" 
          name="description" 
          rows="5" 
          placeholder="Please provide detailed instructions for our technician..."
          required
        ></textarea>
        
        <div className="form-check mb-3">
          <input 
            type="checkbox" 
            className="form-check-input" 
            id="timeEstimation" 
            style={{ 
              marginRight: '5px',
              marginLeft: '0',
              marginTop: '0',
              marginBottom: '0',
              padding: '0',
              position: 'relative',
              float: 'none'
            }} 
          />
          <label className="form-check-label" htmlFor="timeEstimation">
            I understand that remote hands services are billed in 15-minute increments at €45.00/hour.
          </label>
        </div>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          Remote hands services are charged based on actual time spent. You will receive an estimate before work begins.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Submit Request</button></>
  };

  const accessRequestModal = {
    title: <>Request Datacenter Access</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Visitor Information:</label>
        <input 
          type="text" 
          className="form-control mb-3" 
          placeholder="Full Name" 
          required
        />
        <input 
          type="text" 
          className="form-control mb-3" 
          placeholder="ID or Passport Number" 
          required
        />
        
        <label className="form-label mb-2">Access Date and Time:</label>
        <div className="row mb-3">
          <div className="col">
            <input 
              type="date" 
              className="form-control" 
              required
            />
          </div>
          <div className="col">
            <input 
              type="time" 
              className="form-control" 
              required
            />
          </div>
        </div>
        
        <label className="form-label mb-2">Estimated Duration:</label>
        <select name="duration" className="form-control custom-select mb-3">
          <option value="1">1 hour</option>
          <option value="2">2 hours</option>
          <option value="4">4 hours</option>
          <option value="8">8 hours</option>
          <option value="24">24 hours</option>
        </select>
        
        <label className="form-label mb-2">Purpose of Visit:</label>
        <textarea 
          className="form-control mb-3" 
          rows="3" 
          placeholder="Describe the purpose of your visit..."
          required
        ></textarea>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          Access requests must be submitted at least 24 hours in advance. For emergency access, please contact support directly.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Submit Request</button></>
  };

  const renewModal = {
    title: <>Renew Colocation Service</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Payment Method:</label>
        <select name="payment_method" className="form-control custom-select mb-3">
          <option value="cc">Credit Card</option>
          <option value="cr">Cryptocurrency</option>
          <option value="bt">Bank Transfer</option>
        </select>
        
        <label className="form-label mb-2">Billing Period:</label>
        <select name="billing_period" className="form-control custom-select mb-3">
          <option value="1">1 Month (€499.00)</option>
          <option value="3">3 Months - 5% discount (€1,422.15)</option>
          <option value="6">6 Months - 10% discount (€2,695.60)</option>
          <option value="12">12 Months - 15% discount (€5,088.80)</option>
        </select>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          Longer billing periods come with automatic discounts.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-success" onClick={() => setModalShow(false)}>Renew Service</button></>
  };

  const powerUpgradeModal = {
    title: <>Power Upgrade</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Current Power:</label>
        <input 
          type="text" 
          className="form-control mb-3" 
          value={colocation?.power || "20A, 208V, 4.2kW"}
          readOnly
        />
        
        <label className="form-label mb-2">Select New Power Configuration:</label>
        <select name="power_upgrade" className="form-control custom-select mb-3">
          <option value="30a208v">30A, 208V, 6.2kW (+€150.00/mo)</option>
          <option value="20a208vr">20A, 208V Redundant (+€180.00/mo)</option>
          <option value="30a208vr">30A, 208V Redundant (+€280.00/mo)</option>
        </select>
        
        <div className="alert alert-warning mb-3">
          <i className="fa fa-info-circle me-2"style={{paddingRight: '0.5rem'}}></i>
          Upgrading power may require scheduled downtime for your equipment. Our team will contact you to schedule this.
        </div>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2"style={{paddingRight: '0.5rem'}}></i>
          Power upgrades typically take 1-2 business days to implement after approval.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Request Upgrade</button></>
  };

  const openModal = useCallback((title, body, footer) => {
    // Special case for the IP Configuration modal
    if (title && title.type === React.Fragment && title.props.children === "IP Configuration") {
      const ipModal = createIpConfigModal();
      setModalTitle(ipModal.title);
      setModalBody(ipModal.body);
      setModalFooter(ipModal.footer);
    } else {
      setModalTitle(title);
      setModalBody(body);
      setModalFooter(footer);
    }
    setModalShow(true);
  }, [createIpConfigModal]);

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: "300px" }}>
        <div className="spinner-border text-primary" role="status">
          
        </div>
      </div>
    );
  }

  if (!colocation) {
    return (
      <div className="alert alert-danger" role="alert">
        Colocation service not found. <Link to="/">Return to services</Link>
      </div>
    );
  }

  return (
    <>
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Services</Link></li>
            <li className="breadcrumb-item1">Colocation {colocation.name}</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-12">
          <div className="alert alert-success" role="alert">
            <i className="fa fa-check-circle-o me-2" aria-hidden="true"></i> <strong>Well done!</strong> Activate another colocation service to get <b>15% discount</b> on all colocation &nbsp; <Link to="/reseller"><button className="btn btn-default btn-sm">Check Discounts</button></Link>
          </div>

          <div className="card smaller">
            <div className="card-header">
              <div className="card-title d-flex align-items-center">
                <span className="me-2">Colocation {colocation.name}</span>
                {colocation.status === 'Active' && <span className="badge bg-success">Active</span>}
                {colocation.status === 'Offline' && <span className="badge bg-danger">Offline</span>}
                {colocation.status === 'Maintenance' && <span className="badge bg-warning">Maintenance</span>}
              </div>
              <div className="card-options">
                <button className="btn btn-primary btn-sm me-2" onClick={() => openModal(remoteHandsModal.title, remoteHandsModal.body, remoteHandsModal.footer)}>
                  <i className="fa fa-wrench me-1"></i> Remote Hands
                </button>
                <button className="btn btn-info btn-sm me-2" onClick={() => openModal(accessRequestModal.title, accessRequestModal.body, accessRequestModal.footer)}>
                  <i className="fa fa-key me-1"></i> Request Access
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="panel panel-primary">
                <div className="tab-menu-heading">
                  <div className="tabs-menu">
                    <ul className="nav panel-tabs">
                      <li>
                        <a 
                          className={activeTab === 'overview' ? 'active' : ''} 
                          onClick={() => setActiveTab('overview')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-screen-desktop me-1"></i> Overview
                        </a>
                      </li>
                      <li>
                        <a 
                          className={activeTab === 'network' ? 'active' : ''} 
                          onClick={() => setActiveTab('network')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-graph me-1"></i> Network
                        </a>
                      </li>
                      <li>
                        <a 
                          className={activeTab === 'settings' ? 'active' : ''} 
                          onClick={() => setActiveTab('settings')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-settings me-1"></i> Settings
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="panel-body tabs-menu-body">
                  <div className="tab-content">
                    {/* Overview Tab */}
                    {activeTab === 'overview' && (
                      <div className="tab-pane active">
                        <div className="row">
                          {/* Colocation Overview Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-server me-2 text-primary"></i>Colocation Details</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3" style={{paddingRight: '0.5rem'}}>Quick Actions</h5>
                                <div className="row">
                                  <div className="col-6 mb-3">
                                    <button className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(remoteHandsModal.title, remoteHandsModal.body, remoteHandsModal.footer)}>
                                      <i className="fa fa-wrench fa-2x d-block mb-2 text-primary"></i>
                                      Remote Hands
                                    </button>
                                  </div>
                                  <div className="col-6 mb-3">
                                    <button className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(accessRequestModal.title, accessRequestModal.body, accessRequestModal.footer)}>
                                      <i className="fa fa-key fa-2x d-block mb-2 text-info"></i>
                                      Request Access
                                    </button>
                                  </div>
                                </div>
                                
                                <h5 className="text-muted mb-3">Hardware</h5>
                                <ul className="list-group list-group-flush">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-hdd-o text-primary me-2"></i> Rack Size:</span>
                                    <span className="text-muted">{colocation.rackSize}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-bolt text-primary me-2"></i> Power:</span>
                                    <span className="text-muted">{colocation.power}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-tachometer text-primary me-2"></i> Bandwidth:</span>
                                    <span className="text-muted">{colocation.bandwidth}</span>
                                  </li>
                                </ul>
                                
                                <div className="mt-4 d-flex justify-content-end">
  <button 
    className="btn btn-primary btn-sm me-2"
    onClick={() => openModal(powerUpgradeModal.title, powerUpgradeModal.body, powerUpgradeModal.footer)}
  >
    <i className="fa fa-bolt me-1"></i> Upgrade Power
  </button>
</div>
</div>
</div>
</div>
                          
                          {/* Status and Location Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-info-circle me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Location & Access</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Service Status</h5>
                                <ul className="list-group list-group-flush mb-4">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                   <span><i className="fa fa-plug text-primary me-2"></i> Power Status:</span>
                                    <span className="badge bg-success">Online</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-plug text-primary me-2"></i>Network Status:</span>
                                    <span className="badge bg-success">Connected</span>
                                  </li>
                                </ul>
                                
                                <h5 className="text-muted mb-3">Location</h5>
                                <ul className="list-group list-group-flush mb-4">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0" >
                                    <span><i className="fa fa-building text-primary me-2"></i> Data Center:</span>
                                    <span className="text-muted">
                                      <i className={`flag flag-${colocation.location.flag} me-1`} style={{paddingRight: '1rem'}}></i>
                                      {colocation.location.name}
                                    </span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-map text-primary me-2"></i> Facility:</span>
                                    <span className="text-muted"><i className="fa fa-building text-primary me-2" style={{paddingRight: '0.5rem'}}></i>{colocation.location.datacenter}</span>
                                  </li>
                                </ul>
                                
                                <h5 className="text-muted mb-3">Access Information</h5>
                                <ul className="list-group list-group-flush">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-id-card text-primary me-2"></i> Access Code:</span>
                                    <span className="text-muted">{colocation.accessDetails.accessCode}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-clock-o text-primary me-2"></i> Access Hours:</span>
                                    <span className="text-muted">{colocation.accessDetails.accessHours}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-wrench text-primary me-2"></i> Remote Hands:</span>
                                    <span className="text-muted">{colocation.accessDetails.remoteHands}</span>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          </div>
                          
                          {/* Billing Card */}
                          <div className="col-xl-4 col-lg-12 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Billing Details</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Plan Details</h5>
                                <ul className="list-group list-group-flush mb-4">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-money text-primary me-2"></i> Monthly Price:</span>
                                    <span className="text-muted fw-bold">{colocation.billing.price}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-refresh text-primary me-2"></i> Billing Cycle:</span>
                                    <span className="text-muted">{colocation.billing.billingCycle}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-calendar text-primary me-2"></i> Next Renewal:</span>
                                    <span className="text-muted">{colocation.billing.nextRenewal}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-repeat text-primary me-2"></i> Auto-Renewal:</span>
                                    <span className={`badge ${colocation.billing.autoRenew ? 'bg-success' : 'bg-danger'}`}>
                                      {colocation.billing.autoRenew ? 'Enabled' : 'Disabled'}
                                    </span>
                                  </li>
                                </ul>
                                
                                <div className="mt-4 d-grid gap-2">
                                  <div className="mt-4 d-flex justify-content-between">
                                    <button className="btn btn-danger btn-sm me-2" onClick={() => openModal(terminateModal.title, terminateModal.body, terminateModal.footer)}>
                                      <i className="fa fa-times-circle me-1"></i> Terminate
                                    </button>
                                    
                                    <div>
                                      <Link to={`/billing/invoices`} className="btn btn-info btn-sm">
                                        <i className="fa fa-arrow-circle-left me-1"></i> View Invoices
                                      </Link>
                                      <button className="btn btn-success btn-sm ms-2" onClick={() => openModal(renewModal.title, renewModal.body, renewModal.footer)}>
                                        <i className="fa fa-refresh me-1"></i> Renew Service
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Network Tab */}
                    {activeTab === 'network' && (
                      <div className="tab-pane active">
                        <div className="row">
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-globe me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Network Configuration</h3>
                              </div>
                              <div className="card-body">
                                <ul className="list-group list-group-flush">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa "></i> Primary IP:</span>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2">{colocation.network.primaryIp}</span>
                                    </div>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i></i> Subnet:</span>
                                    <span>{colocation.network.subnet}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i></i> Gateway:</span>
                                    <span>{colocation.network.gateway}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i></i> Bandwidth:</span>
                                    <span>{colocation.network.bandwidth.speed}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i></i> Monthly Traffic:</span>
                                    <span>{colocation.network.bandwidth.monthly}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i></i> Traffic Used:</span>
                                    <span>{colocation.network.bandwidth.used}</span>
                                  </li>
                                </ul>
                                
                                <div className="mt-4 d-flex justify-content-end">
  <button className="btn btn-primary" onClick={() => openModal(networkModal.title, networkModal.body, networkModal.footer)}>
    <i className="fa fa-edit me-1"></i> Change Bandwidth Plan
  </button>
</div>
</div>
</div>
</div>
                          
                          <div className="col-xl-8 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-globe me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>IP Addresses</h3>
                                <div className="card-options">
                                  <button className="btn btn-sm btn-primary" onClick={() => openModal(<>IP Configuration</>, null, null)}>
                                    <i className="fa fa-plus me-1"></i> Upgrade Subnet
                                  </button>
                                </div>
                              </div>
                              <div className="card-body">
                                <div className="table-responsive">
                                  <table className="table table-hover">
                                    <thead className="bg-light">
                                      <tr>
                                        <th>IP Address</th>
                                        <th>Type</th>
                                        <th>Reverse DNS</th>
                                        <th>Actions</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {colocation.ipAddresses.map((ip, index) => (
                                        <tr key={index}>
                                          <td>
                                            <div className="d-flex align-items-center">
                                              <span className="me-2">{ip}</span>
                                            </div>
                                          </td>
                                          <td>{index === 0 ? 'Primary' : 'Secondary'}</td>
                                          <td>colo{index + 1}.example.com</td>
                                          <td>
                                            <div className="btn-group" role="group">
                                              <button 
                                                className="btn btn-sm btn-primary" 
                                                onClick={() => {
                                                  // Open IP Configuration Modal
                                                  setModalTitle('Set Reverse DNS');
                                                  setModalBody(
                                                    <div className="form-group">
                                                      <label>Reverse DNS (rDNS) for {ip}</label>
                                                      <input
                                                        type="text"
                                                        className="form-control"
                                                        defaultValue={`colo${index + 1}.example.com`}
                                                        placeholder="Enter reverse DNS hostname"
                                                        pattern="^[a-zA-Z0-9-]+\.[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$"
                                                        title="Please enter a valid reverse DNS hostname (e.g., colo1.example.com)"
                                                        required
                                                      />
                                                      <small className="form-text text-muted">
                                                        Specify the fully qualified domain name (FQDN) for this IP address.
                                                      </small>
                                                    </div>
                                                  );
                                                  setModalFooter(
                                                    <>
                                                      <button 
                                                        className="btn btn-secondary" 
                                                        onClick={() => setModalShow(false)}
                                                      >
                                                        Cancel
                                                      </button>
                                                      <button 
                                                        className="btn btn-primary" 
                                                        onClick={() => {
                                                          // Here you would typically call an API to update the rDNS
                                                          setModalShow(false);
                                                        }}
                                                      >
                                                        Save rDNS
                                                      </button>
                                                    </>
                                                  );
                                                  setModalShow(true);
                                                }}
                                              >
                                                <i className="fa fa-edit me-1"></i> Set rDNS
                                              </button>
                                            </div>
                                          </td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="col-12">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-globe me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Network Traffic</h3>
                                <div className="card-options">
                                  <select className="form-control form-control-sm" style={{ minHeight: "35px" }}>
                                    <option>Last 24 hours</option>
                                    <option>Last 7 days</option>
                                    <option>Last 30 days</option>
                                  </select>
                                </div>
                              </div>
                              <div className="card-body">
                                <div style={{ width: '100%', height: 300 }}>
                                  <ResponsiveContainer>
                                    <LineChart
                                      data={bandwidthData}
                                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                    >
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis dataKey="time" />
                                      <YAxis />
                                      <Tooltip formatter={(value) => [`${value} Gbps`]} />
                                      <Legend />
                                      <Line 
                                        type="monotone" 
                                        dataKey="inbound" 
                                        stroke="#3498db" 
                                        activeDot={{ r: 8 }} 
                                        name="Inbound" 
                                        strokeWidth={2}
                                      />
                                      <Line 
                                        type="monotone" 
                                        dataKey="outbound" 
                                        stroke="#e74c3c" 
                                        name="Outbound" 
                                        strokeWidth={2}
                                      />
                                    </LineChart>
                                  </ResponsiveContainer>
                                </div>
                                <div className="mt-4">
                                  <h5 className="text-muted mb-3">Network Statistics</h5>
                                  <div className="row">
                                    <div className="col-md-3 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Peak Traffic</h6>
                                        <h3 className="mb-0">7.1 Gbps</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-3 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Average Traffic</h6>
                                        <h3 className="mb-0">3.8 Gbps</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-3 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Monthly Usage</h6>
                                        <h3 className="mb-0">12.3 TB</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-3 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Packets/sec</h6>
                                        <h3 className="mb-0">245k</h3>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Settings Tab */}
                    {activeTab === 'settings' && (
                      <div className="tab-pane active">
                        <div className="row">
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-cog me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Colocation Settings</h3>
                              </div>
                              <div className="card-body">
                                <form>
                                  <div className="form-group mb-3">
                                    <label className="form-label">Service Label</label>
                                    <input 
                                      type="text" 
                                      className="form-control" 
                                      defaultValue={colocation.name}
                                      placeholder="Service Label"
                                    />
                                    <small className="form-text text-muted">
                                      This is for your reference only and does not affect the service.
                                    </small>
                                  </div>
                                  
                                  <div className="form-group mb-3">
                                    <label className="form-label">Maintenance Notifications</label>
                                    <select className="form-control">
                                      <option selected>All Notifications (Recommended)</option>
                                      <option>Critical Only</option>
                                      <option>None</option>
                                    </select>
                                    <small className="form-text text-muted">
                                      Select which notifications you want to receive for planned maintenance.
                                    </small>
                                  </div>
                                  
                                  <div className="form-group mb-3">
                                    <label className="form-label">Access Notification Recipients</label>
                                    <textarea 
                                      className="form-control" 
                                      rows="3" 
                                      placeholder="Enter email addresses separated by commas"
                                      defaultValue="<EMAIL>, <EMAIL>"
                                    ></textarea>
                                    <small className="form-text text-muted">
                                      These email addresses will receive notifications when someone requests datacenter access.
                                    </small>
                                  </div>
                                  
                                  <div className="d-flex justify-content-end">
  <button type="submit" className="btn btn-primary">Save Settings</button>
</div>
</form>
</div>
</div>
</div>
                          
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Billing Settings</h3>
                              </div>
                              <div className="card-body">
                                <form>
                                  <div className="form-group mb-3">
                                    <label className="form-label">Billing Cycle</label>
                                    <select className="form-control">
                                      <option selected>Monthly (€{parseFloat(colocation.billing.price.replace('€', '')).toFixed(2)}/mo)</option>
                                      <option>Quarterly (€{(parseFloat(colocation.billing.price.replace('€', '')) * 3 * 0.95).toFixed(2)}/3mo - 5% discount)</option>
                                      <option>Semi-Annual (€{(parseFloat(colocation.billing.price.replace('€', '')) * 6 * 0.9).toFixed(2)}/6mo - 10% discount)</option>
                                      <option>Annual (€{(parseFloat(colocation.billing.price.replace('€', '')) * 12 * 0.85).toFixed(2)}/yr - 15% discount)</option>
                                    </select>
                                  </div>
                                  
                                  <div className="form-group mb-3">
                                    <label className="form-label">Next Renewal</label>
                                    <input 
                                      type="text" 
                                      className="form-control" 
                                      value={colocation.billing.nextRenewal} 
                                      readOnly
                                    />
                                  </div>
                                  
                                  <div className="form-group form-check mb-3">
                                    <input 
                                      type="checkbox" 
                                      className="form-check-input" 
                                      id="autoRenewCheck" 
                                      checked={colocation.billing.autoRenew}
                                      style={{ 
                                        marginRight: '5px',
                                        marginLeft: '0',
                                        marginTop: '0',
                                        marginBottom: '0',
                                        padding: '0',
                                        position: 'relative',
                                        float: 'none'
                                      }}
                                    />
                                    <label className="form-check-label" htmlFor="autoRenewCheck">
                                      Enable automatic renewal
                                    </label>
                                    <small className="form-text text-muted d-block">
                                      When disabled, your service will be suspended after the billing period ends unless manually renewed.
                                    </small>
                                  </div>
                                  
                                  <div className="form-group form-check mb-3">
                                    <input 
                                      type="checkbox" 
                                      className="form-check-input" 
                                      id="useAccountCredit" 
                                      style={{ 
                                        marginRight: '5px',
                                        marginLeft: '0',
                                        marginTop: '0',
                                        marginBottom: '0',
                                        padding: '0',
                                        position: 'relative',
                                        float: 'none'
                                      }} 
                                      checked  
                                    />
                                    <label className="form-check-label" htmlFor="useAccountCredit">
                                      Use account credit for renewals when available
                                    </label>
                                  </div>
                                  
                                  <div className="d-flex justify-content-end">
  <button type="submit" className="btn btn-primary">Save Billing Settings</button>
</div>
</form>

</div>
</div>   </div>

                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Modal for various actions */}
      <MDBModal show={modalShow} setShow={setModalShow} tabIndex='-1'>
        <MDBModalDialog>
          <MDBModalContent>
            <MDBModalHeader>
              <MDBModalTitle>{modalTitle}</MDBModalTitle>
              <button type="button" className="close" onClick={() => setModalShow(false)}>
                <span aria-hidden="true">&times;</span>
              </button>
            </MDBModalHeader>
            <MDBModalBody>
              {modalBody}
            </MDBModalBody>
            <MDBModalFooter>
              {modalFooter}
            </MDBModalFooter>
          </MDBModalContent>
        </MDBModalDialog>
      </MDBModal>
    </>
  );
};

export default ColocationDetailsView;