import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Card, CardBody, Row, Col, Container, <PERSON><PERSON>, Mo<PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON>, <PERSON><PERSON> } from 'reactstrap';
import PaymentModal from './PaymentModal.js';
import DiscountAlert from "../components/DiscountAlert";
 // Import the PaymentModal component

const IPTransitOrder = () => {
  // State for configurations
  const [selectedPortType, setSelectedPortType] = useState(null);
  const [selectedPortSize, setSelectedPortSize] = useState(null);
  const [selectedBandwidth, setSelectedBandwidth] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedSubnet, setSelectedSubnet] = useState(null);
  const [selectedContract, setSelectedContract] = useState(null);
  const [totalPrice, setTotalPrice] = useState(0);
  const [delivery, setDelivery] = useState('INSTANT');
  const [termsAccepted, setTermsAccepted] = useState(false);
  // State for API data
  const [configs, setConfigs] = useState([]);
  
  // State for modals
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  
  // Temporary state for modal selections
  const [tempSubnet, setTempSubnet] = useState(null);
  const [tempContract, setTempContract] = useState(null);

  const navigate = useNavigate(); // Add navigate hook for redirection

  // Fetch configuration data
  const fetchConfigData = () => {
    fetch("/New_client/api.php?f=iptransitorder")
      .then(response => response.json())
      .then(data => {
        setConfigs(data);
        
        // Set initial selections from the data
        if (data[0]) {
          const portType = data[0].porttype.find(item => item.checked);
          const portSize = data[0].portsize.find(item => item.checked);
          const bandwidth = data[0].bandwidth.find(item => item.checked);
          const location = data[0].location.find(item => item.checked);
          const subnet = data[0].subnet.find(item => item.checked);
          const contract = data[0].contract.find(item => item.checked);
          
          setSelectedPortType(portType);
          setSelectedPortSize(portSize);
          setSelectedBandwidth(bandwidth);
          setSelectedLocation(location);
          setSelectedSubnet(subnet);
          setSelectedContract(contract);
          
          setTotalPrice(parseFloat(data[0].price));
          setDelivery(data[0].delivery);
        }
      })
      .catch(error => console.error('Error fetching data:', error));
  };

  // Initial data fetch
  useEffect(() => {
    fetchConfigData();
  }, []);

  // Function to refresh data when selections change
  const refreshConfigData = (name, value) => {
    let porttype_id = selectedPortType ? selectedPortType.id : "1";
    let portsize_id = selectedPortSize ? selectedPortSize.id : "1";
    let bandwidth_id = selectedBandwidth ? selectedBandwidth.id : "1";
    let location_id = selectedLocation ? selectedLocation.id : "1";
    let subnet_id = selectedSubnet ? selectedSubnet.id : "1";
    let contract_id = selectedContract ? selectedContract.id : "1";
    
    // Update the respective ID based on the selection
    switch(name) {
      case "porttype_id":
        porttype_id = value;
        // Reset dependent fields
        portsize_id = "1";
        location_id = "1";
        break;
      case "portsize_id":
        portsize_id = value;
        // Reset dependent fields
        location_id = "1";
        break;
      case "bandwidth_id":
        bandwidth_id = value;
        // Reset dependent fields
        location_id = "1";
        break;
      case "location_id":
        location_id = value;
        break;
      case "subnet_id":
        subnet_id = value;
        break;
      case "contract_id":
        contract_id = value;
        break;
    }

    fetch(`/New_client/api.php?f=iptransitorder&porttype_id=${porttype_id}&portsize_id=${portsize_id}&bandwidth_id=${bandwidth_id}&location_id=${location_id}&subnet_id=${subnet_id}&contract_id=${contract_id}`)
      .then(response => response.json())
      .then(data => {
        setConfigs(data);
        
        // Update selected items based on new data
        if (data[0]) {
          // Only update the changed selection and related fields
          switch(name) {
            case "porttype_id":
              setSelectedPortType(data[0].porttype.find(item => item.id === value));
              setSelectedPortSize(data[0].portsize.find(item => item.checked));
              setSelectedLocation(data[0].location.find(item => item.checked));
              break;
            case "portsize_id":
              setSelectedPortSize(data[0].portsize.find(item => item.id === value));
              setSelectedLocation(data[0].location.find(item => item.checked));
              break;
            case "bandwidth_id":
              setSelectedBandwidth(data[0].bandwidth.find(item => item.id === value));
              setSelectedLocation(data[0].location.find(item => item.checked));
              break;
            case "location_id":
              setSelectedLocation(data[0].location.find(item => item.id === value));
              break;
            case "subnet_id":
              setSelectedSubnet(data[0].subnet.find(item => item.id === value));
              break;
            case "contract_id":
              setSelectedContract(data[0].contract.find(item => item.id === value));
              break;
          }
          
          setTotalPrice(parseFloat(data[0].price));
          setDelivery(data[0].delivery);
        }
      })
      .catch(error => console.error('Error refreshing data:', error));
  };

  // Toggle confirmation modal
  const toggleConfirmModal = () => {
    if (showConfirmModal) {
      // When closing the modal, reset temporary values
      setTempSubnet(null);
      setTempContract(null);
    } else {
      // When opening the modal, initialize temp values with current selections
      setTempSubnet(selectedSubnet);
      setTempContract(selectedContract);
    }
    setShowConfirmModal(!showConfirmModal);
  };

  // Toggle payment modal
  const togglePaymentModal = () => {
    setShowPaymentModal(!showPaymentModal);
  };

  // Handle order button click
  const handleOrderClick = () => {
    // Open the confirmation modal directly
    toggleConfirmModal();
  };
  
  // Handle temporary subnet change (only within modal)
  const handleTempSubnetChange = (subnet) => {
    setTempSubnet(subnet);
  };
  
  // Handle temporary contract change (only within modal)
  const handleTempContractChange = (contract) => {
    setTempContract(contract);
  };

  // Handle order submission
  const handleOrder = () => {
    if (!termsAccepted) {
      return; // Exit if terms not accepted
    }
    
    // Apply the temporary selections to the actual state
    if (tempSubnet) setSelectedSubnet(tempSubnet);
    if (tempContract) setSelectedContract(tempContract);
    
    // Close the confirmation modal
    toggleConfirmModal();
    
    // Open the payment modal
    togglePaymentModal();
  };


  // Handle payment completion
  const handlePaymentComplete = () => {
    // Close the payment modal
    togglePaymentModal();
    
    // Navigate to dashboard or another page
    navigate('/dashboard');
  };

  return (
    <Container fluid className="p-0">
      {/* Page Header */}
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1">New IP Transit</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>

      <DiscountAlert />

      <Card className="shadow-sm mb-4">
        <CardBody>
          <h2 className="card-title text-primary mb-4">Configure Your IP Transit</h2>
          
          <Row className="g-4 mb-4">
            {/* Port Type Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-plug text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Port Type
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {configs[0]?.porttype.map(item => (
                      <div key={item.id} className="position-relative">
                        <input
                          type="radio"
                          id={`porttype_${item.id}`}
                          name="portType"
                          className="better-radio"
                          checked={selectedPortType && selectedPortType.id === item.id}
                          onChange={() => refreshConfigData("porttype_id", item.id)}
                        />
                        <label 
                          htmlFor={`porttype_${item.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedPortType && selectedPortType.id === item.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{item.size}</div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Port Size Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-tachometer text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Port Size
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {configs[0]?.portsize.map(item => (
                      <div key={item.id} className="position-relative">
                        <input
                          type="radio"
                          id={`portsize_${item.id}`}
                          name="portSize"
                          className="better-radio"
                          checked={selectedPortSize && selectedPortSize.id === item.id}
                          onChange={() => refreshConfigData("portsize_id", item.id)}
                        />
                        <label 
                          htmlFor={`portsize_${item.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedPortSize && selectedPortSize.id === item.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{item.name}</div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Bandwidth Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-exchange text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Bandwidth
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {configs[0]?.bandwidth.map(item => (
                      <div key={item.id} className="position-relative">
                        <input
                          type="radio"
                          id={`bandwidth_${item.id}`}
                          name="bandwidth"
                          className="better-radio"
                          checked={selectedBandwidth && selectedBandwidth.id === item.id}
                          onChange={() => refreshConfigData("bandwidth_id", item.id)}
                        />
                        <label 
                          htmlFor={`bandwidth_${item.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedBandwidth && selectedBandwidth.id === item.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{item.name}</div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Location Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-map-marker text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Location
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {configs[0]?.location.map(item => (
                      <div key={item.id} className="position-relative">
                        <input
                          type="radio"
                          id={`location_${item.id}`}
                          name="location"
                          className="better-radio"
                          checked={selectedLocation && selectedLocation.id === item.id}
                          onChange={() => refreshConfigData("location_id", item.id)}
                        />
                        <label 
                          htmlFor={`location_${item.id}`} 
                          className={`list-group-item d-flex align-items-center p-3 ${selectedLocation && selectedLocation.id === item.id ? 'selected' : ''}`}
                        >
                          <i className={`flag flag-${item.flag}`} style={{ marginRight: '10px' }}></i>
                          <div>
                            <div className="fw-bold">{item.name}</div>
                            <div className="text-muted small">
                              <span style={{ color: item.stockcolor }}>{item.stock}</span>
                            </div>
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* IPv4 Subnet Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-sitemap text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    IPv4 Subnet
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {configs[0]?.subnet.map(item => (
                      <div key={item.id} className="position-relative">
                        <input
                          type="radio"
                          id={`subnet_${item.id}`}
                          name="subnet"
                          className="better-radio"
                          checked={selectedSubnet && selectedSubnet.id === item.id}
                          onChange={() => refreshConfigData("subnet_id", item.id)}
                        />
                        <label 
                          htmlFor={`subnet_${item.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedSubnet && selectedSubnet.id === item.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{item.name}</div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Contract Term Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-file-text text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Contract Term
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {configs[0]?.contract.map(item => (
                      <div key={item.id} className="position-relative">
                        <input
                          type="radio"
                          id={`contract_${item.id}`}
                          name="contract"
                          className="better-radio"
                          checked={selectedContract && selectedContract.id === item.id}
                          onChange={() => refreshConfigData("contract_id", item.id)}
                        />
                        <label 
                          htmlFor={`contract_${item.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedContract && selectedContract.id === item.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{item.name}</div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>
          </Row>

          {/* Order Summary and Action Buttons */}
          <Card className="border-0 bg-light shadow-sm p-4">
            <Row className="align-items-center">
              <Col xs={12} md={6}>
                <h3 className="mb-0">Total Price: <span className="text-success">€{totalPrice}/mo</span></h3>
                <p className="text-muted mb-0">Delivery: {delivery}</p>
              </Col>
              <Col xs={12} md={6} className="text-md-end mt-3 mt-md-0 d-flex justify-content-end">
                <Button className="btn btn-success" size="lg" onClick={handleOrderClick}>
                  <i className="fa fa-check-circle me-2" style={{paddingRight: '0.5rem'}}></i>
                  Order Now
                </Button>
              </Col>
            </Row>
          </Card>
        </CardBody>
      </Card>

      {/* Order Confirmation Modal */}
      <Modal 
        isOpen={showConfirmModal} 
        toggle={toggleConfirmModal}
        className="order-summary-modal"
        backdropClassName="fixed-backdrop"
        backdrop={true}
        zIndex={9999}  // Higher z-index for the modal
      >
        <ModalHeader toggle={null} close={false}>Order Summary</ModalHeader>
        <ModalBody>
          {selectedPortType && selectedPortSize && selectedBandwidth && selectedLocation && selectedSubnet && selectedContract && (
            <div className="table-responsive">
              <table className="table card-table table-vcenter text-nowrap mb-0">
                <tbody>
                  <tr>
                    <td><b>Port Type</b></td>
                    <td>{selectedPortType.size}</td>
                  </tr>
                  <tr>
                    <td><b>Port Size</b></td>
                    <td>{selectedPortSize.name}</td>
                  </tr>
                  <tr>
                    <td><b>Bandwidth</b></td>
                    <td>{selectedBandwidth.name}</td>
                  </tr>
                  <tr>
                    <td><b>Location</b></td>
                    <td><i className={`flag flag-${selectedLocation.flag}`}></i> {selectedLocation.name}</td>
                  </tr>
                  <tr>
                    <td><b>IP Subnet</b></td>
                    <td>
                      <select 
                        className="form-control" 
                        value={tempSubnet ? tempSubnet.id : selectedSubnet.id}
                        onChange={(e) => {
                          const subnet = configs[0].subnet.find(s => s.id === e.target.value);
                          handleTempSubnetChange(subnet);
                        }}
                      >
                        {configs[0]?.subnet.map(subnet => (
                          <option key={subnet.id} value={subnet.id}>
                            {subnet.name}
                          </option>
                        ))}
                      </select>
                    </td>
                  </tr>
                  <tr>
                    <td><b>Contract Term</b></td>
                    <td>
                      <select 
                        className="form-control" 
                        value={tempContract ? tempContract.id : selectedContract.id}
                        onChange={(e) => {
                          const contract = configs[0].contract.find(c => c.id === e.target.value);
                          handleTempContractChange(contract);
                        }}
                      >
                        {configs[0]?.contract.map(contract => (
                          <option key={contract.id} value={contract.id}>
                            {contract.name}
                          </option>
                        ))}
                      </select>
                    </td>
                  </tr>
                  <tr>
                    <td><b>Delivery</b></td>
                    <td>{delivery}</td>
                  </tr>
                  <tr>
                    <td><b>Price</b></td>
                    <td>€{totalPrice}/mo</td>
                  </tr>
                </tbody>
              </table>
            </div>
          )}
        </ModalBody>
        <ModalFooter className="d-flex flex-column p-3">
    {/* Top row with terms of service */}
    <div className="w-100 mb-3">
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <input
          type="checkbox"
          id="agreeTerms"
          checked={termsAccepted}
          onChange={() => setTermsAccepted(!termsAccepted)}
          style={{ 
            marginRight: '5px',
            marginLeft: '0',
            marginTop: '0',
            marginBottom: '0',
            padding: '0',
            position: 'relative',
            float: 'none'
          }}
          required
        />
        I accept the <a href="https://zetservers.com/tos.php" target="_blank" style={{ marginLeft: '3px', color: '#1e88e5' }}>Terms of Service</a>
      </div>
    </div>
    
    {/* Bottom row with buttons */}
    <div className="d-flex justify-content-between w-100">
      <Button color="secondary" onClick={toggleConfirmModal}>Cancel</Button>
      <Button color="success" onClick={handleOrder} disabled={!termsAccepted}>Confirm Order</Button>
    </div>
  </ModalFooter>
      </Modal>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        toggle={togglePaymentModal}
        totalAmount={totalPrice}
        onComplete={handlePaymentComplete}
      />

    </Container>
  );
};

export default IPTransitOrder;