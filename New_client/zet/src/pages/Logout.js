import React, { useState } from 'react';
import { Outlet, Link, useParams, useLocation, useNavigate } from "react-router-dom";
import PropTypes from 'prop-types';

const Logout = () => {
	// Clear both localStorage and sessionStorage to ensure complete logout
	localStorage.removeItem('token');
	sessionStorage.removeItem('token');
	sessionStorage.clear();
	window.location = "/";
}

export default Logout;