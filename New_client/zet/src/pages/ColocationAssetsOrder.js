import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Card, CardBody, Row, Col, Container, Al<PERSON>, Modal, ModalHeader, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON> } from 'reactstrap';
import PaymentModal from './PaymentModal.js'; // Import the PaymentModal component

const ColocationAssetsOrder = () => {
  // State for configurations
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedAsset, setSelectedAsset] = useState(null);
  const [selectedUnits, setSelectedUnits] = useState(null);
  const [selectedColo, setSelectedColo] = useState(null);
  const [selectedRenting, setSelectedRenting] = useState(null);
  const [selectedContract, setSelectedContract] = useState(null);
  const [totalPrice, setTotalPrice] = useState(0);
  const [delivery, setDelivery] = useState('INSTANT');
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [basePrice, setBasePrice] = useState(0);
  const [selectedAssets, setSelectedAssets] = useState(1);
  // State for API data
  const [configs, setConfigs] = useState([]);
  
  // State for modals
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  
  // Temporary state for modal selections
  const [tempUnits, setTempUnits] = useState(null);
  const [tempRenting, setTempRenting] = useState(null);
  const [tempContract, setTempContract] = useState(null);

  const navigate = useNavigate(); // Add navigate hook for redirection

  // Fetch configuration data
  const fetchConfigData = () => {
    fetch("/New_client/api.php?f=colocationassetorder")
      .then(response => response.json())
      .then(data => {
        setConfigs(data);
        
        // Set initial selections from the data
        if (data[0]) {
          const category = data[0].cat.find(item => item.checked);
          const asset = data[0].asset.find(item => item.checked);
          const units = data[0].units.find(item => item.checked);
          const colo = data[0].colo.find(item => item.checked);
          const renting = data[0].renting.find(item => item.checked);
          const contract = data[0].contract.find(item => item.checked);
          
          setSelectedCategory(category);
          setSelectedAsset(asset);
          setSelectedUnits(units);
          setSelectedColo(colo);
          setSelectedRenting(renting);
          setSelectedContract(contract);
          
          const price = parseFloat(data[0].price);
          setBasePrice(price);
          setTotalPrice(price);
          setDelivery(data[0].delivery);
        }
      })
      .catch(error => console.error('Error fetching data:', error));
  };

  // Initial data fetch
  useEffect(() => {
    fetchConfigData();
  }, []);
  useEffect(() => {
    if (basePrice > 0) {
      setTotalPrice(basePrice * selectedAssets);
    }
  }, [basePrice, selectedAssets]);
  // Function to refresh data when selections change
  const refreshConfigData = (name, value) => {
    let cat_id = selectedCategory ? selectedCategory.id : "1";
    let asset_id = selectedAsset ? selectedAsset.id : "1";
    let units_id = selectedUnits ? selectedUnits.id : "1";
    let colo_id = selectedColo ? selectedColo.id : "1";
    let renting_id = selectedRenting ? selectedRenting.id : "1";
    let contract_id = selectedContract ? selectedContract.id : "1";
    
    // Update the respective ID based on the selection
    switch(name) {
      case "cat_id":
        cat_id = value;
        // Reset dependent fields
        asset_id = "1";
        colo_id = "1";
        units_id = "1";
        break;
      case "asset_id":
        asset_id = value;
        // Reset dependent fields
        colo_id = "1";
        units_id = "1";
        break;
      case "units_id":
        units_id = value;
        break;
      case "colo_id":
        colo_id = value;
        units_id = "1";
        break;
      case "renting_id":
        renting_id = value;
        break;
      case "contract_id":
        contract_id = value;
        break;
    }

    fetch(`/New_client/api.php?f=colocationassetorder&cat_id=${cat_id}&asset_id=${asset_id}&colo_id=${colo_id}&units_id=${units_id}&renting_id=${renting_id}&contract_id=${contract_id}`)
      .then(response => response.json())
      .then(data => {
        setConfigs(data);
        
        // Update selected items based on new data
        if (data[0]) {
          // Only update the changed selection and related fields
          switch(name) {
            case "cat_id":
              setSelectedCategory(data[0].cat.find(item => item.id === value));
              setSelectedAsset(data[0].asset.find(item => item.checked));
              setSelectedColo(data[0].colo.find(item => item.checked));
              setSelectedUnits(data[0].units.find(item => item.checked));
              break;
            case "asset_id":
              setSelectedAsset(data[0].asset.find(item => item.id === value));
              setSelectedColo(data[0].colo.find(item => item.checked));
              setSelectedUnits(data[0].units.find(item => item.checked));
              break;
            case "units_id":
              setSelectedUnits(data[0].units.find(item => item.id === value));
              break;
            case "colo_id":
              setSelectedColo(data[0].colo.find(item => item.id === value));
              setSelectedUnits(data[0].units.find(item => item.checked));
              break;
            case "renting_id":
              setSelectedRenting(data[0].renting.find(item => item.id === value));
              break;
            case "contract_id":
              setSelectedContract(data[0].contract.find(item => item.id === value));
              break;
          }
          
          const price = parseFloat(data[0].price);
          setBasePrice(price);
          setTotalPrice(price * selectedAssets);
          
          setDelivery(data[0].delivery);
        }
      })
      .catch(error => console.error('Error refreshing data:', error));
  };

  // Toggle confirmation modal
  const toggleConfirmModal = () => {
    if (showConfirmModal) {
      // When closing the modal, reset temporary values
      setTempUnits(null);
      setTempRenting(null);
      setTempContract(null);
    } else {
      // When opening the modal, initialize temp values with current selections
      setTempUnits(selectedUnits);
      setTempRenting(selectedRenting);
      setTempContract(selectedContract);
    }
    setShowConfirmModal(!showConfirmModal);
  };

  // Toggle payment modal
  const togglePaymentModal = () => {
    setShowPaymentModal(!showPaymentModal);
  };

  // Handle order button click
  const handleOrderClick = () => {
    // Open the confirmation modal directly
    toggleConfirmModal();
  };
  
  // Handle temporary units change (only within modal)
  const handleTempUnitsChange = (units) => {
    setTempUnits(units);
  };
  
  // Handle temporary renting change (only within modal)
  const handleTempRentingChange = (renting) => {
    setTempRenting(renting);
  };
  
  // Handle temporary contract change (only within modal)
  const handleTempContractChange = (contract) => {
    setTempContract(contract);
  };

  // Handle order submission
  const handleOrder = () => {
    if (!termsAccepted) {
      return; // Exit if terms not accepted
    }
    // Apply the temporary selections to the actual state
    if (tempUnits) setSelectedUnits(tempUnits);
    if (tempRenting) setSelectedRenting(tempRenting);
    if (tempContract) setSelectedContract(tempContract);
    
    // Update price based on number of assets
    setTotalPrice(basePrice * selectedAssets);
    
    // Close the confirmation modal
    toggleConfirmModal();
    
    // Open the payment modal
    togglePaymentModal();
  };

  // Handle payment completion
  const handlePaymentComplete = () => {
    // Close the payment modal
    togglePaymentModal();
    
    // Navigate to dashboard or another page
    navigate('/dashboard');
  };

  return (
    <Container fluid className="p-0">
      {/* Page Header */}
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1">New Colocation Asset</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>

      <Alert color="success" className="mb-4">
        <i className="fa fa-check-circle-o me-2" aria-hidden="true"></i> <strong>Special offer!</strong> Get <b>20% discount</b> on all colocation assets when ordering with a 12-month contract &nbsp; <Link to="/reseller"><button className="btn btn-default btn-sm">Check Discounts</button></Link>
      </Alert>

      <Card className="shadow-sm mb-4">
        <CardBody>
          <h2 className="card-title text-primary mb-4">Configure Your Colocation Asset</h2>
          
          <Row className="g-4 mb-4">
            {/* Category Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-list text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Category
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {configs[0]?.cat.map(item => (
                      <div key={item.id} className="position-relative">
                        <input
                          type="radio"
                          id={`cat_${item.id}`}
                          name="category"
                          className="better-radio"
                          checked={selectedCategory && selectedCategory.id === item.id}
                          onChange={() => refreshConfigData("cat_id", item.id)}
                        />
                        <label 
                          htmlFor={`cat_${item.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedCategory && selectedCategory.id === item.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{item.name}</div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Asset Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-hdd-o text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Asset
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {configs[0]?.asset.map(item => (
                      <div key={item.id} className="position-relative">
                        <input
                          type="radio"
                          id={`asset_${item.id}`}
                          name="asset"
                          className="better-radio"
                          checked={selectedAsset && selectedAsset.id === item.id}
                          onChange={() => refreshConfigData("asset_id", item.id)}
                        />
                        <label 
                          htmlFor={`asset_${item.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedAsset && selectedAsset.id === item.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{item.name}</div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Colocation Space Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-building text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Colocation Space
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {configs[0]?.colo.map(item => (
                      <div key={item.id} className="position-relative">
                        <input
                          type="radio"
                          id={`colo_${item.id}`}
                          name="colo"
                          className="better-radio"
                          checked={selectedColo && selectedColo.id === item.id}
                          onChange={() => refreshConfigData("colo_id", item.id)}
                        />
                        <label 
                          htmlFor={`colo_${item.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedColo && selectedColo.id === item.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{item.name}</div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Rack Units Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-server text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Rack Unit(s)
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {configs[0]?.units.map(item => (
                      <div key={item.id} className="position-relative">
                        <input
                          type="radio"
                          id={`units_${item.id}`}
                          name="units"
                          className="better-radio"
                          checked={selectedUnits && selectedUnits.id === item.id}
                          onChange={() => refreshConfigData("units_id", item.id)}
                        />
                        <label 
                          htmlFor={`units_${item.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedUnits && selectedUnits.id === item.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{item.name}</div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Renting Type Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-credit-card text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Renting Type
                  </h5>
                </div>
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {configs[0]?.renting.map(item => (
                      <div key={item.id} className="position-relative">
                        <input
                          type="radio"
                          id={`renting_${item.id}`}
                          name="renting"
                          className="better-radio"
                          checked={selectedRenting && selectedRenting.id === item.id}
                          onChange={() => refreshConfigData("renting_id", item.id)}
                        />
                        <label 
                          htmlFor={`renting_${item.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedRenting && selectedRenting.id === item.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{item.name}</div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Contract Term Selection */}
            <Col md={12} lg={2}>
              <Card className="h-100 border">
                <div className="card-header bg-light">
                  <h5 className="mb-0">
                    <i className="fa fa-file-text text-primary me-2" style={{paddingRight: '0.5rem'}}></i>
                    Contract Term
                  </h5>
                </div>
                
                <CardBody className="p-0">
                  <div className="better-radio-group">
                    {configs[0]?.contract.map(item => (
                      <div key={item.id} className="position-relative">
                        <input
                          type="radio"
                          id={`contract_${item.id}`}
                          name="contract"
                          className="better-radio"
                          checked={selectedContract && selectedContract.id === item.id}
                          onChange={() => refreshConfigData("contract_id", item.id)}
                        />
                        <label 
                          htmlFor={`contract_${item.id}`} 
                          className={`list-group-item d-flex flex-column p-3 ${selectedContract && selectedContract.id === item.id ? 'selected' : ''}`}
                        >
                          <div className="fw-bold">{item.name}</div>
                        </label>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>
          </Row>

          {/* Order Summary and Action Buttons */}
          <Card className="border-0 bg-light shadow-sm p-4">
            <Row className="align-items-center">
              <Col xs={12} md={6}>
                <h3 className="mb-0">Total Price: <span className="text-success">€{totalPrice}/mo</span></h3>
                <p className="text-muted mb-0">Delivery: {delivery}</p>
              </Col>
              <Col xs={12} md={6} className="text-md-end mt-3 mt-md-0 d-flex justify-content-end">
                <Button className="btn btn-success" size="lg" onClick={handleOrderClick}>
                  <i className="fa fa-check-circle me-2" style={{paddingRight: '0.5rem'}}></i>
                  Order Now
                </Button>
              </Col>
            </Row>
          </Card>
        </CardBody>
      </Card>

      {/* Order Confirmation Modal */}
      <Modal 
        isOpen={showConfirmModal} 
        toggle={toggleConfirmModal}
        className="order-summary-modal"
        backdropClassName="fixed-backdrop"
        backdrop={true}
        zIndex={9999}  // Higher z-index for the modal
      >
        <ModalHeader toggle={null} close={false}>Order Summary</ModalHeader>
        <ModalBody>
          {selectedCategory && selectedAsset && selectedColo && selectedUnits && selectedRenting && selectedContract && (
            <div className="table-responsive">
              <table className="table card-table table-vcenter text-nowrap mb-0">
                <tbody>
                  <tr>
                    <td><b>Category</b></td>
                    <td>{selectedCategory.name}</td>
                  </tr>
                  <tr>
                    <td><b>Asset</b></td>
                    <td>{selectedAsset.name}</td>
                  </tr>
                  <tr>
                    <td><b>Colocation Space</b></td>
                    <td>{selectedColo.name}</td>
                  </tr>

                  <tr>
                    <td><b>Contract Term</b></td>
                    <td>
                      <select 
                        className="form-control" 
                        value={tempContract ? tempContract.id : selectedContract.id}
                        onChange={(e) => {
                          const contract = configs[0].contract.find(c => c.id === e.target.value);
                          handleTempContractChange(contract);
                        }}
                      >
                        {configs[0]?.contract.map(contract => (
                          <option key={contract.id} value={contract.id}>
                            {contract.name}
                          </option>
                        ))}
                      </select>
                    </td>
                  </tr>
                  <tr>
                    <td><b>Assets</b></td>
                    <td>
                      <select 
                        className="form-control" 
                        value={selectedAssets}
                        onChange={(e) => setSelectedAssets(parseInt(e.target.value))}
                      >
                        <option value="1">1 Asset</option>
                        <option value="2">2 Assets</option>
                        <option value="3">3 Assets</option>
                        <option value="4">4 Assets</option>
                        <option value="5">5 Assets</option>
                        <option value="10">10 Assets</option>
                        <option value="20">20 Assets</option>
                      </select>
                    </td>
                  </tr>
                  <tr>
                    <td><b>Delivery</b></td>
                    <td>{delivery}</td>
                  </tr>
                  <tr>
  <td><b>Price</b></td>
  <td>
    
  €{totalPrice}/mo

    
  </td>
</tr>
                </tbody>
              </table>
            </div>
          )}
        </ModalBody>
        <ModalFooter className="d-flex flex-column p-3">
    {/* Top row with terms of service */}
    <div className="w-100 mb-3">
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <input
          type="checkbox"
          id="agreeTerms"
          checked={termsAccepted}
          onChange={() => setTermsAccepted(!termsAccepted)}
          style={{ 
            marginRight: '5px',
            marginLeft: '0',
            marginTop: '0',
            marginBottom: '0',
            padding: '0',
            position: 'relative',
            float: 'none'
          }}
          required
        />
        I accept the <a href="https://zetservers.com/tos.php" target="_blank" style={{ marginLeft: '3px', color: '#1e88e5' }}>Terms of Service</a>
      </div>
    </div>
    
    {/* Bottom row with buttons */}
    <div className="d-flex justify-content-between w-100">
      <Button color="secondary" onClick={toggleConfirmModal}>Cancel</Button>
      <Button color="success" onClick={handleOrder} disabled={!termsAccepted}>Confirm Order</Button>
    </div>
  </ModalFooter>
      </Modal>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        toggle={togglePaymentModal}
        totalAmount={totalPrice}
        onComplete={handlePaymentComplete}
      />


    </Container>
  );
};

export default ColocationAssetsOrder;