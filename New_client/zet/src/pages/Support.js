import React, { useState, useEffect } from "react";
import { Outlet, Link, useNavigate } from "react-router-dom";
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import 'primereact/resources/themes/mdc-light-indigo/theme.css';
import "primereact/resources/primereact.min.css";
import 'primeflex/primeflex.css';
import DiscountAlert from "../components/DiscountAlert";

const Support = () => {
  const navigate = useNavigate();
  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Add access control state
  const [hasAccess, setHasAccess] = useState(null); // Start with null to show loading
  const [accessError, setAccessError] = useState('');

  // Navigate to ticket details
  const navigateToTicket = (id) => {
    navigate(`/support/${id}`);
  };

  // No Access Component
  const NoAccessMessage = () => (
    <div className="text-center p-5">
      <i className="fa fa-lock fa-4x text-danger mb-4"></i>
      <h3 className="text-danger mb-3">Access Denied</h3>
      <p className="text-muted mb-4" style={{fontSize: '1.1rem'}}>
        {accessError || 'You do not have permission to access support tickets.'}
      </p>
      <p className="text-muted">
        Please contact your administrator if you believe this is an error.
      </p>
      <div className="mt-4">
        <button 
          className="btn btn-secondary me-2" 
          onClick={() => window.location.reload()}
        >
          <i className="fa fa-refresh me-1"></i> Refresh Page
        </button>
      </div>
    </div>
  );

  // Fetch tickets data
  const fetchTicketsData = () => {
    setLoading(true);
    setError(null);
    
    // Get token from both storage locations
    function getToken() {
      const sessionToken = sessionStorage.getItem('token');
      const localToken = localStorage.getItem('token');
      return sessionToken || localToken;
    }
    const token = getToken();
    
    if (!token) {
      console.error("No authentication token found");
      setError("Authentication required. Please log in.");
      setLoading(false);
      return;
    }
    
    fetch("/api.php?f=tickets", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
      .then(response => {
        console.log("Tickets Response status:", response.status);
        
        // Check for 403 status code BEFORE trying to parse JSON
        if (response.status === 403) {
          console.log("403 detected - setting no access for tickets");
          setHasAccess(false);
          setAccessError('You do not have permission to access support tickets');
          setLoading(false);
          throw new Error('Access denied'); // Stop the promise chain
        }
        
        // Check if response is ok for other status codes
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return response.json();
      })
      .then(data => {
        console.log("Tickets Response Data:", data);
        
        // Additional check for error messages in the JSON response
        if (data.error) {
          console.error("API error:", data);
          if (data.error === 5) {
            setError("Authentication error. Please log in again.");
          } else if (data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
            // Handle permission errors in JSON response
            console.log("Permission error in JSON response");
            setHasAccess(false);
            setAccessError(data.message || 'Access denied');
            setLoading(false);
            return;
          } else {
            setError(data.message || "Failed to load tickets");
          }
          setLoading(false);
          return;
        }
        
        // Success - data loaded properly
        console.log("Tickets data loaded successfully");
        setTickets(Array.isArray(data) ? data : []);
        setHasAccess(true); // Ensure access is set to true on successful load
        setLoading(false);
      })
      .catch(error => {
        console.error("Error fetching tickets:", error);
        
        // If the error is "Access denied", don't show additional error
        if (error.message !== 'Access denied') {
          setError("Failed to load tickets. Please try again later.");
          setHasAccess(true); // Keep page accessible for network errors
        }
        setLoading(false);
      });
  };

  // Load tickets on component mount
  useEffect(() => {
    fetchTicketsData();
    
    // Refresh tickets every 60 seconds (only if access is granted)
    const interval = setInterval(() => {
      if (hasAccess === true) {
        fetchTicketsData();
      }
    }, 60000);
    return () => clearInterval(interval);
  }, [hasAccess]);

  // Row click handler
  const handleRowClick = (e) => {
    navigateToTicket(e.data.id);
  };

  // Custom formatter for ticket status
  const statusFormatter = (rowData) => {
    const status = rowData.status;
    let badgeClass = "bg-secondary";
    
    switch(status) {
      case 'Open':
        badgeClass = "bg-primary";
        break;
      case 'Customer Reply':
        badgeClass = "bg-info";
        break;
      case 'In Progress':
        badgeClass = "bg-warning";
        break;
      case 'Answered':
        badgeClass = "bg-success";
        break;
      case 'Closed':
        badgeClass = "bg-danger";
        break;
      default:
        badgeClass = "bg-secondary";
    }
    
    return <span className={`badge ${badgeClass}`} style={{ minWidth: '130px', color:'white'}}>{rowData.status}</span>;
  };

  // Show loading state
  if (hasAccess === null) {
    return (
      <>
        <div className="page-header">
          <div className="page-leftheader">
            <ol className="breadcrumb">
              <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
              <li className="breadcrumb-item1">Support</li>
            </ol>
          </div>
        </div>

        <div className="row">
          <div className="col-12">
            <div className="card smaller">
              <div className="card-header">
                <div className="card-title">Help Desk</div>
              </div>
              <div className="card-body">
                <div className="d-flex justify-content-center p-5">
                  <div className="spinner-border text-primary" role="status">
        
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Show no access message
  if (hasAccess === false) {
    return (
      <>
        <div className="page-header">
          <div className="page-leftheader">
            <ol className="breadcrumb">
              <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
              <li className="breadcrumb-item1">Support</li>
            </ol>
          </div>
        </div>

        <div className="row">
          <div className="col-12">
            <div className="card smaller">
              <div className="card-header">
                <div className="card-title">Help Desk</div>
              </div>
              <div className="card-body">
                <NoAccessMessage />
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1">Support</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-12">
          <DiscountAlert />

          <div className="card smaller">
            <div className="card-header">
              <div className="card-title">Help Desk</div>
              <div className="card-options">
                <Link to="/support/newcase" className="btn btn-success btn-sm">
                  <i className="fa fa-plus mr-1"></i> New Case
                </Link>
                <button onClick={fetchTicketsData} className="btn btn-outline-primary btn-sm ml-2" disabled={loading}>
                  <i className="fa fa-refresh mr-1"></i> Refresh
                </button>
              </div>
            </div>
            <div className="card-body">
              {error && (
                <div className="alert alert-danger mb-4">
                  <i className="fa fa-exclamation-triangle mr-2"></i> {error}
                </div>
              )}
              
              {loading ? (
                <div className="d-flex justify-content-center p-5">
                  <div className="spinner-border text-primary" role="status">
                  </div>
                </div>
              ) : tickets.length === 0 ? (
                <div className="text-center p-5">
                  <i className="fa fa-life-ring fa-3x text-muted mb-3"></i>
                  <h4>No Support Tickets</h4>
                  <p className="text-muted">
                    You haven't created any support tickets yet. Need help? Open a new case.
                  </p>
                  <Link to="/support/newcase" className="btn btn-primary mt-2">
                    <i className="fa fa-plus mr-1"></i> Open New Case
                  </Link>
                </div>
              ) : (
                <DataTable 
                  value={tickets} 
                  onRowClick={handleRowClick}
                  sortField="id" 
                  sortOrder={-1} 
                  paginator 
                  rows={10} 
                  rowsPerPageOptions={[5, 10, 25, 50]} 
                  emptyMessage="No tickets found"
                  loading={loading}
                  responsiveLayout="scroll"
                  className="support-tickets-table"
                  rowHover
                >
                  <Column field="id" sortable header="ID" headerStyle={{ fontWeight: 'bold', width: '60px' }} style={{cursor: 'pointer'}}></Column>
                  <Column field="subject" sortable header="Subject" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                  
                  <Column field="department" sortable  header="Department" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                  <Column 
                    field="status" 
                    body={statusFormatter} 
                    sortable 
                    header="Status" 
                    headerStyle={{ fontWeight: 'bold', minWidth: '120px' }} 
                    style={{cursor: 'pointer'}}
                  ></Column>
                  <Column field="last_reply" sortable  header="Last Reply" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}}></Column>
                </DataTable>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Support;