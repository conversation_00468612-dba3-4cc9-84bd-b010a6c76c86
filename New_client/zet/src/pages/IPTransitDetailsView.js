import React, { useState, useEffect, useCallback } from "react";
import { Outlet, Link, useParams, useNavigate, useLocation } from "react-router-dom";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, AreaChart, Area } from 'recharts';
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
} from 'mdb-react-ui-kit';

const IPTransitDetailsView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [transitService, setTransitService] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [modalShow, setModalShow] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalBody, setModalBody] = useState('');
  const [modalFooter, setModalFooter] = useState('');
  
  // Sample traffic data for the graph
  const trafficData = [
    { time: '00:00', inbound: 7.8, outbound: 4.2 },
    { time: '02:00', inbound: 6.5, outbound: 3.8 },
    { time: '04:00', inbound: 5.2, outbound: 2.9 },
    { time: '06:00', inbound: 6.8, outbound: 3.5 },
    { time: '08:00', inbound: 9.2, outbound: 5.1 },
    { time: '10:00', inbound: 13.5, outbound: 8.2 },
    { time: '12:00', inbound: 12.8, outbound: 7.5 },
    { time: '14:00', inbound: 14.2, outbound: 8.9 },
    { time: '16:00', inbound: 15.8, outbound: 9.5 },
    { time: '18:00', inbound: 17.2, outbound: 10.8 },
    { time: '20:00', inbound: 13.8, outbound: 8.4 },
    { time: '22:00', inbound: 10.5, outbound: 6.2 },
  ];
  
  // Sample packet data
  const packetData = [
    { time: '00:00', packets: 1250000 },
    { time: '02:00', packets: 980000 },
    { time: '04:00', packets: 850000 },
    { time: '06:00', packets: 1100000 },
    { time: '08:00', packets: 1850000 },
    { time: '10:00', packets: 2450000 },
    { time: '12:00', packets: 2250000 },
    { time: '14:00', packets: 2650000 },
    { time: '16:00', packets: 2950000 },
    { time: '18:00', packets: 3250000 },
    { time: '20:00', packets: 2450000 },
    { time: '22:00', packets: 1950000 },
  ];
  
  // Sample latency data
  const latencyData = [
    { time: '00:00', latency: 8 },
    { time: '02:00', latency: 7 },
    { time: '04:00', latency: 6 },
    { time: '06:00', latency: 9 },
    { time: '08:00', latency: 12 },
    { time: '10:00', latency: 15 },
    { time: '12:00', latency: 14 },
    { time: '14:00', latency: 13 },
    { time: '16:00', latency: 16 },
    { time: '18:00', latency: 18 },
    { time: '20:00', latency: 14 },
    { time: '22:00', latency: 10 },
  ];
  
  // Sample jitter data
  const jitterData = [
    { time: '00:00', jitter: 1.2 },
    { time: '02:00', jitter: 1.1 },
    { time: '04:00', jitter: 0.9 },
    { time: '06:00', jitter: 1.5 },
    { time: '08:00', jitter: 1.8 },
    { time: '10:00', jitter: 2.2 },
    { time: '12:00', jitter: 2.0 },
    { time: '14:00', jitter: 1.9 },
    { time: '16:00', jitter: 2.4 },
    { time: '18:00', jitter: 2.6 },
    { time: '20:00', jitter: 2.1 },
    { time: '22:00', jitter: 1.6 },
  ];

  // Sample transit service data - in a real application, this would come from an API
  const sampleTransitService = {
    id: id,
    name: "TR-BX5T9",
    status: "Active",
    portInfo: {
      type: "Fiber",
      size: "10 Gbps",
      interface: "SFP+ LC",
      redundancy: "Single Port",
      mediaType: "Single Mode"
    },
    location: {
      flag: "de",
      name: "Frankfurt, Germany",
      datacenter: "Interxion FRA3",
      facility: "Rack 12, Unit 35-36"
    },
    network: {
      bandwidth: {
        committed: "10 Gbps",
        burstable: "15 Gbps",
        peakUsage: "12.8 Gbps",
        averageUsage: "8.5 Gbps",
        billingModel: "95th Percentile"
      },
      subnet: {
        prefix: "/29",
        addresses: ["*************", "*************", "*************", "*************"],
        gateway: "*************",
        usableIPs: 5
      },
      bgp: {
        asn: "AS64512",
        peers: ["************", "************"],
        prefixes: 48,
        routes: 125
      }
    },
    uptime: {
      current: "67 days, 14 hours",
      percentage: "99.99%",
      lastOutage: "Never"
    },
    billing: {
      price: "€450.00",
      billingCycle: "Monthly",
      nextRenewal: "May 12, 2025",
      autoRenew: true,
      contract: "12 Months"
    }
  };

  useEffect(() => {
    // Simulate API call to fetch service details
    const fetchTransitDetails = () => {
      setLoading(true);
      // For demo purposes, use sample data
      setTimeout(() => {
        setTransitService(sampleTransitService);
        setLoading(false);
      }, 300);
    };

    fetchTransitDetails();
  }, [id]);

  // Modal content templates
  const bandwidthUpgradeModal = {
    title: <>Upgrade Bandwidth</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Select New Bandwidth Package:</label>
        <select name="bandwidth" className="form-control custom-select mb-3">
          <option>10 Gbps Committed (Current)</option>
          <option>15 Gbps Committed (+€150/mo)</option>
          <option>20 Gbps Committed (+€275/mo)</option>
          <option>25 Gbps Committed (+€400/mo)</option>
          <option>40 Gbps Committed (+€650/mo)</option>
          <option>100 Gbps Committed (+€1,500/mo)</option>
        </select>
        
        <div className="alert alert-info mb-3">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          Bandwidth upgrades are usually processed within 24 hours. A technician may need to contact you to schedule the upgrade if physical port changes are required.
        </div>
        
        <div className="alert alert-warning mb-0">
          <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
          <strong>Note:</strong> Upgrading your committed bandwidth may require port reconfiguration that could result in 5-15 minutes of downtime.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Request Upgrade</button></>
  };

  const portRedundancyModal = {
    title: <>Add Port Redundancy</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Select Redundancy Option:</label>
        <select name="redundancy" className="form-control custom-select mb-3">
          <option>Dual-port redundancy (+€200/mo)</option>
          <option>Dual-router redundancy (+€350/mo)</option>
          <option>Full path redundancy (+€500/mo)</option>
        </select>
        
        <div className="alert alert-info mb-3">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          Adding redundancy requires physical installation and configuration. Our team will contact you to schedule the installation.
        </div>
        
        <div className="alert alert-warning mb-0">
          <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
          <strong>Note:</strong> Installation typically takes 3-5 business days. The installation process itself may require up to 1 hour of downtime.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Request Redundancy</button></>
  };

  const subnetUpgradeModal = {
    title: <>Upgrade IP Subnet</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Select New Subnet Size:</label>
        <select name="subnet" className="form-control custom-select mb-3">
          <option>/29 - 8 IPs, 5 usable (Current)</option>
          <option>/28 - 16 IPs, 13 usable (+€30/mo)</option>
          <option>/27 - 32 IPs, 29 usable (+€60/mo)</option>
          <option>/26 - 64 IPs, 61 usable (+€120/mo)</option>
          <option>/25 - 128 IPs, 125 usable (+€240/mo)</option>
          <option>/24 - 256 IPs, 253 usable (+€480/mo)</option>
        </select>
        
        <div className="alert alert-info mb-3">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          IP allocation is typically processed within 24 hours. You'll receive configuration instructions when the allocation is complete.
        </div>
        
        <div className="alert alert-warning mb-0">
          <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
          <strong>Warning:</strong> Changing your IP allocation will require reconfiguration of your equipment. Please ensure you have the technical capability to make the necessary changes.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Request IP Upgrade</button></>
  };

  const bgpConfigModal = {
    title: <>Configure BGP</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">BGP Configuration:</label>
        
        <div className="mb-3">
          <label className="form-label">Your ASN:</label>
          <input type="text" className="form-control" placeholder="Enter your ASN" defaultValue={transitService?.network.bgp.asn || ""} />
        </div>
        
        <div className="mb-3">
          <label className="form-label">Announced Prefixes:</label>
          <textarea className="form-control" rows="4" placeholder="Enter prefixes, one per line" defaultValue="*************/29"></textarea>
        </div>
        
        <div className="mb-3">
          <label className="form-label">Authentication:</label>
          <select className="form-control">
            <option>MD5 Authentication</option>
            <option>No Authentication</option>
          </select>
        </div>
        
        <div className="mb-3">
          <label className="form-label">MD5 Password:</label>
          <input type="password" className="form-control" placeholder="Enter MD5 password" />
        </div>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          BGP configuration changes are typically processed within 2 hours. Our team will contact you if there are any issues with your configuration.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Update BGP Configuration</button></>
  };
  
  const terminateModal = {
    title: <>Terminate Service</>,
    body: <>
      <div className="form-group">
        <div className="alert alert-danger mb-3">
          <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
          <strong>WARNING:</strong> Terminating this service will permanently disconnect your IP Transit and remove all associated configurations.
        </div>
        <p>Please confirm that you want to terminate IP Transit service <strong>{transitService?.name}</strong>.</p>
        
        {/* Termination timing options */}
        <div className="mb-3">
          <label className="form-label">When would you like to terminate the service?</label>
          <div className="form-check mb-2">
            <input 
              type="radio" 
              className="form-check-input" 
              id="terminateImmediately" 
              name="terminationTiming"
              value="immediately"
              style={{ 
                marginRight: '5px',
                marginLeft: '0',
                marginTop: '0',
                marginBottom: '0',
                padding: '0',
                position: 'relative',
                float: 'none'
              }}
              defaultChecked 
            />
            <label className="form-check-label" htmlFor="terminateImmediately">
              Terminate immediately
            </label>
          </div>
          <div className="form-check">
            <input 
              type="radio" 
              className="form-check-input" 
              id="terminateEndOfBilling" 
              name="terminationTiming"
              value="end-of-billing"
              style={{ 
                marginRight: '5px',
                marginLeft: '0',
                marginTop: '0',
                marginBottom: '0',
                padding: '0',
                position: 'relative',
                float: 'none'
              }}
            />
            <label className="form-check-label" htmlFor="terminateEndOfBilling">
              Terminate at the end of billing period {transitService?.billing?.nextRenewal && `(${transitService.billing.nextRenewal})`}
            </label>
          </div>
        </div>
        
        {/* Reason for termination */}
        <div className="mb-3">
          <label htmlFor="terminationReason" className="form-label">Reason for termination <span className="text-danger">*</span></label>
          <textarea 
            className="form-control" 
            id="terminationReason" 
            rows="3" 
            placeholder="Please tell us why you're terminating this service..."
            required
          ></textarea>
          <div className="invalid-feedback" id="reasonFeedback" style={{display: 'none'}}>
            Please provide a reason for termination.
          </div>
        </div>
        
        <div className="form-check mb-3">
          <input 
            type="checkbox" 
            className="form-check-input" 
            id="confirmTermination"
            style={{ 
              marginRight: '5px',
              marginLeft: '0',
              marginTop: '0',
              marginBottom: '0',
              padding: '0',
              position: 'relative',
              float: 'none'
            }}
          />
          <label className="form-check-label" htmlFor="confirmTermination">
            I understand that this will permanently terminate my IP Transit service.
          </label>
        </div>
      </div>
    </>,
    footer: <>
      <button className="btn btn-secondary" onClick={() => setModalShow(false)}>Cancel</button>
      <button 
        className="btn btn-danger ms-2" 
        onClick={() => {
          // Validation logic here
          setModalShow(false);
        }}
      >
        Terminate Service
      </button>
    </>
  };

  const renewModal = {
    title: <>Renew Service</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Payment Method:</label>
        <select name="payment_method" className="form-control custom-select mb-3">
          <option value="cc">Credit Card</option>
          <option value="cr">Cryptocurrency</option>
          <option value="bt">Bank Transfer</option>
        </select>
        
        <label className="form-label mb-2">Billing Period:</label>
        <select name="billing_period" className="form-control custom-select mb-3">
          <option value="1">1 Month (€450.00)</option>
          <option value="3">3 Months - 5% discount (€1,282.50)</option>
          <option value="6">6 Months - 10% discount (€2,430.00)</option>
          <option value="12">12 Months - 15% discount (€4,590.00)</option>
        </select>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          Longer billing periods come with automatic discounts. Your service will be automatically renewed when the payment is processed.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Renew Service</button></>
  };

  const openModal = (title, body, footer) => {
    setModalTitle(title);
    setModalBody(body);
    setModalFooter(footer);
    setModalShow(true);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: "300px" }}>
        <div className="spinner-border text-primary" role="status">
       
        </div>
      </div>
    );
  }

  if (!transitService) {
    return (
      <div className="alert alert-danger" role="alert">
        Service not found. <Link to="/">Return to services</Link>
      </div>
    );
  }

  return (
    <>
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Services</Link></li>
            <li className="breadcrumb-item1">IP Transit {transitService.name}</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-12">
          <div className="alert alert-success" role="alert">
            <i className="fa fa-check-circle-o me-2" aria-hidden="true"></i> <strong>Well done!</strong> Activate another IP Transit service to get <b>15% discount</b> on all IP Transit services &nbsp; <Link to="/reseller"><button className="btn btn-default btn-sm">Check Discounts</button></Link>
          </div>

          <div className="card smaller">
            <div className="card-header">
              <div className="card-title d-flex align-items-center">
                <span className="me-2">IP Transit {transitService.name}</span>
                {transitService.status === 'Active' && <span className="badge bg-success">Active</span>}
                {transitService.status === 'Offline' && <span className="badge bg-danger">Offline</span>}
                {transitService.status === 'Maintenance' && <span className="badge bg-warning">Maintenance</span>}
              </div>
              <div className="card-options">
                
                <button className="btn btn-info btn-sm me-2" onClick={() => openModal(bgpConfigModal.title, bgpConfigModal.body, bgpConfigModal.footer)}>
                  <i className="fa fa-cogs me-1"></i> BGP Config
                </button>

                <button className="btn btn-warning btn-sm" onClick={() => openModal(subnetUpgradeModal.title, subnetUpgradeModal.body, subnetUpgradeModal.footer)}>
                  <i className="fa fa-sitemap me-1"></i> IP Subnet
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="panel panel-primary">
                <div className="tab-menu-heading">
                  <div className="tabs-menu">
                    <ul className="nav panel-tabs">
                      <li>
                        <a 
                          className={activeTab === 'overview' ? 'active' : ''} 
                          onClick={() => setActiveTab('overview')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-screen-desktop me-1"></i> Overview
                        </a>
                      </li>
                      <li>
                        <a 
                          className={activeTab === 'network' ? 'active' : ''} 
                          onClick={() => setActiveTab('network')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-graph me-1"></i> Network Statistics
                        </a>
                      </li>
                      <li>
                        <a 
                          className={activeTab === 'settings' ? 'active' : ''} 
                          onClick={() => setActiveTab('settings')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-settings me-1"></i> Settings
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="panel-body tabs-menu-body">
                  <div className="tab-content">
                    {/* Overview Tab */}
                    {activeTab === 'overview' && (
                      <div className="tab-pane active">
                        <div className="row">
                          {/* Port Information Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-plug me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Port Information</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Quick Actions</h5>
                                <div className="row">
                                  <div className="col-6 mb-3">
                                    <button 
                                      className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(portRedundancyModal.title, portRedundancyModal.body, portRedundancyModal.footer)}
                                    >
                                      <i className="fa fa-shield fa-2x d-block mb-2 text-success"></i>
                                      Add Redundancy
                                    </button>
                                  </div>
                                  <div className="col-6 mb-3">
                                    <button 
                                      className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(bandwidthUpgradeModal.title, bandwidthUpgradeModal.body, bandwidthUpgradeModal.footer)}
                                    >
                                      <i className="fa fa-tachometer fa-2x d-block mb-2 text-primary"></i>
                                      Upgrade Bandwidth
                                    </button>
                                  </div>
                                </div>
                                
                                <h5 className="text-muted mb-3">Port Details</h5>
                                <ul className="list-group list-group-flush">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-plug text-primary me-2"></i> Port Type:</span>
                                    <span className="text-muted">{transitService.portInfo.type}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-tachometer text-primary me-2"></i> Port Size:</span>
                                    <span className="text-muted">{transitService.portInfo.size}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-exchange text-primary me-2"></i> Interface:</span>
                                    <span className="text-muted">{transitService.portInfo.interface}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-shield text-primary me-2"></i> Redundancy:</span>
                                    <span className="text-muted">{transitService.portInfo.redundancy}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-flash text-primary me-2"></i> Media Type:</span>
                                    <span className="text-muted">{transitService.portInfo.mediaType}</span>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          </div>
                          
                          {/* Status and Activity Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-info-circle me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Status & Activity</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Service Status</h5>
                                <ul className="list-group list-group-flush mb-4">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-power-off text-primary me-2"></i> Status:</span>
                                    <span className="badge bg-success">Active</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-clock-o text-primary me-2"></i> Uptime:</span>
                                    <span className="text-muted">{transitService.uptime.current}</span>
                                  </li>
    
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-exclamation-triangle text-primary me-2"></i> Last Outage:</span>
                                    <span className="text-muted">{transitService.uptime.lastOutage}</span>
                                  </li>
                                </ul>
                                
                                <h5 className="text-muted mb-3">Location</h5>
                                <ul className="list-group list-group-flush mb-4">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0" >
                                    <span><i className="fa fa-map-marker text-primary me-2"></i> Data Center:</span>
                                    <span className="text-muted">
                                      <i className={`flag flag-${transitService.location.flag} me-1`} style={{paddingRight: '1rem'}}></i>
                                      {transitService.location.name}
                                    </span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-building text-primary me-2"></i> Facility:</span>
                                    <span className="text-muted">{transitService.location.datacenter}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-server text-primary me-2"></i> Location:</span>
                                    <span className="text-muted">{transitService.location.facility}</span>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          </div>
                          
                          {/* Billing Card */}
                          <div className="col-xl-4 col-lg-12 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Details & Billing</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Bandwidth Details</h5>
                                <ul className="list-group list-group-flush mb-4">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-exchange text-primary me-2"></i> Committed:</span>
                                    <span className="text-muted">{transitService.network.bandwidth.committed}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-arrow-up text-primary me-2"></i> Burstable:</span>
                                    <span className="text-muted">{transitService.network.bandwidth.burstable}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-area-chart text-primary me-2"></i> Peak Usage:</span>
                                    <span className="text-muted">{transitService.network.bandwidth.peakUsage}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-calculator text-primary me-2"></i> Billing Model:</span>
                                    <span className="text-muted">{transitService.network.bandwidth.billingModel}</span>
                                  </li>
                                </ul>

                                <h5 className="text-muted mb-3">Plan Details</h5>
                                <ul className="list-group list-group-flush mb-4">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-money text-primary me-2"></i> Monthly Price:</span>
                                    <span className="text-muted fw-bold">{transitService.billing.price}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-refresh text-primary me-2"></i> Billing Cycle:</span>
                                    <span className="text-muted">{transitService.billing.billingCycle}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-calendar text-primary me-2"></i> Next Renewal:</span>
                                    <span className="text-muted">{transitService.billing.nextRenewal}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-file-text text-primary me-2"></i> Contract:</span>
                                    <span className="text-muted">{transitService.billing.contract}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-refresh text-primary me-2"></i> Auto-Renewal:</span>
                                    <span className={`badge ${transitService.billing.autoRenew ? 'bg-success' : 'bg-danger'}`}>
                                      {transitService.billing.autoRenew ? 'Enabled' : 'Disabled'}
                                    </span>
                                  </li>
                                </ul>
                                
                                <div className="mt-4 d-flex justify-content-between">
                                  <button className="btn btn-danger btn-sm me-2" onClick={() => openModal(terminateModal.title, terminateModal.body, terminateModal.footer)}>
                                    <i className="fa fa-times-circle me-1"></i> Terminate
                                  </button>
                                  
                                  <div>
                              <Link 
  to={`/billing/invoices`} 
  className="btn btn-info btn-sm"
  onClick={() => {
    // Scroll to top when the button is clicked
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }}
>
  <i className="fa fa-arrow-circle-left me-1"></i> View Invoices
</Link>
                                    <button className="btn btn-success btn-sm" onClick={() => openModal(renewModal.title, renewModal.body, renewModal.footer)}>
                                      <i className="fa fa-refresh me-1"></i> Renew Service
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Network Statistics Tab */}
                    {activeTab === 'network' && (
                      <div className="tab-pane active">
                        <div className="row">
                          {/* Subnet Information Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-sitemap me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>IP Subnet Information</h3>
                              </div>
                              <div className="card-body">
                                <ul className="list-group list-group-flush">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-globe text-primary me-2"></i> IP Prefix:</span>
                                    <span className="text-muted">{transitService.network.subnet.prefix}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-hashtag text-primary me-2"></i> Usable IPs:</span>
                                    <span className="text-muted">{transitService.network.subnet.usableIPs}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-sign-in text-primary me-2"></i> Gateway:</span>
                                    <span className="text-muted">{transitService.network.subnet.gateway}</span>
                                  </li>
                                </ul>
                                
                                <h5 className="mt-4 mb-3">IP Addresses</h5>
                                <div className="table-responsive">
                                  <table className="table table-hover">
                                    <thead className="bg-light">
                                      <tr>
                                        <th>IP Address</th>
                                        <th>Usage</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {transitService.network.subnet.addresses.map((ip, index) => (
                                        <tr key={index}>
                                          <td>
                                            <div className="d-flex align-items-center">
                                              <span>{ip}</span>
                                            </div>
                                          </td>
                                          <td>
                                            {index === 0 ? 'Network Address' : 
                                             ip === transitService.network.subnet.gateway ? 'Gateway' : 
                                             index === transitService.network.subnet.addresses.length - 1 ? 'Broadcast Address' : 
                                             'Available'}
                                          </td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                                
                                <div className="mt-4 d-flex justify-content-end">
  <button className="btn btn-primary" onClick={() => openModal(subnetUpgradeModal.title, subnetUpgradeModal.body, subnetUpgradeModal.footer)}>
    <i className="fa fa-plus me-1"></i> Request Additional IPs
  </button>
</div>
</div>
</div>
</div>
                          
                          {/* BGP Information Card */}
                          <div className="col-xl-8 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-exchange me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>BGP Information</h3>
                                <div className="card-options">
                                  <button className="btn btn-sm btn-primary" onClick={() => openModal(bgpConfigModal.title, bgpConfigModal.body, bgpConfigModal.footer)}>
                                    <i className="fa fa-cogs me-1"></i> Update BGP Config
                                  </button>
                                </div>
                              </div>
                              <div className="card-body">
                                <div className="row">
                                  <div className="col-md-6">
                                    <h5 className="mb-3">BGP Configuration</h5>
                                    <ul className="list-group list-group-flush mb-4">
                                       <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span><i className="fa fa-id-card text-primary me-2"></i> Your ASN:</span>
                                        <span className="text-muted">{transitService.network.bgp.asn}</span>
                                      </li>
                                       <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span><i className="fa fa-random text-primary me-2"></i> Peer IPs:</span>
                                        <span className="text-muted">{transitService.network.bgp.peers.join(', ')}</span>
                                      </li>
                                       <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span><i className="fa fa-sitemap text-primary me-2"></i> Announced Prefixes:</span>
                                        <span className="text-muted">{transitService.network.bgp.prefixes}</span>
                                      </li>
                                       <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span><i className="fa fa-map-signs text-primary me-2"></i> Learned Routes:</span>
                                        <span className="text-muted">{transitService.network.bgp.routes}</span>
                                      </li>
                                    </ul>
                                  </div>
                                  <div className="col-md-6">
                                    <h5 className="mb-3">BGP Session Status</h5>
                                    <div className="table-responsive">
                                      <table className="table table-hover">
                                        <thead className="bg-light">
                                          <tr>
                                            <th>Peer IP</th>
                                            <th>Status</th>
                                            <th>Uptime</th>
                                          </tr>
                                        </thead>
                                        <tbody>
                                          {transitService.network.bgp.peers.map((peer, index) => (
                                            <tr key={index}>
                                              <td>{peer}</td>
                                              <td><span className="badge bg-success">Established</span></td>
                                              <td>{index === 0 ? '67d 14h 22m' : '67d, 14h 18m'}</td>
                                            </tr>
                                          ))}
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          {/* Traffic Graph */}
                          <div className="col-12 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-area-chart me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Traffic Statistics</h3>
                                <div className="card-options">
                                  <select className="form-control form-control-sm" style={{ minHeight: "35px" }}>
                                    <option>Last 24 hours</option>
                                    <option>Last 7 days</option>
                                    <option>Last 30 days</option>
                                  </select>
                                </div>
                              </div>
                              <div className="card-body">
                                <div style={{ width: '100%', height: 300 }}>
                                  <ResponsiveContainer>
                                    <LineChart
                                      data={trafficData}
                                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                    >
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis dataKey="time" />
                                      <YAxis />
                                      <Tooltip formatter={(value) => [`${value} Gbps`]} />
                                      <Legend />
                                      <Line 
                                        type="monotone" 
                                        dataKey="inbound" 
                                        stroke="#3498db" 
                                        activeDot={{ r: 8 }} 
                                        name="Inbound" 
                                        strokeWidth={2}
                                      />
                                      <Line 
                                        type="monotone" 
                                        dataKey="outbound" 
                                        stroke="#e74c3c" 
                                        name="Outbound" 
                                        strokeWidth={2}
                                      />
                                    </LineChart>
                                  </ResponsiveContainer>
                                </div>
                                <div className="mt-4">
                                  <h5 className="text-muted mb-3">Traffic Statistics</h5>
                                  <div className="row">
                                    <div className="col-md-3 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Peak Traffic</h6>
                                        <h3 className="mb-0">17.2 Gbps</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-3 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Average Traffic</h6>
                                        <h3 className="mb-0">10.3 Gbps</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-3 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">95th Percentile</h6>
                                        <h3 className="mb-0">15.8 Gbps</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-3 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Monthly Volume</h6>
                                        <h3 className="mb-0">87.5 TB</h3>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          {/* Performance Metrics */}
                          <div className="col-12">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-dashboard me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Performance Metrics</h3>
                              </div>
                              <div className="card-body">
                                <div className="row">
                                  <div className="col-md-4 mb-4">
                                    <h5 className="text-muted mb-3">Packets Per Second</h5>
                                    <div style={{ width: '100%', height: 200 }}>
                                      <ResponsiveContainer>
                                        <AreaChart
                                          data={packetData}
                                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                        >
                                          <CartesianGrid strokeDasharray="3 3" />
                                          <XAxis dataKey="time" />
                                          <YAxis />
                                          <Tooltip formatter={(value) => [`${(value / 1000).toFixed(1)}k pps`]} />
                                          <Area 
                                            type="monotone" 
                                            dataKey="packets" 
                                            stroke="#8884d8" 
                                            fill="#8884d8" 
                                            name="Packets/sec" 
                                          />
                                        </AreaChart>
                                      </ResponsiveContainer>
                                    </div>
                                  </div>
                                  <div className="col-md-4 mb-4">
                                    <h5 className="text-muted mb-3">Latency (ms)</h5>
                                    <div style={{ width: '100%', height: 200 }}>
                                      <ResponsiveContainer>
                                        <LineChart
                                          data={latencyData}
                                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                        >
                                          <CartesianGrid strokeDasharray="3 3" />
                                          <XAxis dataKey="time" />
                                          <YAxis />
                                          <Tooltip formatter={(value) => [`${value} ms`]} />
                                          <Line 
                                            type="monotone" 
                                            dataKey="latency" 
                                            stroke="#82ca9d" 
                                            name="Latency" 
                                            strokeWidth={2}
                                          />
                                        </LineChart>
                                      </ResponsiveContainer>
                                    </div>
                                  </div>
                                  <div className="col-md-4 mb-4">
                                    <h5 className="text-muted mb-3">Jitter (ms)</h5>
                                    <div style={{ width: '100%', height: 200 }}>
                                      <ResponsiveContainer>
                                        <LineChart
                                          data={jitterData}
                                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                        >
                                          <CartesianGrid strokeDasharray="3 3" />
                                          <XAxis dataKey="time" />
                                          <YAxis />
                                          <Tooltip formatter={(value) => [`${value} ms`]} />
                                          <Line 
                                            type="monotone" 
                                            dataKey="jitter" 
                                            stroke="#ffc658" 
                                            name="Jitter" 
                                            strokeWidth={2}
                                          />
                                        </LineChart>
                                      </ResponsiveContainer>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Settings Tab */}
                    {activeTab === 'settings' && (
                      <div className="tab-pane active">
                        <div className="row">
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-cog me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Service Settings</h3>
                              </div>
                              <div className="card-body">
                                <form>
                                  <div className="form-group mb-3">
                                    <label className="form-label">Service Label</label>
                                    <input 
                                      type="text" 
                                      className="form-control" 
                                      defaultValue={transitService.name}
                                      placeholder="Service Label"
                                    />
                                    <small className="form-text text-muted">
                                      This is for your reference only and does not affect the service.
                                    </small>
                                  </div>
                                  
                                  <div className="form-group mb-3">
                                    <label className="form-label">Traffic Alerts</label>
                                    <div className="form-check mb-2">
                                      <input 
                                        type="checkbox" 
                                        className="form-check-input" 
                                        id="alertBandwidth"
                                        style={{ 
                                          marginRight: '5px',
                                          marginLeft: '0',
                                          marginTop: '0',
                                          marginBottom: '0',
                                          padding: '0',
                                          position: 'relative',
                                          float: 'none'
                                        }}
                                        defaultChecked
                                      />
                                      <label className="form-check-label" htmlFor="alertBandwidth">
                                        Alert me when bandwidth usage exceeds 80% of committed bandwidth
                                      </label>
                                    </div>
                                    <div className="form-check mb-2">
                                      <input 
                                        type="checkbox" 
                                        className="form-check-input" 
                                        id="alertOutage"
                                        style={{ 
                                          marginRight: '5px',
                                          marginLeft: '0',
                                          marginTop: '0',
                                          marginBottom: '0',
                                          padding: '0',
                                          position: 'relative',
                                          float: 'none'
                                        }}
                                        defaultChecked
                                      />
                                      <label className="form-check-label" htmlFor="alertOutage">
                                        Alert me about service outages
                                      </label>
                                    </div>
                                    <div className="form-check">
                                      <input 
                                        type="checkbox" 
                                        className="form-check-input" 
                                        id="alertMaintenance"
                                        style={{ 
                                          marginRight: '5px',
                                          marginLeft: '0',
                                          marginTop: '0',
                                          marginBottom: '0',
                                          padding: '0',
                                          position: 'relative',
                                          float: 'none'
                                        }}
                                        defaultChecked
                                      />
                                      <label className="form-check-label" htmlFor="alertMaintenance">
                                        Alert me about scheduled maintenance
                                      </label>
                                    </div>
                                  </div>
                                  
                                  <div className="form-group mb-3">
                                    <label className="form-label">Contact Preferences</label>
                                    <div className="form-check mb-2">
                                      <input 
                                        type="checkbox" 
                                        className="form-check-input" 
                                        id="contactEmail"
                                        style={{ 
                                          marginRight: '5px',
                                          marginLeft: '0',
                                          marginTop: '0',
                                          marginBottom: '0',
                                          padding: '0',
                                          position: 'relative',
                                          float: 'none'
                                        }}
                                        defaultChecked
                                      />
                                      <label className="form-check-label" htmlFor="contactEmail">
                                        Send alerts via email
                                      </label>
                                    </div>

                                  </div>
                                  
                                  <div className="d-flex justify-content-end">
                            <button type="submit" className="btn btn-primary">Save Settings</button>
                          </div>
                                </form>
                              </div>
                            </div>
                          </div>
                          
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Billing Settings</h3>
                              </div>
                              <div className="card-body">
                                <form>
                                  <div className="form-group mb-3">
                                    <label className="form-label">Billing Cycle</label>
                                    <select className="form-control">
                                      <option selected>Monthly (€{parseFloat(transitService.billing.price).toFixed(2)}/mo)</option>
                                      <option>Quarterly (€{(parseFloat(transitService.billing.price) * 3 * 0.95).toFixed(2)}/3mo - 5% discount)</option>
                                      <option>Semi-Annual (€{(parseFloat(transitService.billing.price) * 6 * 0.9).toFixed(2)}/6mo - 10% discount)</option>
                                      <option>Annual (€{(parseFloat(transitService.billing.price) * 12 * 0.85).toFixed(2)}/yr - 15% discount)</option>
                                    </select>
                                  </div>
                                  
                                  <div className="form-group mb-3">
                                    <label className="form-label">Next Renewal</label>
                                    <input 
                                      type="text" 
                                      className="form-control" 
                                      value={transitService.billing.nextRenewal} 
                                      readOnly
                                    />
                                  </div>
                                  
                                  <div className="form-group mb-3">
                                    <label className="form-label">Payment Method</label>
                                    <select className="form-control">
                                      <option>Credit Card (ending in 4857)</option>
                                      <option>Add New Payment Method</option>
                                    </select>
                                  </div>
                                  
                                  <div className="form-group form-check mb-3">
                                    <input 
                                      type="checkbox" 
                                      className="form-check-input" 
                                      id="autoRenewCheck" 
                                      checked={transitService.billing.autoRenew}
                                      style={{ 
                                        marginRight: '5px',
                                        marginLeft: '0',
                                        marginTop: '0',
                                        marginBottom: '0',
                                        padding: '0',
                                        position: 'relative',
                                        float: 'none'
                                      }}
                                    />
                                    <label className="form-check-label" htmlFor="autoRenewCheck">
                                      Enable automatic renewal
                                    </label>
                                    <small className="form-text text-muted d-block">
                                      When disabled, your service will be suspended after the billing period ends unless manually renewed.
                                    </small>
                                  </div>
                                  
                                  <div className="form-group form-check mb-3">
                                    <input 
                                      type="checkbox" 
                                      className="form-check-input" 
                                      id="useAccountCredit"
                                      style={{ 
                                        marginRight: '5px',
                                        marginLeft: '0',
                                        marginTop: '0',
                                        marginBottom: '0',
                                        padding: '0',
                                        position: 'relative',
                                        float: 'none'
                                      }}
                                      defaultChecked
                                    />
                                    <label className="form-check-label" htmlFor="useAccountCredit">
                                      Use account credit for renewals when available
                                    </label>
                                  </div>
                                  
                                  <div className="d-flex justify-content-end">
                                    <button type="submit" className="btn btn-primary">Save Billing Settings</button>
                                  </div>
                                </form>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Modal for various actions */}
      <MDBModal show={modalShow} setShow={setModalShow} tabIndex='-1'>
        <MDBModalDialog>
          <MDBModalContent>
            <MDBModalHeader>
              <MDBModalTitle>{modalTitle}</MDBModalTitle>
              <button type="button" className="close" onClick={() => setModalShow(false)}>
                <span aria-hidden="true">&times;</span>
              </button>
            </MDBModalHeader>
            <MDBModalBody>
              {modalBody}
            </MDBModalBody>
            <MDBModalFooter>
              {modalFooter}
            </MDBModalFooter>
          </MDBModalContent>
        </MDBModalDialog>
      </MDBModal>
    </>
  );
};

export default IPTransitDetailsView;