import React, { useState, useEffect, useCallback } from "react";
import { Toast } from 'primereact/toast';
import { Outlet, Link, useParams, useNavigate, useLocation } from "react-router-dom";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, AreaChart, Area } from 'recharts';
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
} from 'mdb-react-ui-kit';
import PaymentModal from './PaymentModal';
import DiscountAlert from "../components/DiscountAlert";

const CloudServerDetailsView = () => {
  const toast = React.useRef(null);
  const { id } = useParams();
  const navigate = useNavigate();
  const [server, setServer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [modalShow, setModalShow] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalBody, setModalBody] = useState('');
  const [modalFooter, setModalFooter] = useState('');
  
  // Sample CPU usage data for the graph
  const cpuData = [
    { time: '00:00', usage: 8 },
    { time: '02:00', usage: 5 },
    { time: '04:00', usage: 4 },
    { time: '06:00', usage: 7 },
    { time: '08:00', usage: 15 },
    { time: '10:00', usage: 32 },
    { time: '12:00', usage: 28 },
    { time: '14:00', usage: 35 },
    { time: '16:00', usage: 42 },
    { time: '18:00', usage: 35 },
    { time: '20:00', usage: 25 },
    { time: '22:00', usage: 15 },
  ];
  
  // Sample memory usage data
  const memoryData = [
    { time: '00:00', usage: 25 },
    { time: '02:00', usage: 22 },
    { time: '04:00', usage: 20 },
    { time: '06:00', usage: 18 },
    { time: '08:00', usage: 35 },
    { time: '10:00', usage: 52 },
    { time: '12:00', usage: 45 },
    { time: '14:00', usage: 38 },
    { time: '16:00', usage: 42 },
    { time: '18:00', usage: 50 },
    { time: '20:00', usage: 35 },
    { time: '22:00', usage: 28 },
  ];
  
  // Sample bandwidth data for the graph
  const bandwidthData = [
    { time: '00:00', inbound: 1.3, outbound: 0.8 },
    { time: '02:00', inbound: 0.8, outbound: 0.5 },
    { time: '04:00', inbound: 0.5, outbound: 0.3 },
    { time: '06:00', inbound: 0.7, outbound: 0.4 },
    { time: '08:00', inbound: 1.8, outbound: 1.2 },
    { time: '10:00', inbound: 3.2, outbound: 2.1 },
    { time: '12:00', inbound: 2.8, outbound: 1.9 },
    { time: '14:00', inbound: 3.5, outbound: 2.4 },
    { time: '16:00', inbound: 4.2, outbound: 2.8 },
    { time: '18:00', inbound: 3.5, outbound: 2.2 },
    { time: '20:00', inbound: 2.5, outbound: 1.8 },
    { time: '22:00', inbound: 1.5, outbound: 1.0 },
  ];
  
  // Sample disk IO data
  const diskData = [
    { time: '00:00', read: 8, write: 3 },
    { time: '02:00', read: 6, write: 2 },
    { time: '04:00', read: 4, write: 1 },
    { time: '06:00', read: 7, write: 3 },
    { time: '08:00', read: 15, write: 7 },
    { time: '10:00', read: 22, write: 10 },
    { time: '12:00', read: 18, write: 8 },
    { time: '14:00', read: 12, write: 5 },
    { time: '16:00', read: 25, write: 12 },
    { time: '18:00', read: 20, write: 10 },
    { time: '20:00', read: 15, write: 8 },
    { time: '22:00', read: 10, write: 4 },
  ];

  // Sample cloud server data - in a real application, this would come from an API
  const sampleServer = {
    id: id,
    plan: "Plan I",
    name: "cloud-vm3",
    status: "Active",
    ipAddress: "************",
    username: "admin",
    password: "Cl0udP@ss!234",
    location: {
      flag: "de",
      name: "Frankfurt, Germany",
      datacenter: "CloudHub DC2"
    },
    hardware: {
      cpu: "4 vCPU",
      cores: 4,
      ram: "8 GB",
      storage: "120 GB SSD",
      bandwidth: "1 Gbps"
    },
    network: {
      primaryIp: "************",
      subnet: "************/29",
      gateway: "************",
      publicIps: ["************", "************"],
      bandwidth: {
        speed: "1 Gbps",
        monthly: "3 TB",
        used: "0.8 TB",
        remaining: "2.2 TB"
      }
    },
    os: {
      name: "Ubuntu Server 22.04 LTS",
      logo: "/assets/images/os/ubuntu.png",
      type: "Linux"
    },
    billing: {
      price: "€29.99",
      billingCycle: "Monthly",
      nextRenewal: "April 15, 2025",
      autoRenew: true
    },
    uptime: {
      current: "23 days, 5 hours",
      percentage: "99.95%"
    },
    virtualResources: {
      cpuUtilization: "18%",
      ramUtilization: "42%",
      diskUtilization: "35%"
    }
  };

  useEffect(() => {
    // Simulate API call to fetch cloud server details
    const fetchServerDetails = () => {
      setLoading(true);
      // For demo purposes, use sample data
      setTimeout(() => {
        setServer(sampleServer);
        setLoading(false);
      }, 300);
    };

    fetchServerDetails();
  }, [id]);

  const togglePassword = () => {
    setShowPassword(!showPassword);
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
  };
  
  // Function to create a snapshot
  const createSnapshotModal = {
    title: <>Create Snapshot</>,
    body: <>
      <div className="form-group">
        <label className="form-label">Snapshot Name</label>
        <input 
          type="text" 
          className="form-control mb-3" 
          placeholder="Enter a name for this snapshot"
          defaultValue={`snapshot-${server?.name}-${new Date().toISOString().split('T')[0]}`}
        />
        
        <div className="alert alert-info mb-3">
          <i className="fa fa-info-circle me-2"></i>
          Snapshots capture the current state of your cloud server, including all data on the disk. You can use snapshots to restore your server to a previous state or to create new servers with the same configuration.
        </div>
        
        <div className="alert alert-warning mb-0">
          <i className="fa fa-exclamation-triangle me-2"></i>
          <strong>Note:</strong> Creating a snapshot does not cause downtime, but it may temporarily impact performance. For best results, create snapshots during periods of low activity.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-success" onClick={() => setModalShow(false)}>Create Snapshot</button></>
  };
  
  // Resize server modal
  // More compact resizeServerModal with processing indicator
const resizeServerModal = {
  title: <>Upgrade Cloud Server Plan</>,
  body: <>
    <div className="form-group">
      <div className="d-flex justify-content-between align-items-center mb-2">
        <label className="form-label mb-0"><strong>Select an upgrade plan:</strong></label>
        <span className="badge bg-success">Upgrades Only</span>
      </div>
      
      <hr className="my-2" />
      
      <div className="mb-3">
        <div className="list-group">
          <div className="list-group-item disabled" style={{ backgroundColor: '#f8f9fa', cursor: 'not-allowed', opacity: '0.8' }}>
            <div className="d-flex w-100 justify-content-between">
              <strong>Plan I</strong>
              <span>€29.99/mo</span>
            </div>
            <div className="small text-muted">4 vCPU • 16GB RAM • 200GB NVMe • 4Gbps</div>
            <span className="badge bg-secondary text-muted">Current Plan</span>
          </div>
          
          <div 
            className="list-group-item plan-option" 
            id="planUpgrade1" 
            data-plan="II"
            onClick={(e) => {
              // Remove active class from all plan options
              document.querySelectorAll('.plan-option').forEach(el => {
                el.classList.remove('active');
                el.style.backgroundColor = ''; // Reset background color for all options
              });
              // Add active class to selected plan
              e.currentTarget.classList.add('active');
              e.currentTarget.style.backgroundColor = '#e9f5ff'; // Set background color for selected option
            }}
            style={{ cursor: 'pointer', transition: 'background-color 0.2s' }}
            onMouseOver={(e) => {
              if (!e.currentTarget.classList.contains('active')) {
                e.currentTarget.style.backgroundColor = '#f8f9fa';
              }
            }}
            onMouseOut={(e) => {
              if (!e.currentTarget.classList.contains('active')) {
                e.currentTarget.style.backgroundColor = '';
              }
            }}
          >
            <div className="d-flex w-100 justify-content-between">
              <strong>Plan II</strong>
              <span className="text-primary">€49.99/mo</span>
            </div>
            <div className="small text-muted">8 vCPU • 32GB RAM • 400GB NVMe • 6Gbps</div>
       
          </div>
          
          <div 
            className="list-group-item plan-option" 
            id="planUpgrade2" 
            data-plan="III"
            onClick={(e) => {
              // Remove active class from all plan options
              document.querySelectorAll('.plan-option').forEach(el => {
                el.classList.remove('active');
                el.style.backgroundColor = ''; // Reset background color for all options
              });
              // Add active class to selected plan
              e.currentTarget.classList.add('active');
              e.currentTarget.style.backgroundColor = '#e9f5ff'; // Set background color for selected option
            }}
            style={{ cursor: 'pointer', transition: 'background-color 0.2s' }}
            onMouseOver={(e) => {
              if (!e.currentTarget.classList.contains('active')) {
                e.currentTarget.style.backgroundColor = '#f8f9fa';
              }
            }}
            onMouseOut={(e) => {
              if (!e.currentTarget.classList.contains('active')) {
                e.currentTarget.style.backgroundColor = '';
              }
            }}
          >
            <div className="d-flex w-100 justify-content-between">
              <strong>Plan III</strong>
              <span className="text-primary">€79.99/mo</span>
            </div>
            <div className="small text-muted">16 vCPU • 64GB RAM • 800GB NVMe • 8Gbps</div>

          </div>
        </div>
      </div>
      
      <div className="alert alert-warning py-2 mb-3 small">
        <i className="fa fa-exclamation-triangle me-1" style={{paddingRight: '0.5rem'}}></i>
        No downtime for upgrades.
      </div>
      
      {/* Processing stages - hidden by default, shown during upgrade */}
      <div id="upgradeProcessContainer" style={{ display: 'none' }}>
        <hr className="my-2" />
        <h6 className="mb-2">Upgrade Progress</h6>
        
        <div className="progress mb-3">
          <div 
            className="progress-bar progress-bar-striped progress-bar-animated" 
            role="progressbar" 
            id="upgradeProgressBar"
            style={{ width: '0%' }} 
            aria-valuenow="0" 
            aria-valuemin="0" 
            aria-valuemax="100"
          ></div>
        </div>
        
        <ul className="list-group">
          <li className="list-group-item py-2 small d-flex align-items-center" id="step1">
            <span className="badge bg-secondary me-2" id="step1Badge">1</span>
            Preparing for upgrade
            <span className="spinner-border spinner-border-sm ms-auto" id="step1Spinner" style={{ display: 'none' }}></span>
            <i className="fa fa-check-circle text-success ms-auto" id="step1Check" style={{ display: 'none' }}></i>
          </li>
          <li className="list-group-item py-2 small d-flex align-items-center" id="step2">
            <span className="badge bg-secondary me-2" id="step2Badge">2</span>
            Creating server snapshot
            <span className="spinner-border spinner-border-sm ms-auto" id="step2Spinner" style={{ display: 'none' }}></span>
            <i className="fa fa-check-circle text-success ms-auto" id="step2Check" style={{ display: 'none' }}></i>
          </li>
          <li className="list-group-item py-2 small d-flex align-items-center" id="step3">
            <span className="badge bg-secondary me-2" id="step3Badge">3</span>
            Shutting down server
            <span className="spinner-border spinner-border-sm ms-auto" id="step3Spinner" style={{ display: 'none' }}></span>
            <i className="fa fa-check-circle text-success ms-auto" id="step3Check" style={{ display: 'none' }}></i>
          </li>
          <li className="list-group-item py-2 small d-flex align-items-center" id="step4">
            <span className="badge bg-secondary me-2" id="step4Badge">4</span>
            Allocating resources
            <span className="spinner-border spinner-border-sm ms-auto" id="step4Spinner" style={{ display: 'none' }}></span>
            <i className="fa fa-check-circle text-success ms-auto" id="step4Check" style={{ display: 'none' }}></i>
          </li>
          <li className="list-group-item py-2 small d-flex align-items-center" id="step5">
            <span className="badge bg-secondary me-2" id="step5Badge">5</span>
            Starting upgraded server
            <span className="spinner-border spinner-border-sm ms-auto" id="step5Spinner" style={{ display: 'none' }}></span>
            <i className="fa fa-check-circle text-success ms-auto" id="step5Check" style={{ display: 'none' }}></i>
          </li>
        </ul>
      </div>
    </div>
  </>,
  footer: <>
    <button className="btn btn-secondary" onClick={() => setModalShow(false)}>Cancel</button>
    <button 
      className="btn btn-primary" 
      id="upgradeButton"
      onClick={() => {
        // Get selected plan
        const selectedPlan = document.querySelector('.plan-option.active');
        if (!selectedPlan) {
          toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Please select an upgrade plan to continue.',
        life: 3000
      });
          return;
        }
        
        // Hide the upgrade button and show processing button
        document.getElementById('upgradeButton').style.display = 'none';
        document.getElementById('processingButton').style.display = 'inline-block';
        
        // Show the upgrade process container
        document.getElementById('upgradeProcessContainer').style.display = 'block';
        
        // Simulate the upgrade process
        let currentStep = 1;
        const totalSteps = 5;
        
        const simulateStep = (step) => {
          // Update progress bar
          const progressPercent = (step - 1) * (100 / totalSteps);
          document.getElementById('upgradeProgressBar').style.width = `${progressPercent}%`;
          document.getElementById('upgradeProgressBar').setAttribute('aria-valuenow', progressPercent);
          
          // Show spinner for current step
          document.getElementById(`step${step}Badge`).classList.remove('bg-secondary');
          document.getElementById(`step${step}Badge`).classList.add('bg-primary');
          document.getElementById(`step${step}Spinner`).style.display = 'inline-block';
          
          // Simulate processing time (random between 1.5 and 3 seconds)
          const processingTime = Math.floor(Math.random() * 1500) + 1500;
          
          setTimeout(() => {
            // Complete the current step
            document.getElementById(`step${step}Spinner`).style.display = 'none';
            document.getElementById(`step${step}Check`).style.display = 'inline-block';
            document.getElementById(`step${step}Badge`).classList.remove('bg-primary');
            document.getElementById(`step${step}Badge`).classList.add('bg-success');
            
            if (step < totalSteps) {
              // Move to the next step
              simulateStep(step + 1);
            } else {
              // Complete the process
              document.getElementById('upgradeProgressBar').style.width = '100%';
              document.getElementById('upgradeProgressBar').setAttribute('aria-valuenow', 100);
              
              // Change processing button to completed
              document.getElementById('processingButton').style.display = 'none';
              document.getElementById('completedButton').style.display = 'inline-block';
              
              // After 2 seconds, close the modal
              setTimeout(() => {
                setModalShow(false);
                // Here you would typically update the UI to reflect the upgraded server
              }, 2000);
            }
          }, processingTime);
        };
        
        // Start the simulation
        simulateStep(currentStep);
      }}
    >
      Upgrade Server
    </button>
    <button 
      className="btn btn-primary" 
      id="processingButton" 
      disabled 
      style={{ display: 'none' }}
    >
      <span className="spinner-border spinner-border-sm me-2"></span>
      Processing...
    </button>
    <button 
      className="btn btn-success" 
      id="completedButton" 
      style={{ display: 'none' }}
    >
      <i className="fa fa-check me-2"></i>
      Upgrade Complete
    </button>
  </>
};
  
  // Modal content templates
  const rebootModal = {
    title: <>Reboot Server</>,
    body: <>
      <div className="form-group">
        <div className="alert alert-warning mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          After rebooting, please allow up to 2 minutes for the server to become accessible. If you can't access your server after that time, please use the Console to debug it remotely.
        </div>
        <div className="alert alert-danger mt-3 mb-0">
          <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
          <strong>WARNING:</strong> This action leads to downtime.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-warning" onClick={() => setModalShow(false)}>Reboot</button></>
  };

  const startModal = {
    title: <>Turn On Server</>,
    body: <>
      <div className="form-group">
        <div className="alert alert-warning mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          The server will start within approximately 30 seconds. If the server is not online within 2 minutes, please check the console for further details.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-success" onClick={() => setModalShow(false)}>Turn On</button></>
  };

  const shutdownModal = {
    title: <>Shutdown Server</>,
    body: <>
      <div className="form-group">
        <div className="alert alert-warning mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          This action will shut down your server, and you will need to manually restart it afterward.
        </div>
        <div className="alert alert-danger mt-3 mb-0">
          <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
          <strong>WARNING:</strong> This action leads to downtime.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-warning" onClick={() => setModalShow(false)}>Shutdown</button></>
  };
  
  const reinstallModal = {
    title: <>Reinstall Operating System</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Select Operating System:</label>
        <select name="os" className="form-control custom-select mb-3">
          <option value="ubuntu2204">Ubuntu Server 22.04 LTS</option>
          <option value="ubuntu2004">Ubuntu Server 20.04 LTS</option>
          <option value="debian11">Debian 11</option>
          <option value="centos9">CentOS Stream 9</option>
          <option value="windows2022">Windows Server 2022</option>
        </select>
        
        <div className="alert alert-info mb-3">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          After initiating the reinstallation procedure, the remote power control and reinstallation functions are disabled to avoid interrupting the process. You will receive an email and a notification in the client area when the reinstallation is complete.
        </div>
        
        <div className="alert alert-danger mb-0">
          <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
          <strong>WARNING:</strong> This function will delete all existing data from your server and leads to 5-10 min downtime.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-danger" onClick={() => setModalShow(false)}>Reinstall</button></>
  };
  
  const networkModal = {
    title: <>Bandwidth Upgrade</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Select Bandwidth Plan:</label>
        <select className="form-control custom-select mb-3">
          <option selected>3 TB (+€0.00/mo)</option>
          <option>5 TB (+€5.00/mo)</option>
          <option>10 TB (+€15.00/mo)</option>
          <option>Unmetered 1 Gbps (+€25.00/mo)</option>
        </select>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2"></i>
          Changes are processed automatically. Your new bandwidth plan will take effect immediately. No downtime is expected.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-success" onClick={() => setModalShow(false)}>Update Bandwidth</button></>
  };
  
  const renewModal = {
    title: <>Renew Server</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Payment Method:</label>
        <select name="payment_method" className="form-control custom-select mb-3">
          <option value="cc">Credit Card</option>
          <option value="cr">Cryptocurrency</option>
          <option value="bt">Bank Transfer</option>
        </select>
        
        <label className="form-label mb-2">Billing Period:</label>
        <select name="billing_period" className="form-control custom-select mb-3">
          <option value="1">1 Month (€29.99)</option>
          <option value="3">3 Months - 5% discount (€85.47)</option>
          <option value="6">6 Months - 10% discount (€161.95)</option>
          <option value="12">12 Months - 15% discount (€305.90)</option>
        </select>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2"></i>
          Longer billing periods come with automatic discounts.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-success" onClick={() => setModalShow(false)}>Renew Server</button></>
  };

  // IP Configuration Modal
  const ipConfigModal = {
    title: <>IP Configuration</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Select Upgrade Option:</label>
        <select name="ip_option" className="form-control custom-select mb-3">
          <option value="ip">Additional IPv4 Address (+€3.00/mo)</option>
          <option value="29">IPv4 /29 Subnet - 5 IPs (+€15.00/mo)</option>
          <option value="28">IPv4 /28 Subnet - 13 IPs (+€30.00/mo)</option>
        </select>
        
        <div className="alert alert-info mb-3">
          <i className="fa fa-info-circle me-2"></i>
          IP allocation is processed automatically and will be available within a few minutes.
          Configuration instructions will be provided in your client area.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-success" onClick={() => setModalShow(false)}>Add IP Resources</button></>
  };
  
  const terminateModal = {
    title: <>Terminate Server</>,
    body: <>
      <div className="form-group">
        <div className="alert alert-danger mb-3">
          <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
          <strong>WARNING:</strong> Terminating the server will permanently delete all data. This action cannot be undone.
        </div>
        <p>Please confirm that you want to terminate server <strong>{server?.name}</strong>.</p>
        
        {/* Termination timing options */}
        <div className="mb-3">
          <label className="form-label">When would you like to terminate the server?</label>
          <div className="form-check mb-2">
            <input 
              type="radio" 
              className="form-check-input" 
              id="terminateImmediately" 
              name="terminationTiming"
              value="immediately"
                style={{ 
        marginRight: '5px',
        marginLeft: '0',
        marginTop: '0',
        marginBottom: '0',
        padding: '0',
        position: 'relative',
        float: 'none'
      }}
              defaultChecked 
            />
            <label className="form-check-label" htmlFor="terminateImmediately">
              Terminate immediately
            </label>
          </div>
          <div className="form-check">
            <input 
              type="radio" 
              className="form-check-input" 
              id="terminateEndOfBilling" 
              name="terminationTiming"
              value="end-of-billing" 
                style={{ 
        marginRight: '5px',
        marginLeft: '0',
        marginTop: '0',
        marginBottom: '0',
        padding: '0',
        position: 'relative',
        float: 'none'
      }}
            />
            <label className="form-check-label" htmlFor="terminateEndOfBilling">
              Terminate at the end of billing period {server?.billing?.nextRenewal && `(${server.billing.nextRenewal})`}
            </label>
          </div>
        </div>
        
        {/* Reason for termination */}
        <div className="mb-3">
          <label htmlFor="terminationReason" className="form-label">Reason for termination <span className="text-danger">*</span></label>
          <textarea 
            className="form-control" 
            id="terminationReason" 
            rows="3" 
            placeholder="Please tell us why you're terminating this server..."
            required
          ></textarea>
          <div className="invalid-feedback" id="reasonFeedback" style={{display: 'none'}}>
            Please provide a reason for termination.
          </div>
        </div>
        
        <div className="form-check mb-3">
          <input type="checkbox" className="form-check-input" id="confirmTermination"   style={{ 
        marginRight: '5px',
        marginLeft: '0',
        marginTop: '0',
        marginBottom: '0',
        padding: '0',
        position: 'relative',
        float: 'none'
      }} />
          <label className="form-check-label" htmlFor="confirmTermination">
            I understand that all data on this server will be permanently deleted.
          </label>
        </div>
      </div>
    </>,
    footer: <>
      <button type="button" className="close" onClick={() => setModalShow(false)}>
        <span aria-hidden="true">&times;</span>
      </button>
      <button 
        className="btn btn-secondary" 
        onClick={() => setModalShow(false)}
      >
        Cancel
      </button>
      <button 
        className="btn btn-danger ms-2" 
        onClick={() => {
          const checkbox = document.getElementById('confirmTermination');
          const reasonTextarea = document.getElementById('terminationReason');
          const reasonFeedback = document.getElementById('reasonFeedback');
          const terminationReason = reasonTextarea.value.trim();
          
          // Check confirmation checkbox
          if (!checkbox || !checkbox.checked) {
            toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Please check the confirmation checkbox to proceed.',
        life: 3000
      });
            return;
          }
          
          // Validate reason field
          if (!terminationReason) {
            reasonFeedback.style.display = 'block';
            reasonTextarea.classList.add('is-invalid');
            return;
          } else {
            reasonFeedback.style.display = 'none';
            reasonTextarea.classList.remove('is-invalid');
          }
          
          // Handle termination with the collected data
          console.log('Server termination requested:', {
            server: server?.name,
            timing: document.getElementById('terminateImmediately').checked ? 'immediately' : 'end-of-billing',
            reason: terminationReason
          });
          
          // Close the modal
          setModalShow(false);
          
          // Show confirmation message
          toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Termination request submitted.',
        life: 3000
      });
        }}
      >
        Terminate Server
      </button>
    </>
  };

  const openModal = (title, body, footer) => {
    setModalTitle(title);
    setModalBody(body);
    setModalFooter(footer);
    setModalShow(true);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: "300px" }}>
        <div className="spinner-border text-primary" role="status">
          
        </div>
      </div>
    );
  }

  if (!server) {
    return (
      <div className="alert alert-danger" role="alert">
        Cloud server not found. <Link to="/">Return to services</Link>
      </div>
    );
  }

  return (
    <>
      <Toast ref={toast} />
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Services</Link></li>
            <li className="breadcrumb-item1">Cloud Server {server.name}</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-12">
          <DiscountAlert />

          <div className="card smaller">
            <div className="card-header">
              <div className="card-title d-flex align-items-center">
                <span className="me-2">Cloud Server {server.name}</span>
                {server.status === 'Active' && <span className="badge bg-success">Online</span>}
                {server.status === 'Offline' && <span className="badge bg-danger">Offline</span>}
                {server.status === 'Maintenance' && <span className="badge bg-warning">Maintenance</span>}
              </div>
              <div className="card-options">
                <button className="btn btn-success btn-sm me-2" onClick={() => openModal(createSnapshotModal.title, createSnapshotModal.body, createSnapshotModal.footer)}>
                  <i className="fa fa-camera me-1"></i> Snapshot
                </button>
  
                <button className="btn btn-secondary btn-sm me-2" onClick={() => openModal(reinstallModal.title, reinstallModal.body, reinstallModal.footer)}>
                  <i className="fa fa-cogs me-1"></i> Reinstall
                </button>
                <button className="btn btn-warning btn-sm me-2" onClick={() => openModal(rebootModal.title, rebootModal.body, rebootModal.footer)}>
                  <i className="fa fa-refresh me-1"></i> Reboot
                </button>
                <button className="btn btn-danger btn-sm" onClick={() => openModal(shutdownModal.title, shutdownModal.body, shutdownModal.footer)}>
                  <i className="fa fa-power-off me-1"></i> Shutdown
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="panel panel-primary">
                <div className="tab-menu-heading">
                  <div className="tabs-menu">
                    <ul className="nav panel-tabs">
                      <li>
                        <a 
                          className={activeTab === 'overview' ? 'active' : ''} 
                          onClick={() => setActiveTab('overview')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-screen-desktop me-1"></i> Overview
                        </a>
                      </li>
                      <li>
                        <a 
                          className={activeTab === 'performance' ? 'active' : ''} 
                          onClick={() => setActiveTab('performance')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-graph me-1"></i> Performance
                        </a>
                      </li>
                      <li>
                        <a 
                          className={activeTab === 'network' ? 'active' : ''} 
                          onClick={() => setActiveTab('network')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-globe me-1"></i> Network
                        </a>
                      </li>
                      <li>
                        <a 
                          className={activeTab === 'settings' ? 'active' : ''} 
                          onClick={() => setActiveTab('settings')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-settings me-1"></i> Settings
                        </a>
                      </li>
                      <li>
                        <a 
                          className={activeTab === 'snapshots' ? 'active' : ''} 
                          onClick={() => setActiveTab('snapshots')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-camera me-1"></i> Snapshots
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="panel-body tabs-menu-body">
                  <div className="tab-content">
                    {/* Overview Tab */}
                    {activeTab === 'overview' && (
                      <div className="tab-pane active">
                        <div className="row">
                          {/* Server Access and Quick Actions Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-server me-2 text-primary"
                                style={{paddingRight: '0.5rem'}}></i>Server Access</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3" style={{paddingRight: '0.5rem'}}>Quick Actions</h5>
                                <div className="row">
                                  <div className="col-4 mb-3">
                                    <button className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(startModal.title, startModal.body, startModal.footer)}>
                                      <i className="fa fa-power-off fa-2x d-block mb-2 text-success"></i>
                                      Start
                                    </button>
                                  </div>
                                  <div className="col-4 mb-3">
                                    <button className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm">
                                      <i className="fa fa-desktop fa-2x d-block mb-2 text-primary"></i>
                                      Console
                                    </button>
                                  </div>
                                  <div className="col-4 mb-3">
                                    <button className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(createSnapshotModal.title, createSnapshotModal.body, createSnapshotModal.footer)}>
                                      <i className="fa fa-camera fa-2x d-block mb-2 text-secondary"></i>
                                      Snapshot
                                    </button>
                                  </div>
                                </div>
                                
                                <h5 className="text-muted mb-3">Virtual Hardware</h5>
                                <ul className="list-group list-group-flush">

                                 <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-server text-primary me-2"></i> Current Plan:</span>
                                    <span className="text-muted">{server.plan}</span>
                                  </li>


                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                   <span><i className="fa fa-microchip text-primary me-2"></i> vCPU:</span>
                                    <span className="text-muted">{server.hardware.cpu}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-database text-primary me-2"></i> RAM:</span>
                                    <span className="text-muted">{server.hardware.ram}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-hdd-o text-primary me-2"></i> Storage:</span>
                                    <span className="text-muted">{server.hardware.storage}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-wifi text-primary me-2"></i> Bandwidth:</span>
                                    <span className="text-muted">{server.hardware.bandwidth}</span>
                                  </li>
                                </ul>
                                
                                <div className="mt-3">
                                  <button 
                                    className="btn btn-primary w-100" 
                                    onClick={() => openModal(resizeServerModal.title, resizeServerModal.body, resizeServerModal.footer)}
                                  >
                                    <i className="fa fa-expand me-1"></i> Resize Resources
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          {/* Status and Resource Usage Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-info-circle me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Status & Resources</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Server Status</h5>
                                <ul className="list-group list-group-flush mb-4">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                   <span><i className="fa fa-plug text-primary me-2"></i> Power Status:</span>
                                    <span className="badge bg-success">Online</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                   <span><i className="fa fa-plug text-primary me-2"></i> Uptime:</span>
                                    <span className="text-muted">{server.uptime.current}</span>
                                  </li>
                                </ul>
                                
                                <h5 className="text-muted mb-3">Current Resource Usage</h5>
                                <div className="mb-3">
                                  <label className="form-label d-flex justify-content-between">
                                    <span>CPU Usage</span>
                                    <span className="text-muted">{server.virtualResources.cpuUtilization}</span>
                                  </label>
                                  <div className="progress">
                                    <div 
                                      className="progress-bar bg-primary" 
                                      role="progressbar" 
                                      style={{ width: server.virtualResources.cpuUtilization }} 
                                      aria-valuenow={parseInt(server.virtualResources.cpuUtilization)} 
                                      aria-valuemin="0" 
                                      aria-valuemax="100"
                                    ></div>
                                  </div>
                                </div>
                                
                                <div className="mb-3">
                                  <label className="form-label d-flex justify-content-between">
                                    <span>RAM Usage</span>
                                    <span className="text-muted">{server.virtualResources.ramUtilization}</span>
                                  </label>
                                  <div className="progress">
                                    <div 
                                      className="progress-bar bg-success" 
                                      role="progressbar" 
                                      style={{ width: server.virtualResources.ramUtilization }} 
                                      aria-valuenow={parseInt(server.virtualResources.ramUtilization)} 
                                      aria-valuemin="0" 
                                      aria-valuemax="100"
                                    ></div>
                                  </div>
                                </div>
                                
                                <div className="mb-3">
                                  <label className="form-label d-flex justify-content-between">
                                    <span>Disk Usage</span>
                                    <span className="text-muted">{server.virtualResources.diskUtilization}</span>
                                  </label>
                                  <div className="progress">
                                    <div 
                                      className="progress-bar bg-warning" 
                                      role="progressbar" 
                                      style={{ width: server.virtualResources.diskUtilization }} 
                                      aria-valuenow={parseInt(server.virtualResources.diskUtilization)} 
                                      aria-valuemin="0" 
                                      aria-valuemax="100"
                                    ></div>
                                  </div>
                                </div>
                                
                                <h5 className="text-muted mb-3">Location</h5>
                                <ul className="list-group list-group-flush mb-3">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0" >
                                    <span><i className="fa fa-building text-primary me-2"></i> Data Center:</span>
                                    <span className="text-muted">
                                      <i className={`flag flag-${server.location.flag} me-1`} style={{paddingRight: '1rem'}}></i>
                                      {server.location.name}
                                    </span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-map text-primary me-2"></i> Facility:</span>
                                    <span className="text-muted">{server.location.datacenter}</span>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          </div>
                          
                          {/* Billing and Access Card */}
                          <div className="col-xl-4 col-lg-12 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Details & Billing</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Access Details</h5>
                                <ul className="list-group list-group-flush mb-4">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-globe text-primary me-2" ></i> IP Address:</span>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2 text-start" >{server.ipAddress}</span>
                                      <button 
                                        className="btn btn-sm btn-light"  style={{marginLeft:'1rem'}}
                                        onClick={() => copyToClipboard(server.ipAddress)}
                                        title="Copy to clipboard"
                                       
                                      >
                                        <i className="fa fa-copy"></i>
                                      </button>
                                    </div>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-user text-primary me-2"></i> Username:</span>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2 text-start">{server.username} </span>
                                      <button 
                                        className="btn btn-sm btn-light" style={{marginLeft:'1rem'}}
                                        onClick={() => copyToClipboard(server.username)}
                                        title="Copy to clipboard"
                                      >
                                        <i className="fa fa-copy"></i>
                                      </button>
                                    </div>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-key text-primary me-2"></i> Password:</span>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2 text-start">
                                        {showPassword ? server.password : '••••••••••••'}
                                      </span>
                                      <button 
                                        className="btn btn-sm btn-light me-2" style={{marginLeft:'1rem'}}
                                        onClick={togglePassword}
                                      >
                                        <i className={`fa fa-${showPassword ? 'eye-slash' : 'eye'}`}></i>
                                      </button>
                                      <button 
                                        className="btn btn-sm btn-light" style={{marginLeft:'0.5rem'}}
                                        onClick={() => copyToClipboard(server.password)}
                                        title="Copy to clipboard"
                                      >
                                        <i className="fa fa-copy"></i>
                                      </button>
                                    </div>
                                  </li>
                                </ul>
                                
                                <h5 className="text-muted mb-3">Operating System</h5>
<div className="card mb-4" style={{ maxHeight: "100px", overflow: "hidden" }}>
  <div className="card-body p-3 d-flex align-items-center">
    <div style={{
      width: "40px",
      height: "40px",
      backgroundImage: "url('/assets/images/os/ubuntu.png')",
      backgroundSize: "contain",
      backgroundPosition: "center",
      backgroundRepeat: "no-repeat",
      flexShrink: 0,
      marginRight: "15px"
    }}></div>
    
    <div style={{ flex: "1" }}>
      <h6 className="mb-1">Ubuntu Server 22.04 LTS</h6>
      <div className="text-muted">Linux</div>
    </div>
    
    <button 
      className="btn btn-primary ms-auto" 
      style={{ flexShrink: 0 }}
      onClick={() => openModal(reinstallModal.title, reinstallModal.body, reinstallModal.footer)}
    >
      Change
    </button>
  </div>
</div>

  
                                <h5 className="text-muted mb-3 mt-4">Plan Details</h5>
                                <ul className="list-group list-group-flush mb-4">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-money text-primary me-2"></i> Monthly Price:</span>
                                    <span className="text-muted fw-bold">{server.billing.price}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-refresh text-primary me-2"></i> Billing Cycle:</span>
                                    <span className="text-muted">{server.billing.billingCycle}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-calendar text-primary me-2"></i> Next Renewal:</span>
                                    <span className="text-muted">{server.billing.nextRenewal}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-repeat text-primary me-2"></i> Auto-Renewal:</span>
                                    <span className={`badge ${server.billing.autoRenew ? 'bg-success' : 'bg-danger'}`}>
                                      {server.billing.autoRenew ? 'Enabled' : 'Disabled'}
                                    </span>
                                  </li>
                                </ul>
                                
                                <div className="mt-4 d-flex justify-content-between">
                                  <button className="btn btn-danger btn-sm" onClick={() => openModal(terminateModal.title, terminateModal.body, terminateModal.footer)}>
                                    <i className="fa fa-times-circle me-1"></i> Terminate
                                  </button>
      
                                  <div>
                                    <Link 
  to={`/billing/invoices`} 
  className="btn btn-info btn-sm"
  onClick={() => {
    // Scroll to top when the button is clicked
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }}
>
  <i className="fa fa-arrow-circle-left me-1"></i> View Invoices
</Link>
                                    <button className="btn btn-success btn-sm" onClick={() => openModal(renewModal.title, renewModal.body, renewModal.footer)}>
                                      <i className="fa fa-refresh me-1"></i> Renew Service
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Performance Tab */}
                    {activeTab === 'performance' && (
                      <div className="tab-pane active">
                        <div className="row">
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-microchip me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>CPU Usage</h3>
                                <div className="card-options">
                                  <select className="form-control form-control-sm" style={{ minHeight: "35px" }}>
                                    <option>Last 24 hours</option>
                                    <option>Last 7 days</option>
                                    <option>Last 30 days</option>
                                  </select>
                                </div>
                              </div>
                              <div className="card-body">
                                <div style={{ width: '100%', height: 300 }}>
                                  <ResponsiveContainer>
                                    <AreaChart
                                      data={cpuData}
                                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                    >
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis dataKey="time" />
                                      <YAxis />
                                      <Tooltip formatter={(value) => [`${value}%`]} />
                                      <Legend />
                                      <Area 
                                        type="monotone" 
                                        dataKey="usage" 
                                        fill="#0d6efd" 
                                        stroke="#0d6efd" 
                                        name="CPU Usage" 
                                      />
                                    </AreaChart>
                                  </ResponsiveContainer>
                                </div>
                                <div className="mt-4">
                                  <div className="row">
                                    <div className="col-md-4 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Peak Usage</h6>
                                        <h3 className="mb-0">42%</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-4 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Average Usage</h6>
                                        <h3 className="mb-0">18%</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-4 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Current Usage</h6>
                                        <h3 className="mb-0">15%</h3>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-database me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Memory Usage</h3>
                                <div className="card-options">
                                  <select className="form-control form-control-sm" style={{ minHeight: "35px" }}>
                                    <option>Last 24 hours</option>
                                    <option>Last 7 days</option>
                                    <option>Last 30 days</option>
                                  </select>
                                </div>
                              </div>
                              <div className="card-body">
                                <div style={{ width: '100%', height: 300 }}>
                                  <ResponsiveContainer>
                                    <AreaChart
                                      data={memoryData}
                                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                    >
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis dataKey="time" />
                                      <YAxis />
                                      <Tooltip formatter={(value) => [`${value}%`]} />
                                      <Legend />
                                      <Area 
                                        type="monotone" 
                                        dataKey="usage" 
                                        fill="#198754" 
                                        stroke="#198754" 
                                        name="Memory Usage" 
                                      />
                                    </AreaChart>
                                  </ResponsiveContainer>
                                </div>
                                <div className="mt-4">
                                  <div className="row">
                                    <div className="col-md-4 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Peak Usage</h6>
                                        <h3 className="mb-0">52%</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-4 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Average Usage</h6>
                                        <h3 className="mb-0">42%</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-4 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Current Usage</h6>
                                        <h3 className="mb-0">35%</h3>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-hdd-o me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Disk I/O</h3>
                                <div className="card-options">
                                  <select className="form-control form-control-sm" style={{ minHeight: "35px" }}>
                                    <option>Last 24 hours</option>
                                    <option>Last 7 days</option>
                                    <option>Last 30 days</option>
                                  </select>
                                </div>
                              </div>
                              <div className="card-body">
                                <div style={{ width: '100%', height: 300 }}>
                                  <ResponsiveContainer>
                                    <LineChart
                                      data={diskData}
                                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                    >
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis dataKey="time" />
                                      <YAxis />
                                      <Tooltip formatter={(value) => [`${value} MB/s`]} />
                                      <Legend />
                                      <Line 
                                        type="monotone" 
                                        dataKey="read" 
                                        stroke="#0d6efd" 
                                        name="Read" 
                                        strokeWidth={2}
                                      />
                                      <Line 
                                        type="monotone" 
                                        dataKey="write" 
                                        stroke="#dc3545" 
                                        name="Write" 
                                        strokeWidth={2}
                                      />
                                    </LineChart>
                                  </ResponsiveContainer>
                                </div>
                                <div className="mt-4">
                                  <div className="row">
                                    <div className="col-md-4 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Peak Read</h6>
                                        <h3 className="mb-0">25 MB/s</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-4 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Peak Write</h6>
                                        <h3 className="mb-0">12 MB/s</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-4 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Disk Usage</h6>
                                        <h3 className="mb-0">35%</h3>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-tachometer me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Network Traffic</h3>
                                <div className="card-options">
                                  <select className="form-control form-control-sm" style={{ minHeight: "35px" }}>
                                    <option>Last 24 hours</option>
                                    <option>Last 7 days</option>
                                    <option>Last 30 days</option>
                                  </select>
                                </div>
                              </div>
                              <div className="card-body">
                                <div style={{ width: '100%', height: 300 }}>
                                  <ResponsiveContainer>
                                    <LineChart
                                      data={bandwidthData}
                                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                    >
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis dataKey="time" />
                                      <YAxis />
                                      <Tooltip formatter={(value) => [`${value} Mbps`]} />
                                      <Legend />
                                      <Line 
                                        type="monotone" 
                                        dataKey="inbound" 
                                        stroke="#0d6efd" 
                                        name="Inbound" 
                                        strokeWidth={2}
                                      />
                                      <Line 
                                        type="monotone" 
                                        dataKey="outbound" 
                                        stroke="#dc3545" 
                                        name="Outbound" 
                                        strokeWidth={2}
                                      />
                                    </LineChart>
                                  </ResponsiveContainer>
                                </div>
                                <div className="mt-4">
                                  <div className="row">
                                    <div className="col-md-4 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Peak Traffic</h6>
                                        <h3 className="mb-0">4.2 Mbps</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-4 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Data Used</h6>
                                        <h3 className="mb-0">0.8 TB</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-4 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Remaining</h6>
                                        <h3 className="mb-0">2.2 TB</h3>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Network Tab */}
                    {activeTab === 'network' && (
                      <div className="tab-pane active">
                        <div className="row">
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-globe me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Network Configuration</h3>
                              </div>
                              <div className="card-body">
                                <ul className="list-group list-group-flush">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa "></i> Primary IP:</span>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2">{server.network.primaryIp}</span>
                                  
                                    </div>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i></i> Subnet:</span>
                                    <span>{server.network.subnet}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i></i> Gateway:</span>
                                    <span>{server.network.gateway}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i></i> Bandwidth:</span>
                                    <span>{server.network.bandwidth.speed}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i></i> Monthly Traffic:</span>
                                    <span>{server.network.bandwidth.monthly}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i></i> Traffic Used:</span>
                                    <span>{server.network.bandwidth.used}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i></i> Traffic Remaining:</span>
                                    <span>{server.network.bandwidth.remaining}</span>
                                  </li>
                                </ul>
                                
            
                              </div>
                            </div>
                          </div>
                          
                          <div className="col-xl-8 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-globe me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>IP Addresses</h3>
                                <div className="card-options">
                                  <button className="btn btn-sm btn-primary" onClick={() => openModal(ipConfigModal.title, ipConfigModal.body, ipConfigModal.footer)}>
                                    <i className="fa fa-plus me-1"></i> Add IP Address
                                  </button>
                                </div>
                              </div>
                              <div className="card-body">
                                <div className="table-responsive">
                                  <table className="table table-hover">
                                    <thead className="bg-light">
                                      <tr>
                                        <th>IP Address</th>
                                        <th>Type</th>
                                        <th>Reverse DNS</th>
                                        <th>Actions</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {server.network.publicIps.map((ip, index) => (
                                        <tr key={index}>
                                          <td>
                                            <div className="d-flex align-items-center">
                                              <span className="me-2">{ip}</span>
                             
                                            </div>
                                          </td>
                                          <td>{index === 0 ? 'Primary' : 'Secondary'}</td>
                                          <td>server{index + 1}.example.com</td>
                                          <td>
                                            <div className="btn-group" role="group">
                                              <button 
                                                className="btn btn-sm btn-primary" 
                                                onClick={() => {
                                                  // Open IP Configuration Modal
                                                  setModalTitle('Set Reverse DNS');
                                                  setModalBody(
                                                    <div className="form-group">
                                                      <label>Reverse DNS (rDNS) for {ip}</label>
                                                      <input
                                                        type="text"
                                                        className="form-control"
                                                        defaultValue={`server${index + 1}.example.com`}
                                                        placeholder="Enter reverse DNS hostname"
                                                        pattern="^[a-zA-Z0-9-]+\.[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$"
                                                        title="Please enter a valid reverse DNS hostname (e.g., server1.example.com)"
                                                        required
                                                      />
                                                      <small className="form-text text-muted">
                                                        Specify the fully qualified domain name (FQDN) for this IP address.
                                                      </small>
                                                    </div>
                                                  );
                                                  setModalFooter(
                                                    <>
                                                      <button 
                                                        className="btn btn-secondary" 
                                                        onClick={() => setModalShow(false)}
                                                      >
                                                        Cancel
                                                      </button>
                                                      <button 
                                                        className="btn btn-primary" 
                                                        onClick={() => {
                                                          // Here you would typically call an API to update the rDNS
                                                          setModalShow(false);
                                                        }}
                                                      >
                                                        Save rDNS
                                                      </button>
                                                    </>
                                                  );
                                                  setModalShow(true);
                                                }}
                                              >
                                                <i className="fa fa-edit me-1"></i> Set rDNS
                                              </button>
                                              {index !== 0 && (
                                              <button 
                                                className="btn btn-sm btn-danger" 
                                                onClick={() => {
                                                  // Open Remove rDNS confirmation modal
                                                  setModalTitle('Remove Reverse DNS');
                                                  setModalBody(
                                                    <div className="alert alert-warning">
                                                      Are you sure you want to remove the reverse DNS for {ip}?
                                                      <p className="mt-2 text-muted">Current rDNS: server{index + 1}.example.com</p>
                                                    </div>
                                                  );
                                                  setModalFooter(
                                                    <>
                                                      <button 
                                                        className="btn btn-secondary" 
                                                        onClick={() => setModalShow(false)}
                                                      >
                                                        Cancel
                                                      </button>
                                                      <button 
                                                        className="btn btn-danger" 
                                                        onClick={() => {
                                                          // Here you would typically call an API to remove the rDNS
                                                          setModalShow(false);
                                                        }}
                                                      >
                                                        Remove rDNS
                                                      </button>
                                                    </>
                                                  );
                                                  setModalShow(true);
                                                }}
                                              >
                                                <i className="fa fa-trash me-1"></i> Remove rDNS
                                              </button>

                                              )}
                                            </div>
                                          </td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="col-12">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-tachometer me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Network Traffic</h3>
                                <div className="card-options">
                                  <select className="form-control form-control-sm" style={{ minHeight: "35px" }}>
                                    <option>Last 24 hours</option>
                                    <option>Last 7 days</option>
                                    <option>Last 30 days</option>
                                  </select>
                                </div>
                              </div>
                              <div className="card-body">
                                <div style={{ width: '100%', height: 300 }}>
                                  <ResponsiveContainer>
                                    <LineChart
                                      data={bandwidthData}
                                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                    >
                                      <CartesianGrid strokeDasharray="3 3" />
                                      <XAxis dataKey="time" />
                                      <YAxis />
                                      <Tooltip formatter={(value) => [`${value} Mbps`]} />
                                      <Legend />
                                      <Line 
                                        type="monotone" 
                                        dataKey="inbound" 
                                        stroke="#0d6efd" 
                                        activeDot={{ r: 8 }} 
                                        name="Inbound" 
                                        strokeWidth={2}
                                      />
                                      <Line 
                                        type="monotone" 
                                        dataKey="outbound" 
                                        stroke="#dc3545" 
                                        name="Outbound" 
                                        strokeWidth={2}
                                      />
                                    </LineChart>
                                  </ResponsiveContainer>
                                </div>
                                <div className="mt-4">
                                  <h5 className="text-muted mb-3">Network Statistics</h5>
                                  <div className="row">
                                    <div className="col-md-3 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Peak Traffic</h6>
                                        <h3 className="mb-0">4.2 Mbps</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-3 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Average Traffic</h6>
                                        <h3 className="mb-0">1.8 Mbps</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-3 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Monthly Usage</h6>
                                        <h3 className="mb-0">0.8 TB</h3>
                                      </div>
                                    </div>
                                    <div className="col-md-3 col-6">
                                      <div className="card bg-light p-3 text-center h-100">
                                        <h6 className="text-muted mb-2">Bandwidth Plan</h6>
                                        <h3 className="mb-0">3 TB</h3>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Settings Tab */}
                    {activeTab === 'settings' && (
                      <div className="tab-pane active">
                        <div className="row">
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-cog me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Server Settings</h3>
                              </div>
                              <div className="card-body">
                                <form>
                                  <div className="form-group mb-3">
                                    <label className="form-label">Server Label</label>
                                    <input 
                                      type="text" 
                                      className="form-control" 
                                      defaultValue={server.name}
                                      placeholder="Server Label"
                                    />
                                    <small className="form-text text-muted">
                                      This is for your reference only and does not affect the server.
                                    </small>
                                  </div>
                                  
                                  <div className="form-group mb-3">
                                    <label className="form-label">Boot Mode</label>
                                    <select className="form-control">
                                      <option>Normal Boot</option>
                                      <option>Recovery Mode</option>
                                      <option>Rescue Console</option>
                                    </select>
                                  </div>
                                  
                                  <div className="form-group mb-3">
  <label className="form-label">SSH Keys</label>
  <select className="form-control mb-2">
    <option value="">Select SSH Key to Add</option>
    <option value="1">my-macbook-key</option>
    <option value="2">work-laptop-key</option>
    <option value="3">dev-server-key</option>
  </select>
  <div className="d-flex justify-content-between mb-3">
    <button type="button" className="btn btn-sm btn-primary">
      Add New SSH Key
    </button>
    <button type="submit" className="btn btn-primary">Save Settings</button>
  </div>
</div>
</form>
</div>
</div>
</div>
                          
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Billing Settings</h3>
                              </div>
                              <div className="card-body">
                                <form>
                                  <div className="form-group mb-3">
                                    <label className="form-label">Billing Cycle</label>
                                    <select className="form-control">
                                      <option selected>Monthly (€{parseFloat(server.billing.price).toFixed(2)}/mo)</option>
                                      <option>Quarterly (€{(parseFloat(server.billing.price) * 3 * 0.95).toFixed(2)}/3mo - 5% discount)</option>
                                      <option>Semi-Annual (€{(parseFloat(server.billing.price) * 6 * 0.9).toFixed(2)}/6mo - 10% discount)</option>
                                      <option>Annual (€{(parseFloat(server.billing.price) * 12 * 0.85).toFixed(2)}/yr - 15% discount)</option>
                                    </select>
                                  </div>
                                  
                                  <div className="form-group mb-3">
                                    <label className="form-label">Next Renewal</label>
                                    <input 
                                      type="text" 
                                      className="form-control" 
                                      value={server.billing.nextRenewal} 
                                      readOnly
                                    />
                                  </div>
                                  
                                  <div className="form-group form-check mb-3">
                                    <input 
                                      type="checkbox" 
                                      className="form-check-input" 
                                      id="autoRenewCheck" 
                                      checked={server.billing.autoRenew}
                                      style={{ 
                                        marginRight: '5px',
                                        marginLeft: '0',
                                        marginTop: '0',
                                        marginBottom: '0',
                                        padding: '0',
                                        position: 'relative',
                                        float: 'none'
                                      }}
                                    />
                                    <label className="form-check-label" htmlFor="autoRenewCheck">
                                      Enable automatic renewal
                                    </label>
                                    <small className="form-text text-muted d-block">
                                      When disabled, your server will be suspended after the billing period ends unless manually renewed.
                                    </small>
                                  </div>
                                  
<div className="form-group form-check mb-3">
  <input type="checkbox" className="form-check-input" id="useAccountCredit" style={{ 
    marginRight: '5px',
    marginLeft: '0',
    marginTop: '0',
    marginBottom: '0',
    padding: '0',
    position: 'relative',
    float: 'none'
  }} checked />
  <label className="form-check-label" htmlFor="useAccountCredit">
    Use account credit for renewals when available
  </label>
</div>

<div className="d-flex justify-content-end">
  <button type="submit" className="btn btn-primary">Save Billing Settings</button>
</div>
</form>
</div>
</div>
</div>
                          
                          </div>
                    
                      </div>
                    )}
                    
                    {/* Snapshots Tab */}
                    {activeTab === 'snapshots' && (
                      <div className="tab-pane active">
                        <div className="row">
                          <div className="col-12 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-camera me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Server Snapshots</h3>
                                <div className="card-options">
                                  <button className="btn btn-sm btn-primary" onClick={() => openModal(createSnapshotModal.title, createSnapshotModal.body, createSnapshotModal.footer)}>
                                    <i className="fa fa-plus me-1"></i> Create Snapshot
                                  </button>
                                </div>
                              </div>
                              <div className="card-body">
                                <div className="table-responsive">
                                  <table className="table table-hover">
                                    <thead className="bg-light">
                                      <tr>
                                        <th>Name</th>
                                        <th>Created</th>
                                        <th>Size</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <tr>
                                        <td>snapshot-cloud-vm3-2025-03-01</td>
                                        <td>March 1, 2025</td>
                                        <td>12.3 GB</td>
                                        <td><span className="badge bg-success">Available</span></td>
                                        <td>
                                          <div className="btn-group" role="group">
                                            <button className="btn btn-sm btn-primary">
                                              <i className="fa fa-undo me-1"></i> Restore
                                            </button>
                                            <button className="btn btn-sm btn-secondary">
                                              <i className="fa fa-copy me-1"></i> Clone
                                            </button>
                                            <button className="btn btn-sm btn-danger">
                                              <i className="fa fa-trash me-1"></i> Delete
                                            </button>
                                          </div>
                                        </td>
                                      </tr>
                                      <tr>
                                        <td>pre-update-2025-02-15</td>
                                        <td>February 15, 2025</td>
                                        <td>11.8 GB</td>
                                        <td><span className="badge bg-success">Available</span></td>
                                        <td>
                                          <div className="btn-group" role="group">
                                            <button className="btn btn-sm btn-primary">
                                              <i className="fa fa-undo me-1"></i> Restore
                                            </button>
                                            <button className="btn btn-sm btn-secondary">
                                              <i className="fa fa-copy me-1"></i> Clone
                                            </button>
                                            <button className="btn btn-sm btn-danger">
                                              <i className="fa fa-trash me-1"></i> Delete
                                            </button>
                                          </div>
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </div>
                                
                                <div className="alert alert-info mt-3 mb-0">
                                  <i className="fa fa-info-circle me-2"  style={{paddingRight: '0.5rem'}}></i>
                                  <strong>Snapshot Information:</strong>
                                  <ul className="mb-0 mt-2">
                                    <li>Snapshots capture the entire state of your server, including all data on the disk.</li>
                                    <li>You can create up to 5 snapshots per cloud server.</li>
                                    <li>Snapshots are stored for up to 30 days.</li>
                                    <li>Restoring from a snapshot will overwrite all data on the server.</li>
                                  </ul>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Modal for various actions */}
      <MDBModal show={modalShow} setShow={setModalShow} tabIndex='-1'>
        <MDBModalDialog>
          <MDBModalContent>
            <MDBModalHeader>
              <MDBModalTitle>{modalTitle}</MDBModalTitle>
              <button type="button" className="close" onClick={() => setModalShow(false)}>
                <span aria-hidden="true">&times;</span>
              </button>
            </MDBModalHeader>
            <MDBModalBody>
              {modalBody}
            </MDBModalBody>
            <MDBModalFooter>
              {modalFooter}
            </MDBModalFooter>
          </MDBModalContent>
        </MDBModalDialog>
      </MDBModal>
    </>
  );
};

export default CloudServerDetailsView;