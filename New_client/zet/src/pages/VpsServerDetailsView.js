import React, { useState, useEffect, useCallback } from "react";
import { Link, useParams, useNavigate } from "react-router-dom";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { Toast } from 'primereact/toast';
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
} from 'mdb-react-ui-kit';
import {
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
  <PERSON><PERSON>,
  Spinner
} from 'reactstrap';
import VncModal from '../components/VncModal';
import DiscountAlert from "../components/DiscountAlert";
import VpsUpgradeModal from '../components/VpsUpgradeModal';
import VpsIpUpgradeModal from '../components/VpsIpUpgradeModal';
import PaymentModal from './PaymentModal';

const VpsServerDetailsView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const toast = React.useRef(null);
  const [server, setServer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [powerStatus, setPowerStatus] = useState('unknown');
  const [powerLoading, setPowerLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalBody, setModalBody] = useState('');
  const [modalFooter, setModalFooter] = useState('');
  const [usageData, setUsageData] = useState([]);
  const [powerActionLoading, setPowerActionLoading] = useState(false);
  const [isUpdatingAutoRenewal, setIsUpdatingAutoRenewal] = useState(false);
  const [isUpdatingUseCredit, setIsUpdatingUseCredit] = useState(false);
  const [isUpdatingBillingCycle, setIsUpdatingBillingCycle] = useState(false);
  const [powerActionResult, setPowerActionResult] = useState(null);
  const [trafficPeriod, setTrafficPeriod] = useState('24h');
  const [loadingTraffic, setLoadingTraffic] = useState(false);
  const [loadingVnc, setLoadingVnc] = useState(false);
  const [showVncModal, setShowVncModal] = useState(false);
  const [vncDetails, setVncDetails] = useState(null);
  const [availableOSOptions, setAvailableOSOptions] = useState([]);
  const [availableApplications, setAvailableApplications] = useState([]);
  const [selectedApplication, setSelectedApplication] = useState('');
  const [selectedOS, setSelectedOS] = useState('');
  const [reinstallLoading, setReinstallLoading] = useState(false);
  const [reinstallResult, setReinstallResult] = useState(null);
  // Removed terminateConfirmation state as it's no longer needed
  const [terminateLoading, setTerminateLoading] = useState(false);
  const [terminateResult, setTerminateResult] = useState(null);
  const [resetPasswordLoading, setResetPasswordLoading] = useState(false);
  const [resetPasswordResult, setResetPasswordResult] = useState(null);
  const [newHostname, setNewHostname] = useState('');
  const [hostnameUpdateLoading, setHostnameUpdateLoading] = useState(false);
  const [hostnameUpdateResult, setHostnameUpdateResult] = useState(null);
  const [selectedIpId, setSelectedIpId] = useState(null);
  const [networkStats, setNetworkStats] = useState({
    peakTraffic: 0,
    avgTraffic: 0,
    monthlyUsage: 0,
    packetsPerSec: 0
  });
  const [showVpsUpgradeModal, setShowVpsUpgradeModal] = useState(false);
  const toggleVpsUpgradeModal = () => setShowVpsUpgradeModal(!showVpsUpgradeModal);
  const [showVpsIpUpgradeModal, setShowVpsIpUpgradeModal] = useState(false);
  const toggleVpsIpUpgradeModal = () => setShowVpsIpUpgradeModal(!showVpsIpUpgradeModal);
  
  // Payment and renewal state
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showRenewalModal, setShowRenewalModal] = useState(false);
  const [renewalOrderId, setRenewalOrderId] = useState(null);
  const [renewalInvoiceId, setRenewalInvoiceId] = useState(null);
  const [renewalVatRate, setRenewalVatRate] = useState(0);
  const [renewalVatAmount, setRenewalVatAmount] = useState(0);
  const [renewalTotalWithVat, setRenewalTotalWithVat] = useState(0);
  const [renewalLoading, setRenewalLoading] = useState(false);
  const [selectedBillingPeriod, setSelectedBillingPeriod] = useState(1);
  const [renewalModalPrice, setRenewalModalPrice] = useState(0);
  const [termsAccepted, setTermsAccepted] = useState(false);
  
  // Force re-render state to help with visual updates
  const [forceRerender, setForceRerender] = useState(0);

  // Reverse DNS state variables
  const [rdnsRecords, setRdnsRecords] = useState([]);
  const [rdnsLoading, setRdnsLoading] = useState(false);
  const [selectedIp, setSelectedIp] = useState(null);
  const [showRdnsModal, setShowRdnsModal] = useState(false);
  const [rdnsDomain, setRdnsDomain] = useState('');
  const [rdnsUpdateLoading, setRdnsUpdateLoading] = useState(false);
  const [rdnsUpdateResult, setRdnsUpdateResult] = useState(null);

  // Application configuration state
  const [applicationData, setApplicationData] = useState({});

  // Modal toggle function
  const toggleModal = () => {
    console.log('toggleModal called, current showModal:', showModal);
    setShowModal(!showModal);
  };

  // Close modal function
  const closeModal = () => {
    console.log('closeModal called');
    setShowModal(false);
    // Reset any modal-related state
    setPowerActionLoading(false);
    setPowerActionResult(null);
    setReinstallLoading(false);
    setReinstallResult(null);
    setTerminateLoading(false);
    setTerminateResult(null);
    setResetPasswordLoading(false);
    setResetPasswordResult(null);
    setRdnsUpdateLoading(false);
    setRdnsUpdateResult(null);
  };

  // Payment modal toggle functions
  const togglePaymentModal = () => {
    setShowPaymentModal(!showPaymentModal);
  };

  // Renewal modal toggle functions
  const toggleRenewalModal = () => {
    if (showRenewalModal) {
      // Reset when closing
      setTermsAccepted(false);
    } else {
      // Initialize when opening
      const currentPeriod = parseInt(server?.payment_period || server?.billing_cycle || 1);
      setSelectedBillingPeriod(currentPeriod);
      calculateRenewalPrice(currentPeriod);
    }
    setShowRenewalModal(!showRenewalModal);
  };

  // Calculate renewal price based on billing period
  const calculateRenewalPrice = (billingPeriod) => {
    const monthlyPrice = parseFloat(server?.requirement_price || server?.price || server?.monthly_price || 0);
    const totalPrice = monthlyPrice * billingPeriod;
    
    setRenewalModalPrice(Math.round(totalPrice * 100) / 100); // Round to 2 decimal places
    return totalPrice;
  };

  // Handle billing period change in modal
  const handleBillingPeriodChange = (period) => {
    setSelectedBillingPeriod(period);
    calculateRenewalPrice(period);
  };

  // Handle opening renewal modal (instead of direct renewal)
  const handleRenewalService = () => {
    if (!server || !server.id) {
      toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Server information not available',
        life: 3000
      });
      return;
    }
    toggleRenewalModal();
  };

  // Handle actual renewal invoice creation
  const handleConfirmRenewal = async () => {
    if (!termsAccepted) {
      toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Please accept the Terms of Service before proceeding.',
        life: 3000
      });
      return;
    }

    setRenewalLoading(true);
    
    try {
      const token = sessionStorage.getItem('token');
      console.log(`Creating renewal invoice for VPS server ID: ${server.id} with billing period: ${selectedBillingPeriod}`);

      // Create renewal invoice using the correct endpoint
      const response = await fetch('/api.php?f=generate_server_renewal_invoice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          server_id: server.id,
          amount: renewalModalPrice,
          billing_period: selectedBillingPeriod
        })
      });

      const responseText = await response.text();
      console.log("Raw renewal response:", responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        console.error("Failed to parse renewal response:", e);
        throw new Error("Invalid server response");
      }

      if (data.success === true && data.invoice_id) {
        // Extract VAT information from response
        const details = data.details || {};
        const calculatedVatRate = details.tax_rate || 0;
        const calculatedVatAmount = details.tax || 0;
        const calculatedSubtotal = details.subtotal || renewalModalPrice;
        const calculatedTotalWithVat = details.total || renewalModalPrice;

        // Set renewal state
        setRenewalInvoiceId(data.invoice_id);
        setRenewalOrderId(data.order_id || null);
        setRenewalVatRate(calculatedVatRate);
        setRenewalVatAmount(calculatedVatAmount);
        setRenewalTotalWithVat(calculatedTotalWithVat);

        console.log(`Renewal invoice created successfully: Invoice ID=${data.invoice_id}, Subtotal=${calculatedSubtotal}, VAT=${calculatedVatAmount}, Total=${calculatedTotalWithVat}`);

        // Close renewal modal and open payment modal
        toggleRenewalModal();
        setTimeout(() => {
          togglePaymentModal();
        }, 100);
      } else {
        throw new Error(data.error || data.message || 'Failed to create renewal invoice');
      }
    } catch (error) {
      console.error('Error creating renewal invoice:', error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Failed to create renewal invoice: ${error.message}`,
        life: 5000
      });
    } finally {
      setRenewalLoading(false);
    }
  };

  // Handle payment completion for renewals
  const handleRenewalPaymentComplete = () => {
    // Close the payment modal
    togglePaymentModal();

    // Navigate to the invoice page if we have an invoice ID
    if (renewalInvoiceId) {
      navigate(`/billing/invoices/${renewalInvoiceId}`);
    } else {
      // Refresh the server details to update the renewal date
      fetchServerDetails();
    }
  };

  // Helper function to render OS logo (same as CloudOrder.js)
  const renderOSLogo = (osName, size = '24px', marginRight = '10px') => {
    if (!osName) {
      console.log('renderOSLogo: osName is null/undefined');
      // Return a default icon when OS name is not available
      return (
        <i className="fa fa-server" style={{ 
          fontSize: size, 
          color: '#6c757d', 
          marginRight: marginRight,
          width: size,
          height: size,
          display: 'inline-block',
          textAlign: 'center',
          lineHeight: size
        }}></i>
      );
    }
    
    // Clean up the OS name and extract the first word
    const cleanOsName = osName.toString().trim();
    const osImageName = cleanOsName.split(' ')[0].toLowerCase();
    const imagePath = `/assets/images/os/${osImageName}.png`;
    
    console.log(`renderOSLogo: osName="${osName}", cleanOsName="${cleanOsName}", osImageName="${osImageName}", imagePath="${imagePath}"`);
    
    return (
      <img 
        src={imagePath}
        alt={osName} 
        style={{
          width: size, 
          height: size, 
          marginRight: marginRight,
          objectFit: 'contain',
          display: 'block'
        }}
        onError={(e) => {
          console.log(`renderOSLogo: Failed to load ${imagePath}, falling back to linux.png`);
          // Try linux.png first, then fall back to a generic icon
          if (e.target.src.includes('linux.png')) {
            console.log(`renderOSLogo: linux.png also failed, showing generic server icon`);
            // Replace with generic server icon
            const iconElement = document.createElement('i');
            iconElement.className = 'fa fa-server';
            iconElement.style.fontSize = size;
            iconElement.style.color = '#6c757d';
            iconElement.style.marginRight = marginRight;
            iconElement.style.width = size;
            iconElement.style.height = size;
            iconElement.style.display = 'inline-block';
            iconElement.style.textAlign = 'center';
            iconElement.style.lineHeight = size;
            e.target.parentNode.replaceChild(iconElement, e.target);
          } else {
            e.target.src = '/assets/images/os/linux.png';
          }
        }}
        onLoad={() => {
          console.log(`renderOSLogo: Successfully loaded ${imagePath}`);
        }}
      />
    );
  };

  // Helper function to render application logo (same as CloudOrder.js)
  const renderApplicationLogo = (app, size = '24px', marginRight = '10px') => {
    if (!app) return null;

    return (
      <>
        {app.icon ? (
          <img 
            src={app.icon} 
            alt={app.name} 
            style={{
              width: size, 
              height: size, 
              marginRight: marginRight, 
              objectFit: 'contain'
            }} 
            onError={(e) => {
              e.target.style.display = 'none';
              e.target.nextElementSibling.style.display = 'inline-block';
            }}
          />
        ) : null}
        {!app.icon && (
          <i className="fa fa-cube" style={{ 
            fontSize: '1.25rem', 
            color: '#007bff', 
            marginRight: marginRight 
          }}></i>
        )}
      </>
    );
  };

  // Handle application selection change
  const handleApplicationChange = (applicationId) => {
    console.log('handleApplicationChange called with:', applicationId);
    console.log('Current selectedApplication before change:', selectedApplication);
    setSelectedApplication(applicationId);
    setForceRerender(prev => prev + 1);
    // Reset application data when changing applications
    setApplicationData({});
    console.log('selectedApplication should now be:', applicationId);
  };

  const fetchAvailableOSOptions = useCallback(async () => {
    try {
      const token = sessionStorage.getItem('token');
      console.log(`Fetching OS options for server ID: ${id}`);

      setAvailableOSOptions([]);

      const response = await fetch(`/api.php?f=vps_get_os_options&id=${id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();

      if (data.error === 0 && Array.isArray(data.os_options)) {
        console.log(`Loaded ${data.os_options.length} OS options from database`);

        if (data.os_options.length > 0) {
          setAvailableOSOptions(data.os_options);

          // Set default selected OS - prefer Ubuntu 22.04 if available, otherwise first option
          const ubuntu = data.os_options.find(os =>
            os.name.toLowerCase().includes('ubuntu') && os.name.includes('22.04')
          );

          if (ubuntu) {
            setSelectedOS(ubuntu.id);
          } else {
            setSelectedOS(data.os_options[0].id);
          }
        } else {
          console.error("No OS options available in database");
        }
      } else {
        console.error("Error fetching OS options:", data.message || "Invalid response format");
      }
    } catch (error) {
      console.error("Error fetching OS options:", error);
    }
  }, [id]);

  const fetchAvailableApplications = useCallback(async () => {
    try {
      const token = sessionStorage.getItem('token');
      console.log(`Fetching applications for server ID: ${id}`);

      const response = await fetch(`/api.php?f=vps_get_applications&id=${id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();

      if (data.error === 0 && Array.isArray(data.applications)) {
        console.log(`Loaded ${data.applications.length} applications from SolusVM`);
        setAvailableApplications(data.applications);
        
        // Set default to "None" (empty selection)
        setSelectedApplication('');
        setApplicationData({});
      } else {
        console.error("Error fetching applications:", data.message || "Invalid response format");
        // Don't set empty array on error - just log the error and keep current state
        // This prevents breaking the UI if the API is temporarily unavailable
        if (availableApplications.length === 0) {
          setAvailableApplications([]);
        }
      }
    } catch (error) {
      console.error("Error fetching applications:", error);
      // Don't set empty array on error - just log the error and keep current state
      if (availableApplications.length === 0) {
        setAvailableApplications([]);
      }
    }
  }, [id, availableApplications.length]);

const handleVpsIpUpgradeSuccess = (data) => {
  console.log('VPS IP upgrade invoice generated successfully:', data);
  
  const invoiceId = data.invoice_id || null;
  
  // Refresh server details to get updated information
  fetchServerDetails();
  
  // Suggest to the user to check their invoices
  setTimeout(() => {
    const goToInvoices = window.confirm("Would you like to view your invoices to complete the payment?");
    if (goToInvoices) {
      // Navigate to specific invoice if available, otherwise to invoices list
      window.location.href = invoiceId ? `/billing/invoices/${invoiceId}` : `/billing/invoices/`;
    }
  }, 500);
};

// Update the handleReinstallOS function
const handleReinstallOS = async () => {
  if (!selectedOS) {
    toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Please select an operating system',
        life: 3000
      });
    return;
  }

  // Double confirmation
  if (!confirm('Are you sure you want to reinstall the OS? This will delete all data on the server and cause downtime of 10-20 minutes.')) {
    return;
  }

  setReinstallLoading(true);
  setReinstallResult(null);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Reinstalling server ID: ${id} with OS: ${selectedOS}`);

    const selectedOSName = availableOSOptions.find(os => os.id === selectedOS)?.name || selectedOS;

    // First update the UI with "in progress" indicator


    // Prepare the request body
    const requestBody = {
      token,
      server_id: id,
      os_id: selectedOS,
      hostname: server.hostname // Preserve current hostname
    };

    // Add application data if an application is selected
    if (selectedApplication) {
      requestBody.application_id = selectedApplication;
      
      // Always send application_data as an array when application is selected
      if (Object.keys(applicationData).length > 0) {
        // Convert object to array format expected by API
        requestBody.application_data = Object.entries(applicationData).map(([key, value]) => ({
          key: key,
          value: value
        }));
      } else {
        // Send empty array if no configuration data
        requestBody.application_data = [];
      }
      
      console.log(`Including application data - ID: ${selectedApplication}, Config:`, requestBody.application_data);
    }

    console.log('Sending reinstall request with data:', requestBody);

    const response = await fetch(`/api.php?f=vps_reinstall_os`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    const data = await response.json();

    // Set final result
    setReinstallResult({
      error: data.error,
      message: data.message || (data.error === 0 ? 'Reinstallation started successfully' : 'Failed to reinstall OS'),
      details: data.details || null,
      processing: false
    });

    if (data.error === 0) {
      // Success
      console.log(`Server reinstall command successful:`, data);

      // Close modal after success with a delay
      setTimeout(() => {
        setShowModal(false);
        setReinstallResult(null);
        setReinstallLoading(false);
      }, 5000);

      // Refresh server data after a delay
      setTimeout(() => {
        fetchServerDetails();
      }, 8000);
    }
  } catch (error) {
    console.error(`Error reinstalling OS:`, error);

    // Set error result for display
    setReinstallResult({
      error: 1,
      message: `Failed to reinstall OS: ${error.message}`,
      details: error.stack,
      processing: false
    });
  } finally {
    setReinstallLoading(false);
  }
};

  // Add useEffect to fetch OS options when component mounts
  useEffect(() => {
    if (server) {
      fetchAvailableOSOptions();
      fetchAvailableApplications(); // NEW: Fetch applications from SolusVM
    }
  }, [server, fetchAvailableOSOptions, fetchAvailableApplications]);

  // Update modal content when selectedOS or selectedApplication changes
  useEffect(() => {
    if (showModal && modalTitle === 'Reinstall Operating System') {
      const modalContent = getReinstallModalContent();
      setModalBody(modalContent.body);
      setModalFooter(modalContent.footer);
    }
  }, [selectedOS, selectedApplication, applicationData, availableOSOptions, availableApplications, reinstallLoading, reinstallResult, showModal, modalTitle]);

  // Function to open modal with custom content
  const openModal = (title, body, footer) => {
    // Reset power action state when opening a new modal
    setPowerActionLoading(false);
    setPowerActionResult(null);

    setModalTitle(title);
    setModalBody(body);
    setModalFooter(footer);
    setShowModal(true);
  };

  const handleVpsUpgradeSuccess = (data) => {
    console.log('VPS upgrade invoice generated successfully:', data);
    
    const invoiceId = data.invoice_id || null;
    
    // Refresh server details to get updated information
    fetchServerDetails();

    // Suggest to the user to check their invoices
    setTimeout(() => {
      const goToInvoices = window.confirm("Would you like to view your invoices to complete the payment?");
      if (goToInvoices) {
        // Navigate to specific invoice if available, otherwise to invoices list
        window.location.href = invoiceId ? `/billing/invoices/${invoiceId}` : `/billing/invoices/`;
      }
    }, 500);
  };
  
  const renderApplicationConfig = () => {
    if (!selectedApplication) return null;

    const selectedApp = availableApplications.find(app => app.id === selectedApplication);
    if (!selectedApp || !selectedApp.json_schema) return null;

    try {
      const schema = JSON.parse(selectedApp.json_schema);
      
      // Only show configuration if there are actual properties to configure
      if (!schema.properties || Object.keys(schema.properties).length === 0) {
        return null;
      }
      
      return (
        <div className="mt-3">
          <div className="card">
            <div className="card-header bg-light">
              <h6 className="card-title mb-0">
                <i className="fa fa-cogs me-2"></i>
                Application Configuration
              </h6>
            </div>
            <div className="card-body">
              {Object.entries(schema.properties).map(([key, property]) => (
                <div key={key} className="mb-3">
                  <label className="form-label">{property.title || key}:</label>
                  {property.type === 'string' && property.format === 'password' ? (
                    <input
                      type="password"
                      className="form-control"
                      placeholder={property.description || ''}
                      value={applicationData[key] || ''}
                      onChange={(e) => setApplicationData(prev => ({
                        ...prev,
                        [key]: e.target.value
                      }))}
                    />
                  ) : property.type === 'boolean' ? (
                    <select
                      className="form-control"
                      value={applicationData[key] || 'false'}
                      onChange={(e) => setApplicationData(prev => ({
                        ...prev,
                        [key]: e.target.value === 'true'
                      }))}
                    >
                      <option value="false">No</option>
                      <option value="true">Yes</option>
                    </select>
                  ) : (
                    <input
                      type="text"
                      className="form-control"
                      placeholder={property.description || ''}
                      value={applicationData[key] || ''}
                      onChange={(e) => setApplicationData(prev => ({
                        ...prev,
                        [key]: e.target.value
                      }))}
                    />
                  )}
                  {property.description && (
                    <small className="form-text text-muted">{property.description}</small>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      );
    } catch (e) {
      console.error('Error rendering application config:', e);
      return null;
    }
  };

  // Function to handle server power actions
  const handlePowerAction = async (action) => {
    if (!id) {
      toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Server ID is not available',
        life: 3000
      });
      return;
    }

    setPowerActionLoading(true);
    try {
      const token = sessionStorage.getItem('token');
      console.log(`Sending power action ${action} for server ID: ${id}`);

      const response = await fetch(`/api.php?f=vps_power_action`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          server_id: id,
          action: action
        })
      });

      const data = await response.json();

      // Set the action result for display in the modal
      setPowerActionResult(data);

      if (data.error === 0) {
        // Success
        console.log(`Server ${action} command successful:`, data);

        // Update power status immediately for better UI feedback
        if (action === 'start') {
          console.log(`Setting power status to 'online' after start action`);
          setPowerStatus('online');
        }
        else if (action === 'stop') {
          console.log(`Setting power status to 'offline' after stop action`);
          setPowerStatus('offline');
        }
        else if (action === 'restart') {
          console.log(`Setting power status to 'restarting' after restart action`);
          setPowerStatus('restarting');
        }

        // Schedule multiple status checks to confirm the status change
        const checkIntervals = [5000, 10000, 20000, 30000]; // 5s, 10s, 20s, 30s

        checkIntervals.forEach(interval => {
          setTimeout(() => {
            console.log(`Checking server status after ${interval/1000}s`);
            checkServerPowerStatus();
          }, interval);
        });

        // For restart action, set a final check to ensure we're back online
        if (action === 'restart') {
          setTimeout(() => {
            console.log(`Final check after restart - ensuring we're online`);
            if (powerStatus === 'restarting') {
              console.log(`Setting power status to 'online' after restart timeout`);
              setPowerStatus('online');
            }
          }, 35000);
        }

        // Close modal after success with a short delay
        setTimeout(() => {
          setShowModal(false);
          setPowerActionResult(null);
          setPowerActionLoading(false);
        }, 2000);

        // Refresh server data after a short delay
        setTimeout(() => {
          fetchServerDetails();
        }, 5000);
      }
    } catch (error) {
      console.error(`Error sending ${action} command:`, error);

      // Set error result for display
      setPowerActionResult({
        error: 1,
        message: `Failed to ${action} server: ${error.message}`,
        details: error.stack
      });
    } finally {
      setPowerActionLoading(false);
    }
  };

  // Function to check server power status
  const checkServerPowerStatus = async () => {
    // Use the ID from URL params directly, don't depend on server object being loaded
    if (!id) return;

    setPowerLoading(true);
    try {
      const token = sessionStorage.getItem('token');

      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      console.log(`Checking server power status for ID: ${id}`);

      // Use the server details endpoint since it contains the status
      const response = await fetch(`/api.php?f=vps_server_details&id=${id}&_=${timestamp}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();
      console.log('Server status API response:', data);

      if (data.error === 0 && data.server && data.server.status) {
        // Map server status to power status
        const status = (data.server.status || '').toLowerCase();
        let powerState = 'unknown';

        // Map various status values to our standardized states
        if (status.includes('online') || status.includes('active') || status === 'running' || status === 'on' || status === 'started') {
          powerState = 'online';
        } else if (status.includes('offline') || status === 'stopped' || status === 'off') {
          powerState = 'offline';
        } else if (status.includes('restart') || status.includes('reboot')) {
          powerState = 'restarting';
        }

        console.log(`Mapping status "${status}" to power state "${powerState}"`)

        setPowerStatus(powerState);
        console.log(`Server status from API check: "${status}", mapped to power status: "${powerState}"`);

        // Update the server object if it's different
        if (server && JSON.stringify(server) !== JSON.stringify(data.server)) {
          setServer(data.server);
        }
      } else {
        console.error('Error checking power status:', data);
        setPowerStatus('unknown');
      }
    } catch (error) {
      console.error('Error checking server power status:', error);
      setPowerStatus('unknown');
    } finally {
      setPowerLoading(false);
    }
  };

  // Function to fetch server details
  const fetchServerDetails = useCallback(async () => {
    setLoading(true);
    try {
      const token = sessionStorage.getItem('token');
      const response = await fetch(`/api.php?f=vps_server_details&id=${id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();
      if (data.error === 0 && data.server) {
        console.log('Server data from API:', data.server);
        console.log('Location data:', {
          location: data.server.location,
          location_code: data.server.location_code,
          location_flag: data.server.location?.flag
        });
        
        // Log billing-related data for debugging
        console.log('Billing data from API:', {
          payment_period: data.server.payment_period,
          auto_renewal: data.server.auto_renewal,
          use_credit: data.server.use_credit,
          requirement_price: data.server.requirement_price,
          price: data.server.price,
          monthly_price: data.server.monthly_price
        });
        
        // Ensure payment_period is parsed as an integer if it exists
        if (data.server.payment_period) {
          data.server.payment_period = parseInt(data.server.payment_period);
        } else {
          // Default to monthly (1) if not provided
          data.server.payment_period = 1;
        }
        
        // Ensure auto_renewal is a boolean
        data.server.auto_renewal = data.server.auto_renewal === 1 || 
                                  data.server.auto_renewal === '1' || 
                                  data.server.auto_renewal === true;
        
        // Ensure use_credit is a boolean
        data.server.use_credit = data.server.use_credit === 1 || 
                                data.server.use_credit === '1' || 
                                data.server.use_credit === true;
        
        setServer(data.server);

        // Map server status to power status
        const status = (data.server.status || '').toLowerCase();
        let powerState = 'unknown';

        // Map various status values to our standardized states
        if (status.includes('online') || status.includes('active') || status === 'running' || status === 'on' || status === 'started') {
          powerState = 'online';
        } else if (status.includes('offline') || status === 'stopped' || status === 'off') {
          powerState = 'offline';
        } else if (status.includes('restart') || status.includes('reboot')) {
          powerState = 'restarting';
        }

        console.log(`Mapping status "${status}" to power state "${powerState}"`)

        setPowerStatus(powerState);
        console.log(`Server status from API: "${status}", mapped to power status: "${powerState}"`);

        // Set usage data from API response
        if (data.usage) {
          setUsageData(data.usage);
        }
      } else {
        console.error("Error fetching VPS server details:", data.message || "Unknown error");
      }
    } catch (error) {
      console.error("Error fetching VPS server details:", error);
    } finally {
      setLoading(false);
    }
  }, [id]);

  // Fetch traffic data
  const fetchTrafficData = useCallback(async (period = '24h') => {
    if (!server || !server.id) return;

    setLoadingTraffic(true);
    try {
      const token = sessionStorage.getItem('token');
      console.log(`Fetching traffic data for server ID: ${server.id}, period: ${period}`);

      const response = await fetch(`/api.php?f=vps_traffic_data&id=${server.id}&period=${period}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();
      console.log('Traffic data response:', data);

      if (data.error === 0 && data.traffic) {
        console.log(`Received ${data.traffic.length} traffic data points`);
        setUsageData(data.traffic);
        calculateNetworkStats(data.traffic);
      } else {
        console.error('Error fetching traffic data:', data.message || 'Unknown error');
        // If we get an error, set empty data to avoid displaying stale data
        setUsageData([]);
        setNetworkStats({
          peakTraffic: 0,
          avgTraffic: 0,
          monthlyUsage: 0,
          packetsPerSec: 0
        });
      }
    } catch (error) {
      console.error('Error fetching traffic data:', error);
      // If we get an error, set empty data to avoid displaying stale data
      setUsageData([]);
      setNetworkStats({
        peakTraffic: 0,
        avgTraffic: 0,
        monthlyUsage: 0,
        packetsPerSec: 0
      });
    } finally {
      setLoadingTraffic(false);
    }
  }, [server]);

  // Calculate network statistics
  const calculateNetworkStats = (data) => {
    if (!data || data.length === 0) {
      setNetworkStats({
        peakTraffic: 0,
        avgTraffic: 0,
        monthlyUsage: 0,
        packetsPerSec: 0
      });
      return;
    }

    // Calculate peak traffic (highest network value)
    let peakTraffic = 0;
    let totalTraffic = 0;
    let totalPackets = 0;

    data.forEach(point => {
      if (point.network > peakTraffic) {
        peakTraffic = point.network;
      }
      totalTraffic += point.network;

      // Calculate packets if available
      if (point.read_kb && point.write_kb) {
        // Estimate packets based on KB/s (rough approximation)
        const estimatedPackets = (point.read_kb + point.write_kb) / 1.5; // Assuming average packet size
        totalPackets += estimatedPackets;
      }
    });

    // Average traffic in Mbps
    const avgTraffic = parseFloat((totalTraffic / data.length).toFixed(1));

    // Monthly usage in GB (approximate)
    const monthlyUsage = parseFloat((avgTraffic * 60*60*24*30 / 8 / 1024).toFixed(1));

    // Average packets per second
    let avgPacketsPerSec = 0;
    if (totalPackets > 0) {
      avgPacketsPerSec = Math.round(totalPackets / data.length);
    } else {
      // Fallback calculation if packet data not available
      avgPacketsPerSec = Math.round(avgTraffic * 100); // Example scaling factor
    }

    setNetworkStats({
      peakTraffic: parseFloat(peakTraffic.toFixed(1)),
      avgTraffic,
      monthlyUsage,
      packetsPerSec: avgPacketsPerSec
    });
  };

  // Function to toggle VNC modal
  const toggleVncModal = () => {
    setShowVncModal(!showVncModal);
  };

  // Function to handle VNC console connection
  const handleVncConnection = async () => {
    if (!id) {
      toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Server ID is not available',
        life: 3000
      });
      return;
    }

    setLoadingVnc(true);
    try {
      const token = sessionStorage.getItem('token');
      console.log(`Fetching VNC details for server ID: ${id}`);

      const response = await fetch(`/api.php?f=vps_vnc_details&id=${id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const data = await response.json();

      if (data.error === 0) {
        console.log('VNC details retrieved successfully:', data);

        // Store VNC details and open modal
        setVncDetails(data.vnc_details || {
          fallback_url: `https://virt.zetservers.com/console.php?vserverid=${id}`
        });
        setShowVncModal(true);

        // Log activity
        console.log(`VNC console opened for server ID: ${id}`);
      } else {
        console.error('Error fetching VNC details:', data.message || 'Unknown error');

        // Still open the modal but with fallback URL
        setVncDetails({
          fallback_url: `https://virt.zetservers.com/console.php?vserverid=${id}`
        });
        setShowVncModal(true);
      }
    } catch (error) {
      console.error('Error connecting to VNC console:', error);

      // Still open the modal but with fallback URL
      setVncDetails({
        fallback_url: `https://virt.zetservers.com/console.php?vserverid=${id}`
      });
      setShowVncModal(true);
    } finally {
      setLoadingVnc(false);
    }
  };

  // Handle period change from dropdown
  const handlePeriodChange = (e) => {
    const newPeriod = e.target.value;
    console.log(`Changing traffic period to: ${newPeriod}`);
    setTrafficPeriod(newPeriod);
    fetchTrafficData(newPeriod);
  };

  // Function to fetch reverse DNS records
// Function to fetch reverse DNS records
// Function to fetch reverse DNS records
const fetchRdnsRecords = useCallback(async (ip) => {
  setRdnsLoading(true);
  try {
    const token = sessionStorage.getItem('token');
    console.log(`Fetching reverse DNS for IP: ${ip}`);

    // First we need to get server details to find the IP ID
    const serverResponse = await fetch(`/api.php?f=vps_server_details&id=${id}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify({ token })
    });

    const serverData = await serverResponse.json();
    let ipId = null;

    // Find the IP ID for the given IP address
    if (serverData.error === 0 && serverData.server && serverData.server.ip_addresses) {
      // SolusVM may return IP addresses in different formats, check both structures
      if (serverData.server.ip_addresses.ipv4) {
        // Format: { ipv4: [ { ip, gateway, netmask, id }, ... ] }
        const ipObj = serverData.server.ip_addresses.ipv4.find(addr => addr.ip === ip);
        if (ipObj && ipObj.id) {
          ipId = ipObj.id;
          console.log(`Found IP ID ${ipId} for IP ${ip}`);
        }
      } else if (Array.isArray(serverData.server.ip_addresses)) {
        // Format: [ { address, type, id }, ... ]
        const ipObj = serverData.server.ip_addresses.find(addr => 
          addr.address === ip || addr.ip === ip
        );
        if (ipObj && ipObj.id) {
          ipId = ipObj.id;
          console.log(`Found IP ID ${ipId} for IP ${ip}`);
        }
      }
    }

    // If we found the IP ID, use it to fetch RDNS records
    if (ipId) {
      const response = await fetch(`/api.php?f=get_reverse_dns`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({
          token,
          ip_id: ipId,
          domain: 'placeholder.domain.com' // Required by SolusVM API
        })
      });

      const data = await response.json();

      if (data.error === 0 && data.data) {
        console.log('Reverse DNS records:', data.data);
        setRdnsRecords(data.data);
        
        // Store the IP ID for later use
        if (data.data.length > 0 && data.data[0].id) {
          setSelectedIpId(data.data[0].id);
        } else {
          setSelectedIpId(ipId); // Store the IP ID we found earlier
        }
        
        return data.data;
      }
    } else {
      console.log(`Could not find IP ID for ${ip}, trying direct IP lookup`);
      
      // Fallback to direct IP lookup if we couldn't find the IP ID
      const response = await fetch(`/api.php?f=get_reverse_dns`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({
          token,
          ip: ip,
          domain: 'placeholder.domain.com' // Required by SolusVM API
        })
      });

      const data = await response.json();

      if (data.error === 0 && data.data) {
        console.log('Reverse DNS records:', data.data);
        setRdnsRecords(data.data);
        
        // Store the IP ID if available
        if (data.data.length > 0 && data.data[0].id) {
          setSelectedIpId(data.data[0].id);
        }
        
        return data.data;
      }
    }
    
    console.error('Error fetching reverse DNS records: No data returned');
    setRdnsRecords([]);
    return [];
  } catch (error) {
    console.error('Error fetching reverse DNS records:', error);
    setRdnsRecords([]);
    return [];
  } finally {
    setRdnsLoading(false);
  }
}, [id]);

// Function to create or update reverse DNS
const handleRdnsUpdate = async () => {
  if (!rdnsDomain) {
    toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Please enter a valid domain name',
        life: 3000
      });
    return;
  }

  setRdnsUpdateLoading(true);
  setRdnsUpdateResult(null);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Updating reverse DNS for IP: ${selectedIp} to domain: ${rdnsDomain}`);

    // Use the direct RDNS update endpoint which doesn't require an IP ID
    const response = await fetch(`/api.php?f=direct_rdns_update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        ip: selectedIp,
        domain: rdnsDomain
      })
    });

    const data = await response.json();
    console.log("Direct RDNS update response:", data);
    setRdnsUpdateResult(data);

    if (data.error === 0) {
      console.log("Reverse DNS update successful!");

      // Close modal after success with a delay
      setTimeout(() => {
        closeRdnsModal();

        // Refresh server details to get updated DNS info
        fetchServerDetails();
      }, 2000);
    } else {
      console.error("Reverse DNS update failed:", data.message);
    }
  } catch (error) {
    console.error(`Error updating reverse DNS:`, error);
    setRdnsUpdateResult({
      error: 1,
      message: `Failed to update reverse DNS: ${error.message}`,
      details: error.stack
    });
  } finally {
    setRdnsUpdateLoading(false);
  }
};

// Function to directly update reverse DNS without needing IP ID
const handleDirectRdnsUpdate = async () => {
  if (!rdnsDomain) {
    toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Please enter a valid domain name',
        life: 3000
      });
    return;
  }

  setRdnsUpdateLoading(true);
  setRdnsUpdateResult(null);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Directly updating reverse DNS for IP: ${selectedIp} to domain: ${rdnsDomain}`);
    
    const response = await fetch(`/api.php?f=direct_rdns_update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        ip: selectedIp,
        domain: rdnsDomain
      })
    });

    const data = await response.json();
    console.log(`Direct RDNS update response:`, data);
    setRdnsUpdateResult(data);

    if (data.error === 0) {
      console.log(`Direct reverse DNS update successful!`);

      // Close modal after success with a delay
      setTimeout(() => {
        closeRdnsModal();

        // Refresh server details to get updated DNS info
        fetchServerDetails();
      }, 2000);
    } else {
      console.error(`Direct RDNS update failed:`, data.message || "Unknown error");
    }
  } catch (error) {
    console.error(`Error in direct RDNS update:`, error);
    setRdnsUpdateResult({
      error: 1,
      message: `Failed to update reverse DNS: ${error.message}`,
      details: error.stack
    });
  } finally {
    setRdnsUpdateLoading(false);
  }
};

  // Function to delete reverse DNS
// Function to delete reverse DNS
const handleRdnsDelete = async (id) => {
  if (!confirm('Are you sure you want to delete this reverse DNS record?')) {
    return;
  }

  setRdnsUpdateLoading(true);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Deleting reverse DNS for IP: ${selectedIp}`);

    // Use the direct endpoint for deletion too
    const response = await fetch(`/api.php?f=direct_rdns_delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        ip: selectedIp
      })
    });

    const data = await response.json();
    console.log("RDNS delete response:", data);

    if (data.error === 0) {
      console.log("Reverse DNS deletion successful");
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Reverse DNS record deleted successfully',
        life: 3000
      });

      // Refresh server details to get updated DNS info
      fetchServerDetails();
    } else {
      console.error("Error deleting reverse DNS:", data.message || 'Unknown error');
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Failed to delete reverse DNS: ${data.message || 'Unknown error'}`,
        life: 5000
      });
    }
  } catch (error) {
    console.error('Error deleting reverse DNS:', error);
    toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Failed to delete reverse DNS: ${error.message}`,
        life: 5000
      });
  } finally {
    setRdnsUpdateLoading(false);
  }
};

  // Function to open the reverse DNS modal
// Function to open the reverse DNS modal
const openRdnsModal = (ip, currentDomain = '', ipId = null) => {
  setSelectedIp(ip);
  setSelectedIpId(ipId);
  setRdnsDomain(currentDomain);
  setRdnsUpdateResult(null);
  setShowRdnsModal(true);
};

// Function to close the rDNS modal
const closeRdnsModal = () => {
  setShowRdnsModal(false);
  setRdnsUpdateLoading(false);
  setRdnsUpdateResult(null);
};

  // Fetch server details on component mount
  useEffect(() => {
    fetchServerDetails();
  }, [fetchServerDetails]);

  // Initialize newHostname when server data is loaded
  useEffect(() => {
    if (server && (server.hostname || server.name)) {
      setNewHostname(server.hostname || server.name);
    }
  }, [server]);

  // Fetch reverse DNS records when server data is loaded
  useEffect(() => {
    if (server && server.main_ip) {
      fetchRdnsRecords(server.main_ip);
    }
  }, [server, fetchRdnsRecords]);

  // Check power status when component mounts and when server data is loaded
  useEffect(() => {
    // Check power status immediately
    checkServerPowerStatus();

    // Set up interval to check power status every 30 seconds
    const intervalId = setInterval(checkServerPowerStatus, 30000);

    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, [id]); // Only re-run if the ID changes

  // Load traffic data when server changes or tab changes
  useEffect(() => {
    if (server && server.id && activeTab === 'network') {
      console.log(`Network tab activated, fetching traffic data with period: ${trafficPeriod}`);
      fetchTrafficData(trafficPeriod);
    }
  }, [server, activeTab, fetchTrafficData, trafficPeriod]);

  // Toggle password visibility
  const togglePassword = () => {
    setShowPassword(!showPassword);
  };

  // Copy text to clipboard
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    if (toast.current) {
      toast.current.show({
        severity: 'success',
        summary: 'Copied!',
        detail: 'Copied to clipboard',
        life: 2000
      });
    }
  };

  // Function to get billing cycle name
  const getBillingCycleName = (cycle) => {
    // Handle both string and numeric values
    const cycleValue = String(cycle).toLowerCase();
    
    switch (cycleValue) {
      case '3':
      case 'quarterly':
        return 'Quarterly';
      case '6':
      case 'semi-annual':
        return 'Semi-Annual';
      case '12':
      case 'annual':
        return 'Annual';
      case '1':
      default:
        return 'Monthly';
    }
  };

  // Modal content for power actions
  const startModal = {
    title: 'Start Server',
    body: (
      <div>
        <div className="alert alert-warning mb-3">
          <i className="fa fa-info-circle me-2"></i>
          This action may take up to 2 minutes. If the server is not online within this time frame, please check the console for further details.
        </div>

        {powerActionLoading && (
          <div className="alert alert-info mt-3">
            <div className="d-flex align-items-center">
              <div className="spinner-border spinner-border-sm me-2" role="status"></div>
              Sending power on command to server...
            </div>
          </div>
        )}

        {powerActionResult && (
          <div className={`alert ${powerActionResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
            <i className={`fa ${powerActionResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
            {powerActionResult.message}
            {powerActionResult.details && (
              <div className="mt-2 small">
                <strong>Details:</strong><br />
                <pre className="mb-0">{powerActionResult.details}</pre>
              </div>
            )}
          </div>
        )}
      </div>
    ),
    footer: (
      <div>
        <button className="btn btn-secondary" onClick={closeModal}>Cancel</button>
        <button
          className="btn btn-success ms-2"
          onClick={() => handlePowerAction('start')}
          disabled={powerActionLoading || powerStatus === 'online'}
        >
          {powerActionLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Starting...
            </>
          ) : 'Start Server'}
        </button>
      </div>
    )
  };

  const stopModal = {
    title: 'Stop Server',
    body: (
      <div>
        <div className="alert alert-warning mb-3">
          <i className="fa fa-info-circle me-2"></i>
          This action will shut down your server, and you will need to manually restart it afterward.
        </div>
        <div className="alert alert-danger mt-3 mb-0">
          <i className="fa fa-exclamation-triangle me-2"></i>
          <strong>WARNING:</strong> This will perform a hard shutdown, which may cause data loss if applications are running.
        </div>

        {powerActionLoading && (
          <div className="alert alert-info mt-3">
            <div className="d-flex align-items-center">
              <div className="spinner-border spinner-border-sm me-2" role="status"></div>
              Sending shutdown command to server...
            </div>
          </div>
        )}

        {powerActionResult && (
          <div className={`alert ${powerActionResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
            <i className={`fa ${powerActionResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
            {powerActionResult.message}
            {powerActionResult.details && (
              <div className="mt-2 small">
                <strong>Details:</strong><br />
                <pre className="mb-0">{powerActionResult.details}</pre>
              </div>
            )}
          </div>
        )}
      </div>
    ),
    footer: (
      <div>
        <button className="btn btn-secondary" onClick={closeModal}>Cancel</button>
        <button
          className="btn btn-danger ms-2"
          onClick={() => handlePowerAction('stop')}
          disabled={powerActionLoading || powerStatus === 'offline'}
        >
          {powerActionLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Stopping...
            </>
          ) : 'Stop Server'}
        </button>
      </div>
    )
  };

  const rebootModal = {
    title: 'Reboot Server',
    body: (
      <div>
        <div className="alert alert-warning mb-3">
          <i className="fa fa-info-circle me-2"></i>
          After rebooting, please allow up to 2 minutes for the server to become accessible again.
        </div>
        <div className="alert alert-danger mt-3 mb-0">
          <i className="fa fa-exclamation-triangle me-2"></i>
          <strong>WARNING:</strong> This will perform a hard reboot, which may cause data loss if applications are running.
        </div>

        {powerActionLoading && (
          <div className="alert alert-info mt-3">
            <div className="d-flex align-items-center">
              <div className="spinner-border spinner-border-sm me-2" role="status"></div>
              Sending reboot command to server...
            </div>
          </div>
        )}

        {powerActionResult && (
          <div className={`alert ${powerActionResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
            <i className={`fa ${powerActionResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
            {powerActionResult.message}
            {powerActionResult.details && (
              <div className="mt-2 small">
                <strong>Details:</strong><br />
                <pre className="mb-0">{powerActionResult.details}</pre>
              </div>
            )}
          </div>
        )}
      </div>
    ),
    footer: (
      <div>
        <button className="btn btn-secondary" onClick={closeModal}>Cancel</button>
        <button
          className="btn btn-warning ms-2"
          onClick={() => handlePowerAction('restart')}
          disabled={powerActionLoading || powerStatus === 'offline'}
        >
          {powerActionLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Rebooting...
            </>
          ) : 'Reboot Server'}
        </button>
      </div>
    )
  };

  // Add the handler function
const handleTerminate = async () => {

  setTerminateLoading(true);
  setTerminateResult(null);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Sending terminate request for server ID: ${id}`);

    const response = await fetch(`/api.php?f=vps_terminate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        server_id: id
      })
    });

    const data = await response.json();
    setTerminateResult(data);

    if (data.error === 0) {
      console.log(`Server termination successful:`, data);

      setTimeout(() => {
        setShowModal(false);
        setTerminateResult(null);
        setTerminateLoading(false);

        // Redirect to homepage
        navigate('/');
      }, 3000);
    }
  } catch (error) {
    console.error(`Error terminating server:`, error);
    setTerminateResult({
      error: 1,
      message: `Failed to terminate server: ${error.message}`,
      details: error.stack
    });
  } finally {
    setTerminateLoading(false);
  }
};

// Handler function for resetting the password
const handleResetPassword = async () => {
  setResetPasswordLoading(true);
  setResetPasswordResult(null);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Sending password reset request for server ID: ${id}`);

    const response = await fetch(`/api.php?f=vps_reset_password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        server_id: id
      })
    });

    const data = await response.json();
    setResetPasswordResult(data);

    if (data.error === 0) {
      console.log(`Password reset successful:`, data);

      // Update the server object with the new password if provided in the response
      if (data.new_password) {
        setServer({
          ...server,
          password: data.new_password
        });
      }

      // Close modal after success with a delay
      setTimeout(() => {
        setShowModal(false);
        setResetPasswordResult(null);
        setResetPasswordLoading(false);

        // Refresh server details to get the updated password
        fetchServerDetails();
      }, 3000);
    }
  } catch (error) {
    console.error(`Error resetting password:`, error);
    setResetPasswordResult({
      error: 1,
      message: `Failed to reset password: ${error.message}`,
      details: error.stack
    });
  } finally {
    setResetPasswordLoading(false);
  }
};

// Handler function for updating the hostname
const handleUpdateHostname = async (e) => {
  e.preventDefault();

  // Validate hostname
  if (!newHostname || newHostname.trim() === '') {
    toast.current.show({
        severity: 'info',
        summary: 'Info',
        detail: 'Please enter a valid hostname',
        life: 3000
      });
    return;
  }

  setHostnameUpdateLoading(true);
  setHostnameUpdateResult(null);

  try {
    const token = sessionStorage.getItem('token');
    console.log(`Updating hostname for server ID: ${id} to: ${newHostname}`);

    // Call the API to update the hostname
    const response = await fetch(`/api.php?f=vps_update_hostname`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token,
        server_id: id,
        hostname: newHostname.trim()
      })
    });

    const data = await response.json();
    setHostnameUpdateResult(data);

    if (data.error === 0) {
      console.log(`Hostname update successful:`, data);

      // Update the server object with the new hostname
      setServer({
        ...server,
        hostname: newHostname.trim()
      });

      // Show success message
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Hostname updated successfully',
        life: 3000
      });

      // Refresh server details to confirm the update
      setTimeout(() => {
        fetchServerDetails();
      }, 1000);
    } else {
      // Show error message
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Failed to update hostname: ${data.message || 'Unknown error'}`,
        life: 5000
      });
    }
  } catch (error) {
    console.error(`Error updating hostname:`, error);
    setHostnameUpdateResult({
      error: 1,
      message: `Failed to update hostname: ${error.message}`,
      details: error.stack
    });

    // Show error message
    toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `Failed to update hostname: ${error.message}`,
        life: 5000
      });
  } finally {
    setHostnameUpdateLoading(false);
  }
};

// Define the terminate confirmation modal
const terminateModal = {
  title: 'Terminate Server',
  body: (
    <div>
      <div className="alert alert-danger mb-3">
        <i className="fa fa-exclamation-triangle me-2"></i>
        <strong>WARNING:</strong> This will permanently delete your server and all data on it. This action cannot be undone.
      </div>

      <p>Are you sure you want to terminate this server?</p>

      {terminateLoading && (
        <div className="alert alert-info mt-3">
          <div className="d-flex align-items-center">
            <div className="spinner-border spinner-border-sm me-2" role="status"></div>
            Processing termination request...
          </div>
        </div>
      )}

      {terminateResult && (
        <div className={`alert ${terminateResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
          <i className={`fa ${terminateResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`} style={{paddingRight: '0.5rem'}}></i>
          {terminateResult.message}
          {terminateResult.details && (
            <div className="mt-2 small">
              <strong>Details:</strong><br />
              <pre className="mb-0">{terminateResult.details}</pre>
            </div>
          )}
        </div>
      )}
    </div>
  ),
  footer: (
    <div>
      <button className="btn btn-secondary" onClick={closeModal}>Cancel</button>
      <button
        className="btn btn-danger ms-2"
        onClick={handleTerminate}
        disabled={terminateLoading}
      >
        {terminateLoading ? (
          <>
            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            Terminating...
          </>
        ) : 'Terminate'}
      </button>
    </div>
  )
};

// Define the reset password modal
const resetPasswordModal = {
  title: 'Reset Password',
  body: (
    <div>
      <div className="alert alert-warning mb-3">
        <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
        This will reset your server's root password to a new random password. You will need to use the new password to access your server.
      </div>

      <p>Are you sure you want to reset the password for this server?</p>

      {resetPasswordLoading && (
        <div className="alert alert-info mt-3">
          <div className="d-flex align-items-center">
            <div className="spinner-border spinner-border-sm me-2" role="status"></div>
            Processing password reset request...
          </div>
        </div>
      )}

      {resetPasswordResult && (
        <div className={`alert ${resetPasswordResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
          <i className={`fa ${resetPasswordResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
          {resetPasswordResult.message}
          {resetPasswordResult.new_password && (
            <div className="mt-2">
              <strong>New Password:</strong> {resetPasswordResult.new_password}
            </div>
          )}
          {resetPasswordResult.details && (
            <div className="mt-2 small">
              <strong>Details:</strong><br />
              <pre className="mb-0">{resetPasswordResult.details}</pre>
            </div>
          )}
        </div>
      )}
    </div>
  ),
  footer: (
    <div>
      <button className="btn btn-secondary" onClick={closeModal}>Cancel</button>
      <button
        className="btn btn-primary ms-2"
        onClick={handleResetPassword}
        disabled={resetPasswordLoading}
      >
        {resetPasswordLoading ? (
          <>
            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            Resetting...
          </>
        ) : 'Reset Password'}
      </button>
    </div>
  )
};

// Function to get reinstall modal content (moved up for useEffect dependency)
const getReinstallModalContent = () => {
  return {
    title: 'Reinstall Operating System',
    body: (
      <div className="form-group">
        {/* Debug info */}
        <div style={{ display: 'none' }}>
          {console.log('Modal rendering - selectedOS:', selectedOS, 'selectedApplication:', selectedApplication)}
        </div>

        {/* Warnings and Information - At the top */}
        <div className="row mb-3">
          <div className="col-12">
            <div className="alert alert-info mb-3">
              <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
              After initiating the reinstallation procedure, your server will be rebooted and will be unavailable during the installation process (typically 10-20 minutes).
              {selectedApplication && (
                <div className="mt-2">
                  <strong>Note:</strong> The selected application will be automatically configured and ready to use after installation.
                </div>
              )}
            </div>

            <div className="alert alert-danger mb-0">
              <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
              <strong>WARNING:</strong> This function will delete all existing data from your server. Make sure you have backed up any important data before proceeding.
            </div>
          </div>
        </div>

        {/* Operating System Selection */}
        <div className="row">
          <div className="col-12">
            <label className="form-label mb-2">Select Operating System:</label>
            {availableOSOptions.length === 0 ? (
              <div className="text-center py-3">
                <div className="spinner-border text-primary" role="status"></div>
                <p className="mt-2">Loading operating systems...</p>
              </div>
            ) : (
              <div className="better-radio-group" style={{ maxHeight: '200px', overflowY: 'auto', border: '1px solid #dee2e6', borderRadius: '0.375rem' }}>
                {(() => {
                  const categories = {};

                  // Group options by category
                  availableOSOptions.forEach(option => {
                    const category = option.category || 'Other';
                    if (!categories[category]) {
                      categories[category] = [];
                    }
                    categories[category].push(option);
                  });

                  // Convert to JSX with visual options like CloudOrder.js
                  return Object.entries(categories).map(([category, options]) => (
                    <div key={category}>
                      <div className="category-header bg-light p-2 fw-bold text-muted border-bottom">
                        {category}
                      </div>
                      {options.map(option => (
                        <div key={option.id} className="position-relative">
                          <input
                            type="radio"
                            id={`os_${option.id}`}
                            name="osType"
                            className="better-radio"
                            checked={selectedOS === option.id}
                            disabled={reinstallLoading}
                            style={{ position: 'absolute', opacity: 0, pointerEvents: 'none' }}
                            readOnly
                          />
                          <label 
                            htmlFor={`os_${option.id}`} 
                            className={`list-group-item d-flex align-items-center p-2 border-0 border-bottom ${selectedOS === option.id ? 'bg-primary text-white' : ''}`}
                            style={{ cursor: 'pointer', userSelect: 'none' }}
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              if (!reinstallLoading) {
                                console.log('Selecting OS:', option.id, option.name);
                                console.log('Current selectedOS before change:', selectedOS);
                                setSelectedOS(option.id);
                                setForceRerender(prev => prev + 1);
                                console.log('selectedOS should now be:', option.id);
                              }
                            }}
                          >
                            {renderOSLogo(option.name, '24px', '10px')}
                            <div>
                              <div className="fw-bold">{option.name}</div>
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>
                  ));
                })()}
              </div>
            )}
          </div>
        </div>

        {/* Application Selection */}
        <div className="row mt-3">
          <div className="col-12">
            <label className="form-label mb-2">Application (Optional):</label>
            {availableApplications.length === 0 ? (
              <div className="text-center py-2">
                <div className="spinner-border spinner-border-sm text-primary" role="status"></div>
                <p className="mt-1 small">Loading applications...</p>
              </div>
            ) : (
              <div className="better-radio-group" style={{ maxHeight: '180px', overflowY: 'auto', border: '1px solid #dee2e6', borderRadius: '0.375rem' }}>
                {/* None option */}
                <div className="position-relative">
                  <input
                    type="radio"
                    id="app_none"
                    name="application"
                    className="better-radio"
                    checked={!selectedApplication}
                    disabled={reinstallLoading}
                    style={{ position: 'absolute', opacity: 0, pointerEvents: 'none' }}
                    readOnly
                  />
                  <label 
                    htmlFor="app_none" 
                    className={`list-group-item d-flex align-items-center p-2 border-0 border-bottom ${!selectedApplication ? 'bg-primary text-white' : ''}`}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      if (!reinstallLoading) {
                        console.log('Selecting application: None');
                        handleApplicationChange('');
                      }
                    }}
                  >
                    <i className="fa fa-times-circle me-2" style={{ fontSize: '1.25rem', color: !selectedApplication ? '#fff' : '#6c757d', paddingRight: '0.5rem' }}></i>
                    <div>
                      <div className="fw-bold">None</div>
                      <div className="small text-muted">Clean OS Install</div>
                    </div>
                  </label>
                </div>
                
                {/* Application options */}
                {availableApplications.map(app => (
                  <div key={app.id} className="position-relative">
                    <input
                      type="radio"
                      id={`app_${app.id}`}
                      name="application"
                      className="better-radio"
                      checked={selectedApplication === app.id}
                      disabled={reinstallLoading}
                      style={{ position: 'absolute', opacity: 0, pointerEvents: 'none' }}
                      readOnly
                    />
                    <label 
                      htmlFor={`app_${app.id}`} 
                      className={`list-group-item d-flex align-items-center p-2 border-0 border-bottom ${selectedApplication === app.id ? 'bg-primary text-white' : ''}`}
                      style={{ cursor: 'pointer', userSelect: 'none' }}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (!reinstallLoading) {
                          console.log('Selecting application:', app.id, app.name);
                          handleApplicationChange(app.id);
                        }
                      }}
                    >
                      {renderApplicationLogo(app, '24px', '10px')}
                      <div>
                        <div className="fw-bold">{app.name}</div>
                        {app.description && (
                          <div className="small text-muted">{app.description}</div>
                        )}
                      </div>
                    </label>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Application Configuration Form - Below the application selection */}
        {renderApplicationConfig()}

        {/* Status Messages - Only for reinstall feedback */}
        <div className="row mt-3">
          <div className="col-12">

            {reinstallLoading && (
              <div className="alert alert-info mt-3">
                <div className="d-flex align-items-center">
                   <div className="spinner-border spinner-border-sm me-2" role="status" style={{paddingRight: '0.5rem'}}></div>
                  Sending reinstall command to server...
                </div>
              </div>
            )}

            {reinstallResult && (
              <div className={`alert ${reinstallResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
                <i className={`fa ${reinstallResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
                {reinstallResult.message}

                {reinstallResult.processing && (
                  <div className="progress mt-2" style={{height: "5px"}}>
                    <div className="progress-bar progress-bar-striped progress-bar-animated"
                         style={{width: "100%"}} />
                  </div>
                )}

                {reinstallResult.details && (
                  <div className="mt-2 small">
                    <strong>Details:</strong><br />
                    <pre className="mb-0" style={{maxHeight: "150px", overflow: "auto"}}>
                      {typeof reinstallResult.details === 'object'
                        ? JSON.stringify(reinstallResult.details, null, 2)
                        : reinstallResult.details}
                    </pre>
                  </div>
                )}

                {reinstallResult.error === 0 && !reinstallResult.processing && (
                  <div className="mt-2">
                    <p className="mb-0">
                      The reinstall process is now running on the server. The system will be unavailable during installation.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    ),
    footer: (
      <div>
        <button className="btn btn-secondary" onClick={closeModal} disabled={reinstallLoading}>
          <i className="fa fa-times me-1"></i> Cancel
        </button>
        <button
          className="btn btn-danger ms-2"
          onClick={handleReinstallOS}
          disabled={reinstallLoading || !selectedOS || availableOSOptions.length === 0}
        >
          {reinstallLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Reinstalling...
            </>
          ) : (
            <>
              <i className="fa fa-refresh me-1"></i> Reinstall

            </>
          )}
        </button>
      </div>
    )
  };
};

  // Loading state
  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: "300px" }}>
        <div className="spinner-border text-primary" role="status">
    
        </div>
      </div>
    );
  }

  // Error state
  if (!server) {
    return (
      <div className="alert alert-danger" role="alert">
        VPS server not found. <Link to="/">Return to services</Link>
      </div>
    );
  }

      return (
      <>
        <Toast ref={toast} />
        {/* Renewal Confirmation Modal */}
        <Modal
          isOpen={showRenewalModal}
          toggle={toggleRenewalModal}
          className="order-summary-modal"
          backdropClassName="fixed-backdrop"
          backdrop={true}
          zIndex={9999}
          size="lg"
        >
          <ModalHeader className="d-flex justify-content-between align-items-center">
            <div>Service Renewal</div>
            <button type="button" className="close" onClick={toggleRenewalModal} style={{ position: 'absolute', right: '15px', top: '15px', background: 'none', border: 'none', fontSize: '1.5rem', lineHeight: '1', color: '#000', opacity: '0.5' }}>
              <span aria-hidden="true">&times;</span>
            </button>
          </ModalHeader>
          <ModalBody>
            {server && (
              <>
                <h5 className="mb-3">Service Configuration</h5>
                <div className="table-responsive mb-4">
                  <table className="table card-table table-vcenter text-nowrap">
                    <tbody>
                      <tr>
                        <td><b>Server</b></td>
                        <td>{server.hostname || server.name}</td>
                      </tr>
                      <tr>
                        <td><b>Plan</b></td>
                        <td>{server.plan || 'VPS Server'}</td>
                      </tr>
                      <tr>
                        <td><b>CPU</b></td>
                        <td>{server.cpu || 'N/A'}</td>
                      </tr>
                      <tr>
                        <td><b>RAM</b></td>
                        <td>{server.ram || 'N/A'}</td>
                      </tr>
                      <tr>
                        <td><b>Storage</b></td>
                        <td>{server.disk || 'N/A'}</td>
                      </tr>
                      <tr>
                        <td><b>Location</b></td>
                        <td>
                          {server.location_flag && (
                            <i className={`flag flag-${server.location_flag}`} style={{marginRight: '8px'}}></i>
                          )}
                          {server.location || 'N/A'}
                        </td>
                      </tr>
                      <tr>
                        <td><b>Current Expiry</b></td>
                        <td>{server.next_renewal || 'N/A'}</td>
                      </tr>
                      <tr>
                        <td><b>Billing Period</b></td>
                        <td>
                          <select
                            className="form-select form-control"
                            value={selectedBillingPeriod}
                            onChange={(e) => handleBillingPeriodChange(parseInt(e.target.value))}
                          >
                            <option value={1}>1 Month (€{parseFloat(server.requirement_price || server.price || server.monthly_price || 0).toFixed(2)})</option>
                            <option value={3}>3 Months (€{(parseFloat(server.requirement_price || server.price || server.monthly_price || 0) * 3).toFixed(2)})</option>
                            <option value={6}>6 Months (€{(parseFloat(server.requirement_price || server.price || server.monthly_price || 0) * 6).toFixed(2)})</option>
                            <option value={12}>12 Months (€{(parseFloat(server.requirement_price || server.price || server.monthly_price || 0) * 12).toFixed(2)})</option>
                          </select>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <div className="card border-0 bg-light shadow-sm p-3 mb-3">
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      <h4 className="mb-0">
                        Total Price: <span className="text-success">€{renewalModalPrice.toFixed(2)}</span>
                      </h4>
                      

                      
                      {selectedBillingPeriod > 1 && (
                        <p className="text-muted mb-0">
                          €{(renewalModalPrice / selectedBillingPeriod).toFixed(2)}/month average
                        </p>
                      )}
                      <p className="text-muted mb-0">Extension: {selectedBillingPeriod} month{selectedBillingPeriod > 1 ? 's' : ''}</p>
                    </div>
                  </div>
                </div>
              </>
            )}
          </ModalBody>
          <ModalFooter className="d-flex flex-column p-3">
            {/* Top row with terms of service */}
            <div className="w-100 mb-3">
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <input
                  type="checkbox"
                  id="agreeTerms"
                  checked={termsAccepted}
                  onChange={() => setTermsAccepted(!termsAccepted)}
                  style={{
                    marginRight: '5px',
                    marginLeft: '0',
                    marginTop: '0',
                    marginBottom: '0',
                    padding: '0',
                    position: 'relative',
                    float: 'none'
                  }}
                  required
                />
                I accept the <a href="https://zetservers.com/tos.php" target="_blank" style={{ marginLeft: '3px', color: '#1e88e5' }}>Terms of Service</a>
              </div>
            </div>

            {/* Bottom row with buttons */}
            <div className="d-flex justify-content-between w-100">
              <Button color="secondary" onClick={toggleRenewalModal} disabled={renewalLoading}>Cancel</Button>
              <Button
                color="success"
                onClick={handleConfirmRenewal}
                disabled={!termsAccepted || renewalLoading}
              >
                {renewalLoading ? (
                  <>
                    <Spinner size="sm" className="me-2" />
                    Creating Invoice...
                  </>
                ) : (
                  'Generate Renewal Invoice'
                )}
              </Button>
            </div>
          </ModalFooter>
        </Modal>

        {/* Payment Modal for Renewal */}
        <PaymentModal
          isOpen={showPaymentModal}
          toggle={togglePaymentModal}
          totalAmount={renewalTotalWithVat - renewalVatAmount} // Pass subtotal
          onComplete={handleRenewalPaymentComplete}
          orderId={renewalOrderId}
          invoiceId={renewalInvoiceId}
          vatRate={renewalVatRate}
          vatAmount={renewalVatAmount}
          totalWithVat={renewalTotalWithVat}
        />

      {/* VNC Modal */}
      <VncModal
        isOpen={showVncModal}
        toggle={toggleVncModal}
        vncDetails={vncDetails}
        serverId={id}
      />

      {/* rDNS Modal */}
      <Modal
        isOpen={showRdnsModal}
        toggle={closeRdnsModal}
        className="rdns-modal"
        backdrop={true}
        size="md"
      >
        <ModalHeader className="d-flex justify-content-between align-items-center">
          <div>{selectedIpId ? 'Update Reverse DNS' : 'Set Reverse DNS'}</div>
          <button type="button" className="close" onClick={closeRdnsModal} style={{ position: 'absolute', right: '15px', top: '15px', background: 'none', border: 'none', fontSize: '1.5rem', lineHeight: '1', color: '#000', opacity: '0.5' }}>
            <span aria-hidden="true">&times;</span>
          </button>
        </ModalHeader>
        <ModalBody>
          <div className="alert alert-info mb-3">
            <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
            Reverse DNS (rDNS) allows you to specify the domain name that will be returned when a reverse lookup is performed on your IP address.
          </div>

          <div className="form-group mb-3">
            <label className="form-label">IP Address</label>
            <input
              type="text"
              className="form-control"
              value={selectedIp || ''}
              disabled
            />
            {selectedIpId && (
              <small className="form-text text-muted">
                IP ID: {selectedIpId}
              </small>
            )}
          </div>

          <div className="form-group mb-3">
            <label className="form-label">Domain Name</label>
            <input
              type="text"
              className="form-control"
              value={rdnsDomain}
              onChange={(e) => {
                console.log('Domain input changed:', e.target.value);
                setRdnsDomain(e.target.value);
              }}
              placeholder="e.g., server.yourdomain.com"
              autoFocus
            />
            <small className="form-text text-muted">
              Enter a fully qualified domain name that points to this IP address.
            </small>
          </div>

          {rdnsUpdateLoading && (
            <div className="alert alert-info mt-3">
              <div className="d-flex align-items-center">
                <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                Processing reverse DNS update...
              </div>
            </div>
          )}

          {rdnsUpdateResult && (
            <div className={`alert ${rdnsUpdateResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
              <i className={`fa ${rdnsUpdateResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
              {rdnsUpdateResult.message}
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <Button color="secondary" onClick={closeRdnsModal}>Cancel</Button>
          <Button
            color="primary"
            onClick={handleRdnsUpdate}
            disabled={rdnsUpdateLoading}
          >
            {rdnsUpdateLoading ? (
              <>
                <Spinner size="sm" className="me-2" />
                Updating...
              </>
            ) : (selectedIpId ? 'Update' : 'Create')}
          </Button>
        </ModalFooter>
      </Modal>
        {/* VPS Upgrade Modal */}
  <VpsUpgradeModal
    isOpen={showVpsUpgradeModal}
    onClose={toggleVpsUpgradeModal}
    server={server}
    onSuccess={handleVpsUpgradeSuccess}
  />

      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item"><Link to="/">Dashboard</Link></li>
            <li className="breadcrumb-item"><Link to="/">Services</Link></li>
            <li className="breadcrumb-item"> {server.hostname || server.name}</li>
          </ol>

        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">

              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-12">
          <DiscountAlert />

          <div className="card">
            <div className="card-header">
              <div className="card-title d-flex align-items-center">
                <span className="me-2">{server.hostname || server.name}</span>
                {powerStatus === 'online' && <span className="badge bg-success">Online</span>}
                {powerStatus === 'offline' && <span className="badge bg-danger">Offline</span>}
                {powerStatus === 'restarting' && <span className="badge bg-warning">Restarting</span>}
                {powerStatus === 'unknown' && <span className="badge bg-secondary">Unknown</span>}
              </div>
              <div className="card-options">
                <button className="btn btn-success btn-sm me-2" onClick={() => {
                  const modalContent = getReinstallModalContent();
                  openModal(modalContent.title, modalContent.body, modalContent.footer);
                }}>
                  <i className="fa fa-cogs me-1"></i> Reinstall
                </button>
                {powerStatus === 'offline' && (
                  <button
                    className="btn btn-success btn-sm me-2"
                    onClick={() => openModal(startModal.title, startModal.body, startModal.footer)}
                  >
                    <i className="fa fa-play me-1"></i> Start
                  </button>
                )}
                <button
                  className="btn btn-warning btn-sm me-2"
                  onClick={() => openModal(rebootModal.title, rebootModal.body, rebootModal.footer)}
                  disabled={powerStatus === 'offline'}
                >
                  <i className="fa fa-refresh me-1"></i> Reboot
                </button>
                <button
                  className="btn btn-danger btn-sm"
                  onClick={() => openModal(stopModal.title, stopModal.body, stopModal.footer)}
                  disabled={powerStatus === 'offline'}
                >
                  <i className="fa fa-power-off me-1"></i> Shutdown
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="panel panel-primary">
                <div className="tab-menu-heading">
                  <div className="tabs-menu">
                    <ul className="nav panel-tabs">
                      <li>
                        <a
                          className={activeTab === 'overview' ? 'active' : ''}
                          onClick={() => setActiveTab('overview')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-screen-desktop me-1"></i> Overview
                        </a>
                      </li>
                      <li>
                        <a
                          className={activeTab === 'network' ? 'active' : ''}
                          onClick={() => setActiveTab('network')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-graph me-1"></i> Network
                        </a>
                      </li>

                      <li>
                        <a
                          className={activeTab === 'settings' ? 'active' : ''}
                          onClick={() => setActiveTab('settings')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-settings me-1"></i> Settings
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="panel-body tabs-menu-body">
                  <div className="tab-content">
                    {/* Overview Tab */}
                    {activeTab === 'overview' && (
                      <div className="tab-pane active">
                        <div className="row">
                          {/* Server Access Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-server me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Server Access</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Quick Actions</h5>
                                <div className="row">
                                  <div className="col-6 mb-3">
                                    <button
                                      className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(startModal.title, startModal.body, startModal.footer)}
                                      disabled={powerStatus === 'online' || powerStatus === 'restarting'}
                                    >
                                      <i className="fa fa-power-off fa-2x d-block mb-2 text-success"></i>
                                      Turn On
                                    </button>
                                  </div>
                                  <div className="col-6 mb-3">
                                    <button
                                      className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(stopModal.title, stopModal.body, stopModal.footer)}
                                      disabled={powerStatus === 'offline' || powerStatus === 'restarting'}
                                    >
                                      <i className="fa fa-power-off fa-2x d-block mb-2 text-danger"></i>
                                      Turn Off
                                    </button>
                                  </div>
                                  <div className="col-6 mb-3">
                                    <button
                                      className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(rebootModal.title, rebootModal.body, rebootModal.footer)}
                                      disabled={powerStatus === 'offline' || powerStatus === 'restarting'}
                                    >
                                      <i className="fa fa-refresh fa-2x d-block mb-2 text-warning"></i>
                                      Reboot
                                    </button>
                                  </div>
                                  <div className="col-6 mb-3">
                                    <button
                                      onClick={handleVncConnection}
                                      className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      disabled={loadingVnc}
                                    >
                                      {loadingVnc ? (
                                        <>
                                          <div className="spinner-border spinner-border-sm mb-2" role="status"></div>
                                          <span className="d-block">Connecting...</span>
                                        </>
                                      ) : (
                                        <>
                                          <i className="fa fa-desktop fa-2x d-block mb-2 text-primary"></i>
                                          Console
                                        </>
                                      )}
                                    </button>
                                  </div>

                                </div>

                                <h5 className="text-muted mb-3">Access Details</h5>
                                <ul className="list-group list-group-flush">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-globe text-primary me-2"></i> IP Address:</span>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2" style={{paddingRight: '0.5rem'}}>{server.main_ip}</span>
                                      <button
                                        className="btn btn-sm btn-light"
                                        onClick={() => copyToClipboard(server.main_ip)}
                                        title="Copy to clipboard"
                                      >
                                        <i className="fa fa-copy"></i>
                                      </button>
                                    </div>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-user text-primary me-2"></i> Username:</span>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2" style={{paddingRight: '0.5rem'}}>{server.username || 'root'}</span>
                                      <button
                                        className="btn btn-sm btn-light"
                                        onClick={() => copyToClipboard(server.username || 'root')}
                                        title="Copy to clipboard"
                                      >
                                        <i className="fa fa-copy"></i>
                                      </button>
                                    </div>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-key text-primary me-2"></i> Password:</span>
                                    <div className="d-flex align-items-center">
                                      <span className="me-2" style={{paddingRight: '0.5rem'}}>
                                        {showPassword ? server.password : '••••••••••••'}
                                      </span>
                                      <button
                                        className="btn btn-sm btn-light me-2"
                                        onClick={togglePassword}
                                        title={showPassword ? "Hide password" : "Show password"}
                                      >
                                        <i className={`fa fa-${showPassword ? 'eye-slash' : 'eye'}`}></i>
                                      </button>
                                      <button
                                        className="btn btn-sm btn-light me-2"
                                        onClick={() => copyToClipboard(server.password)}
                                        title="Copy to clipboard"
                                      >
                                        <i className="fa fa-copy"></i>
                                      </button>
                                      <button
                                        className="btn btn-sm btn-warning"
                                        onClick={() => openModal(resetPasswordModal.title, resetPasswordModal.body, resetPasswordModal.footer)}
                                        title="Reset password"
                                      >
                                        <i className="fa fa-refresh"></i>
                                      </button>
                                    </div>
                                  </li>
                                </ul>
                              </div>

<button 
    className="btn btn-primary w-100"
    onClick={toggleVpsUpgradeModal}
  >
    <i className="fa fa-arrow-up me-1"></i> Upgrade Plan
  </button>
                            </div>
                          </div>

                          {/* Status and Hardware Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-info-circle me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Status & Hardware</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Server Status</h5>
                                <ul className="list-group list-group-flush mb-4">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-plug text-primary me-2"></i> Power Status:</span>
                                    {powerLoading ? (
                                      <div className="d-flex align-items-center">
                                        <div className="spinner-border spinner-border-sm me-2" role="status" style={{ width: '1rem', height: '1rem' }}></div>
                                        <span>Checking...</span>
                                      </div>
                                    ) : (
                                      <>
                                        {powerStatus === 'online' && <span className="badge bg-success">Online</span>}
                                        {powerStatus === 'offline' && <span className="badge bg-danger">Offline</span>}
                                        {powerStatus === 'restarting' && <span className="badge bg-warning">Restarting</span>}
                                        {powerStatus === 'unknown' && <span className="badge bg-secondary">Unknown</span>}
                                      </>
                                    )}
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-server text-primary me-2"></i> Hostname:</span>
                                    <span className="text-muted">{server.hostname || server.name}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-building text-primary me-2"></i> Location:</span>
                                    <span className="text-muted">
                                      {server.location_flag && (
                                        <i className={`flag flag-${server.location_flag}`} style={{marginRight: '8px'}}></i>
                                      )}
                                      {server.location || 'Unknown'}
                                    </span>
                                  </li>
                                </ul>

                                <h5 className="text-muted mb-3">Hardware Specifications</h5>
                                <ul className="list-group list-group-flush mb-4">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-microchip text-primary me-2"></i> CPU:</span>
                                    <span className="text-muted">{server.cpu || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-microchip text-primary me-2"></i> RAM:</span>
                                    <span className="text-muted">{server.ram || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-hdd-o text-primary me-2"></i> Storage:</span>
                                    <span className="text-muted">{server.disk || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-wifi text-primary me-2"></i> Bandwidth:</span>
                                    <span className="text-muted">{server.bandwidth || 'N/A'}</span>
                                  </li>
                                </ul>

                                <div className="mb-4">
                                  <h5 className="text-muted mb-3">Operating System</h5>
                                  <div className="card">
                                    <div className="card-body p-4">
                                      <div className="row align-items-center">
                                        <div className="col-auto">
                                          {/* OS Logo using same mechanic as CloudOrder.js */}
                                          {console.log('Overview tab - server.os value:', server.os)}
                                          {server.os ? renderOSLogo(server.os, '40px', '0') : (
                                            <i className="fa fa-server" style={{ 
                                              fontSize: '40px', 
                                              color: '#6c757d', 
                                              width: '40px',
                                              height: '40px',
                                              display: 'flex',
                                              alignItems: 'center',
                                              justifyContent: 'center'
                                            }}></i>
                                          )}
                                        </div>
                                        <div className="col">
                                          <h6 className="mb-1">{server.os || 'Linux'}</h6>
                                          <div className="text-muted">{server.os_version || ''}</div>
                                        </div>
                                        <div className="col-auto">
                                          <button
                                            className="btn btn-primary"
                                            onClick={() => {
                                              const modalContent = getReinstallModalContent();
                                              openModal(modalContent.title, modalContent.body, modalContent.footer);
                                            }}
                                          >
                                            Change
                                          </button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Billing Card */}
                          <div className="col-xl-4 col-lg-12 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Billing Information</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Plan Details</h5>
                                <ul className="list-group list-group-flush mb-4">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-tag text-primary me-2"></i> Plan:</span>
                                    <span className="text-muted fw-bold">{server.plan || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-money text-primary me-2"></i> Price:</span>
                                    <span className="text-muted fw-bold">{server.requirement_price || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-refresh text-primary me-2"></i> Billing Cycle:</span>
                                    <span className="text-muted">{getBillingCycleName(server.billing_cycle)}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-calendar text-primary me-2"></i> Next Renewal:</span>
                                    <span className="text-muted">{server.next_renewal || 'N/A'}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-repeat text-primary me-2"></i> Auto-Renewal:</span>
                                    <div className="d-flex align-items-center">
                                      {isUpdatingAutoRenewal && (
                                        <div className="spinner-border spinner-border-sm me-2" role="status" style={{ width: '1rem', height: '1rem' }}>

                                        </div>
                                      )}
                                      <span
                                        className={`badge ${server.auto_renewal ? 'bg-success' : 'bg-danger'}`}
                                        style={{ cursor: isUpdatingAutoRenewal ? 'wait' : 'pointer' }}
                                        onClick={() => {
                                          if (isUpdatingAutoRenewal) return; // Prevent clicking while updating

                                          // Get the new state (opposite of current)
                                          const newAutoRenewalState = !server.auto_renewal;

                                          // Immediately update the UI to show the change
                                          setServer({...server, auto_renewal: newAutoRenewalState});

                                          // Set updating state to true to show loading
                                          setIsUpdatingAutoRenewal(true);

                                          console.log(`Auto-renewal changing to: ${newAutoRenewalState ? 'enabled' : 'disabled'}`);

                                          // Call the API to update auto-renewal status
                                          (async () => {
                                            try {
                                            const token = sessionStorage.getItem('token');
                                            const response = await fetch('/api.php?f=update_auto_renewal', {
                                              method: 'POST',
                                              headers: {
                                                'Content-Type': 'application/json'
                                              },
                                              body: JSON.stringify({
                                                token,
                                                server_id: server.id,
                                                auto_renewal: newAutoRenewalState
                                              })
                                            });

                                            const data = await response.json();
                                            console.log("Auto-renewal update response:", data);

                                            if (data.success) {
                                              // Update status message (could be shown as a toast notification)
                                              console.log(`Auto-renewal ${newAutoRenewalState ? 'enabled' : 'disabled'} successfully`);
                                            } else {
                                              // Show error message
                                              console.error('Error updating auto-renewal status:', data.error || data.message);

                                              // Revert checkbox state
                                              setServer({...server, auto_renewal: !newAutoRenewalState});

                                              // Show error message
                                              toast.current.show({
                                                severity: 'error',
                                                summary: 'Update Error',
                                                detail: 'Error updating auto-renewal status: ' + (data.error || data.message || 'Unknown error'),
                                                life: 5000
                                              });
                                            }
                                          } catch (error) {
                                            console.error('Error updating auto-renewal status:', error);

                                            // Revert checkbox state
                                            setServer({...server, auto_renewal: !newAutoRenewalState});

                                                                                          // Show error message
                                              toast.current.show({
                                                severity: 'error',
                                                summary: 'Update Error',
                                                detail: 'Error updating auto-renewal status: ' + error.message,
                                                life: 5000
                                              });
                                          } finally {
                                            // Re-enable the badge
                                            setIsUpdatingAutoRenewal(false);
                                          }
                                          })();
                                        }}
                                      >
                                        {server.auto_renewal ? 'Enabled' : 'Disabled'}
                                      </span>
                                    </div>
                                  </li>
                                </ul>






                                <div className="mt-4 d-flex justify-content-between">
        <button
          className="btn btn-danger ms-2"
          onClick={() => openModal(terminateModal.title, terminateModal.body, terminateModal.footer)}
        >
          {terminateLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Terminating...
            </>
          ) : 'Terminate'}
        </button>
        <button 
          className="btn btn-success"
          onClick={handleRenewalService}
          disabled={renewalLoading}
        >
          {renewalLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Creating Invoice...
            </>
          ) : (
            <>
              <i className="fa fa-refresh me-1"></i> Renew Service
            </>
          )}
        </button>
                                  <Link to="/billing/invoices" className="btn btn-info btn-sm">
                                    <i className="fa fa-file-text-o me-1"></i> View Invoices
                                  </Link>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Network Tab */}
                    {activeTab === 'network' && (
                      <div className="tab-pane active">
                        <div className="row">
                          {/* Network Configuration Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-globe me-2 text-primary" style={{paddingRight:'0.5rem'}}></i>Network Configuration</h3>
                              </div>
                              <div className="card-body">
                                <ul className="list-group list-group-flush">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-sitemap me-2"></i> Main IP:</span>
                                    <span>{server.main_ip || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-sitemap me-2"></i> Gateway:</span>
                                    <span>{server.gateway || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-sitemap me-2"></i> Netmask:</span>
                                    <span>{server.netmask || 'N/A'}</span>
                                  </li>
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-wifi me-2"></i> Bandwidth:</span>
                                    <span>{server.bandwidth || 'N/A'}</span>
                                  </li>
                                </ul>


                              </div>
                            </div>
                          </div>

                          {/* IP Addresses Card */}
                          <div className="col-xl-8 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-globe me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>IP Addresses</h3>
                                <div className="card-options">
                                <button 
  className="btn btn-sm btn-primary"
  onClick={toggleVpsIpUpgradeModal}
>
  <i className="fa fa-plus me-1"></i> Add IP Address
</button>
                                </div>
                              </div>
                              <div className="card-body">
                                <div className="table-responsive">
                                  <table className="table table-hover">
                                    <thead className="bg-light">
                                      <tr>
                                        <th>IP Address</th>
                                        <th>Type</th>
                                        <th>Gateway</th>
                                        <th>Netmask</th>
                                        <th>Reverse DNS</th>
                                        <th>Actions</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <tr>
                                        <td>
                                          <div className="d-flex align-items-center">
                                            <span className="me-2" style={{paddingRight: '0.5rem'}}>{server.main_ip}</span>
                                            <span className="badge bg-primary ms-1">Primary</span>
                                          </div>
                                        </td>
                                        <td>Primary</td>
                                        <td>{server.gateway || 'N/A'}</td>
                                        <td>{server.netmask || 'N/A'}</td>
                                        <td>{server.rdns || <span className="text-muted">Not set</span>}</td>
                                        <td>
                                          <div className="btn-group" role="group">
<button
  className="btn btn-sm btn-primary"
  onClick={() => openRdnsModal(server.main_ip, server.rdns || '', server.rdns_id || null)}
>
  <i className="fa fa-edit me-1"></i> {server.rdns ? 'Edit rDNS' : 'Set rDNS'}
</button>
                                            {server.rdns && server.rdns_id && (
                                              <button
                                                className="btn btn-sm btn-danger"
                                                onClick={() => handleRdnsDelete(server.rdns_id)}
                                              >
                                                <i className="fa fa-trash me-1"></i> Delete rDNS
                                              </button>
                                            )}
                                          </div>
                                        </td>
                                      </tr>
                                      {server.additional_ips && server.additional_ips.map((ip, index) => (
                                        <tr key={index}>
                                          <td>
                                            <div className="d-flex align-items-center">
                                              <span className="me-2">{ip.address}</span>
                                              {ip.rdns && (
                                                <span className="badge bg-info ms-1" title={`rDNS: ${ip.rdns}`}>
                                                  <i className="fa fa-check me-1"></i> rDNS
                                                </span>
                                              )}
                                            </div>
                                          </td>
                                          <td>Additional</td>
                                          <td>{ip.gateway || server.gateway || 'N/A'}</td>
                                          <td>{ip.netmask || server.netmask || 'N/A'}</td>
                                          <td>{ip.rdns || <span className="text-muted">Not set</span>}</td>
                                          <td>
                                            <div className="btn-group" role="group">
                                              <button
                                                className="btn btn-sm btn-primary"
                                                onClick={() => openRdnsModal(ip.address, ip.rdns || '', ip.rdns_id || null)}
                                              >
                                                <i className="fa fa-edit me-1"></i> {ip.rdns ? 'Edit rDNS' : 'Set rDNS'}
                                              </button>
                                              {ip.rdns && ip.rdns_id && (
                                                <button
                                                  className="btn btn-sm btn-danger"
                                                  onClick={() => handleRdnsDelete(ip.rdns_id)}
                                                >
                                                  <i className="fa fa-trash me-1"></i> Delete rDNS
                                                </button>
                                              )}
                      
                                            </div>
                                          </td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>

                        </div>
                      </div>
                    )}



                    {/* Settings Tab */}
                    {activeTab === 'settings' && (
                      <div className="tab-pane active">
                        <div className="row">
                          {/* Server Settings Card */}
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-cog me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Server Settings</h3>
                              </div>
                              <div className="card-body">
                                <form onSubmit={handleUpdateHostname}>
                                  <div className="form-group mb-3">
                                    <label className="form-label">Server Hostname</label>
                                    <input
                                      type="text"
                                      className="form-control"
                                      defaultValue={server.hostname || server.name}
                                      placeholder="Server Hostname"
                                      onChange={(e) => setNewHostname(e.target.value)}
                                      required
                                    />
                                    <small className="form-text text-muted">
                                      This hostname identifies your server on the network.
                                    </small>
                                  </div>

                                  {hostnameUpdateResult && (
                                    <div className={`alert ${hostnameUpdateResult.error === 0 ? 'alert-success' : 'alert-danger'} mt-3`}>
                                      <i className={`fa ${hostnameUpdateResult.error === 0 ? 'fa-check-circle' : 'fa-exclamation-circle'} me-2`}></i>
                                      {hostnameUpdateResult.message}
                                    </div>
                                  )}

                                  <div className="d-flex justify-content-end">
                                    <button
                                      type="submit"
                                      className="btn btn-primary"
                                      disabled={hostnameUpdateLoading}
                                    >
                                      {hostnameUpdateLoading ? (
                                        <>
                                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                          Updating...
                                        </>
                                      ) : 'Save Settings'}
                                    </button>
                                  </div>
                                </form>
                              </div>
                            </div>
                          </div>

                          {/* Billing Settings Card */}
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">

                                <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Billing Settings</h3>
                              </div>
                              <div className="card-body">
                                <form>
                  

                     

                                  <div className="form-group mb-3">

  <div className="d-flex align-items-center mb-2">
  <span
      className={`badge ${server.auto_renewal ? 'bg-success' : 'bg-danger'}`}
      style={{
        cursor: isUpdatingAutoRenewal ? 'wait' : 'pointer',
        padding: '8px 12px',
        fontSize: '14px'
      }}
      onClick={() => {
        if (isUpdatingAutoRenewal) return; // Prevent clicking while updating

        // Get the new state (opposite of current)
        const newAutoRenewalState = !server.auto_renewal;

        // Immediately update the UI to show the change
        setServer({...server, auto_renewal: newAutoRenewalState});

        // Set updating state to true to show loading
        setIsUpdatingAutoRenewal(true);

        // Set updating state to show the spinner

        console.log(`Auto-renewal changing to: ${newAutoRenewalState ? 'enabled' : 'disabled'}`);

        // Call the API to update auto-renewal status
        (async () => {
          try {
            const token = sessionStorage.getItem('token');
            const response = await fetch('/api.php?f=update_auto_renewal', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                token,
                server_id: server.id,
                auto_renewal: newAutoRenewalState
              })
            });

            const data = await response.json();
            console.log("Auto-renewal update response:", data);

            if (data.success) {
              // Success - no message needed
              console.log(`Auto-renewal ${newAutoRenewalState ? 'enabled' : 'disabled'} successfully`);
            } else {
              // Show error message
              console.error('Error updating auto-renewal status:', data.error || data.message);

              // Revert checkbox state
              setServer({...server, auto_renewal: !newAutoRenewalState});
            }
          } catch (error) {
            console.error('Error updating auto-renewal status:', error);

            // Revert badge state
            setServer({...server, auto_renewal: !newAutoRenewalState});
          } finally {
            // Re-enable the badge
            setIsUpdatingAutoRenewal(false);
          }
        })();
      }}
    >
      {server.auto_renewal ? 'Enabled' : 'Disabled'}
    </span>
  <label className="form-label">Auto-Renewal</label>

    {isUpdatingAutoRenewal && (
      <div className="spinner-border spinner-border-sm ms-2" role="status" style={{ width: '1rem', height: '1rem' }}>
      </div>
    )}
  </div>
  <small className="form-text text-muted d-block">
    When disabled, your server will be suspended after the billing period ends unless manually renewed.
    Click the badge to toggle auto-renewal.
  </small>
</div>

<div className="form-group mb-3">
  <div className="d-flex align-items-center mb-2">
  <span
      className={`badge ${server.use_credit ? 'bg-success' : 'bg-danger'}`}
      style={{
        cursor: isUpdatingUseCredit ? 'wait' : 'pointer',
        padding: '8px 12px',
        fontSize: '14px'
      }}
      onClick={() => {
        if (isUpdatingUseCredit) return; // Prevent clicking while updating

        // Get the new state (opposite of current)
        const newUseCreditState = !server.use_credit;

        // Immediately update the UI to show the change
        setServer({...server, use_credit: newUseCreditState});

        // Set updating state to true to show loading
        setIsUpdatingUseCredit(true);

        console.log(`Use credit changing to: ${newUseCreditState ? 'enabled' : 'disabled'}`);

        // Call the API to update use_credit status
        (async () => {
          try {
            const token = sessionStorage.getItem('token');
            const response = await fetch('/api.php?f=update_use_credit', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                token,
                server_id: server.id,
                use_credit: newUseCreditState
              })
            });

            const data = await response.json();
            console.log("Use credit update response:", data);

            if (data.success) {
              // Success - no message needed
              console.log(`Use credit ${newUseCreditState ? 'enabled' : 'disabled'} successfully`);
            } else {
              // Show error message
              console.error('Error updating use credit status:', data.error || data.message);

              // Revert checkbox state
              setServer({...server, use_credit: !newUseCreditState});
            }
          } catch (error) {
            console.error('Error updating use credit status:', error);

            // Revert badge state
            setServer({...server, use_credit: !newUseCreditState});
          } finally {
            // Re-enable the badge
            setIsUpdatingUseCredit(false);
          }
        })();
      }}
    >
      {server.use_credit ? 'Enabled' : 'Disabled'}
    </span>
    <label className="form-label">Use Account Credit for Renewals</label>

    {isUpdatingUseCredit && (
      <div className="spinner-border spinner-border-sm ms-2" role="status" style={{ width: '1rem', height: '1rem' }}>
      </div>
    )}
  </div>
  <small className="form-text text-muted d-block">
    When enabled, available account credit will be automatically applied to renewal invoices.
    Click the badge to toggle this setting.
  </small>
</div>

</form>
                              </div>
                            </div>
                          </div>

                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <VpsIpUpgradeModal
  isOpen={showVpsIpUpgradeModal}
  onClose={toggleVpsIpUpgradeModal}
  server={server}
  onSuccess={handleVpsIpUpgradeSuccess}
/>
      {/* Modal for various actions */}
      <MDBModal show={showModal} tabIndex='-1'>
        <MDBModalDialog size="lg" scrollable>
          <MDBModalContent style={{ maxHeight: '90vh' }}>
            <MDBModalHeader>
              <MDBModalTitle>{modalTitle}</MDBModalTitle>
              <button type="button" className="close" onClick={closeModal}>
                <span aria-hidden="true">&times;</span>
              </button>
            </MDBModalHeader>
            <MDBModalBody style={{ maxHeight: '70vh', overflowY: 'auto' }}>{modalBody}</MDBModalBody>
            <MDBModalFooter>{modalFooter}</MDBModalFooter>
          </MDBModalContent>
        </MDBModalDialog>
      </MDBModal>
    </>
  );
};

export default VpsServerDetailsView;