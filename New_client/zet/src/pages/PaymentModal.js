import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON>dal<PERSON>ead<PERSON>, ModalBody, Modal<PERSON>ooter, Button, Nav, NavItem, NavLink, TabContent, TabPane, Row, Col, Input, FormGroup, Label, Alert, Spinner } from 'reactstrap';
import { Link } from 'react-router-dom';



const PaymentModal = ({
  isOpen,
  toggle,
  totalAmount,
  onComplete,
  orderId,
  invoiceId,
  generatingInvoice,
  allocationMessage,
  vatRate: initialVatRate,
  vatAmount: initialVatAmount,
  totalWithVat: initialTotalWithVat,
  isCreditsInvoice = false // Flag to identify if this is a Credit Purchase invoice
}) => {
  const [activeTab, setActiveTab] = useState('card');
  const [paymentCompleted, setPaymentCompleted] = useState(false);
  const [processingPayment, setProcessingPayment] = useState(false);
  const [creditAmount, setCreditAmount] = useState(0);
  const [availableCredit, setAvailableCredit] = useState(0);
  const [applyFullCredit, setApplyFullCredit] = useState(false);
  const [error, setError] = useState(null);
  const [debugInfo, setDebugInfo] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [customCreditAmount, setCustomCreditAmount] = useState(0);
  const [applyingCredit, setApplyingCredit] = useState(false);
  const [showCreditModal, setShowCreditModal] = useState(false);
  const [stripeRedirectUrl, setStripeRedirectUrl] = useState('');

  // VAT related state
  const [vatRate, setVatRate] = useState(20); // Default 20%
  const [userCountry, setUserCountry] = useState('');
  const [vatNumber, setVatNumber] = useState('');
  const [subTotal, setSubTotal] = useState(parseFloat(totalAmount) || 0);
  const [vatAmount, setVatAmount] = useState(0);
  const [grandTotal, setGrandTotal] = useState(parseFloat(totalAmount) || 0);
  const [loadingVat, setLoadingVat] = useState(false);

  const toast = useRef(null);

  // Check URL parameters for Stripe session on component mount
  useEffect(() => {
    // Check for URL parameters indicating return from Stripe
    const urlParams = new URLSearchParams(window.location.search);
    const paymentStatus = urlParams.get('payment_status');
    const sessionId = urlParams.get('session_id');
    const invoiceIdParam = urlParams.get('invoice_id');
    
    // If returning from Stripe with session_id, verify payment
    if (paymentStatus === 'success' && sessionId && invoiceIdParam) {
      verifyStripePayment(sessionId, invoiceIdParam);
      
      // Clear the URL parameters after handling them
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, []);





  // Add body class when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('modal-open');
      
      // Only fetch credit if this is not a Credit Purchase invoice
      if (!isCreditsInvoice) {
        fetchAvailableCredit();
      } else {
        // For Credit Purchase invoices, ensure credit amount is zero
        setAvailableCredit(0);
        setCreditAmount(0);
      }

      // Use passed VAT values if available, otherwise fetch them
      if (initialVatRate !== undefined && initialVatAmount !== undefined && initialTotalWithVat !== undefined) {
        console.log(`Using passed VAT values: Rate=${initialVatRate}%, Amount=${initialVatAmount}, Total=${initialTotalWithVat}`);
        setVatRate(parseFloat(initialVatRate) || 0);
        setVatAmount(parseFloat(initialVatAmount) || 0);
        setGrandTotal(parseFloat(initialTotalWithVat) || 0);
        // Use the passed totalAmount as the subtotal
        setSubTotal(parseFloat(totalAmount) || 0);
      } else {
        // Set subtotal first
        setSubTotal(parseFloat(totalAmount) || 0);
        // Then fetch VAT rate to calculate VAT amount
        fetchVatRate();
      }
    } else {
      document.body.classList.remove('modal-open');
    }

    return () => {
      document.body.classList.remove('modal-open');
    };
  }, [isOpen, initialVatRate, initialVatAmount, initialTotalWithVat, totalAmount]);

  // Calculate VAT amount and grand total when VAT rate or total amount changes
  useEffect(() => {
    // Only calculate VAT if we don't have provided VAT values
    if (initialVatRate === undefined || initialVatAmount === undefined) {
      const calcVatAmount = (subTotal * vatRate) / 100;
      setVatAmount(parseFloat(calcVatAmount) || 0);
      setGrandTotal(parseFloat(subTotal + calcVatAmount) || 0);
    }
  }, [vatRate, subTotal, initialVatRate, initialVatAmount]);

  // Function to get token from session storage
  const getToken = () => {
    return sessionStorage.getItem('token');
  };

  // Updated createPayPalSession function with proper error handling
  const createPayPalSession = async (amount) => {
    try {
      console.log(`Creating PayPal session for amount: ${amount}`);
      
      const token = getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }
  
      // Use the invoice page as the return URL instead of the current page
      const returnUrl = `${window.location.origin}/billing/invoices/${invoiceId}`;
      console.log("Will redirect to invoice page after payment:", returnUrl);
  
      // Create a PayPal Checkout session
      const apiUrl = "/api.php?f=create_paypal_session";
      console.log(`Making request to: ${apiUrl}`);
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          invoice_id: invoiceId,
          order_id: orderId,
          amount: parseFloat(amount),
          return_url: returnUrl // Explicitly set redirect to invoice page
        })
      });
  
      console.log("Response status:", response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response text:", errorText);
        throw new Error(`Server returned ${response.status}: ${errorText}`);
      }
  
      const text = await response.text();
      console.log("Raw response:", text);
      
      let data;
      try {
        data = JSON.parse(text);
      } catch (e) {
        console.error("Error parsing JSON response:", e);
        console.log("Response was:", text);
        throw new Error("Invalid response from server: " + text.substring(0, 100));
      }
  
      if (!data.success) {
        console.error("PayPal session creation error:", data);
        throw new Error(data.error || "Failed to create payment session");
      }
      
      console.log("Successfully created PayPal session:", data);
      
      // CRITICAL FIX: Store payment information with correct structure
      try {
        const paymentInfo = {
          payment_id: data.payment_id,  // This is the order ID from PayPal
          invoice_id: invoiceId,
          amount: amount,
          timestamp: new Date().toISOString(),
          success_url: data.success_url,
          cancel_url: data.cancel_url,
          return_url: returnUrl,
          verified: false,
          // Add these for debugging
          order_id: data.payment_id,
          checkout_url: data.checkout_url
        };
        
        localStorage.setItem('paypal_payment_info', JSON.stringify(paymentInfo));
        console.log("Saved PayPal payment info to localStorage:", paymentInfo);
      } catch (e) {
        console.warn("Failed to save payment info to localStorage:", e);
      }
      
      return data;
    } catch (error) {
      console.error('PayPal session creation error:', error);
      throw error;
    }
  };
  
  // Process PayPal payment
  const processPayPalPayment = async (amount) => {
    try {
      // Create PayPal Checkout session
      const sessionData = await createPayPalSession(amount);
      
      // Add to debug info
      let debugData = {
        step: "PayPal session",
        status: "success",
        payment_id: sessionData.payment_id,
        checkout_url: sessionData.checkout_url
      };
      
      // Update debugInfo
      setDebugInfo(debugData);
      
      // Redirect to PayPal Checkout
      console.log("Redirecting to PayPal checkout:", sessionData.checkout_url);
      
      // Small delay before redirect to ensure debug info is displayed
      setTimeout(() => {
        window.location.href = sessionData.checkout_url;
      }, 500);
      
      return sessionData;
    } catch (error) {
      console.error("PayPal payment processing error:", error);
      throw error;
    }
  };
  
  // Verify Stripe payment after redirect
  const verifyStripePayment = async (sessionId, invoiceIdParam) => {
    try {
      const token = getToken();
      if (!token) return;

      const response = await fetch("/api.php?f=verify_stripe_payment", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          token, 
          session_id: sessionId,
          invoice_id: invoiceIdParam 
        })
      });

      if (!response.ok) {
        throw new Error("Failed to verify payment");
      }

      const data = await response.json();
      
      if (data.success && data.is_paid) {
        setPaymentCompleted(true);
      } else if (data.success && data.session_status === 'pending') {
        // Payment is being processed, check again after a delay
        setTimeout(() => verifyStripePayment(sessionId, invoiceIdParam), 3000);
      } else {
        // Handle other cases
        console.warn("Payment verification issue:", data);
      }
    } catch (err) {
      console.error("Payment verification error:", err);
      setError("Failed to verify payment: " + err.message);
    }
  };



  // Fetch VAT rate based on user country
  const fetchVatRate = async () => {
    setLoadingVat(true);

    try {
      const token = getToken();
      if (!token) return;

      const response = await fetch("/api.php?f=get_vat_rate", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      if (!response.ok) {
        console.warn("Failed to fetch VAT info:", response.status);
        return;
      }

      const data = await response.json();

      if (data.success) {
        setVatRate(data.vat_rate);
        setUserCountry(data.country);
        setVatNumber(data.vat_number || '');

        // Recalculate amounts with new VAT rate
        // Note: subtotal should already be set correctly
        const newVatAmount = (parseFloat(subTotal) * data.vat_rate) / 100;
        setVatAmount(parseFloat(newVatAmount) || 0);
        setGrandTotal(parseFloat(subTotal + newVatAmount) || 0);
      }
    } catch (err) {
      console.error("Error fetching VAT rate:", err);
    } finally {
      setLoadingVat(false);
    }
  };

  // Fetch available credit
  const fetchAvailableCredit = async () => {
    try {
      const token = getToken();
      if (!token) return;

      console.log("Fetching available credit...");

      const response = await fetch("/api.php?f=get_available_credit", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      if (!response.ok) {
        console.warn("Failed to fetch credit info:", response.status);
        return;
      }

      const data = await response.json();
      console.log("Credit fetch response:", data);

      if (!data.error) {
        const total = (parseFloat(data.available_credit) || 0) +
                      (parseFloat(data.free_credit) || 0);

        console.log("Setting available credit to:", total);
        setAvailableCredit(total);

        if (total <= 0) {
          setAvailableCredit(0);
        }
      }
    } catch (err) {
      console.error("Error fetching credit:", err);
      setAvailableCredit(0);
    }
  };

  // Handle tab change
  const toggleTab = (tab) => {
    if (activeTab !== tab) {
      setActiveTab(tab);
      setPaymentMethod(tab); // Update payment method when tab changes
    }
  };

  // Initialize Stripe Checkout session with custom redirect
  const createStripeSession = async (amount) => {
    try {
      console.log(`Creating Stripe session for amount: ${amount}`);
      
      const token = getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }
  
      // Use the invoice page as the return URL instead of the current page
      const returnUrl = `${window.location.origin}/billing/invoices/${invoiceId}`;
      console.log("Will redirect to invoice page after payment:", returnUrl);
  
      // Create a Stripe Checkout session
      const apiUrl = "/api.php?f=create_stripe_session";
      console.log(`Making request to: ${apiUrl}`);
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          invoice_id: invoiceId,
          order_id: orderId,
          amount: parseFloat(amount),
          return_url: returnUrl // Explicitly set redirect to invoice page
        })
      });
  
      console.log("Response status:", response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response text:", errorText);
        throw new Error(`Server returned ${response.status}: ${errorText}`);
      }
  
      const text = await response.text();
      console.log("Raw response:", text);
      
      let data;
      try {
        data = JSON.parse(text);
      } catch (e) {
        console.error("Error parsing JSON response:", e);
        console.log("Response was:", text);
        throw new Error("Invalid response from server: " + text.substring(0, 100));
      }
  
      if (!data.success) {
        console.error("Stripe session creation error:", data);
        throw new Error(data.error || "Failed to create payment session");
      }
      
      console.log("Successfully created Stripe session:", data);
      
      // Store payment information in localStorage for retrieval after redirect
      try {
        localStorage.setItem('stripe_payment_info', JSON.stringify({
          session_id: data.session_id,
          invoice_id: invoiceId,
          amount: amount,
          timestamp: new Date().toISOString(),
          success_url: data.success_url,
          cancel_url: data.cancel_url,
          return_url: returnUrl
        }));
        console.log("Saved payment info to localStorage");
      } catch (e) {
        console.warn("Failed to save payment info to localStorage:", e);
      }
      
      return data;
    } catch (error) {
      console.error('Stripe session creation error:', error);
      throw error;
    }
  };

  // CoinGate session creation function
  const createCoinGateSession = async (amount) => {
    try {
      console.log(`Creating CoinGate session for amount: ${amount}`);
      
      const token = getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      // Use the invoice page as the return URL instead of the current page
      const returnUrl = `${window.location.origin}/billing/invoices/${invoiceId}`;
      console.log("Will redirect to invoice page after payment:", returnUrl);

      // Create a CoinGate Checkout session
      const apiUrl = "/api.php?f=create_coingate_session";
      console.log(`Making request to: ${apiUrl}`);
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          invoice_id: invoiceId,
          order_id: orderId,
          amount: parseFloat(amount),
          return_url: returnUrl // Explicitly set redirect to invoice page
        })
      });

      console.log("Response status:", response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response text:", errorText);
        throw new Error(`Server returned ${response.status}: ${errorText}`);
      }

      const text = await response.text();
      console.log("Raw response:", text);
      
      let data;
      try {
        data = JSON.parse(text);
      } catch (e) {
        console.error("Error parsing JSON response:", e);
        console.log("Response was:", text);
        throw new Error("Invalid response from server: " + text.substring(0, 100));
      }

      if (!data.success) {
        console.error("CoinGate session creation error:", data);
        throw new Error(data.error || "Failed to create payment session");
      }
      
      console.log("Successfully created CoinGate session:", data);
      
      // Store payment information with correct structure
      try {
        const paymentInfo = {
          payment_id: data.payment_id,  // This is the CoinGate order ID
          invoice_id: invoiceId,
          amount: amount,
          timestamp: new Date().toISOString(),
          success_url: data.success_url,
          cancel_url: data.cancel_url,
          return_url: returnUrl,
          verified: false,
          // Add these for debugging
          coingate_id: data.payment_id,
          checkout_url: data.checkout_url,
          tracking_token: data.tracking_token
        };
        
        localStorage.setItem('coingate_payment_info', JSON.stringify(paymentInfo));
        console.log("Saved CoinGate payment info to localStorage:", paymentInfo);
      } catch (e) {
        console.warn("Failed to save payment info to localStorage:", e);
      }
      
      return data;
    } catch (error) {
      console.error('CoinGate session creation error:', error);
      throw error;
    }
  };





  // Process CoinGate payment - UPDATED
  const processCryptoPayment = async (amount) => {
    try {
      // Create CoinGate Checkout session
      const sessionData = await createCoinGateSession(amount);
      
      // Add to debug info
      let debugData = {
        step: "CoinGate session",
        status: "success",
        payment_id: sessionData.payment_id,
        checkout_url: sessionData.checkout_url,
        tracking_token: sessionData.tracking_token
      };
      
      // Update debugInfo
      setDebugInfo(debugData);
      
      // Redirect to CoinGate Checkout
      console.log("Redirecting to CoinGate checkout:", sessionData.checkout_url);
      
      // Small delay before redirect to ensure debug info is displayed
      setTimeout(() => {
        window.location.href = sessionData.checkout_url;
      }, 500);
      
      return sessionData;
    } catch (error) {
      console.error("CoinGate payment processing error:", error);
      throw error;
    }
  };



  // Update handling function for payment processing
  const handlePayment = async (e) => {
    // Prevent any default form submission or page refresh
    if (e) {
      e.preventDefault();
      e.stopPropagation();
      
      // Check if stopImmediatePropagation exists (not all events have it)
      if (typeof e.stopImmediatePropagation === 'function') {
        e.stopImmediatePropagation();
      }
      
      // For React SyntheticEvents, also check nativeEvent
      if (e.nativeEvent && typeof e.nativeEvent.stopImmediatePropagation === 'function') {
        e.nativeEvent.stopImmediatePropagation();
      }
    }
    
    // Additional safety: prevent any form submission
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      form.addEventListener('submit', (formEvent) => {
        formEvent.preventDefault();
        return false;
      }, { once: true });
    });
    
    setProcessingPayment(true);
    setError(null);
    setDebugInfo(null);
  
    // Ensure we're using the latest VAT information
    const currentVatRate = initialVatRate !== undefined ? initialVatRate : vatRate;
    const currentVatAmount = initialVatAmount !== undefined ? initialVatAmount : vatAmount;
    const currentGrandTotal = initialTotalWithVat !== undefined ? initialTotalWithVat : grandTotal;
  
    // Log the VAT information being used
    console.log(`Payment using VAT: Rate=${currentVatRate}%, Amount=${currentVatAmount}, Total=${currentGrandTotal}`);
  
    // Clear debug info and prepare new debug data
    let debugData = {
      startTime: new Date().toISOString(),
      invoiceId: invoiceId,
      orderId: orderId,
      subTotal: subTotal,
      vatRate: currentVatRate,
      vatAmount: typeof currentVatAmount === 'number' ? currentVatAmount : Number(currentVatAmount),
      grandTotal: currentGrandTotal,
      creditAmount: creditAmount,
      remainingAmount: currentGrandTotal - creditAmount,
      steps: []
    };
  
    try {
      debugData.steps.push({
        step: "Start payment process",
        timestamp: new Date().toISOString()
      });
  
      // Validate invoice ID
      if (!invoiceId) {
        throw new Error("No invoice ID provided - cannot process payment without an invoice");
      }
  
      debugData.steps.push({
        step: "Invoice ID handling",
        status: "success",
        message: `Using invoice ID: ${invoiceId}`
      });
  
      const token = getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }
  
      // If using credit, handle it properly
      if (creditAmount > 0) {
        debugData.steps.push({
          step: "Credit application",
          substep: "start",
          message: `Applying ${creditAmount} credit to invoice ${invoiceId}`
        });
  
        console.log(`Applying ${creditAmount} credit to invoice ${invoiceId}`);
  
        // Use the direct credit application endpoint to handle everything in one go
        const creditResponse = await fetch("/api.php?f=direct_credit_application", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token,
            invoice_id: invoiceId,
            amount: parseFloat(creditAmount)
          })
        });
  
        if (!creditResponse.ok) {
          const errorText = await creditResponse.text();
          console.error("Credit application error response:", errorText);
          throw new Error(`Credit application failed: ${errorText}`);
        }
  
        const creditData = await creditResponse.json();
  
        debugData.steps.push({
          step: "Credit application",
          substep: "response",
          status: creditData.success ? "success" : "error",
          response: creditData
        });
  
        if (!creditData.success) {
          throw new Error(creditData.error || "Failed to apply credit");
        }
  
        // Update UI to reflect credit application
        setAvailableCredit(prev => {
          const newValue = creditData.credit_balance?.total_credit ?? Math.max(0, prev - creditAmount);
          console.log(`Updating available credit: ${prev} -> ${newValue}`);
          return newValue;
        });
  
        // If credit fully paid the invoice, we're done
        if (creditData.fully_paid) {
          debugData.steps.push({
            step: "Invoice payment",
            status: "success",
            message: "Invoice fully paid with credit"
          });
  
          setPaymentCompleted(true);
          debugData.steps.push({
            step: "Payment completion",
            status: "success",
            message: "Payment process completed successfully with credit"
          });
  
          debugData.endTime = new Date().toISOString();
          setDebugInfo(debugData);
          setProcessingPayment(false);
          return;
        }
      }
  
      // Process payment if there's a remaining amount
      const currentRemainingAmount = Number(currentGrandTotal - creditAmount);
      if (currentRemainingAmount > 0) {
        debugData.steps.push({
          step: "Payment processing",
          substep: "start",
          message: `Processing ${currentRemainingAmount} payment via ${activeTab} (includes VAT at ${currentVatRate}%)`
        });

        try {
          // Process payment based on selected method
          if (activeTab === 'card') {
            // Create Stripe Checkout session
            const sessionData = await createStripeSession(currentRemainingAmount);
            
            debugData.steps.push({
              step: "Stripe session",
              status: "success",
              session_id: sessionData.session_id,
              checkout_url: sessionData.checkout_url
            });
            
            // Redirect to Stripe Checkout
            console.log("Redirecting to Stripe checkout:", sessionData.checkout_url);
            debugData.endTime = new Date().toISOString();
            setDebugInfo(debugData);
            
            // Small delay before redirect to ensure debug info is displayed
            setTimeout(() => {
              window.location.href = sessionData.checkout_url;
            }, 500);
            return; // Stop execution as we're redirecting
          } 
          else if (activeTab === 'paypal') {
            // Handle PayPal payment
            debugData.steps.push({
              step: "PayPal payment",
              substep: "start",
              message: `Initiating PayPal payment for €${currentRemainingAmount}`
            });
            
            try {
              const sessionData = await createPayPalSession(currentRemainingAmount);
              
              // Update debug info with PayPal session data
              debugData.steps.push({
                step: "PayPal session",
                status: "success",
                payment_id: sessionData.payment_id,
                checkout_url: sessionData.checkout_url
              });
              
              // Set debug info and redirect
              debugData.endTime = new Date().toISOString();
              setDebugInfo(debugData);
              
              console.log("Redirecting to PayPal checkout:", sessionData.checkout_url);
              setTimeout(() => {
                window.location.href = sessionData.checkout_url;
              }, 500);
              
              return; // Stop execution as we're redirecting
            } catch (paypalError) {
              debugData.steps.push({
                step: "PayPal payment",
                substep: "error",
                status: "error",
                message: paypalError.message
              });
              
              throw paypalError;
            }
          }

          else if (activeTab === 'crypto') {
            // Handle CoinGate crypto payment
            debugData.steps.push({
              step: "CoinGate payment",
              substep: "start",
              message: `Initiating CoinGate cryptocurrency payment for €${currentRemainingAmount}`
            });
            
            try {
              const sessionData = await processCryptoPayment(currentRemainingAmount);
              
              // Update debug info with CoinGate session data
              debugData.steps.push({
                step: "CoinGate session",
                status: "success", 
                payment_id: sessionData.payment_id,
                checkout_url: sessionData.checkout_url,
                tracking_token: sessionData.tracking_token
              });
              
              // Set debug info and redirect
              debugData.endTime = new Date().toISOString();
              setDebugInfo(debugData);
              
              console.log("Redirecting to CoinGate checkout:", sessionData.checkout_url);
              setTimeout(() => {
                window.location.href = sessionData.checkout_url;
              }, 500);
              
              return; // Stop execution as we're redirecting
            } catch (cryptoError) {
              debugData.steps.push({
                step: "CoinGate payment", 
                substep: "error",
                status: "error",
                message: cryptoError.message
              });
              
              throw cryptoError;
            }
          }



        } catch (paymentError) {
          debugData.steps.push({
            step: "Payment processing",
            substep: "error", 
            status: "error",
            message: paymentError.message
          });

          throw paymentError;
        }
      }

      // This should only be reached for credit-only payments
      debugData.steps.push({
        step: "Payment completion",
        status: "success", 
        message: "Payment process completed successfully"
      });

      // Mark payment as completed (this should only happen for credit-only payments)
      setPaymentCompleted(true);
  
    } catch (error) {
      console.error('Payment error:', error);
  
      debugData.steps.push({
        step: "Error",
        status: "error",
        message: error.message,
        stack: error.stack
      });
  
      setError(error.message || 'An error occurred during payment processing');
    } finally {
      debugData.endTime = new Date().toISOString();
      setDebugInfo(debugData);
      setProcessingPayment(false);
    }
  };

  // Toggle apply full credit
  const handleApplyFullCredit = () => {
    if (!applyFullCredit) {
      const maxCredit = Math.min(availableCredit, grandTotal);
      console.log(`Setting max credit: ${maxCredit}`);
      setCreditAmount(maxCredit);
    } else {
      setCreditAmount(0);
    }
    setApplyFullCredit(!applyFullCredit);
  };

  // Handle credit amount change
  const handleCreditAmountChange = (e) => {
    const inputValue = e.target.value;
    if (inputValue === '') {
      setCreditAmount(0);
      return;
    }
    let value = parseFloat(inputValue) || 0;
    value = Math.min(value, availableCredit, grandTotal);
    value = Math.max(0, value);
    setCreditAmount(value);
  };

  // Calculate remaining amount after credit
  // Use initialTotalWithVat if available, otherwise use grandTotal
  const effectiveGrandTotal = parseFloat(initialTotalWithVat !== undefined ? initialTotalWithVat : grandTotal) || 0;
  const remainingAmount = effectiveGrandTotal - creditAmount;

  return (
    <Modal
      isOpen={isOpen}
      toggle={toggle}
      size="lg"
      backdrop="static"
      className="payment-modal"
      zIndex={11050}
    >
      <ModalHeader className="d-flex justify-content-between align-items-center">
        <div>{paymentCompleted ? 'Payment Successful' : 'Payment Options'}</div>
        <button type="button" className="close" onClick={toggle} style={{ position: 'absolute', right: '15px', top: '15px', background: 'none', border: 'none', fontSize: '1.5rem', lineHeight: '1', color: '#000', opacity: '0.5' }}>
          <span aria-hidden="true">&times;</span>
        </button>
      </ModalHeader>

      <ModalBody className="py-2">
        {error && (
          <Alert color="danger" className="py-2 mb-2">
            <i className="fa fa-exclamation-circle me-2"></i>
            <strong>Error:</strong> {error}
          </Alert>
        )}

        {generatingInvoice && !paymentCompleted && (
          <Alert color="info" className="py-2 mb-2">
            <div className="d-flex align-items-center">
              <i className="fa fa-spinner fa-spin me-2"></i>
              Generating invoice for your order, please wait...
            </div>
          </Alert>
        )}
        {allocationMessage && (
          <Alert color="info" className="py-2 mb-2">
            <div className="d-flex align-items-center">
              <i className="fa fa-info-circle me-2"></i>
              {allocationMessage}
            </div>
          </Alert>
        )}
        {debugInfo && (
          <div className="bg-light p-2 mb-2 border rounded" style={{fontSize: "0.75rem"}}>
            <pre className="m-0" style={{maxHeight: "100px", overflow: "auto"}}>
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>
        )}

        {paymentCompleted ? (
          <div className="text-center py-2">
            <i className="fa fa-check-circle text-success mb-2" style={{ fontSize: '48px' }}></i>
            <h4 className="mb-2">Thank you for your order!</h4>
            <p className="mb-2 small">Your server will be provisioned shortly. You'll receive an email with login details.</p>

            {orderId && <div className="mb-1 small"><strong>Order ID:</strong> #{orderId}</div>}

            {invoiceId && (
              <div className="mb-2 small">
                <strong>Invoice ID:</strong> #{invoiceId}
                <div className="mt-2">
                  <Link to={`/billing/invoices/${invoiceId}`} className="btn btn-sm btn-primary">
                    <i className="fa fa-file-text-o me-1"></i> View Invoice
                  </Link>
                </div>
              </div>
            )}
          </div>
        ) : (
          <>
            <div className="mb-2">
              {/* Combined Order Information and Payment Summary */}
              <div className="card mb-2">
                <div className="card-header py-2">
                  <h6 className="card-title mb-0">Order Details</h6>
                </div>
                <div className="card-body py-2">
                  <Row className="g-2">
                    {/* Order Information Column */}
                    <Col md="5" className="border-end small">
                      <div className="fw-bold mb-1">Order Information</div>
                      {orderId && <div className="mb-1"><strong>Order ID:</strong> #{orderId}</div>}
                      {invoiceId && (
                        <div>
                          <strong>Invoice ID:</strong> #{invoiceId}
                          <Link to={`/billing/invoices/${invoiceId}`} className="text-primary ms-1" style={{fontSize: '0.8rem'}}>
                            <i className="fa fa-external-link"></i>
                          </Link>
                        </div>
                      )}
                    </Col>

                    {/* Payment Summary Column */}
                    <Col md="7" className="small">
                      <div className="fw-bold mb-1">Payment Summary</div>
                      {loadingVat ? (
                        <div className="text-center py-1">
                          <Spinner size="sm" color="primary" />
                        </div>
                      ) : (
                        <>
                          <div className="d-flex justify-content-between mb-1">
                            <span>Subtotal:</span>
                            <span>€{subTotal.toFixed(2)}</span>
                          </div>
                          <div className="d-flex justify-content-between mb-1">
                            <span>VAT ({vatRate}%):</span>
                            <span>€{vatAmount.toFixed(2)}</span>
                          </div>
                          {vatNumber && (
                            <div className="d-flex justify-content-between mb-1">
                              <span>VAT Number:</span>
                              <span>{vatNumber}</span>
                            </div>
                          )}
                          <div className="border-top mt-1 pt-1 d-flex justify-content-between fw-bold">
                            <span>Total:</span>
                            <span>€{grandTotal.toFixed(2)}</span>
                          </div>
                        </>
                      )}
                    </Col>
                  </Row>
                </div>
              </div>

              {/* Credit Application Section - Only shown if user has actual credit AND NOT a Credit Purchase invoice */}
              {!isCreditsInvoice && availableCredit > 0 ? (
                <div className="mb-2 p-2 border rounded bg-light small">
                  <div className="d-flex justify-content-between align-items-center mb-1">
                    <div className="fw-bold">Apply Account Credit</div>
                    <div className="text-muted small">Available: €{availableCredit.toFixed(2)}</div>
                  </div>

                  <div className="input-group input-group-sm mb-1" >
                    <span className="input-group-text">€</span>
                    <Input
                      type="number"
                      value={creditAmount === 0 ? '' : creditAmount}
                      onChange={handleCreditAmountChange}
                      min="0"
                      max={Math.min(availableCredit, grandTotal)}
                      step="0.01"
                      disabled={applyFullCredit}
                      bsSize="sm"
                      style={{ minHeight: '40px' }}
                      placeholder="Enter amount"
                    />
                    <Button
                      color={applyFullCredit ? "primary" : "outline-primary"}
                      onClick={handleApplyFullCredit}
                      className="py-0"
                      size="sm"
                    >
                      Max
                    </Button>
                  </div>

                  {creditAmount > 0 && (
                    <Alert color="success" className="mb-0 py-1 px-2 small">
                      <strong>€{creditAmount.toFixed(2)}</strong> applied. Remaining: <strong>€{remainingAmount.toFixed(2)}</strong>
                    </Alert>
                  )}
                </div>
              ) : !isCreditsInvoice ? (
                // Optional: Link to add credit if user has none (but not for Credit Purchase invoices)
                <div className="mb-2">
                  <Alert color="info" className="mb-0 py-1 px-2 small">
                    <i className="fa fa-info-circle me-1" style={{paddingRight: '0.5rem'}}></i>
                    No credit available.
                    <Link to="/billing/credit" className="alert-link ms-1">Add credit</Link> for faster checkout.
                  </Alert>
                </div>
              ) : (
                // For Credit Purchase invoices, don't show any credit options
                null
              )}

              {/* Payment Method Tabs */}
              {remainingAmount > 0 && (
                <>
                  <div className="fw-bold small mb-1">Select Payment Method</div>
                  <div className="payment-methods mb-2">
                    <div className="d-flex gap-1">
                      <div
                        className={`flex-grow-1 payment-option small text-center py-1 ${activeTab === 'card' ? 'bg-light border border-primary' : 'border'}`}
                        onClick={() => toggleTab('card')}
                        style={{ cursor: 'pointer', borderRadius: '4px' }}
                      >
                        <i className="fa fa-credit-card"></i> Card
                      </div>
                      <div
                        className={`flex-grow-1 payment-option small text-center py-1 ${activeTab === 'paypal' ? 'bg-light border border-primary' : 'border'}`}
                        onClick={() => toggleTab('paypal')}
                        style={{ cursor: 'pointer', borderRadius: '4px' }}
                      >
                        <i className="fa fa-paypal"></i> PayPal
                      </div>
                      
                      <div
                        className={`flex-grow-1 payment-option small text-center py-1 ${activeTab === 'crypto' ? 'bg-light border border-primary' : 'border'}`}
                        onClick={() => toggleTab('crypto')}
                        style={{ cursor: 'pointer', borderRadius: '4px' }}
                      >
                        <i className="fa fa-bitcoin"></i> Crypto
                      </div>



                    </div>
                  </div>

                  <TabContent activeTab={activeTab}>
                    <TabPane tabId="card">
                      <div className="text-center p-2">
                        <img
                          src="/assets/images/stripe-logo.png" 
                          alt="Stripe"
                          style={{ height: '40px', marginBottom: '10px' }}
                        />
                        <p className="small mb-1">
                          Click the button below to proceed to our secure payment page.
                        </p>
                        <div className="d-flex justify-content-center gap-2 mb-2">
                          <img src="/assets/images/visa.png" alt="Visa" height="20" />
                          <img src="/assets/images/mastercard.png" alt="Mastercard" height="20" />
                          <img src="/assets/images/amex.png" alt="American Express" height="20" />
                        </div>
                        <Alert color="info" className="py-1 px-2 mb-0 small">
                          Your payment information is securely processed by Stripe.
                        </Alert>
                      </div>
                    </TabPane>

                    <TabPane tabId="paypal">
                      <div className="text-center p-2">
                        <img
                          src="/assets/images/paypal-logo.png"
                          alt="PayPal"
                          style={{ height: '40px', marginBottom: '10px' }}
                        />
                        <p className="small mb-1">Click the button below to be redirected to PayPal.</p>
                      </div>
                    </TabPane>

                    <TabPane tabId="crypto">
                      <div className="text-center p-2">
                        <div className="d-flex justify-content-center mb-2">
                          <img 
                            src="/assets/images/coingate-logo.png" 
                            alt="CoinGate" 
                            style={{ height: '40px', marginBottom: '10px' }}
                            onError={(e) => {
                              // Fallback to icons if logo not available
                              e.target.style.display = 'none';
                              e.target.nextElementSibling.style.display = 'flex';
                            }}
                          />
                          <div className="d-none justify-content-center">
                            <i className="fa fa-bitcoin text-warning me-1" style={{ fontSize: '20px' }}></i>
                            <i className="fab fa-ethereum text-primary mx-1" style={{ fontSize: '20px' }}></i>
                            <i className="fab fa-litecoin-sign text-secondary mx-1" style={{ fontSize: '20px' }}></i>
                          </div>
                        </div>
                        <p className="small mb-1">Pay with Bitcoin, Ethereum, Litecoin and 50+ other cryptocurrencies.</p>
                        <div className="mb-2">
                          <div className="d-flex justify-content-center align-items-center gap-2 mb-1">
                            <span className="badge bg-primary">Bitcoin</span>
                            <span className="badge bg-info">Ethereum</span>
                            <span className="badge bg-warning">Litecoin</span>
                            <span className="badge bg-secondary">+50 more</span>
                          </div>
                        </div>
                        <Alert color="info" className="py-1 px-2 mb-0 small">
                          <i className="fa fa-shield-alt me-1"></i>
                          Secure cryptocurrency payments powered by CoinGate
                        </Alert>
                      </div>
                    </TabPane>




                  </TabContent>
                </>
              )}
            </div>
          </>
        )}
      </ModalBody>

      <ModalFooter className="py-2">
        {paymentCompleted ? (
          <>
            {invoiceId ? (
              <Link to={`/billing/invoices/${invoiceId}`} className="btn btn-sm btn-primary modal-button">
                <i className="fa fa-file-text-o me-1"></i> View Invoice
              </Link>
            ) : (
              <Button size="sm" color="primary" onClick={onComplete} className="modal-button">
                Go to Dashboard
              </Button>
            )}
          </>
        ) : (
          <>
            <Button size="sm" color="secondary" onClick={toggle} className="modal-button">
              Cancel
            </Button>
            {!isCreditsInvoice && creditAmount === grandTotal ? (
              <Button size="sm" color="success" onClick={handlePayment} disabled={processingPayment} className="modal-button">
                {processingPayment ? (
                  <><i className="fa fa-spinner fa-spin me-1"></i></>
                ) : (
                  <>Apply Credit</>
                )}
              </Button>
            ) : (
              <Button
                size="sm" 
                color="success"
                type="button"
                onClick={handlePayment}
                disabled={processingPayment}
                className="modal-button"
              >
     {processingPayment ? (
  <><i className="fa fa-spinner fa-spin me-1"></i></>
) : (
  <>Pay €{remainingAmount.toFixed(2)}</>
)}
              </Button>
            )}
          </>
        )}
      </ModalFooter>
    </Modal>
  );
};

export default PaymentModal;