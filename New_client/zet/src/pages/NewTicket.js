import { useEffect, useState, useRef } from "react"
import { Link, useNavigate } from "react-router-dom";
import DiscountAlert from "../components/DiscountAlert";


const Ticket = () => {
  let navigate = useNavigate();

  // Function to get token from both storage locations
  function getToken() {
    const sessionToken = sessionStorage.getItem('token');
    const localToken = localStorage.getItem('token');
    return sessionToken || localToken;
  }

  const [services, setServices] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [formData, setFormData] = useState({
    subject: '',
    related_service: '',
    department: '',
    priority: 'Low',
    message: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // File attachment state
  const fileInputRef = useRef(null);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [fileUploadError, setFileUploadError] = useState('');

  useEffect(() => {
    // Fetch user services for dropdown
    const token = getToken();
    if (!token) {
      console.error("No authentication token found");
      setServices([]);
      return;
    }

    fetch("/api.php?f=user_services", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
    .then(response => response.json())
    .then(data => {
      // Ensure data is an array before setting it
      if (Array.isArray(data)) {
        setServices(data);
      } else {
        console.error("Services data is not an array:", data);
        setServices([]); // Set to empty array to avoid mapping errors
      }
    })
    .catch(error => {
      console.error("Error fetching services:", error);
      setServices([]); // Set to empty array on error
    });
  }, []);

  useEffect(() => {
    // Fetch departments for dropdown
    const token = getToken();
    if (!token) {
      console.error("No authentication token found");
      setDepartments([]);
      return;
    }

    fetch("/api.php?f=get_departments", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    })
    .then(response => response.json())
    .then(data => {
      // Ensure data is an array before setting it
      if (Array.isArray(data)) {
        setDepartments(data);
        // Set default department to the first available one (excluding L2 and L3)
        if (data.length > 0 && !formData.department) {
          const availableDepartments = data.filter(dept =>
            dept.department_name !== 'L2 Technical Department' &&
            dept.department_name !== 'L3 Technical Department'
          );
          if (availableDepartments.length > 0) {
            setFormData(prev => ({
              ...prev,
              department: availableDepartments[0].department_name
            }));
          }
        }
      } else {
        console.error("Departments data is not an array:", data);
        setDepartments([]); // Set to empty array to avoid mapping errors
      }
    })
    .catch(error => {
      console.error("Error fetching departments:", error);
      setDepartments([]); // Set to empty array on error
    });
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // File handling functions
  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    const validFiles = [];
    let hasError = false;

    files.forEach(file => {
      if (file.size > maxSize) {
        setFileUploadError(`File "${file.name}" exceeds 5MB limit`);
        hasError = true;
      } else {
        validFiles.push(file);
      }
    });

    if (!hasError) {
      setFileUploadError('');
      setSelectedFiles(validFiles);
    }
  };

  const removeFile = (index) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);
    if (newFiles.length === 0) {
      fileInputRef.current.value = '';
    }
  };

  const clearFiles = () => {
    setSelectedFiles([]);
    setFileUploadError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Format file size for display
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    // Validate form
    if (!formData.subject.trim()) {
      setError('Subject is required');
      setLoading(false);
      return;
    }
    
    if (!formData.message.trim()) {
      setError('Message is required');
      setLoading(false);
      return;
    }

    // Get token from storage
    const token = getToken();
    if (!token) {
      setError('Authentication error. Please try logging in again.');
      setLoading(false);
      return;
    }

    // Log the data being sent (for debugging)
    console.log('Submitting ticket with data:', formData);
    console.log('Using token:', token);

    // Create FormData for file upload
    const formData_submit = new FormData();
    formData_submit.append('subject', formData.subject);
    formData_submit.append('related_service', formData.related_service);
    formData_submit.append('department', formData.department);
    formData_submit.append('priority', formData.priority);
    formData_submit.append('message', formData.message);
    formData_submit.append('token', token);

    // Add files to FormData
    selectedFiles.forEach((file, index) => {
      formData_submit.append(`files[${index}]`, file);
    });

    // Submit ticket
    fetch("/api.php?f=create_ticket", {
      method: "POST",
      body: formData_submit // Don't set Content-Type header, let browser set it with boundary
    })
    .then(response => {
      console.log('Response status:', response.status);
      return response.json().catch(e => {
        // Handle non-JSON responses
        console.error('Error parsing JSON:', e);
        throw new Error('Invalid response format from server');
      });
    })
    .then(data => {
      setLoading(false);
      console.log('Response data:', data);
      
      if (data.success) {
        setSuccess(true);
        clearFiles(); // Clear selected files on success
        // Reset form
        setFormData({
          subject: '',
          related_service: '',
          department: departments.length > 0 ? departments.filter(d =>
            d.department_name !== 'L2 Technical Department' &&
            d.department_name !== 'L3 Technical Department'
          )[0]?.department_name || '' : '',
          priority: 'Low',
          message: ''
        });
        // Redirect to ticket view after 1.5 seconds
        setTimeout(() => {
          navigate(`/support/${data.ticket_id}`);
        }, 1500);
      } else {
        // Show detailed error information
        if (data.error === 3) {
          setError('Please fill in all required fields.');
        } else if (data.error === 4) {
          setError('Authentication error. Please try logging in again.');
        } else if (data.error === 6) {
          setError('Database error: ' + (data.message || 'Unknown error'));
        } else {
          setError(data.message || 'Failed to create ticket. Please try again.');
        }
      }
    })
    .catch(error => {
      setLoading(false);
      console.error("Error creating ticket:", error);
      setError('Network error: ' + error.message);
    });
  };

  return (
    <>

      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/support">Support</Link></li>
            <li className="breadcrumb-item1">New Case</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>
      <div className="row">
        <div className="col-12">
          <DiscountAlert />

          <div className="card noheight">
            <div className="card-header">
              <div className="card-title">New Case</div>
            </div>
            <form onSubmit={handleSubmit}>
              <div className="card-body">
                {error && (
                  <div className="alert alert-danger" role="alert">
                    <i className="fa fa-exclamation-triangle me-2"></i> {error}
                  </div>
                )}
                {success && (
                  <div className="alert alert-success" role="alert">
                    <i className="fa fa-check-circle me-2"></i> Ticket created successfully! Redirecting...
                  </div>
                )}
                <div className="row">
                  <div className="col-sm-6 col-md-6">
                    <div className="form-group">
                      Subject
                      <input 
                        type="text" 
                        className="form-control" 
                        placeholder="Subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="col-sm-6 col-md-6">
                    <div className="form-group">
                      Related Service
                      <select
                        className="form-control nice-select select2"
                        name="related_service"
                        value={formData.related_service}
                        onChange={handleInputChange}
                      >
                        <option value="">--New Service--</option>
                        {services && services.length > 0 ? (
                          services
                            .sort((a, b) => {
                              // Define status priority order
                              const statusPriority = {
                                'active': 1,
                                'suspended': 2,
                                'pending': 3,
                                'terminated': 4,
                                'cancelled': 5,
                                'unknown': 6
                              };

                              const statusA = (a.status || 'unknown').toLowerCase();
                              const statusB = (b.status || 'unknown').toLowerCase();

                              const priorityA = statusPriority[statusA] || 6;
                              const priorityB = statusPriority[statusB] || 6;

                              // Sort by status priority first, then by label alphabetically
                              if (priorityA !== priorityB) {
                                return priorityA - priorityB;
                              }
                              return a.label.localeCompare(b.label);
                            })
                            .map(service => {
                              const displayStatus = service.status || 'Unknown';

                              return (
                                <option
                                  key={service.id}
                                  value={service.id}
                                  title={`${service.label} - Status: ${displayStatus} - Type: ${service.type || 'Unknown'}`}
                                >
                                  {service.label} - {displayStatus}
                                </option>
                              );
                            })
                        ) : (
                          <option disabled value="">No services available</option>
                        )}
                      </select>
                    </div>
                  </div>
                  <div className="col-sm-6 col-md-6">
                    <div className="form-group">
                      Department
                      <select
                        className="form-control nice-select select2"
                        name="department"
                        value={formData.department}
                        onChange={handleInputChange}
                      >
                        {departments && departments.length > 0 ? (
                          departments
                            .filter(department =>
                              department.department_name !== 'L2 Technical Department' &&
                              department.department_name !== 'L3 Technical Department'
                            )
                            .map(department => (
                              <option key={department.id} value={department.department_name}>
                                {department.department_name}
                              </option>
                            ))
                        ) : (
                          <option value="">Loading departments...</option>
                        )}
                      </select>
                    </div>
                  </div>
                  <div className="col-sm-6 col-md-6">
                    <div className="form-group">
                      Priority
                      <select 
                        className="form-control nice-select select2"
                        name="priority"
                        value={formData.priority}
                        onChange={handleInputChange}
                      >
                        <option value="Low">Low</option>
                        <option value="Medium">Medium</option>
                        <option value="High">High</option>
                        <option value="Urgent">Urgent</option>
                      </select>
                    </div>
                  </div>

                  <div className="col-sm-12 col-md-12">
                    <div className="form-group">
                      Message
                      <textarea
                        className="form-control"
                        rows="5"
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                      ></textarea>
                    </div>
                  </div>

                  {/* File attachment section */}
                  <div className="col-sm-12 col-md-12">
                    <div className="form-group">
                      <label>Attachments (optional)</label>
                      <div className="d-flex align-items-center">
                        <button
                          type="button"
                          className="btn btn-outline-secondary"
                          onClick={() => fileInputRef.current?.click()}
                          disabled={loading}
                        >
                          <i className="fa fa-paperclip mr-2"></i>
                          Choose Files
                        </button>
                        <small className="text-muted ml-3">
                          Maximum file size: 5MB each
                        </small>
                      </div>

                      {/* Hidden file input */}
                      <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleFileSelect}
                        multiple
                        className="d-none"
                        disabled={loading}
                      />

                      {/* File upload error */}
                      {fileUploadError && (
                        <div className="alert alert-danger mt-2 py-2">
                          <small>{fileUploadError}</small>
                        </div>
                      )}

                      {/* Selected files preview */}
                      {selectedFiles.length > 0 && (
                        <div className="mt-3">
                          <div className="d-flex justify-content-between align-items-center mb-2">
                            <small className="text-muted">
                              <i className="fa fa-paperclip mr-1"></i>
                              <strong>{selectedFiles.length}</strong> file{selectedFiles.length > 1 ? 's' : ''} selected
                            </small>
                            <button
                              type="button"
                              className="btn btn-sm btn-outline-danger"
                              onClick={clearFiles}
                              disabled={loading}
                            >
                              <i className="fa fa-times mr-1"></i>
                              Clear all
                            </button>
                          </div>
                          {selectedFiles.map((file, index) => {
                            // Get file icon based on type
                            const getFileIcon = (file) => {
                              if (file.type.includes('image/')) return 'fa-file-image-o';
                              if (file.type.includes('pdf')) return 'fa-file-pdf-o';
                              if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) return 'fa-file-word-o';
                              if (file.type.includes('excel') || file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) return 'fa-file-excel-o';
                              if (file.type.includes('zip') || file.type.includes('rar')) return 'fa-file-archive-o';
                              if (file.type.includes('text')) return 'fa-file-text-o';
                              return 'fa-file-o';
                            };

                            return (
                              <div key={index} className="d-flex align-items-center p-2 mb-2 bg-light border rounded">
                                <div className="mr-3">
                                  <i className={`fa ${getFileIcon(file)} fa-lg text-primary`}></i>
                                </div>
                                <div className="flex-grow-1">
                                  <div className="font-weight-bold text-dark" style={{fontSize: '13px'}}>
                                    {file.name}
                                  </div>
                                  <div className="text-muted" style={{fontSize: '11px'}}>
                                    {formatFileSize(file.size)}
                                    {file.type && ` • ${file.type.split('/')[1]?.toUpperCase()}`}
                                  </div>
                                </div>
                                <button
                                  type="button"
                                  className="btn btn-sm btn-outline-danger ml-2"
                                  onClick={() => removeFile(index)}
                                  disabled={loading}
                                  style={{padding: '4px 8px'}}
                                >
                                  <i className="fa fa-times"></i>
                                </button>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <b>*Our services are unmanaged. You should only contact NOC for matters that are not under your control (network issues, availability or hardware failures)<br/>**Sales and Billing departments are only available Monday by Friday between 09:00 and 18:00 AM GMT+3<br/><font color="red">Please open a single case for one matter. Opening multiple cases for the same matter will slower the answering time.</font></b>
              </div>

              <div className="card-footer text-right">
                <button 
                  type="submit" 
                  className="btn btn-success"
                  disabled={loading}
                >
                  {loading ? 'Creating...' : 'Open Case'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default Ticket;