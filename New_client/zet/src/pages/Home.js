import React, { useEffect, useState } from "react";
import { Toast } from 'primereact/toast';
import { Outlet, Link } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import 'primereact/resources/themes/mdc-light-indigo/theme.css';
import "primereact/resources/primereact.min.css";
import 'primeflex/primeflex.css';
import { Dialog } from 'primereact/dialog';
import DiscountAlert from "../components/DiscountAlert";
import './Home.css';
// React Icons imports for metrics
import { FaLayerGroup, FaCheckCircle, FaClock, FaExclamationTriangle } from 'react-icons/fa';

const Home = () => {
  const toast = React.useRef(null);
  let navigate = useNavigate();
  const routeChange = (path) =>{
    navigate(path);
  }

  function getToken() {
    const userToken = sessionStorage.getItem('token');
    return userToken
  }

  const token = getToken();
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [servers, setServer] = useState([]);
  const [loading, setLoading] = useState(true);
  const [hideUnpaid, setHideUnpaid] = useState(true);
  const [totalServers, setTotalServers] = useState(0);
  const [activeServers, setActiveServers] = useState(0);
  const [pendingServers, setPendingServers] = useState(0);
  const [suspendedServers, setSuspendedServers] = useState(0);
  const [canceledServers, setCanceledServers] = useState(0);
  const [terminatedServers, setTerminatedServers] = useState(0);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  
  // Add access control state
  const [hasAccess, setHasAccess] = useState(null); // Start with null to show loading
  const [accessError, setAccessError] = useState('');

  // Add event listener for window resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);

    // Clean up the event listener on component unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  function fetchServersData(input) {
    setLoading(true);
    fetch("/api.php?f=user_services", {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(input)
    })
    .then(response => {
      console.log("Response status:", response.status);
      
      // Check for 403 status code BEFORE trying to parse JSON
      if (response.status === 403) {
        console.log("403 detected - setting no access");
        setHasAccess(false);
        setAccessError('You do not have permission to access services');
        setLoading(false);
        throw new Error('Access denied'); // Stop the promise chain
      }
      
      // Check if response is ok for other status codes
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.json();
    })
    .then(data => {
      console.log("API Response Data:", data);
      
      // Additional check for error messages in the JSON response
      if(data.error) {
        if(data.error == 5) {
          toast.current.show({
            severity: 'error',
            summary: 'Session Timeout',
            detail: 'Your login session has timed out',
            life: 5000
          });
          window.location.reload(false);
        } else if(data.message && (data.message.includes('permission') || data.message.includes('Access denied'))) {
          // Handle permission errors in JSON response
          console.log("Permission error in JSON response");
          setHasAccess(false);
          setAccessError(data.message || 'Access denied');
          setLoading(false);
          return;
        } else {
          toast.current.show({
            severity: 'error',
            summary: 'Error',
            detail: 'ERROR: '+data.error,
            life: 5000
          });
        }
      } else {
        // Success - data loaded properly
        console.log("Services data loaded successfully");
        data.forEach(server => {
          console.log(`Server ID: ${server.id}, Type: ${server.type}, server_id: ${server.server_id}, order_id: ${server.order_id}`);
        });
        
        setServer(data);
        setHasAccess(true); // Ensure access is set to true on successful load

        // Calculate statistics
        setTotalServers(data.length);
        setActiveServers(data.filter(s => s.status === 'Active').length);
        setPendingServers(data.filter(s => s.status === 'Pending' || s.status === 'Installing' || s.status === 'Pending_Termination' || s.status === 'PendingPayment').length);
        setSuspendedServers(data.filter(s => s.status === 'Suspended').length);
        setCanceledServers(data.filter(s => s.status === 'Canceled').length);
        setTerminatedServers(data.filter(s => s.status === 'Terminated' || s.status === 'Unpaid').length);
      }
      setLoading(false);
    })
    .catch(error => {
      console.error("Error fetching server data:", error);
      
      // If the error is "Access denied", don't show additional error
      if (error.message !== 'Access denied') {
        // Handle other network errors
        setHasAccess(true); // Keep page accessible for network errors
      }
      setLoading(false);
    });
  }

  useEffect(() => {
    fetchServersData({token});
  }, []);

  const imageCountry = (server) => {
    return (
      <div className="d-flex align-items-center" style={{ justifyContent: 'flex-start', width: '100%' }}>
        <i className={`flag flag-${server.location}`} style={{ marginRight: '8px', display: 'inline-block' }}></i>
        <span style={{ display: 'inline-block' }}>{server.locationname}</span>
      </div>
    );
  };

  const Price = (server) => {
    return <>&euro;{server.price}</>;
  };

  const Status = (server) => {
    return (
    <>
      <Toast ref={toast} />
        {server.status === 'Active' && <span className="badge bg-success text-white" style={{minWidth: '120px'}}>Active</span>}
        {server.status === 'Pending' && <span className="badge bg-warning text-white" style={{minWidth: '120px'}}>Pending</span>}
        {server.status === 'Suspended' && <span className="badge bg-warning text-white" style={{minWidth: '120px'}}>Suspended</span>}
        {server.status === 'Canceled' && <span className="badge bg-secondary text-white" style={{minWidth: '120px'}}>Canceled</span>}
        {server.status === 'Terminated' && <span className="badge bg-danger text-white" style={{minWidth: '120px'}}>Terminated</span>}
        {server.status === 'Unpaid' && <span className="badge bg-danger text-white" style={{minWidth: '120px'}}>Terminated</span>}
        {server.status === 'Installing' && (
          <span className="badge bg-warning text-white" style={{minWidth: '120px'}}>
            <i className="spinner-border spinner-border-sm" style={{width: "0.5rem", height: "0.75rem", marginRight: '0.75rem'}} role="status" aria-hidden="true"></i>
            Installing
          </span>
        )}
        {server.status === 'PendingPayment' && <span className="badge bg-info text-white" style={{minWidth: '120px'}}>Pending Payment</span>}
        {server.status === 'Pending_Termination' && <span className="badge bg-warning text-white" style={{minWidth: '120px'}}>P. Termination</span>}
      </>
    );
  };

  const ServerLabel = (server) => {
    return (
      <div className="d-flex align-items-center">
        <i className={`fa fa-${server.type === 'Dedicated' ? 'server' : 'cloud'} text-primary me-2`}style={{paddingRight: '0.5rem'}}></i>
        <span>{server.label}</span>
      </div>
    );
  };

  const ExpiryDate = (server) => {
    // If server is suspended, show suspended status
    if (server.status === 'Suspended') {
      return (
        <div>
          <div>{server.expires}</div>
          <small className="text-danger" style={{minWidth: '120px'}}>Suspended</small>
        </div>
      );
    }

    // Calculate days until expiry
    const today = new Date();
    const expiryDate = new Date(server.expires);
    const daysUntil = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

    return (
      <div>
        <div>{server.expires}</div>
        {daysUntil <= 7 && daysUntil >= 0 && (
          <small className="text-warning">{daysUntil} days left</small>
        )}
        {daysUntil < 0 && (
          <small className="text-danger">Expired</small>
        )}
      </div>
    );
  };

  const isSelectable = (server) => {
    return (server.status === 'Installing' || server.status === 'Pending' || server.status === 'Terminated' || server.status === 'Unpaid' || server.status === 'Canceled') ? false : true
  };

  const rowClassName = (server) => {
    return (server.status === 'Installing' || server.status === 'Pending' || server.status === 'Terminated' || server.status === 'Unpaid' || server.status === 'Canceled') ? 'p-disabled' : 'p-selectable-row'
  };

  const handleRowClick = (e) => {
    // Prevent navigation for terminated, canceled, or unpaid services
    if (e.data.status === 'Terminated' || e.data.status === 'Unpaid' || e.data.status === 'Canceled') {
      return;
    }

    // Log the data being processed
    console.log("Row clicked data:", e.data);
    console.log("Type:", e.data.type, "Type exact match check:", e.data.type === 'Cloud', "Type lowercase check:", e.data.type.toLowerCase() === 'Cloud');

    // Check for VPS service type first (case-insensitive)
    if (e.data.type && (e.data.type === 'Cloud' || e.data.type.toLowerCase() === 'Cloud')) {
      // For VPS services, use the server_id instead of order_id
      if (e.data.server_id) {
        console.log("Routing to VPS services with server_id:", '/vps-services/' + e.data.server_id);
        routeChange('/vps-services/' + e.data.server_id);
      } else {
        console.error('Error: Attempted to navigate to a VPS server without a server_id');
        toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Error: No server has been allocated. Please contact support.',
        life: 5000
      });
      }
      return;
    }

    // Check server type and redirect to the appropriate view
    switch (e.data.type.toLowerCase()) {
      case 'cloud':
        // For Cloud services, use the order_id
        console.log("Routing to cloud services:", '/cloud-services/' + e.data.order_id);
        routeChange('/vps-services/' + e.data.order_id);
        break;
      case 'iptransit':
        // For IPTransit services, use the order_id
        console.log("Routing to IP transit services:", '/iptransit-services/' + e.data.order_id);
        routeChange('/iptransit-services/' + e.data.order_id);
        break;
      case 'colocation':
        // For Colocation services, use the order_id
        console.log("Routing to colocation services:", '/colocation-services/' + e.data.order_id);
        routeChange('/colocation-services/' + e.data.order_id);
        break;
      case 'colocationasset':
        // For ColocationAsset services, use the order_id
        console.log("Routing to colocation assets services:", '/colocation-assets-services/' + e.data.order_id);
        routeChange('/colocation-assets-services/' + e.data.order_id);
        break;
      default:
        // For dedicated servers, ONLY use server_id if it exists
        // If server_id doesn't exist, don't navigate (prevent errors)
        console.log("Default case - server type:", e.data.type);
        if (e.data.server_id) {
          console.log("Routing to default services:", '/services/' + e.data.server_id);
          routeChange('/services/' + e.data.server_id);
        } else {
          console.error('Error: Attempted to navigate to a dedicated server without a server_id');
          // Optionally show an error message to the user
          toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Error: No server has been allocated. Please contact support.',
        life: 5000
      });
        }
        break;
    }
  };

  // Filter the servers based on hideUnpaid state
  const filteredServers = hideUnpaid
    ? servers.filter(server => server.status !== 'Unpaid' && server.status !== 'Terminated' && server.status !== 'Canceled')
    : servers;

  // Determine which columns to display based on screen width
  const shouldShowExpiresColumn = windowWidth > 992; // Hide on medium and smaller screens
  const shouldShowPriceColumn = windowWidth > 768; // Hide on small and smaller screens

  // No Access Component
  const NoAccessMessage = () => (
    <div className="text-center p-5">
      <i className="fa fa-lock fa-4x text-danger mb-4"></i>
      <h3 className="text-danger mb-3">Access Denied</h3>
      <p className="text-muted mb-4" style={{fontSize: '1.1rem'}}>
        {accessError || 'You do not have permission to access this section.'}
      </p>
      <p className="text-muted">
        Please contact your administrator if you believe this is an error.
      </p>
      <div className="mt-4">
        <button 
          className="btn btn-secondary me-2" 
          onClick={() => window.location.reload()}
        >
          <i className="fa fa-refresh me-1"></i> Refresh Page
        </button>
        <Link to="/profile" className="btn btn-primary">
          <i className="fa fa-user me-1"></i> Go to Profile
        </Link>
      </div>
    </div>
  );

  return (
    <>
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Home</Link></li>
            <li className="breadcrumb-item1">Services</li>
          </ol>
        </div>
        {hasAccess && (
          <div className="page-rightheader ml-auto">
            <div className="dropdown">
              <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
                <button className="btn btn-success">New Service</button>
              </a>
              <div className="dropdown-menu">
                <Link className="dropdown-item d-flex" to="/dedicatedorder">
                  <div className="mt-1">Dedicated Server</div>
                </Link>
                <Link className="dropdown-item d-flex" to="/cloudorder">
                  <div className="mt-1">Cloud Server</div>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="row">
        <div className="col-12">
          {hasAccess === true && <DiscountAlert />}
          
          {hasAccess === false ? (
            // Show No Access message
            <div className="card">
              <div className="card-body">
                <NoAccessMessage />
              </div>
            </div>
          ) : hasAccess === null ? (
            // Show loading while checking access
            <div className="card">
              <div className="card-body">
                <div className="d-flex justify-content-center p-5">
                  <div className="spinner-border text-primary" role="status">
               
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <>
              {/* Service Summary Cards - only show if user has access */}
              <div className="row mb-4">
                <div className="col-lg-3 col-md-6 col-sm-12">
                  <div className="card">
                    <div className="card-body d-flex align-items-center">
                      <div className="me-4" style={{paddingLeft: '2.0rem'}}>
          
                          <FaLayerGroup className="text-primary" size={30} />
                 
                      </div>
                      <div>
                        <h6 className="mb-1" style={{paddingLeft: '1.5rem'}}>Total Services</h6>
                        <h2 className="mb-0 font-weight-bold" style={{paddingLeft: '1.5rem'}}>{totalServers}</h2>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="col-lg-3 col-md-6 col-sm-12">
                  <div className="card">
                    <div className="card-body d-flex align-items-center">
                      <div className="me-4" style={{paddingLeft: '2.0rem'}}>
                 
                          <FaCheckCircle className="text-success" size={30} />
              
                      </div>
                      <div>
                        <h6 className="mb-1" style={{paddingLeft: '1.5rem'}}>Active Services</h6>
                        <h2 className="mb-0 font-weight-bold" style={{paddingLeft: '1.5rem'}}>{activeServers}</h2>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="col-lg-3 col-md-6 col-sm-12">
                  <div className="card">
                    <div className="card-body d-flex align-items-center">
                      <div className="me-4" style={{paddingLeft: '2.0rem'}}>
                       
                          <FaClock className="text-warning" size={30} />
                   
                      </div>
                      <div>
                        <h6 className="mb-1" style={{paddingLeft: '1.5rem'}}>Pending</h6>
                        <h2 className="mb-0 font-weight-bold" style={{paddingLeft: '1.5rem'}}>{pendingServers}</h2>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="col-lg-3 col-md-6 col-sm-12">
                  <div className="card">
                    <div className="card-body d-flex align-items-center">
                      <div className="me-4" style={{paddingLeft: '2.0rem'}}>
                       
                          <FaExclamationTriangle className="text-danger" size={30} />
                 
                      </div>
                      <div>
                        <h6 className="mb-1" style={{paddingLeft: '1.5rem'}}>Terminated</h6>
                        <h2 className="mb-0 font-weight-bold" style={{paddingLeft: '1.5rem'}}>{terminatedServers}</h2>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="card smaller">
                <div className="card-header">
                  <div className="card-title">Services</div>
                  <div className="card-options">
                    <button
                      className={`btn ${hideUnpaid ? 'btn-primary' : 'btn-secondary'}`}
                      onClick={() => setHideUnpaid(!hideUnpaid)}
                      style={{
                        borderRadius: '0.25rem',
                        padding: '0.375rem 0.75rem',
                        marginRight: '0.5rem',
                        minWidth: '220px'
                      }}
                    >
                      {hideUnpaid ? 'Show Inactive Services' : 'Hide Inactive Services'}
                    </button>
                  </div>
                </div>
                <div className="card-body">
                  {loading ? (
                    <div className="d-flex justify-content-center p-5">
                      <div className="spinner-border text-primary" role="status">
                      </div>
                    </div>
                  ) : filteredServers.length === 0 ? (
                    <div className="text-center p-5">
                      <i className="fa fa-server fa-3x text-muted mb-3"></i>
                      <h4>No Services Found</h4>
                      <p className="text-muted">
                        {hideUnpaid && (terminatedServers + canceledServers > 0) ?
                          `${terminatedServers + canceledServers} inactive servers are hidden. Turn off the filter to see them.` :
                          "You don't have any services yet. Get started by ordering your first server."}
                      </p>
                      <Link to="/dedicatedorder" className="btn btn-primary mt-2">
                        <i className="fa fa-plus me-1"></i> Add New Server
                      </Link>
                    </div>
                  ) : (
                    <DataTable
                      value={filteredServers}
                      isDataSelectable={isSelectable}
                      rowClassName={rowClassName}
                      onRowClick={handleRowClick}
                      sortField="status"
                      sortOrder={1}
                      paginator
                      rows={10}
                      rowsPerPageOptions={[5, 10, 25, 50]}
                      emptyMessage="No services found"
                      loading={loading}
                      responsiveLayout="scroll"
                      className="server-table"
                    >
                      <Column field="id" sortable header="ID" headerStyle={{ fontWeight: 'bold', width: '8%' }} style={{cursor: 'pointer'}}></Column>
                      <Column field="label" body={ServerLabel} sortable header="Label" headerStyle={{ fontWeight: 'bold', width: '25%' }} style={{cursor: 'pointer'}}></Column>
                      <Column field="status" body={Status} sortable header="Status" headerStyle={{ fontWeight: 'bold', width: '10%' }} style={{cursor: 'pointer'}}></Column>
                      <Column
                        field="location"
                        body={imageCountry}
                        sortable
                        header="Location"
                        headerStyle={{ fontWeight: 'bold', width: '15%' }}
                        style={{ cursor: 'pointer' }}
                      />

                      {/* Conditionally render expires column based on screen width */}
                      {shouldShowExpiresColumn && (
                        <Column field="expires" body={ExpiryDate} sortable header="Expires" headerStyle={{ fontWeight: 'bold', width: '15%' }} style={{cursor: 'pointer'}} className="expires-column"></Column>
                      )}

                      <Column field="type" sortable header="Type" headerStyle={{ fontWeight: 'bold', width: '12%' }} style={{cursor: 'pointer'}}></Column>

                      {/* Conditionally render price column based on screen width */}
                      {shouldShowPriceColumn && (
                        <Column field="price" body={Price} sortable header="Price" headerStyle={{ fontWeight: 'bold' }} style={{cursor: 'pointer'}} className="price-column"></Column>
                      )}

                      <Column
                        body={(rowData) => (
                          <div className="text-center">
                            <button
                              className="btn btn-sm btn-primary"
                              disabled={rowData.status === 'Terminated' || rowData.status === 'Unpaid' || rowData.status === 'Canceled'}
                              onClick={(e) => {
                                e.stopPropagation();
                                // Debug the rowData
                                console.log("Action button clicked for row:", rowData);
                                console.log("Row type:", rowData.type, "server_id:", rowData.server_id, "order_id:", rowData.order_id);
                                // Don't modify the data, just pass it to handleRowClick
                                handleRowClick({data: rowData});
                              }}
                              style={{
                                opacity: (rowData.status === 'Terminated' || rowData.status === 'Unpaid' || rowData.status === 'Canceled') ? '0.65' : '1'
                              }}
                            >
                              Manage
                            </button>
                          </div>
                        )}
                        header="Actions"
                        headerStyle={{ fontWeight: 'bold', textAlign: 'center', paddingLeft: '1.5rem' }}
                        style={{textAlign: 'center'}}
                      ></Column>
                    </DataTable>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default Home;