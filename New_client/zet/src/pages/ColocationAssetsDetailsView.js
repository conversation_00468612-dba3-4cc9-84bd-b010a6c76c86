import React, { useState, useEffect, useCallback } from "react";
import { Outlet, Link, useParams, useNavigate, useLocation } from "react-router-dom";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, AreaChart, Area } from 'recharts';
import {
  MDBModal,
  MDBModalDialog,
  MDBModalContent,
  MDBModalHeader,
  MDBModalTitle,
  MDBModalBody,
  MDBModalFooter,
} from 'mdb-react-ui-kit';

const ColocationAssetsDetailsView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [asset, setAsset] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [modalShow, setModalShow] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [modalBody, setModalBody] = useState('');
  const [modalFooter, setModalFooter] = useState('');
  
  // Sample power usage data for the graph
  const powerData = [
    { time: '00:00', usage: 1.3 },
    { time: '02:00', usage: 1.1 },
    { time: '04:00', usage: 0.9 },
    { time: '06:00', usage: 1.2 },
    { time: '08:00', usage: 1.8 },
    { time: '10:00', usage: 2.5 },
    { time: '12:00', usage: 2.2 },
    { time: '14:00', usage: 2.0 },
    { time: '16:00', usage: 2.4 },
    { time: '18:00', usage: 2.6 },
    { time: '20:00', usage: 2.1 },
    { time: '22:00', usage: 1.7 },
  ];
  
  // Sample temperature data
  const temperatureData = [
    { time: '00:00', temp: 21.3 },
    { time: '02:00', temp: 20.9 },
    { time: '04:00', temp: 20.5 },
    { time: '06:00', temp: 20.8 },
    { time: '08:00', temp: 21.2 },
    { time: '10:00', temp: 22.5 },
    { time: '12:00', temp: 23.1 },
    { time: '14:00', temp: 23.5 },
    { time: '16:00', temp: 23.8 },
    { time: '18:00', temp: 23.2 },
    { time: '20:00', temp: 22.4 },
    { time: '22:00', temp: 21.8 },
  ];

  // Sample colocation asset data - in a real application, this would come from an API
  const sampleAsset = {
    id: id,
    name: "COLO-AS734",
    status: "Active",
    assetType: "Server",
    model: "Dell PowerEdge R740",
    specifications: {
      cpu: "2x Intel Xeon Gold 6248R",
      cores: 48,
      ram: "384 GB DDR4",
      storage: "8x 1.92TB SSD RAID10",
      networkCards: "2x 10Gbps"
    },
    rackDetails: {
      size: "2U",
      position: "Rack R012, Units 24-25",
      powerConsumption: "750W (avg), 1100W (max)",
      powerConnections: "2x C14 (redundant)",
      powerMeasured: "2.1 kW",
      powerAllocated: "3.0 kW"
    },
    location: {
      flag: "de",
      name: "Frankfurt, Germany",
      datacenter: "Equinix FR7",
      row: "A",
      rack: "R012"
    },
    ipAddresses: ["************", "************", "************", "************"],
    accessDetails: {
      remoteHands: "Available 24/7",
      remoteAccess: "IPMI 2.0, iDRAC Enterprise",
      physicalAccess: "24/7 with prior notification"
    },
    rental: {
      type: "Full Ownership",
      term: "36 Months",
      startDate: "January 15, 2025",
      endDate: "January 14, 2028"
    },
    monitoring: {
      uptimePercentage: "99.998%",
      currentUptime: "124 days, 7 hours",
      alertsEnabled: true,
      alertContacts: ["<EMAIL>", "<EMAIL>"]
    },
    billing: {
      price: "€349.00",
      billingCycle: "Monthly",
      nextRenewal: "April 15, 2025",
      autoRenew: true
    },
    maintenanceHistory: [
      {
        date: "February 12, 2025",
        type: "Scheduled",
        description: "Monthly preventive maintenance",
        technician: "John Smith"
      },
      {
        date: "January 05, 2025",
        type: "Emergency",
        description: "Power supply replacement",
        technician: "David Jones"
      }
    ]
  };

  useEffect(() => {
    // Simulate API call to fetch asset details
    const fetchAssetDetails = () => {
      setLoading(true);
      // For demo purposes, use sample data
      setTimeout(() => {
        setAsset(sampleAsset);
        setLoading(false);
      }, 300);
    };

    fetchAssetDetails();
  }, [id]);

  // Request Additional IP modal
  const requestIpModal = {
    title: <>Request Additional IP Address</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">IP Address Type:</label>
        <select name="ip_type" className="form-control custom-select mb-3">
          <option value="single">Single IP Address (+€3.00/mo)</option>
          <option value="subnet4">IPv4 /29 Subnet - 8 IPs (+€15.00/mo)</option>
          <option value="subnet8">IPv4 /28 Subnet - 16 IPs (+€30.00/mo)</option>
        </select>
        
        <label className="form-label mb-2">Purpose:</label>
        <textarea 
          className="form-control mb-3" 
          name="purpose" 
          rows="3" 
          placeholder="Please describe the purpose for additional IP addresses..."
          required
        ></textarea>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          IP address allocation is typically processed within 24 hours. You will receive configuration details via email once allocated.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Submit Request</button></>
  };
  
  // Add Alert Contact modal
  const addAlertContactModal = {
    title: <>Add Alert Contact</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Contact Email:</label>
        <input 
          type="email" 
          className="form-control mb-3" 
          placeholder="Enter email address" 
          required
        />
        
        <label className="form-label mb-2">Alert Types:</label>
        <div className="form-check mb-2">
          <input 
            type="checkbox" 
            className="form-check-input" 
            id="powerAlert"
            style={{ 
              marginRight: '5px',
              marginLeft: '0',
              marginTop: '0',
              marginBottom: '0',
              padding: '0',
              position: 'relative',
              float: 'none'
            }}
            defaultChecked
          />
          <label className="form-check-label" htmlFor="powerAlert">
            Power consumption alerts
          </label>
        </div>
        <div className="form-check mb-2">
          <input 
            type="checkbox" 
            className="form-check-input" 
            id="tempAlert"
            style={{ 
              marginRight: '5px',
              marginLeft: '0',
              marginTop: '0',
              marginBottom: '0',
              padding: '0',
              position: 'relative',
              float: 'none'
            }}
            defaultChecked
          />
          <label className="form-check-label" htmlFor="tempAlert">
            Temperature alerts
          </label>
        </div>
        <div className="form-check mb-2">
          <input 
            type="checkbox" 
            className="form-check-input" 
            id="maintenanceAlert"
            style={{ 
              marginRight: '5px',
              marginLeft: '0',
              marginTop: '0',
              marginBottom: '0',
              padding: '0',
              position: 'relative',
              float: 'none'
            }}
            defaultChecked
          />
          <label className="form-check-label" htmlFor="maintenanceAlert">
            Maintenance notifications
          </label>
        </div>
        <div className="form-check mb-3">
          <input 
            type="checkbox" 
            className="form-check-input" 
            id="statusAlert"
            style={{ 
              marginRight: '5px',
              marginLeft: '0',
              marginTop: '0',
              marginBottom: '0',
              padding: '0',
              position: 'relative',
              float: 'none'
            }}
            defaultChecked
          />
          <label className="form-check-label" htmlFor="statusAlert">
            Status change alerts
          </label>
        </div>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          A verification email will be sent to this address. The contact must confirm subscription to begin receiving alerts.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Add Contact</button></>
  };

  // Modal content templates
  const requestMaintenanceModal = {
    title: <>Request Maintenance</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Maintenance Type:</label>
        <select name="maintenance_type" className="form-control custom-select mb-3">
          <option value="preventive">Preventive Maintenance</option>
          <option value="corrective">Corrective Maintenance</option>
          <option value="hardware">Hardware Replacement</option>
          <option value="software">Software/Firmware Update</option>
          <option value="other">Other (Specify in Description)</option>
        </select>
        
        <label className="form-label mb-2">Priority:</label>
        <select name="priority" className="form-control custom-select mb-3">
          <option value="normal">Normal (Within 48 hours)</option>
          <option value="urgent">Urgent (Within 24 hours, +€50.00)</option>
          <option value="emergency">Emergency (Within 4 hours, +€150.00)</option>
        </select>
        
        <label className="form-label mb-2">Description:</label>
        <textarea 
          className="form-control mb-3" 
          name="description" 
          rows="5" 
          placeholder="Please provide detailed information about the maintenance required..."
          required
        ></textarea>
        
        <div className="form-check mb-3">
          <input 
            type="checkbox" 
            className="form-check-input" 
            id="timeEstimation"
            style={{ 
              marginRight: '5px',
              marginLeft: '0',
              marginTop: '0',
              marginBottom: '0',
              padding: '0',
              position: 'relative',
              float: 'none'
            }}
          />
          <label className="form-check-label" htmlFor="timeEstimation">
            I understand that maintenance services are billed based on actual time spent at €70.00/hour.
          </label>
        </div>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          You will receive a maintenance plan and cost estimate prior to any work being performed.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Submit Request</button></>
  };

  const accessRequestModal = {
    title: <>Request Datacenter Access</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Visitor Information:</label>
        <input 
          type="text" 
          className="form-control mb-3" 
          placeholder="Full Name" 
          required
        />
        <input 
          type="text" 
          className="form-control mb-3" 
          placeholder="ID or Passport Number" 
          required
        />
        
        <label className="form-label mb-2">Access Date and Time:</label>
        <div className="row mb-3">
          <div className="col">
            <input 
              type="date" 
              className="form-control" 
              required
            />
          </div>
          <div className="col">
            <input 
              type="time" 
              className="form-control" 
              required
            />
          </div>
        </div>
        
        <label className="form-label mb-2">Estimated Duration:</label>
        <select name="duration" className="form-control custom-select mb-3">
          <option value="1">1 hour</option>
          <option value="2">2 hours</option>
          <option value="4">4 hours</option>
          <option value="8">8 hours</option>
          <option value="24">24 hours</option>
        </select>
        
        <label className="form-label mb-2">Purpose of Visit:</label>
        <textarea 
          className="form-control mb-3" 
          rows="3" 
          placeholder="Describe the purpose of your visit..."
          required
        ></textarea>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          Access requests must be submitted at least 24 hours in advance. For emergency access, please contact support directly.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Submit Request</button></>
  };

  const remoteHandsModal = {
    title: <>Request Remote Hands</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Service Type:</label>
        <select name="service_type" className="form-control custom-select mb-3">
          <option value="reboot">Server Reboot</option>
          <option value="cable">Cable Connection</option>
          <option value="visual">Visual Inspection</option>
          <option value="hardware">Hardware Installation/Replacement</option>
          <option value="testing">Component Testing</option>
          <option value="other">Other (Specify in Description)</option>
        </select>
        
        <label className="form-label mb-2">Priority:</label>
        <select name="priority" className="form-control custom-select mb-3">
          <option value="normal">Normal (Response within 4 hours)</option>
          <option value="urgent">Urgent (Response within 1 hour, +€50.00)</option>
          <option value="emergency">Emergency (Response within 15 minutes, +€150.00)</option>
        </select>
        
        <label className="form-label mb-2">Description:</label>
        <textarea 
          className="form-control mb-3" 
          name="description" 
          rows="5" 
          placeholder="Please provide detailed instructions for our technician..."
          required
        ></textarea>
        
        <div className="form-check mb-3">
          <input 
            type="checkbox" 
            className="form-check-input" 
            id="remoteHandsFee"
            style={{ 
              marginRight: '5px',
              marginLeft: '0',
              marginTop: '0',
              marginBottom: '0',
              padding: '0',
              position: 'relative',
              float: 'none'
            }}
          />
          <label className="form-check-label" htmlFor="remoteHandsFee">
            I understand that remote hands services are billed in 15-minute increments at €45.00/hour.
          </label>
        </div>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          Remote hands services are charged based on actual time spent. You will receive an estimate before work begins.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Submit Request</button></>
  };

  const powerUpgradeModal = {
    title: <>Power Allocation Upgrade</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Current Power Allocation:</label>
        <input 
          type="text" 
          className="form-control mb-3" 
          value={asset?.rackDetails.powerAllocated || "3.0 kW"}
          readOnly
        />
        
        <label className="form-label mb-2">Current Average Consumption:</label>
        <input 
          type="text" 
          className="form-control mb-3" 
          value={asset?.rackDetails.powerMeasured || "2.1 kW"}
          readOnly
        />
        
        <label className="form-label mb-2">Select New Power Allocation:</label>
        <select name="power_upgrade" className="form-control custom-select mb-3">
          <option value="4kw">4.0 kW (+€50.00/mo)</option>
          <option value="5kw">5.0 kW (+€90.00/mo)</option>
          <option value="6kw">6.0 kW (+€130.00/mo)</option>
          <option value="8kw">8.0 kW (+€200.00/mo)</option>
        </select>
        
        <div className="alert alert-info mb-3">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          Power allocation upgrades are usually processed within 24 hours. No physical changes are required.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-primary" onClick={() => setModalShow(false)}>Request Upgrade</button></>
  };
  
  const renewModal = {
    title: <>Renew Contract</>,
    body: <>
      <div className="form-group">
        <label className="form-label mb-2">Current Contract End Date:</label>
        <input 
          type="text" 
          className="form-control mb-3" 
          value={asset?.rental.endDate || "January 14, 2028"}
          readOnly
        />
        
        <label className="form-label mb-2">Payment Method:</label>
        <select name="payment_method" className="form-control custom-select mb-3">
          <option value="cc">Credit Card</option>
          <option value="cr">Cryptocurrency</option>
          <option value="bt">Bank Transfer</option>
        </select>
        
        <label className="form-label mb-2">Renewal Term:</label>
        <select name="renewal_term" className="form-control custom-select mb-3">
          <option value="12">12 Months (€349.00/mo)</option>
          <option value="24">24 Months - 5% discount (€331.55/mo)</option>
          <option value="36">36 Months - 10% discount (€314.10/mo)</option>
          <option value="48">48 Months - 15% discount (€296.65/mo)</option>
        </select>
        
        <div className="alert alert-info mb-0">
          <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
          Longer terms come with automatic discounts. Early renewal will extend your contract from the current end date.
        </div>
      </div>
    </>,
    footer: <><button className="btn btn-success" onClick={() => setModalShow(false)}>Renew Contract</button></>
  };
  
  const terminateModal = {
    title: <>Terminate Contract</>,
    body: <>
      <div className="form-group">
        <div className="alert alert-danger mb-3">
          <i className="fa fa-exclamation-triangle me-2" style={{paddingRight: '0.5rem'}}></i>
          <strong>WARNING:</strong> Terminating your contract early may result in early termination fees as specified in your contract.
        </div>
        <p>Please confirm that you want to terminate your contract for <strong>{asset?.name}</strong>.</p>
        
        {/* Termination timing options */}
        <div className="mb-3">
          <label className="form-label">When would you like to terminate the contract?</label>
          <div className="form-check mb-2">
            <input 
              type="radio" 
              className="form-check-input" 
              id="terminateEndOfContract" 
              name="terminationTiming"
              value="end-of-contract"
              style={{ 
                marginRight: '5px',
                marginLeft: '0',
                marginTop: '0',
                marginBottom: '0',
                padding: '0',
                position: 'relative',
                float: 'none'
              }}
              defaultChecked 
            />
            <label className="form-check-label" htmlFor="terminateEndOfContract">
              Terminate at the end of current contract term ({asset?.rental.endDate})
            </label>
          </div>
          <div className="form-check">
            <input 
              type="radio" 
              className="form-check-input" 
              id="terminateEarly" 
              name="terminationTiming"
              value="early-termination"
              style={{ 
                marginRight: '5px',
                marginLeft: '0',
                marginTop: '0',
                marginBottom: '0',
                padding: '0',
                position: 'relative',
                float: 'none'
              }}
            />
            <label className="form-check-label" htmlFor="terminateEarly">
              Early termination (additional fees will apply)
            </label>
          </div>
        </div>
        
        {/* Reason for termination */}
        <div className="mb-3">
          <label htmlFor="terminationReason" className="form-label">Reason for termination <span className="text-danger">*</span></label>
          <textarea 
            className="form-control" 
            id="terminationReason" 
            rows="3" 
            placeholder="Please tell us why you're terminating this contract..."
            required
          ></textarea>
          <div className="invalid-feedback" id="reasonFeedback" style={{display: 'none'}}>
            Please provide a reason for termination.
          </div>
        </div>
        
        <div className="form-check mb-3">
          <input 
            type="checkbox" 
            className="form-check-input" 
            id="confirmTermination"
            style={{ 
              marginRight: '5px',
              marginLeft: '0',
              marginTop: '0',
              marginBottom: '0',
              padding: '0',
              position: 'relative',
              float: 'none'
            }}
          />
          <label className="form-check-label" htmlFor="confirmTermination">
            I understand the early termination policy and associated fees.
          </label>
        </div>
      </div>
    </>,
    footer: <>
      <button className="btn btn-secondary" onClick={() => setModalShow(false)}>Cancel</button>
      <button 
        className="btn btn-danger ms-2" 
        onClick={() => {
          // Here you would handle the termination logic
          setModalShow(false);
        }}
      >
        Terminate Contract
      </button>
    </>
  };

  const openModal = (title, body, footer) => {
    setModalTitle(title);
    setModalBody(body);
    setModalFooter(footer);
    setModalShow(true);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: "300px" }}>
        <div className="spinner-border text-primary" role="status">
          
        </div>
      </div>
    );
  }

  if (!asset) {
    return (
      <div className="alert alert-danger" role="alert">
        Colocation asset not found. <Link to="/">Return to services</Link>
      </div>
    );
  }

  return (
    <>
      <div className="page-header">
        <div className="page-leftheader">
          <ol className="breadcrumb">
            <li className="breadcrumb-item1"><Link to="/">Services</Link></li>
            <li className="breadcrumb-item1">Colocation Asset {asset.name}</li>
          </ol>
        </div>
        <div className="page-rightheader ml-auto">
          <div className="dropdown">
            <a href="#" className="nav-link pr-0 leading-none" data-toggle="dropdown">
              <button className="btn btn-success">New Service</button>
            </a>
            <div className="dropdown-menu">
              <Link className="dropdown-item d-flex" to="/dedicatedorder">
                <div className="mt-1">Dedicated Server</div>
              </Link>
              <Link className="dropdown-item d-flex" to="/cloudorder">
                <div className="mt-1">Cloud Server</div>
              </Link>

            </div>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-12">
          <div className="alert alert-success" role="alert">
            <i className="fa fa-check-circle-o me-2" aria-hidden="true"></i> <strong>Upgrade offer!</strong> Extend your contract now to get <b>15% discount</b> on your monthly rate &nbsp; <Link to="/reseller"><button className="btn btn-default btn-sm">Check Discounts</button></Link>
          </div>

          <div className="card smaller">
            <div className="card-header">
              <div className="card-title d-flex align-items-center">
                <span className="me-2">Colocation Asset {asset.name}</span>
                {asset.status === 'Active' && <span className="badge bg-success">Active</span>}
                {asset.status === 'Offline' && <span className="badge bg-danger">Offline</span>}
                {asset.status === 'Maintenance' && <span className="badge bg-warning">Maintenance</span>}
              </div>
              <div className="card-options">
                <button className="btn btn-primary btn-sm me-2" onClick={() => openModal(requestMaintenanceModal.title, requestMaintenanceModal.body, requestMaintenanceModal.footer)}>
                  <i className="fa fa-wrench me-1"></i> Maintenance
                </button>
                <button className="btn btn-info btn-sm me-2" onClick={() => openModal(remoteHandsModal.title, remoteHandsModal.body, remoteHandsModal.footer)}>
                  <i className="fa fa-hand-paper-o me-1"></i> Remote Hands
                </button>
                <button className="btn btn-secondary btn-sm" onClick={() => openModal(accessRequestModal.title, accessRequestModal.body, accessRequestModal.footer)}>
                  <i className="fa fa-key me-1"></i> Request Access
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="panel panel-primary">
                <div className="tab-menu-heading">
                  <div className="tabs-menu">
                    <ul className="nav panel-tabs">
                      <li>
                        <a 
                          className={activeTab === 'overview' ? 'active' : ''} 
                          onClick={() => setActiveTab('overview')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-screen-desktop me-1"></i> Overview
                        </a>
                      </li>
                      <li>
                        <a 
                          className={activeTab === 'monitoring' ? 'active' : ''} 
                          onClick={() => setActiveTab('monitoring')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-graph me-1"></i> Monitoring
                        </a>
                      </li>
                      <li>
                        <a 
                          className={activeTab === 'maintenance' ? 'active' : ''} 
                          onClick={() => setActiveTab('maintenance')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-wrench me-1"></i> Maintenance
                        </a>
                      </li>
                      <li>
                        <a 
                          className={activeTab === 'settings' ? 'active' : ''} 
                          onClick={() => setActiveTab('settings')}
                          style={{cursor: 'pointer'}}
                        >
                          <i className="si si-settings me-1"></i> Settings
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="panel-body tabs-menu-body">
                  <div className="tab-content">
                    {/* Overview Tab */}
                    {activeTab === 'overview' && (
                      <div className="tab-pane active">
                        <div className="row">
                          {/* Asset Details Card */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-server me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Asset Details</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Quick Actions</h5>
                                <div className="row">
                                  <div className="col-6 mb-3">
                                    <button className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(remoteHandsModal.title, remoteHandsModal.body, remoteHandsModal.footer)}>
                                      <i className="fa fa-hand-paper-o fa-2x d-block mb-2 text-primary"></i>
                                      Remote Hands
                                    </button>
                                  </div>
                                  <div className="col-6 mb-3">
                                    <button className="btn btn-light btn-block rounded-3 w-100 h-100 py-3 shadow-sm"
                                      onClick={() => openModal(accessRequestModal.title, accessRequestModal.body, accessRequestModal.footer)}>
                                      <i className="fa fa-key fa-2x d-block mb-2 text-info"></i>
                                      Request Access
                                    </button>
                                  </div>
                                </div>
                                
                                <h5 className="text-muted mb-3">Asset Information</h5>
                                <ul className="list-group list-group-flush">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-microchip text-primary me-2"></i> Type:</span>
                                    <span className="text-muted">{asset.assetType}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-server text-primary me-2"></i> Model:</span>
                                    <span className="text-muted">{asset.model}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-microchip text-primary me-2"></i> CPU:</span>
                                    <span className="text-muted">{asset.specifications.cpu}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-database text-primary me-2"></i> RAM:</span>
                                    <span className="text-muted">{asset.specifications.ram}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-hdd-o text-primary me-2"></i> Storage:</span>
                                    <span className="text-muted">{asset.specifications.storage}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-wifi text-primary me-2"></i> Network:</span>
                                    <span className="text-muted">{asset.specifications.networkCards}</span>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          </div>
                          
                          {/* Location and Rack Details */}
                          <div className="col-xl-4 col-lg-6 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-map-marker me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Location & Rack Details</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Location</h5>
                                <ul className="list-group list-group-flush mb-4">
                                  <li className="list-group-item d-flex justify-content-between align-items-center px-0" >
                                    <span><i className="fa fa-building text-primary me-2"></i> Data Center:</span>
                                    <span className="text-muted">
                                      <i className={`flag flag-${asset.location.flag} me-1`} style={{paddingRight: '1rem'}}></i>
                                      {asset.location.name}
                                    </span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-map text-primary me-2"></i> Facility:</span>
                                    <span className="text-muted">{asset.location.datacenter}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-map-signs text-primary me-2"></i> Row/Rack:</span>
                                    <span className="text-muted">Row {asset.location.row}, Rack {asset.location.rack}</span>
                                  </li>
                                </ul>
                                
                                <h5 className="text-muted mb-3">Rack Details</h5>
                                <ul className="list-group list-group-flush mb-3">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-arrows-v text-primary me-2"></i> Size:</span>
                                    <span className="text-muted">{asset.rackDetails.size}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-sort-numeric-asc text-primary me-2"></i> Position:</span>
                                    <span className="text-muted">{asset.rackDetails.position}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-bolt text-primary me-2"></i> Power Consumption:</span>
                                    <span className="text-muted">{asset.rackDetails.powerConsumption}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-plug text-primary me-2"></i> Power Connections:</span>
                                    <span className="text-muted">{asset.rackDetails.powerConnections}</span>
                                  </li>
                                </ul>
                                
                                <div className="mt-3">
                                  <button 
                                    className="btn btn-primary w-100" 
                                    onClick={() => openModal(powerUpgradeModal.title, powerUpgradeModal.body, powerUpgradeModal.footer)}
                                  >
                                    <i className="fa fa-bolt me-1"></i> Upgrade Power Allocation
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          {/* Rental and Billing Card */}
                          <div className="col-xl-4 col-lg-12 col-md-12 mb-4">
                            <div className="card shadow-sm h-100">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Rental & Billing</h3>
                              </div>
                              <div className="card-body">
                                <h5 className="text-muted mb-3">Contract Details</h5>
                                <ul className="list-group list-group-flush mb-4">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-file-text text-primary me-2"></i> Rental Type:</span>
                                    <span className="text-muted">{asset.rental.type}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-calendar text-primary me-2"></i> Contract Term:</span>
                                    <span className="text-muted">{asset.rental.term}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-calendar-plus-o text-primary me-2"></i> Start Date:</span>
                                    <span className="text-muted">{asset.rental.startDate}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-calendar-check-o text-primary me-2"></i> End Date:</span>
                                    <span className="text-muted">{asset.rental.endDate}</span>
                                  </li>
                                </ul>
                                
                                <h5 className="text-muted mb-3">Billing Details</h5>
                                <ul className="list-group list-group-flush mb-4">
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-money text-primary me-2"></i> Monthly Price:</span>
                                    <span className="text-muted fw-bold">{asset.billing.price}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-refresh text-primary me-2"></i> Billing Cycle:</span>
                                    <span className="text-muted">{asset.billing.billingCycle}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-calendar text-primary me-2"></i> Next Renewal:</span>
                                    <span className="text-muted">{asset.billing.nextRenewal}</span>
                                  </li>
                                   <li className="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span><i className="fa fa-repeat text-primary me-2"></i> Auto-Renewal:</span>
                                    <span className={`badge ${asset.billing.autoRenew ? 'bg-success' : 'bg-danger'}`}>
                                      {asset.billing.autoRenew ? 'Enabled' : 'Disabled'}
                                    </span>
                                  </li>
                                </ul>
                                
                                <div className="mt-4 d-flex justify-content-between">
                                  <button className="btn btn-danger btn-sm me-2" onClick={() => openModal(terminateModal.title, terminateModal.body, terminateModal.footer)}>
                                    <i className="fa fa-times-circle me-1"></i> Terminate
                                  </button>
                                  
                                  <div>
                              <Link 
  to={`/billing/invoices`} 
  className="btn btn-info btn-sm"
  onClick={() => {
    // Scroll to top when the button is clicked
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }}
>
  <i className="fa fa-arrow-circle-left me-1"></i> View Invoices
</Link>
                                    <button className="btn btn-success btn-sm me-1" onClick={() => openModal(renewModal.title, renewModal.body, renewModal.footer)}>
                                      <i className="fa fa-refresh me-1"></i> Renew Contract
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Monitoring Tab */}
                    {activeTab === 'monitoring' && (
                      <div className="tab-pane active">
                        <div className="row">
                
                        
                             
                          
                          {/* IP Addresses Card */}
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-globe me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>IP Addresses</h3>
                                <div className="card-options">
                                  <button 
                                    className="btn btn-sm btn-primary"
                                    onClick={() => openModal(requestIpModal.title, requestIpModal.body, requestIpModal.footer)}
                                  >
                                    <i className="fa fa-plus me-1"></i> Request Additional IP
                                  </button>
                                </div>
                              </div>
                              <div className="card-body">
                                <div className="table-responsive">
                                  <table className="table table-hover">
                                    <thead className="bg-light">
                                      <tr>
                                        <th>IP Address</th>
                                        <th>Type</th>
                                        <th>Reverse DNS</th>
                                        <th>Actions</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {asset.ipAddresses.map((ip, index) => (
                                        <tr key={index}>
                                          <td>
                                            <div className="d-flex align-items-center">
                                              <span className="me-2">{ip}</span>
                                            </div>
                                          </td>
                                          <td>{index === 0 ? 'Primary' : 'Secondary'}</td>
                                          <td>asset{index + 1}.example.com</td>
                                          <td>
                                            <div className="btn-group" role="group">
                                              <button className="btn btn-sm btn-primary">
                                                <i className="fa fa-edit me-1"></i> Set rDNS
                                              </button>
                                            </div>
                                          </td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          {/* Uptime Monitoring Card */}
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-clock-o me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Uptime Monitoring</h3>
                              </div>
                              <div className="card-body">
                                <div className="mb-4">
                                  <div className="d-flex justify-content-between align-items-center mb-2">
                                    <h6 className="mb-0">Current Uptime</h6>
                                    <span className="text-muted">{asset.monitoring.currentUptime}</span>
                                  </div>
                                  <div className="progress" style={{ height: '8px' }}>
                                    <div className="progress-bar bg-success" role="progressbar" style={{ width: '100%' }} aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                  </div>
                                </div>
                                
                                <div className="mb-4">
                                  <div className="d-flex justify-content-between align-items-center mb-2">
                                    <h6 className="mb-0">Uptime Last 30 Days</h6>
                                    <span className="text-muted">{asset.monitoring.uptimePercentage}</span>
                                  </div>
                                  <div className="progress" style={{ height: '8px' }}>
                                    <div className="progress-bar bg-success" role="progressbar" style={{ width: '99.998%' }} aria-valuenow="99.998" aria-valuemin="0" aria-valuemax="100"></div>
                                  </div>
                                </div>
                                
                                <h6 className="mb-3">Alert Contacts</h6>
                                <ul className="list-group list-group-flush">
                                  {asset.monitoring.alertContacts.map((contact, index) => (
                                    <li key={index} className="list-group-item d-flex justify-content-between align-items-center px-0">
                                      <span><i className="fa fa-envelope text-primary me-2"></i> {contact}</span>
                                      <div className="btn-group" role="group">
                                        <button className="btn btn-sm btn-outline-danger">
                                          <i className="fa fa-trash"></i>
                                        </button>
                                      </div>
                                    </li>
                                  ))}
                                </ul>
                                
                                <div className="mt-3 d-flex justify-content-end">
                                  <button 
                                    className="btn btn-sm btn-primary"
                                    onClick={() => openModal(addAlertContactModal.title, addAlertContactModal.body, addAlertContactModal.footer)}
                                  >
                                    <i className="fa fa-plus me-1"></i> Add Alert Contact
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Maintenance Tab */}
                    {activeTab === 'maintenance' && (
                      <div className="tab-pane active">
                        <div className="row">
                          {/* Maintenance History Card */}
                          <div className="col-12 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-history me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Maintenance History</h3>
                                <div className="card-options">
                                  <button className="btn btn-sm btn-primary" onClick={() => openModal(requestMaintenanceModal.title, requestMaintenanceModal.body, requestMaintenanceModal.footer)}>
                                    <i className="fa fa-plus me-1"></i> Schedule Maintenance
                                  </button>
                                </div>
                              </div>
                              <div className="card-body">
                                <div className="table-responsive">
                                  <table className="table table-hover">
                                    <thead className="bg-light">
                                      <tr>
                                        <th>Date</th>
                                        <th>Type</th>
                                        <th>Description</th>
                                        <th>Technician</th>
                                        <th>Status</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {asset.maintenanceHistory.map((maintenance, index) => (
                                        <tr key={index}>
                                          <td>{maintenance.date}</td>
                                          <td>
                                          <span 
                                        className={`badge ${maintenance.type === 'Scheduled' ? 'bg-primary' : 'bg-warning'}`} 
                                        style={{ minWidth: '80px' }}
                                        >
                                              {maintenance.type}
                                            </span>
                                          </td>
                                          <td>{maintenance.description}</td>
                                          <td>{maintenance.technician}</td>
                                          <td>
                                            <span className="badge bg-success" style={{ minWidth: '80px'}}>Completed</span>
                                          </td>
                                        </tr>
                                      ))}
                                      {/* Add an upcoming maintenance example */}
                                      <tr>
                                        <td>March 15, 2025</td>
                                        <td>
                                          <span className="badge bg-primary" style={{ minWidth: '80px' }}>Scheduled</span>
                                        </td>
                                        <td>Quarterly preventive maintenance</td>
                                        <td>Not Assigned</td>
                                        <td>
                                          <span className="badge bg-info" style={{ minWidth: '80px' }}>Scheduled</span>
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          {/* Maintenance Plans Card */}
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-calendar me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Maintenance Plans</h3>
                              </div>
                              <div className="card-body">
    
                                
                        
                                
                 
                                <div className="card">
                                  <div className="card-body">
                                    <div className="d-flex justify-content-between align-items-center mb-2">
                                      <div>
                                        <h5 className="card-title mb-1">Enterprise Care</h5>
                                        <p className="text-muted mb-0">€199.00/month</p>
                                      </div>
                                      <button className="btn btn-sm btn-outline-primary">Add</button>
                                    </div>
                                    <ul className="list-unstyled mb-0">
                                      <li><i className="fa fa-check text-success me-2"></i> Bi-weekly preventive maintenance</li>
                                      <li><i className="fa fa-check text-success me-2"></i> Unlimited remote hands</li>
                                      <li><i className="fa fa-check text-success me-2"></i> Monthly detailed health reports</li>
                                      <li><i className="fa fa-check text-success me-2"></i> 24/7 dedicated support team</li>
                                      <li><i className="fa fa-check text-success me-2"></i> On-site emergency response (2 hours)</li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          {/* Remote Hands Usage Card */}
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-hand-paper-o me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Remote Hands Usage</h3>
                                <div className="card-options">
                                  <button className="btn btn-sm btn-primary" onClick={() => openModal(remoteHandsModal.title, remoteHandsModal.body, remoteHandsModal.footer)}>
                                    <i className="fa fa-plus me-1"></i> Request Remote Hands
                                  </button>
                                </div>
                              </div>
                              <div className="card-body">
                                <div className="alert alert-info mb-4">
                                  <i className="fa fa-info-circle me-2" style={{paddingRight: '0.5rem'}}></i>
                                  Remote hands services are available 24/7 and billed in 15-minute increments at €45.00/hour.
                                </div>
                                
                                <div className="table-responsive">
                                  <table className="table table-hover">
                                    <thead className="bg-light">
                                      <tr>
                                        <th>Date</th>
                                        <th>Task</th>
                                        <th>Duration</th>
                                        <th>Cost</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <tr>
                                        <td>February 25, 2025</td>
                                        <td>Server reboot and visual inspection</td>
                                        <td>30 min</td>
                                        <td>€22.50</td>
                                      </tr>
                                      <tr>
                                        <td>January 17, 2025</td>
                                        <td>Network cable replacement</td>
                                        <td>45 min</td>
                                        <td>€33.75</td>
                                      </tr>
                                      <tr>
                                        <td>January 03, 2025</td>
                                        <td>Power supply replacement</td>
                                        <td>1 hour 15 min</td>
                                        <td>€56.25</td>
                                      </tr>
                                    </tbody>
                                    <tfoot className="bg-light">
                                      <tr>
                                        <th colSpan="3" className="text-end">Total (YTD):</th>
                                        <th>€112.50</th>
                                      </tr>
                                    </tfoot>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Settings Tab */}
                    {activeTab === 'settings' && (
                      <div className="tab-pane active">
                        <div className="row">
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-cog me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Asset Settings</h3>
                              </div>
                              <div className="card-body">
                                <form>
                                  <div className="form-group mb-3">
                                    <label className="form-label">Asset Label</label>
                                    <input 
                                      type="text" 
                                      className="form-control" 
                                      defaultValue={asset.name}
                                      placeholder="Asset Label"
                                    />
                                    <small className="form-text text-muted">
                                      This is for your reference only and does not affect the physical asset.
                                    </small>
                                  </div>
                                  
                                  <div className="form-group mb-3">
                                    <label className="form-label">Alert Notifications</label>
                                    <div className="form-check mb-2">
                                      <input 
                                        type="checkbox" 
                                        className="form-check-input" 
                                        id="powerAlerts"
                                        style={{ 
                                          marginRight: '5px',
                                          marginLeft: '0',
                                          marginTop: '0',
                                          marginBottom: '0',
                                          padding: '0',
                                          position: 'relative',
                                          float: 'none'
                                        }}
                                        defaultChecked
                                      />
                                      <label className="form-check-label" htmlFor="powerAlerts">
                                        Power usage alerts
                                      </label>
                                    </div>
                                    <div className="form-check mb-2">
                                      <input 
                                        type="checkbox" 
                                        className="form-check-input" 
                                        id="temperatureAlerts"
                                        style={{ 
                                          marginRight: '5px',
                                          marginLeft: '0',
                                          marginTop: '0',
                                          marginBottom: '0',
                                          padding: '0',
                                          position: 'relative',
                                          float: 'none'
                                        }}
                                        defaultChecked
                                      />
                                      <label className="form-check-label" htmlFor="temperatureAlerts">
                                        Temperature alerts
                                      </label>
                                    </div>
                                    <div className="form-check mb-2">
                                      <input 
                                        type="checkbox" 
                                        className="form-check-input" 
                                        id="statusAlerts"
                                        style={{ 
                                          marginRight: '5px',
                                          marginLeft: '0',
                                          marginTop: '0',
                                          marginBottom: '0',
                                          padding: '0',
                                          position: 'relative',
                                          float: 'none'
                                        }}
                                        defaultChecked
                                      />
                                      <label className="form-check-label" htmlFor="statusAlerts">
                                        Status change alerts
                                      </label>
                                    </div>
                                    <div className="form-check">
                                      <input 
                                        type="checkbox" 
                                        className="form-check-input" 
                                        id="maintenanceAlerts"
                                        style={{ 
                                          marginRight: '5px',
                                          marginLeft: '0',
                                          marginTop: '0',
                                          marginBottom: '0',
                                          padding: '0',
                                          position: 'relative',
                                          float: 'none'
                                        }}
                                        defaultChecked
                                      />
                                      <label className="form-check-label" htmlFor="maintenanceAlerts">
                                        Maintenance notifications
                                      </label>
                                    </div>
                                  </div>
                                  
                                  <div className="form-group mb-3">
                                    <label className="form-label">Alert Recipients</label>
                                    <textarea 
                                      className="form-control" 
                                      rows="3" 
                                      placeholder="Enter email addresses separated by commas"
                                      defaultValue={asset.monitoring.alertContacts.join(', ')}
                                    ></textarea>
                                    <small className="form-text text-muted">
                                      These email addresses will receive alerts and notifications about your asset.
                                    </small>
                                  </div>
                                  
                                  <div className="d-flex justify-content-end">
                                    <button type="submit" className="btn btn-primary">Save Settings</button>
                                  </div>
                                </form>
                              </div>
                            </div>
                          </div>
                          
                          <div className="col-md-6 mb-4">
                            <div className="card shadow-sm">
                              <div className="card-header bg-light">
                                <h3 className="card-title"><i className="fa fa-credit-card me-2 text-primary" style={{paddingRight: '0.5rem'}}></i>Billing Settings</h3>
                              </div>
                              <div className="card-body">
                                <form>
                                  <div className="form-group mb-3">
                                    <label className="form-label">Billing Cycle</label>
                                    <select className="form-control">
                                      <option selected>Monthly (€{parseFloat(asset.billing.price).toFixed(2)}/mo)</option>
                                      <option>Quarterly (€{(parseFloat(asset.billing.price) * 3 * 0.95).toFixed(2)}/3mo - 5% discount)</option>
                                      <option>Semi-Annual (€{(parseFloat(asset.billing.price) * 6 * 0.9).toFixed(2)}/6mo - 10% discount)</option>
                                      <option>Annual (€{(parseFloat(asset.billing.price) * 12 * 0.85).toFixed(2)}/yr - 15% discount)</option>
                                    </select>
                                  </div>
                                  
                                  <div className="form-group mb-3">
                                    <label className="form-label">Next Renewal</label>
                                    <input 
                                      type="text" 
                                      className="form-control" 
                                      value={asset.billing.nextRenewal} 
                                      readOnly
                                    />
                                  </div>
                                  
                                  <div className="form-group mb-3">
                                    <label className="form-label">Payment Method</label>
                                    <select className="form-control">
                                      <option>Credit Card (ending in 4857)</option>
                                      <option>Add New Payment Method</option>
                                    </select>
                                  </div>
                                  
                                  <div className="form-group form-check mb-3">
                                    <input 
                                      type="checkbox" 
                                      className="form-check-input" 
                                      id="autoRenewCheck"
                                      style={{ 
                                        marginRight: '5px',
                                        marginLeft: '0',
                                        marginTop: '0',
                                        marginBottom: '0',
                                        padding: '0',
                                        position: 'relative',
                                        float: 'none'
                                      }}
                                      defaultChecked={asset.billing.autoRenew}
                                    />
                                    <label className="form-check-label" htmlFor="autoRenewCheck">
                                      Enable automatic renewal
                                    </label>
                                    <small className="form-text text-muted d-block">
                                      When disabled, your service will be terminated after the contract end date unless manually renewed.
                                    </small>
                                  </div>
                                  
                                  <div className="form-group form-check mb-3">
                                    <input 
                                      type="checkbox" 
                                      className="form-check-input" 
                                      id="useAccountCredit"
                                      style={{ 
                                        marginRight: '5px',
                                        marginLeft: '0',
                                        marginTop: '0',
                                        marginBottom: '0',
                                        padding: '0',
                                        position: 'relative',
                                        float: 'none'
                                      }}
                                      defaultChecked
                                    />
                                    <label className="form-check-label" htmlFor="useAccountCredit">
                                      Use account credit for renewals when available
                                    </label>
                                  </div>
                                  
                                  <div className="d-flex justify-content-end">
                                    <button type="submit" className="btn btn-primary">Save Billing Settings</button>
                                  </div>
                                </form>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Modal for various actions */}
      <MDBModal show={modalShow} setShow={setModalShow} tabIndex='-1'>
        <MDBModalDialog>
          <MDBModalContent>
            <MDBModalHeader>
              <MDBModalTitle>{modalTitle}</MDBModalTitle>
              <button type="button" className="close" onClick={() => setModalShow(false)}>
                <span aria-hidden="true">&times;</span>
              </button>
            </MDBModalHeader>
            <MDBModalBody>
              {modalBody}
            </MDBModalBody>
            <MDBModalFooter>
              {modalFooter}
            </MDBModalFooter>
          </MDBModalContent>
        </MDBModalDialog>
      </MDBModal>
    </>
  );
};

export default ColocationAssetsDetailsView;