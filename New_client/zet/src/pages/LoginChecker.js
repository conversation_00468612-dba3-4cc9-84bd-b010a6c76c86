import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const LoginChecker = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to home page
    navigate('/');
  }, [navigate]);

  return (
    <div className="d-flex justify-content-center align-items-center" style={{ height: '100vh' }}>
      <div className="spinner-border text-primary" role="status">
        <span className="sr-only">Checking login...</span>
      </div>
    </div>
  );
};

export default LoginChecker;