import React, { useState, useEffect } from 'react';
import { Toast } from 'primereact/toast';
import { useLocation, useNavigate } from 'react-router-dom';

const AcceptInvitation = () => {
  const toast = React.useRef(null);
  const [invitation, setInvitation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [accepting, setAccepting] = useState(false);
  const [step, setStep] = useState('view'); // 'view' or 'create-account'
  const [accountData, setAccountData] = useState({
    password: '',
    password_again: '',
    first_name: '',
    last_name: '',
    address: '',
    city: '',
    country: ''
  });
  const [validationErrors, setValidationErrors] = useState('');
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const token = params.get('token');
    
    if (!token) {
      setError('Invalid invitation link');
      setLoading(false);
      return;
    }

    fetchInvitationDetails(token);
  }, [location]);

  const fetchInvitationDetails = async (token) => {
    try {
      const response = await fetch('/api.php?f=get_invitation_details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });
      
      const data = await response.json();
      
      if (data.error) {
        setError(data.error);
      } else {
        setInvitation(data.invitation);
      }
    } catch (err) {
      setError('Failed to load invitation details');
    } finally {
      setLoading(false);
    }
  };

  const validateAccount = () => {
    setValidationErrors('');

    if (!accountData.first_name || !accountData.last_name) {
      setValidationErrors('Please enter your first and last name');
      return false;
    }

    if (!accountData.address || !accountData.city || !accountData.country) {
      setValidationErrors('Please complete your address, city, and country');
      return false;
    }

    if (!accountData.password || !accountData.password_again) {
      setValidationErrors('Please enter and confirm your password');
      return false;
    }

    if (accountData.password !== accountData.password_again) {
      setValidationErrors('Your two passwords don\'t match');
      return false;
    }

    if (accountData.password.length < 8) {
      setValidationErrors('Please make the password longer than 8 characters');
      return false;
    }

    if (!accountData.password.match(/[a-z]/) || !accountData.password.match(/[A-Z]/)) {
      setValidationErrors('Please use both lowercase and uppercase letters in your password');
      return false;
    }

    if (!accountData.password.match(/\d/)) {
      setValidationErrors('Please include at least one number in your password');
      return false;
    }

    return true;
  };

  const acceptInvitation = async () => {
    if (!validateAccount()) {
      return;
    }

    const params = new URLSearchParams(location.search);
    const token = params.get('token');
    
    setAccepting(true);
    
    try {
      const response = await fetch('/api.php?f=accept_invitation_with_account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          token,
          first_name: accountData.first_name,
          last_name: accountData.last_name,
          password: accountData.password,
          address: accountData.address,
          city: accountData.city,
          country: accountData.country
        })
      });
      
      const data = await response.json();
      
      if (data.error) {
        setValidationErrors(typeof data.error === 'string' ? data.error : 'Failed to accept invitation');
      } else {
        toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Account created and invitation accepted successfully! You can now login.',
        life: 3000
      });
        navigate('/login');
      }
    } catch (err) {
      setValidationErrors('Failed to accept invitation');
    } finally {
      setAccepting(false);
    }
  };

  const getPermissionsList = (permissions) => {
    const permissionLabels = {
      services: 'Services',
      invoices: 'Invoices', 
      billing: 'Billing',
      tickets: 'Support Tickets',
      api: 'API Access',
      full_access: 'Full Access'
    };

    return Object.entries(permissions)
      .filter(([key, value]) => value === true)
      .map(([key]) => permissionLabels[key])
      .join(', ');
  };

  if (loading) {
    return (
      <body className="h-100vh app">
        <div className="page">
          <div className="page-single">
            <div className="p-5">
              <div className="row">
                <div className="col mx-auto">
                  <div className="row justify-content-center">
                    <div className="col-lg-9 col-xl-8 d-flex align-items-stretch">
                      <div className="card-group mb-0">
                        <div className="card p-4 page-content">
                          <div className="card-body page-single-content">
                            <div className="w-100 text-center">
                              <div className="spinner-border text-primary mb-3" role="status"></div>
                              <h4>Loading invitation...</h4>
                            </div>
                          </div>
                        </div>
                        <div className="card text-white py-5 d-md-down-none page-content mt-0">
                          <div className="card-body text-center justify-content-center page-single-content">
                            <img src="/assets/images/brand/favicon.png" alt="img"/>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </body>
    );
  }

  if (error) {
    return (
      <body className="h-100vh app">
        <div className="page">
          <div className="page-single">
            <div className="p-5">
              <div className="row">
                <div className="col mx-auto">
                  <div className="row justify-content-center">
                    <div className="col-lg-9 col-xl-8 d-flex align-items-stretch">
                      <div className="card-group mb-0">
                        <div className="card p-4 page-content">
                          <div className="card-body page-single-content">
                            <div className="w-100">
                              <div className="text-center mb-4">
                                <div className="text-danger mb-3">
                                  <i className="fa fa-exclamation-triangle fa-3x"></i>
                                </div>
                                <h1 className="mb-2 text-danger">Invitation Error</h1>
                                <p className="text-muted text-red">{error}</p>
                              </div>
                              <div className="text-center">
                                <a href="/register" className="btn btn-primary btn-lg">
                                  Create New Account
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="card text-white py-5 d-md-down-none page-content mt-0">
                          <div className="card-body text-center justify-content-center page-single-content">
                            <img src="/assets/images/brand/favicon.png" alt="img"/>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </body>
    );
  }

  return (
    <body className="h-100vh app">
      <div className="page">
        <div className="page-single">
          <div className="p-5">
            <div className="row">
              <div className="col mx-auto">
                <div className="row justify-content-center">
                  <div className="col-lg-9 col-xl-8 d-flex align-items-stretch">
                    <div className="card-group mb-0">
                      <div className="card p-4 page-content">
                        <div className="card-body page-single-content">
                          <div className="w-100">
                            <div className="">
                              <h1 className="mb-2">Accept Invitation</h1>
                              <p className={validationErrors ? "text-muted text-red" : "text-muted"}>
                                {validationErrors || `${invitation.owner_name} has invited you to access their account`}
                              </p>
                            </div>

                            {step === 'view' && (
                              <>
                                {invitation.message && (
                                  <div className="alert alert-info mb-3">
                                    <h6><strong>Personal Message:</strong></h6>
                                    <p className="mb-0" style={{ fontStyle: 'italic' }}>
                                      {invitation.message}
                                    </p>
                                  </div>
                                )}

                                <div className="mb-3">
                                  <h6>Invitation Details:</h6>
                                  <div className="input-group mb-2">
                                    <input 
                                      type="text" 
                                      className="form-control" 
                                      value={`From: ${invitation.owner_name} (${invitation.owner_email})`} 
                                      readOnly 
                                    />
                                  </div>
                                  <div className="input-group mb-2">
                                    <input 
                                      type="text" 
                                      className="form-control" 
                                      value={`For Email: ${invitation.email}`} 
                                      readOnly 
                                    />
                                  </div>
                                  <div className="input-group mb-2">
                                    <input 
                                      type="text" 
                                      className="form-control" 
                                      value={`Sent: ${new Date(invitation.created_at).toLocaleDateString()}`} 
                                      readOnly 
                                    />
                                  </div>
                                </div>

                                <div className="mb-3">
                                  <h6>Access Permissions:</h6>
                                  <div className="d-flex flex-wrap gap-2 mb-2">
                                    {invitation.permissions.full_access ? (
                                      <span className="badge badge-warning badge-lg">
                                        <i className="fa fa-crown me-1"></i>
                                        Full Access
                                      </span>
                                    ) : (
                                      <>
                                        {invitation.permissions.services && (
                                          <span className="badge badge-info">Services</span>
                                        )}
                                        {invitation.permissions.invoices && (
                                          <span className="badge badge-info">Invoices</span>
                                        )}
                                        {invitation.permissions.billing && (
                                          <span className="badge badge-info">Billing</span>
                                        )}
                                        {invitation.permissions.tickets && (
                                          <span className="badge badge-info">Support Tickets</span>
                                        )}
                                        {invitation.permissions.api && (
                                          <span className="badge badge-info">API Access</span>
                                        )}
                                      </>
                                    )}
                                  </div>
                                  {!invitation.permissions.full_access && (
                                    <small className="text-muted d-block">
                                      You will have access to: {getPermissionsList(invitation.permissions)}
                                    </small>
                                  )}
                                </div>

                                <div className="alert alert-warning">
                                  <i className="fa fa-info-circle me-2"></i>
                                  To accept this invitation, you'll need to create an account first.
                                </div>

                                <div className="row">
                                  <div className="col-12">
                                    <button 
                                      className="btn btn-lg btn-primary btn-block"
                                      onClick={() => setStep('create-account')}
                                    >
                                      <i className="fa fa-user-plus me-2"></i>
                                      Create Account & Accept
                                    </button>
                                  </div>
                                  <div className="col-6 text-left">
                                    <a href="/register">Create New Account</a>
                                  </div>
                                  <div className="col-6 text-right">
                                    <a href="/register">Decline Invitation</a>
                                  </div>
                                </div>
                              </>
                            )}

                            {step === 'create-account' && (
                              <>
                                <div className="mb-3">
                                  <p><strong>Creating account for:</strong> {invitation.email}</p>
                                </div>

                                <div className="input-group mb-2">
                                  <input 
                                    type="text" 
                                    className="form-control" 
                                    placeholder="First name" 
                                    value={accountData.first_name}
                                    onChange={(e) => setAccountData(prev => ({...prev, first_name: e.target.value}))}
                                  />
                                </div>
                                <div className="input-group mb-2">
                                  <input 
                                    type="text" 
                                    className="form-control" 
                                    placeholder="Last name" 
                                    value={accountData.last_name}
                                    onChange={(e) => setAccountData(prev => ({...prev, last_name: e.target.value}))}
                                  />
                                </div>
                                <div className="input-group mb-2">
                                  <input 
                                    type="text" 
                                    className="form-control" 
                                    placeholder="Address" 
                                    value={accountData.address}
                                    onChange={(e) => setAccountData(prev => ({...prev, address: e.target.value}))}
                                  />
                                </div>
                                <div className="input-group mb-2">
                                  <input 
                                    type="text" 
                                    className="form-control" 
                                    placeholder="City" 
                                    value={accountData.city}
                                    onChange={(e) => setAccountData(prev => ({...prev, city: e.target.value}))}
                                  />
                                </div>
                                <div className="input-group mb-2">
                                  <select 
                                    className="form-control" 
                                    value={accountData.country}
                                    onChange={(e) => setAccountData(prev => ({...prev, country: e.target.value}))}
                                  >
                                    <option value="" disabled>Country</option>
                                    <option value="AF">Afghanistan</option>
                                    <option value="AX">Aland Islands</option>
                                    <option value="AL">Albania</option>
                                    <option value="DZ">Algeria</option>
                                    <option value="AS">American Samoa</option>
                                    <option value="AD">Andorra</option>
                                    <option value="AO">Angola</option>
                                    <option value="AI">Anguilla</option>
                                    <option value="AQ">Antarctica</option>
                                    <option value="AG">Antigua and Barbuda</option>
                                    <option value="AR">Argentina</option>
                                    <option value="AM">Armenia</option>
                                    <option value="AW">Aruba</option>
                                    <option value="AU">Australia</option>
                                    <option value="AT">Austria</option>
                                    <option value="AZ">Azerbaijan</option>
                                    <option value="BS">Bahamas</option>
                                    <option value="BH">Bahrain</option>
                                    <option value="BD">Bangladesh</option>
                                    <option value="BB">Barbados</option>
                                    <option value="BY">Belarus</option>
                                    <option value="BE">Belgium</option>
                                    <option value="BZ">Belize</option>
                                    <option value="BJ">Benin</option>
                                    <option value="BM">Bermuda</option>
                                    <option value="BT">Bhutan</option>
                                    <option value="BO">Bolivia</option>
                                    <option value="BQ">Bonaire, Sint Eustatius and Saba</option>
                                    <option value="BA">Bosnia and Herzegovina</option>
                                    <option value="BW">Botswana</option>
                                    <option value="BV">Bouvet Island</option>
                                    <option value="BR">Brazil</option>
                                    <option value="IO">British Indian Ocean Territory</option>
                                    <option value="BN">Brunei Darussalam</option>
                                    <option value="BG">Bulgaria</option>
                                    <option value="BF">Burkina Faso</option>
                                    <option value="BI">Burundi</option>
                                    <option value="KH">Cambodia</option>
                                    <option value="CM">Cameroon</option>
                                    <option value="CA">Canada</option>
                                    <option value="CV">Cape Verde</option>
                                    <option value="KY">Cayman Islands</option>
                                    <option value="CF">Central African Republic</option>
                                    <option value="TD">Chad</option>
                                    <option value="CL">Chile</option>
                                    <option value="CN">China</option>
                                    <option value="CX">Christmas Island</option>
                                    <option value="CC">Cocos (Keeling) Islands</option>
                                    <option value="CO">Colombia</option>
                                    <option value="KM">Comoros</option>
                                    <option value="CG">Congo</option>
                                    <option value="CD">Congo, Democratic Republic of the Congo</option>
                                    <option value="CK">Cook Islands</option>
                                    <option value="CR">Costa Rica</option>
                                    <option value="CI">Cote D'Ivoire</option>
                                    <option value="HR">Croatia</option>
                                    <option value="CU">Cuba</option>
                                    <option value="CW">Curacao</option>
                                    <option value="CY">Cyprus</option>
                                    <option value="CZ">Czech Republic</option>
                                    <option value="DK">Denmark</option>
                                    <option value="DJ">Djibouti</option>
                                    <option value="DM">Dominica</option>
                                    <option value="DO">Dominican Republic</option>
                                    <option value="EC">Ecuador</option>
                                    <option value="EG">Egypt</option>
                                    <option value="SV">El Salvador</option>
                                    <option value="GQ">Equatorial Guinea</option>
                                    <option value="ER">Eritrea</option>
                                    <option value="EE">Estonia</option>
                                    <option value="ET">Ethiopia</option>
                                    <option value="FK">Falkland Islands (Malvinas)</option>
                                    <option value="FO">Faroe Islands</option>
                                    <option value="FJ">Fiji</option>
                                    <option value="FI">Finland</option>
                                    <option value="FR">France</option>
                                    <option value="GF">French Guiana</option>
                                    <option value="PF">French Polynesia</option>
                                    <option value="TF">French Southern Territories</option>
                                    <option value="GA">Gabon</option>
                                    <option value="GM">Gambia</option>
                                    <option value="GE">Georgia</option>
                                    <option value="DE">Germany</option>
                                    <option value="GH">Ghana</option>
                                    <option value="GI">Gibraltar</option>
                                    <option value="GR">Greece</option>
                                    <option value="GL">Greenland</option>
                                    <option value="GD">Grenada</option>
                                    <option value="GP">Guadeloupe</option>
                                    <option value="GU">Guam</option>
                                    <option value="GT">Guatemala</option>
                                    <option value="GG">Guernsey</option>
                                    <option value="GN">Guinea</option>
                                    <option value="GW">Guinea-Bissau</option>
                                    <option value="GY">Guyana</option>
                                    <option value="HT">Haiti</option>
                                    <option value="HM">Heard Island and Mcdonald Islands</option>
                                    <option value="VA">Holy See (Vatican City State)</option>
                                    <option value="HN">Honduras</option>
                                    <option value="HK">Hong Kong</option>
                                    <option value="HU">Hungary</option>
                                    <option value="IS">Iceland</option>
                                    <option value="IN">India</option>
                                    <option value="ID">Indonesia</option>
                                    <option value="IR">Iran, Islamic Republic of</option>
                                    <option value="IQ">Iraq</option>
                                    <option value="IE">Ireland</option>
                                    <option value="IM">Isle of Man</option>
                                    <option value="IL">Israel</option>
                                    <option value="IT">Italy</option>
                                    <option value="JM">Jamaica</option>
                                    <option value="JP">Japan</option>
                                    <option value="JE">Jersey</option>
                                    <option value="JO">Jordan</option>
                                    <option value="KZ">Kazakhstan</option>
                                    <option value="KE">Kenya</option>
                                    <option value="KI">Kiribati</option>
                                    <option value="KP">Korea, Democratic People's Republic of</option>
                                    <option value="KR">Korea, Republic of</option>
                                    <option value="XK">Kosovo</option>
                                    <option value="KW">Kuwait</option>
                                    <option value="KG">Kyrgyzstan</option>
                                    <option value="LA">Lao People's Democratic Republic</option>
                                    <option value="LV">Latvia</option>
                                    <option value="LB">Lebanon</option>
                                    <option value="LS">Lesotho</option>
                                    <option value="LR">Liberia</option>
                                    <option value="LY">Libyan Arab Jamahiriya</option>
                                    <option value="LI">Liechtenstein</option>
                                    <option value="LT">Lithuania</option>
                                    <option value="LU">Luxembourg</option>
                                    <option value="MO">Macao</option>
                                    <option value="MK">Macedonia, the Former Yugoslav Republic of</option>
                                    <option value="MG">Madagascar</option>
                                    <option value="MW">Malawi</option>
                                    <option value="MY">Malaysia</option>
                                    <option value="MV">Maldives</option>
                                    <option value="ML">Mali</option>
                                    <option value="MT">Malta</option>
                                    <option value="MH">Marshall Islands</option>
                                    <option value="MQ">Martinique</option>
                                    <option value="MR">Mauritania</option>
                                    <option value="MU">Mauritius</option>
                                    <option value="YT">Mayotte</option>
                                    <option value="MX">Mexico</option>
                                    <option value="FM">Micronesia, Federated States of</option>
                                    <option value="MD">Moldova, Republic of</option>
                                    <option value="MC">Monaco</option>
                                    <option value="MN">Mongolia</option>
                                    <option value="ME">Montenegro</option>
                                    <option value="MS">Montserrat</option>
                                    <option value="MA">Morocco</option>
                                    <option value="MZ">Mozambique</option>
                                    <option value="MM">Myanmar</option>
                                    <option value="NA">Namibia</option>
                                    <option value="NR">Nauru</option>
                                    <option value="NP">Nepal</option>
                                    <option value="NL">Netherlands</option>
                                    <option value="AN">Netherlands Antilles</option>
                                    <option value="NC">New Caledonia</option>
                                    <option value="NZ">New Zealand</option>
                                    <option value="NI">Nicaragua</option>
                                    <option value="NE">Niger</option>
                                    <option value="NG">Nigeria</option>
                                    <option value="NU">Niue</option>
                                    <option value="NF">Norfolk Island</option>
                                    <option value="MP">Northern Mariana Islands</option>
                                    <option value="NO">Norway</option>
                                    <option value="OM">Oman</option>
                                    <option value="PK">Pakistan</option>
                                    <option value="PW">Palau</option>
                                    <option value="PS">Palestinian Territory, Occupied</option>
                                    <option value="PA">Panama</option>
                                    <option value="PG">Papua New Guinea</option>
                                    <option value="PY">Paraguay</option>
                                    <option value="PE">Peru</option>
                                    <option value="PH">Philippines</option>
                                    <option value="PN">Pitcairn</option>
                                    <option value="PL">Poland</option>
                                    <option value="PT">Portugal</option>
                                    <option value="PR">Puerto Rico</option>
                                    <option value="QA">Qatar</option>
                                    <option value="RE">Reunion</option>
                                    <option value="RO">Romania</option>
                                    <option value="RU">Russian Federation</option>
                                    <option value="RW">Rwanda</option>
                                    <option value="BL">Saint Barthelemy</option>
                                    <option value="SH">Saint Helena</option>
                                    <option value="KN">Saint Kitts and Nevis</option>
                                    <option value="LC">Saint Lucia</option>
                                    <option value="MF">Saint Martin</option>
                                    <option value="PM">Saint Pierre and Miquelon</option>
                                    <option value="VC">Saint Vincent and the Grenadines</option>
                                    <option value="WS">Samoa</option>
                                    <option value="SM">San Marino</option>
                                    <option value="ST">Sao Tome and Principe</option>
                                    <option value="SA">Saudi Arabia</option>
                                    <option value="SN">Senegal</option>
                                    <option value="RS">Serbia</option>
                                    <option value="CS">Serbia and Montenegro</option>
                                    <option value="SC">Seychelles</option>
                                    <option value="SL">Sierra Leone</option>
                                    <option value="SG">Singapore</option>
                                    <option value="SX">Sint Maarten</option>
                                    <option value="SK">Slovakia</option>
                                    <option value="SI">Slovenia</option>
                                    <option value="SB">Solomon Islands</option>
                                    <option value="SO">Somalia</option>
                                    <option value="ZA">South Africa</option>
                                    <option value="GS">South Georgia and the South Sandwich Islands</option>
                                    <option value="SS">South Sudan</option>
                                    <option value="ES">Spain</option>
                                    <option value="LK">Sri Lanka</option>
                                    <option value="SD">Sudan</option>
                                    <option value="SR">Suriname</option>
                                    <option value="SJ">Svalbard and Jan Mayen</option>
                                    <option value="SZ">Swaziland</option>
                                    <option value="SE">Sweden</option>
                                    <option value="CH">Switzerland</option>
                                    <option value="SY">Syrian Arab Republic</option>
                                    <option value="TW">Taiwan, Province of China</option>
                                    <option value="TJ">Tajikistan</option>
                                    <option value="TZ">Tanzania, United Republic of</option>
                                    <option value="TH">Thailand</option>
                                    <option value="TL">Timor-Leste</option>
                                    <option value="TG">Togo</option>
                                    <option value="TK">Tokelau</option>
                                    <option value="TO">Tonga</option>
                                    <option value="TT">Trinidad and Tobago</option>
                                    <option value="TN">Tunisia</option>
                                    <option value="TR">Turkey</option>
                                    <option value="TM">Turkmenistan</option>
                                    <option value="TC">Turks and Caicos Islands</option>
                                    <option value="TV">Tuvalu</option>
                                    <option value="UG">Uganda</option>
                                    <option value="UA">Ukraine</option>
                                    <option value="AE">United Arab Emirates</option>
                                    <option value="GB">United Kingdom</option>
                                    <option value="US">United States</option>
                                    <option value="UM">United States Minor Outlying Islands</option>
                                    <option value="UY">Uruguay</option>
                                    <option value="UZ">Uzbekistan</option>
                                    <option value="VU">Vanuatu</option>
                                    <option value="VE">Venezuela</option>
                                    <option value="VN">Viet Nam</option>
                                    <option value="VG">Virgin Islands, British</option>
                                    <option value="VI">Virgin Islands, U.s.</option>
                                    <option value="WF">Wallis and Futuna</option>
                                    <option value="EH">Western Sahara</option>
                                    <option value="YE">Yemen</option>
                                    <option value="ZM">Zambia</option>
                                    <option value="ZW">Zimbabwe</option>
                                  </select>
                                </div>
                                <div className="input-group mb-2">
                                  <input 
                                    type="password" 
                                    className="form-control" 
                                    placeholder="Password" 
                                    value={accountData.password}
                                    onChange={(e) => setAccountData(prev => ({...prev, password: e.target.value}))}
                                  />
                                </div>
                                <div className="input-group mb-2">
                                  <input 
                                    type="password" 
                                    className="form-control" 
                                    placeholder="Password again" 
                                    value={accountData.password_again}
                                    onChange={(e) => setAccountData(prev => ({...prev, password_again: e.target.value}))}
                                  />
                                </div>

                                <div className="alert alert-info">
                                  <i className="fa fa-info-circle me-2"></i>
                                  By creating this account, you'll accept the invitation from {invitation.owner_name} and gain access to their account sections.
                                </div>

                                <div className="row">
                                  <div className="col-12">
                                    <button 
                                      className="btn btn-lg btn-primary btn-block"
                                      onClick={acceptInvitation}
                                      disabled={accepting}
                                    >
                                      {accepting ? (
                                        <>
                                          <div className="spinner-border spinner-border-sm me-2" role="status"></div>
                                          Creating Account...
                                        </>
                                      ) : (
                                        <>
                                          <i className="fa fa-check me-2"></i>
                                          Create Account & Accept
                                        </>
                                      )}
                                    </button>
                                  </div>
                                  <div className="col-6 text-left">
                                    <a onClick={() => setStep('view')} style={{cursor: 'pointer'}}>← Back</a>
                                  </div>
                                  <div className="col-6 text-right">
                                    <a href="/register">Cancel</a>
                                  </div>
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="card text-white py-5 d-md-down-none page-content mt-0">
                        <div className="card-body text-center justify-content-center page-single-content">
                          <img src="/assets/images/brand/favicon.png" alt="img"/>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </body>
  );
};

export default AcceptInvitation;