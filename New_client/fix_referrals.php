<?php
// Include database connection
require_once("mysql.php");

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Referral System Diagnostic Tool</h1>";

// Check if users table has referrer_id column
try {
    $columnCheck = $pdo->query("SHOW COLUMNS FROM users LIKE 'referrer_id'");
    
    if ($columnCheck->rowCount() == 0) {
        echo "<p>The referrer_id column does not exist in the users table. Adding it now...</p>";
        
        // Add referrer_id column if it doesn't exist
        $pdo->exec("ALTER TABLE users ADD COLUMN referrer_id int DEFAULT NULL");
        echo "<p>Successfully added referrer_id column to users table.</p>";
    } else {
        echo "<p>The referrer_id column exists in the users table.</p>";
    }
} catch (Exception $e) {
    echo "<p>Error checking referrer_id column: " . $e->getMessage() . "</p>";
}

// Check if there are any users with referrer_id set
try {
    $referredUsersQuery = $pdo->query("SELECT COUNT(*) as count FROM users WHERE referrer_id IS NOT NULL");
    $referredUsersCount = $referredUsersQuery->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "<p>Number of users with referrer_id set: " . $referredUsersCount . "</p>";
    
    if ($referredUsersCount == 0) {
        echo "<p>No users have a referrer_id set. This is why the 'Active Referrals' count is 0.</p>";
    }
} catch (Exception $e) {
    echo "<p>Error counting referred users: " . $e->getMessage() . "</p>";
}

// List all users
try {
    echo "<h2>All Users</h2>";
    $usersQuery = $pdo->query("SELECT id, email, first_name, last_name, referrer_id FROM users ORDER BY id");
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Email</th><th>Name</th><th>Referrer ID</th></tr>";
    
    while ($user = $usersQuery->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . $user['email'] . "</td>";
        echo "<td>" . $user['first_name'] . " " . $user['last_name'] . "</td>";
        echo "<td>" . ($user['referrer_id'] ? $user['referrer_id'] : 'NULL') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} catch (Exception $e) {
    echo "<p>Error listing users: " . $e->getMessage() . "</p>";
}

// Form to set referrer_id
echo "<h2>Set Referrer ID</h2>";
echo "<form method='post'>";
echo "<p>User ID: <input type='number' name='user_id' required></p>";
echo "<p>Referrer ID: <input type='number' name='referrer_id' required></p>";
echo "<p><input type='submit' name='set_referrer' value='Set Referrer'></p>";
echo "</form>";

// Process form submission
if (isset($_POST['set_referrer'])) {
    $userId = (int)$_POST['user_id'];
    $referrerId = (int)$_POST['referrer_id'];
    
    try {
        // Check if user exists
        $userCheck = $pdo->prepare("SELECT id FROM users WHERE id = :id");
        $userCheck->bindValue(':id', $userId);
        $userCheck->execute();
        
        if ($userCheck->rowCount() == 0) {
            echo "<p style='color: red;'>Error: User ID " . $userId . " does not exist.</p>";
        } else {
            // Check if referrer exists
            $referrerCheck = $pdo->prepare("SELECT id FROM users WHERE id = :id");
            $referrerCheck->bindValue(':id', $referrerId);
            $referrerCheck->execute();
            
            if ($referrerCheck->rowCount() == 0) {
                echo "<p style='color: red;'>Error: Referrer ID " . $referrerId . " does not exist.</p>";
            } else {
                // Update referrer_id
                $updateQuery = $pdo->prepare("UPDATE users SET referrer_id = :referrer_id WHERE id = :user_id");
                $updateQuery->bindValue(':referrer_id', $referrerId);
                $updateQuery->bindValue(':user_id', $userId);
                $updateQuery->execute();
                
                echo "<p style='color: green;'>Successfully set referrer ID " . $referrerId . " for user ID " . $userId . ".</p>";
                
                // Refresh the page to show updated data
                echo "<script>window.location.href = window.location.href;</script>";
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error setting referrer ID: " . $e->getMessage() . "</p>";
    }
}

// Test the get_commission_rates.php functionality
echo "<h2>Test get_commission_rates.php</h2>";
echo "<form method='post'>";
echo "<p>User ID: <input type='number' name='test_user_id' required></p>";
echo "<p><input type='submit' name='test_commission_rates' value='Test Commission Rates'></p>";
echo "</form>";

if (isset($_POST['test_commission_rates'])) {
    $testUserId = (int)$_POST['test_user_id'];
    
    try {
        // Check if user exists
        $userCheck = $pdo->prepare("SELECT id FROM users WHERE id = :id");
        $userCheck->bindValue(':id', $testUserId);
        $userCheck->execute();
        
        if ($userCheck->rowCount() == 0) {
            echo "<p style='color: red;'>Error: User ID " . $testUserId . " does not exist.</p>";
        } else {
            // Count referred users
            $referredUsersQuery = $pdo->prepare("
                SELECT COUNT(*) as referral_count
                FROM users
                WHERE referrer_id = :user_id
            ");
            $referredUsersQuery->bindValue(':user_id', $testUserId);
            $referredUsersQuery->execute();
            $referralCount = (int)($referredUsersQuery->fetch(PDO::FETCH_ASSOC)['referral_count'] ?? 0);
            
            echo "<p>User ID " . $testUserId . " has " . $referralCount . " referred users.</p>";
            
            // Get commission rates
            $ratesQuery = $pdo->query("SELECT * FROM commission_rates ORDER BY min_services ASC");
            $rates = $ratesQuery->fetchAll(PDO::FETCH_ASSOC);
            
            // Determine user's level
            $userLevel = 'Bronze'; // Default level
            foreach ($rates as $rate) {
                if ($referralCount >= $rate['min_services']) {
                    $userLevel = $rate['level'];
                } else {
                    break;
                }
            }
            
            echo "<p>Based on " . $referralCount . " referrals, user's level should be: " . $userLevel . "</p>";
            
            echo "<h3>Commission Rates</h3>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Level</th><th>Requirement</th><th>Min Services</th><th>Rate</th></tr>";
            
            foreach ($rates as $rate) {
                echo "<tr>";
                echo "<td>" . $rate['level'] . "</td>";
                echo "<td>" . $rate['requirement'] . "</td>";
                echo "<td>" . $rate['min_services'] . "</td>";
                echo "<td>" . $rate['rate'] . "%</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error testing commission rates: " . $e->getMessage() . "</p>";
    }
}
?>
