<?php
// Database connection
require_once 'config.php'; // Adjust this to your actual database configuration file

try {
    // Update type for VPS services in orders_items
    // This query updates orders_items where the order is of type 'vps'
    $updateQuery = "
        UPDATE orders_items oi
        JOIN orders o ON oi.order_id = o.id
        SET oi.type = 'vps'
        WHERE o.order_type = 'vps' OR o.order_type LIKE '%vps%'
    ";
    $stmt = $pdo->prepare($updateQuery);
    $result = $stmt->execute();
    
    $rowCount = $stmt->rowCount();
    echo "Updated $rowCount orders_items with type='vps' based on order_type\n";

    // Update type for VPS services based on hostname pattern (if applicable)
    // Adjust the LIKE pattern to match your VPS hostname convention
    $updateQuery2 = "
        UPDATE orders_items
        SET type = 'vps'
        WHERE hostname LIKE 'vps%' OR hostname LIKE '%vps%'
    ";
    $stmt2 = $pdo->prepare($updateQuery2);
    $result2 = $stmt2->execute();
    
    $rowCount2 = $stmt2->rowCount();
    echo "Updated $rowCount2 orders_items with type='vps' based on hostname pattern\n";

    // You might need additional queries to identify VPS services by other criteria
    
    echo "VPS type update completed successfully\n";
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?> 