<?php
/**
 * Test PayPal Vaulting Process Debug <PERSON>ript
 * This script will test the exact vaulting process with the setup token from the database
 */

require_once("./mysql.php");

// Set error handling
error_reporting(E_ALL);
ini_set('display_errors', 1);

// PayPal configuration (same as in your API)
define('PAYPAL_CLIENT_ID', 'AexATmG6J-fd_YGA3h-xCNBQxf8l1btDDfbfciytmiAemB1Dqc58E8L-1HiKogTEg0jkre8nItHCRv50');
define('PAYPAL_SECRET', 'EM-vDbIrr0Els7tRqezoR3v2Kk4-b1-4COXMxUNe5sq9vtdJ9kwrRokJWMkgjpor6y8tX-fJ7pA650KV');
define('PAYPAL_SANDBOX', true);

$api_base = PAYPAL_SANDBOX ? 'https://api-m.sandbox.paypal.com' : 'https://api-m.paypal.com';

function getPayPalAccessToken($api_base) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_base . '/v1/oauth2/token');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
    curl_setopt($ch, CURLOPT_USERPWD, PAYPAL_CLIENT_ID . ':' . PAYPAL_SECRET);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Accept: application/json', 'Accept-Language: en_US'));

    $auth_response = curl_exec($ch);
    $auth_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Auth HTTP Code: $auth_http_code\n";
    echo "Auth Response: $auth_response\n\n";
    
    if ($auth_http_code !== 200) {
        throw new Exception("Failed to get PayPal access token. HTTP code: $auth_http_code");
    }
    
    $auth_data = json_decode($auth_response, true);
    if (!isset($auth_data['access_token'])) {
        throw new Exception('Failed to get PayPal access token from response');
    }
    
    return $auth_data['access_token'];
}

try {
    echo "=== PayPal Vaulting Debug Test ===\n\n";
    
    // Get the payment method from database
    $user_id = 1; // Based on your database record
    $setup_token = '3SC10145SJ475823E'; // From your database record
    
    echo "Testing with:\n";
    echo "- User ID: $user_id\n";
    echo "- Setup Token: $setup_token\n\n";
    
    // Check if setup token exists in database and is pending
    $checkStmt = $pdo->prepare("SELECT * FROM payment_methods 
                               WHERE user_id = :user_id 
                               AND processor = 'paypal' 
                               AND setup_token = :setup_token 
                               AND status = 'pending'");
    $checkStmt->bindValue(":user_id", $user_id);
    $checkStmt->bindValue(":setup_token", $setup_token);
    $checkStmt->execute();
    
    $existingPaymentMethod = $checkStmt->fetch(PDO::FETCH_ASSOC);
    if (!$existingPaymentMethod) {
        echo "❌ No pending PayPal payment method found with this setup token\n";
        exit(1);
    }
    
    echo "✅ Found pending payment method record with ID: " . $existingPaymentMethod['id'] . "\n\n";
    
    // Get PayPal access token
    echo "1. Getting PayPal access token...\n";
    $access_token = getPayPalAccessToken($api_base);
    echo "✅ Got access token: " . substr($access_token, 0, 20) . "...\n\n";
    
    // Check setup token status
    echo "2. Checking setup token status...\n";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_base . '/v3/vault/setup-tokens/' . $setup_token);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $access_token
    ]);

    $setup_check_response = curl_exec($ch);
    $setup_check_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "Setup token check HTTP code: $setup_check_http_code\n";
    echo "Setup token response: $setup_check_response\n\n";

    if ($setup_check_http_code !== 200) {
        echo "❌ Setup token check failed\n";
        if ($setup_check_http_code === 404) {
            echo "Setup token not found or expired. You need to create a new one.\n";
        }
        exit(1);
    }

    $setup_details = json_decode($setup_check_response, true);
    echo "Setup token status: " . ($setup_details['status'] ?? 'unknown') . "\n";
    
    if (!isset($setup_details['status'])) {
        echo "❌ No status found in setup token response\n";
        exit(1);
    }
    
    if ($setup_details['status'] !== 'APPROVED') {
        echo "❌ Setup token is not approved. Status: " . $setup_details['status'] . "\n";
        echo "You need to approve the PayPal setup through the frontend first.\n";
        exit(1);
    }
    
    echo "✅ Setup token is approved! Proceeding to vault...\n\n";

    // Create payment token from approved setup token
    echo "3. Creating PayPal vault token...\n";
    $vault_data = [
        'payment_source' => [
            'token' => [
                'id' => $setup_token,
                'type' => 'SETUP_TOKEN'
            ]
        ]
    ];

    echo "Vault data: " . json_encode($vault_data) . "\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_base . '/v3/vault/payment-tokens');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($vault_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $access_token,
        'PayPal-Request-Id: ' . uniqid('vault_', true)
    ]);

    $vault_response = curl_exec($ch);
    $vault_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "Vault creation HTTP code: $vault_http_code\n";
    echo "Vault response: $vault_response\n\n";

    if ($vault_http_code !== 201 && $vault_http_code !== 200) {
        echo "❌ Failed to create vault token\n";
        $error_data = json_decode($vault_response, true);
        if (isset($error_data['details']) && is_array($error_data['details'])) {
            foreach ($error_data['details'] as $detail) {
                echo "Error: " . $detail['description'] . "\n";
            }
        }
        exit(1);
    }

    $vault_result = json_decode($vault_response, true);
    
    if (!isset($vault_result['id'])) {
        echo "❌ No vault ID in response\n";
        exit(1);
    }

    echo "✅ Successfully created PayPal vault token: " . $vault_result['id'] . "\n\n";

    // Extract payer information
    $payer_email = null;
    $payer_id = null;
    
    if (isset($vault_result['payment_source']['paypal']['email_address'])) {
        $payer_email = $vault_result['payment_source']['paypal']['email_address'];
    }
    if (isset($vault_result['payment_source']['paypal']['payer_id'])) {
        $payer_id = $vault_result['payment_source']['paypal']['payer_id'];
    }

    echo "Payer email: " . ($payer_email ?: 'Not found') . "\n";
    echo "Payer ID: " . ($payer_id ?: 'Not found') . "\n\n";

    // Update database
    echo "4. Updating database...\n";
    $updateStmt = $pdo->prepare("UPDATE payment_methods 
                               SET status = 'active', 
                                   vault_id = :vault_id,
                                   payment_method_id = :payment_method_id,
                                   payer_email = :payer_email,
                                   payer_id = :payer_id,
                                   updated = NOW()
                               WHERE user_id = :user_id 
                               AND processor = 'paypal' 
                               AND setup_token = :setup_token");
    
    $updateStmt->bindValue(":vault_id", $vault_result['id']);
    $updateStmt->bindValue(":payment_method_id", $vault_result['id']); 
    $updateStmt->bindValue(":payer_email", $payer_email);
    $updateStmt->bindValue(":payer_id", $payer_id);
    $updateStmt->bindValue(":user_id", $user_id);
    $updateStmt->bindValue(":setup_token", $setup_token);
    
    if ($updateStmt->execute()) {
        $rowsAffected = $updateStmt->rowCount();
        echo "Database update executed. Rows affected: $rowsAffected\n";
        
        if ($rowsAffected > 0) {
            echo "✅ PayPal vaulting completed successfully!\n";
            
            // Show updated record
            $checkStmt->execute();
            $updatedRecord = $checkStmt->fetch(PDO::FETCH_ASSOC);
            echo "\nUpdated record:\n";
            print_r($updatedRecord);
            
        } else {
            echo "❌ Database update executed but no rows were affected\n";
        }
    } else {
        echo "❌ Database update failed to execute\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?> 