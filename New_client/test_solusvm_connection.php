<?php
// Set error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Function to print results in a readable format
function printResult($title, $data) {
    echo "\n=== $title ===\n";
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                echo "$key: " . json_encode($value) . "\n";
            } else {
                echo "$key: $value\n";
            }
        }
    } else {
        echo $data . "\n";
    }
    echo "\n";
}

// Test different API URLs and authentication methods
$api_urls = [
    'https://virt.zetservers.com/api/v2/',
    'https://virt.zetservers.com/api/v2/admin/',
    'https://virt.zetservers.com/api/admin/v2/',
    'https://virt.zetservers.com/api/',
    'https://virt.zetservers.com/admin/api/v2/'
];

$api_token = '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

// Test different authentication methods
$auth_methods = [
    'Bearer' => 'Authorization: Bearer ' . $api_token,
    'Token' => 'Authorization: Token ' . $api_token,
    'JWT' => 'Authorization: JWT ' . $api_token,
    'API-Key' => 'API-Key: ' . $api_token,
    'X-API-Key' => 'X-API-Key: ' . $api_token
];

echo "Testing SolusVM API Connection with different URLs and authentication methods...\n\n";

foreach ($api_urls as $api_url) {
    echo "\n\n========== Testing API URL: $api_url ==========\n\n";
    
    foreach ($auth_methods as $auth_name => $auth_header) {
        echo "\n----- Testing $auth_name Authentication -----\n";
        
        // Test a simple GET request to the API root
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            $auth_header,
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            echo "cURL Error: " . curl_error($ch) . "\n";
        } else {
            echo "HTTP Code: $httpCode\n";
            
            // Check if response is JSON
            $json_response = json_decode($response, true);
            if ($json_response !== null) {
                echo "Response is valid JSON\n";
                echo "Response: " . json_encode($json_response, JSON_PRETTY_PRINT) . "\n";
            } else {
                echo "Response is not valid JSON\n";
                echo "Response starts with: " . substr($response, 0, 100) . "...\n";
            }
        }
        
        curl_close($ch);
    }
}

// Try to get information about a specific VPS
echo "\n\n========== Testing VPS Information Retrieval ==========\n\n";

// Try different API endpoints for VPS information
$vps_endpoints = [
    'vservers/1973',
    'virtual-servers/1973',
    'vps/1973',
    'servers/1973',
    'vm/1973'
];

foreach ($vps_endpoints as $endpoint) {
    echo "\n----- Testing endpoint: $endpoint -----\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://virt.zetservers.com/api/v2/' . $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $api_token,
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        echo "cURL Error: " . curl_error($ch) . "\n";
    } else {
        echo "HTTP Code: $httpCode\n";
        
        // Check if response is JSON
        $json_response = json_decode($response, true);
        if ($json_response !== null) {
            echo "Response is valid JSON\n";
            echo "Response: " . json_encode($json_response, JSON_PRETTY_PRINT) . "\n";
        } else {
            echo "Response is not valid JSON\n";
            echo "Response starts with: " . substr($response, 0, 100) . "...\n";
        }
    }
    
    curl_close($ch);
}

echo "\n\nTesting complete!\n";
?>
