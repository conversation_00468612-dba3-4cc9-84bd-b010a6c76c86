<?php
/**
 * Test script for SolusVM power off functionality
 * 
 * This script makes a direct API call to the SolusVM API to shut down a server.
 * Usage: php test_power_off.php <server_id>
 */

// Check if server ID is provided
if ($argc < 2) {
    echo "Usage: php test_power_off.php <server_id>\n";
    exit(1);
}

// Get server ID from command line argument
$server_id = $argv[1];

// API configuration
$apiKey = '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
$baseUrl = 'https://virt.zetservers.com';

// First, check if the server exists by fetching the server list
echo "Step 1: Checking if server exists...\n";
$apiEndpoint = $baseUrl . '/api/v1/servers';
echo "API Endpoint: $apiEndpoint\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiEndpoint);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $apiKey,
    'Accept: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if (curl_errno($ch)) {
    echo "cURL Error: " . curl_error($ch) . "\n";
    exit(1);
}

curl_close($ch);

// Process API response
$apiData = json_decode($response, true);

// Check if we got a valid response
if ($httpCode != 200 || !$apiData || !isset($apiData['data']) || empty($apiData['data'])) {
    echo "SolusVM API Error: " . ($apiData['message'] ?? 'Unknown error') . " (HTTP Code: $httpCode)\n";
    exit(1);
}

// Find the server with the matching ID
$serverFound = false;
echo "Looking for server ID: $server_id in " . count($apiData['data']) . " servers\n";

foreach ($apiData['data'] as $server) {
    echo "Checking server ID: " . $server['id'] . " with status: " . ($server['status'] ?? 'N/A') . "\n";
    
    // Convert both IDs to strings for comparison to avoid type issues
    if ((string)$server['id'] === (string)$server_id) {
        $serverFound = true;
        echo "Found server ID: $server_id with status: " . ($server['status'] ?? 'N/A') . "\n";
        break;
    }
}

if (!$serverFound) {
    echo "Server with ID $server_id not found in API response\n";
    exit(1);
}

// Now send the power action to the SolusVM API
echo "\nStep 2: Sending power off command...\n";
$powerEndpoint = $baseUrl . '/api/v1/servers/' . $server_id . '/shutdown';
echo "Power Endpoint: $powerEndpoint\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $powerEndpoint);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true); // Use POST method
curl_setopt($ch, CURLOPT_POSTFIELDS, "{}"); // Empty JSON body
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $apiKey,
    'Accept: application/json',
    'Content-Type: application/json'
]);

$powerResponse = curl_exec($ch);
$powerHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if (curl_errno($ch)) {
    echo "cURL Error in power action: " . curl_error($ch) . "\n";
    exit(1);
} else {
    echo "Power action response (HTTP Code: $powerHttpCode):\n";
    echo $powerResponse . "\n";
}

curl_close($ch);

// Check the response
if ($powerHttpCode != 200) {
    echo "Power action API returned non-200 status code: $powerHttpCode\n";
    if ($powerResponse) {
        echo "Response body: $powerResponse\n";
    }
    exit(1);
} else {
    echo "Power action successful with status code 200\n";
    if ($powerResponse) {
        echo "Response body: $powerResponse\n";
    }
}

// Check server status after power action
echo "\nStep 3: Checking server status after power action...\n";
sleep(5); // Wait 5 seconds before checking status

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiEndpoint);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $apiKey,
    'Accept: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if (curl_errno($ch)) {
    echo "cURL Error: " . curl_error($ch) . "\n";
    exit(1);
}

curl_close($ch);

// Process API response
$apiData = json_decode($response, true);

// Check if we got a valid response
if ($httpCode != 200 || !$apiData || !isset($apiData['data']) || empty($apiData['data'])) {
    echo "SolusVM API Error: " . ($apiData['message'] ?? 'Unknown error') . " (HTTP Code: $httpCode)\n";
    exit(1);
}

// Find the server with the matching ID
foreach ($apiData['data'] as $server) {
    // Convert both IDs to strings for comparison to avoid type issues
    if ((string)$server['id'] === (string)$server_id) {
        echo "Server ID: $server_id current status: " . ($server['status'] ?? 'N/A') . "\n";
        break;
    }
}

echo "\nPower off test completed.\n";
