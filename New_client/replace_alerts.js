const fs = require('fs');
const path = require('path');

// List of files that need to be updated
const filesToUpdate = [
  'zet/src/pages/TransactionDetails.js',
  'zet/src/pages/Faq.js',
  'zet/src/pages/Support.js',
  'zet/src/pages/Billing.js',
  'zet/src/pages/CloudOrder.js',
  'zet/src/pages/Reseller.js',
  'zet/src/pages/NewTicket.js',
  'zet/src/pages/Ticket.js',
  'zet/src/pages/VpsServerDetailsView.js',
  'zet/src/pages/ServerDetailsView.js',
  'zet/src/pages/CloudServerDetailsView.js',
  'zet/src/pages/IPTransitOrder.js'
];

// Static alert patterns to match (different variations of the same alert)
const alertPatterns = [
  // Server discount alert pattern
  /<div className="alert alert-success" role="alert">\s*<i className="fa fa-check-circle-o me-2" aria-hidden="true"><\/i>\s*<strong>Well done!<\/strong>\s*Activate 2 more .* in your account to get\s*<b>10% discount<\/b>\s*on all servers\s*&nbsp;\s*<Link to="\/reseller"><button className="btn btn-default btn-sm">Check Discounts<\/button><\/Link>\s*<\/div>/g,
  
  // Server discount alert pattern (Alert component version)
  /<Alert color="success" className="mb-4">\s*<i className="fa fa-check-circle-o me-2" aria-hidden="true"><\/i>\s*<strong>Well done!<\/strong>\s*Activate 2 more .* in your account to get\s*<b>10% discount<\/b>\s*on all servers\s*&nbsp;\s*<Link to="\/reseller"><button className="btn btn-default btn-sm">Check Discounts<\/button><\/Link>\s*<\/Alert>/g,
  
  // BootstrapAlert version (in CloudOrder.js)
  /<BootstrapAlert variant="success" className="mb-4">\s*<i className="fa fa-check-circle-o me-2" aria-hidden="true"><\/i>\s*<strong>Well done!<\/strong>\s*Activate 2 more servers in your account to get\s*<b>10% discount<\/b>\s*on all servers\s*&nbsp;\s*<Link to="\/reseller"><button className="btn btn-default btn-sm">Check Discounts<\/button><\/Link>\s*<\/BootstrapAlert>/g,
  
  // IP Transit discount alert pattern (specific to IPTransitOrder.js)
  /<div className="alert alert-success" role="alert">\s*<i className="fa fa-check-circle-o me-2" aria-hidden="true"><\/i>\s*<strong>Well done!<\/strong>\s*Activate 2 more services in your account to get\s*<b>10% discount<\/b>\s*on all IP transit\s*&nbsp;\s*<Link to="\/reseller"><button className="btn btn-default btn-sm">Check Discounts<\/button><\/Link>\s*<\/div>/g,
  
  // IP Transit alert component version
  /<Alert color="success" className="mb-4">\s*<i className="fa fa-check-circle-o me-2" aria-hidden="true"><\/i>\s*<strong>Well done!<\/strong>\s*Activate 2 more services in your account to get\s*<b>10% discount<\/b>\s*on all IP transit\s*&nbsp;\s*<Link to="\/reseller"><button className="btn btn-default btn-sm">Check Discounts<\/button><\/Link>\s*<\/Alert>/g,
  
  // Cloud Server specific pattern (for CloudServerDetailsView.js)
  /<div className="alert alert-success" role="alert">\s*<i className="fa fa-check-circle-o me-2" aria-hidden="true"><\/i>\s*<strong>Well done!<\/strong>\s*Activate 2 more cloud servers in your account to get\s*<b>10% discount<\/b>\s*on all servers\s*&nbsp;\s*<Link to="\/reseller"><button className="btn btn-default btn-sm">Check Discounts<\/button><\/Link>\s*<\/div>/g
];

// The replacement component
const discountAlertComponent = '<DiscountAlert />';

// Function to add the import statement if it doesn't exist
function addImportIfNeeded(content) {
  // Check if the import is already present
  if (!content.includes("import DiscountAlert from")) {
    // Find the last import statement
    const lastImportIndex = content.lastIndexOf('import ');
    if (lastImportIndex !== -1) {
      const endOfLastImport = content.indexOf(';', lastImportIndex) + 1;
      if (endOfLastImport !== 0) {
        // Add our import after the last import
        return content.slice(0, endOfLastImport) + 
               '\nimport DiscountAlert from "../components/DiscountAlert";\n' + 
               content.slice(endOfLastImport);
      }
    }
    
    // If no imports were found, add it at the beginning
    return 'import DiscountAlert from "../components/DiscountAlert";\n\n' + content;
  }
  
  return content;
}

// Process each file
filesToUpdate.forEach(filePath => {
  try {
    // Read the file content
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Track if any replacements were made
    let hadReplacement = false;
    
    // Try each pattern
    alertPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches && matches.length > 0) {
        hadReplacement = true;
        content = content.replace(pattern, discountAlertComponent);
      }
    });
    
    // If replacements were made, add the import statement
    if (hadReplacement) {
      content = addImportIfNeeded(content);
      
      // Write the updated content back to the file
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Updated ${filePath}`);
    } else {
      console.log(`⚠️ No replacements made in ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log('Replacement process completed!'); 