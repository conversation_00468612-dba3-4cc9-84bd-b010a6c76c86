-- Create vouchers table
CREATE TABLE `vouchers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `voucher_code` varchar(20) NOT NULL,
  `user_id` int NOT NULL,
  `value` decimal(10,2) NOT NULL COMMENT 'Voucher value in euros',
  `generated_by` int NOT NULL COMMENT 'Affiliate ID that generated this voucher',
  `status` enum('active','used','expired') NOT NULL DEFAULT 'active',
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expiry_date` timestamp NOT NULL,
  `used_date` timestamp NULL DEFAULT NULL,
  `used_by` int DEFAULT NULL COMMENT 'User ID who used the voucher',
  `order_id` int DEFAULT NULL COMMENT 'Order ID where voucher was used',
  `order_value` decimal(10,2) DEFAULT NULL COMMENT 'Value of the order where voucher was used',
  PRIMARY <PERSON> (`id`),
  UNIQUE KEY `voucher_code` (`voucher_code`),
  KEY `idx_user_id` (`user_id`),
  <PERSON>EY `idx_generated_by` (`generated_by`),
  KEY `idx_status` (`status`),
  KEY `idx_used_by` (`used_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
