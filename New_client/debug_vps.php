<?php
// Include database connection
require_once("mysql.php");

// Set error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Function to print results in a readable format
function printResult($title, $data) {
    echo "\n=== $title ===\n";
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                echo "$key: " . json_encode($value) . "\n";
            } else {
                echo "$key: $value\n";
            }
        }
    } else {
        echo $data . "\n";
    }
    echo "\n";
}

// Get the VPS ID from the URL parameter
$vps_id = isset($_GET['id']) ? $_GET['id'] : 84; // Default to 84 if not specified

// Simulate the API authentication
$user_id = 1; // Assuming user ID 1 for testing

try {
    // Log the request parameters for debugging
    echo "VPS Details Request - VPS ID: $vps_id, User ID: $user_id\n\n";

    // First, check if the VPS exists without type restriction
    $check_stmt = $pdo->prepare("
        SELECT oi.id, oi.type, oi.user_id, oi.server_id
        FROM orders_items oi
        WHERE oi.id = :vps_id
    ");
    $check_stmt->bindValue(':vps_id', $vps_id);
    $check_stmt->execute();

    if ($check_stmt->rowCount() > 0) {
        $check_row = $check_stmt->fetch(PDO::FETCH_ASSOC);
        echo "VPS Found - ID: {$check_row['id']}, Type: " . (isset($check_row['type']) ? $check_row['type'] : 'NULL') . 
             ", Owner: {$check_row['user_id']}, Server ID: " . (isset($check_row['server_id']) ? $check_row['server_id'] : 'NULL') . "\n\n";
    } else {
        echo "VPS Not Found - ID: $vps_id\n\n";
    }

    // Now verify that this VPS belongs to the authenticated user
    $stmt = $pdo->prepare("
        SELECT oi.*, o.order_type
        FROM orders_items oi
        LEFT JOIN orders o ON oi.order_id = o.id
        WHERE oi.id = :vps_id AND oi.user_id = :user_id
    ");
    $stmt->bindValue(':vps_id', $vps_id);
    $stmt->bindValue(':user_id', $user_id);
    $stmt->execute();

    echo "VPS Query Result - Rows: " . $stmt->rowCount() . "\n\n";

    if ($stmt->rowCount() == 0) {
        // Check if the VPS exists but belongs to another user
        $check_owner = $pdo->prepare("SELECT user_id FROM orders_items WHERE id = :vps_id");
        $check_owner->bindValue(':vps_id', $vps_id);
        $check_owner->execute();
        
        if ($check_owner->rowCount() > 0) {
            $owner = $check_owner->fetch(PDO::FETCH_ASSOC);
            echo "VPS belongs to user ID: " . $owner['user_id'] . ", not user ID: $user_id\n\n";
        }
        
        echo "Error: VPS not found or access denied\n";
        exit;
    }

    $vps_order = $stmt->fetch(PDO::FETCH_ASSOC);
    $solusvm_id = $vps_order['server_id']; // This should be the SolusVM virtual server ID
    
    // Check if server_id exists
    if (empty($solusvm_id)) {
        echo "Missing SolusVM ID for VPS ID: $vps_id\n";
        exit;
    }
    
    echo "Using SolusVM ID: $solusvm_id for VPS ID: $vps_id\n\n";
    
    // Print all VPS order details for debugging
    printResult("VPS Order Details", $vps_order);
    
    // Check the database structure
    echo "Checking database structure for orders_items table:\n";
    $structure = $pdo->query("DESCRIBE orders_items");
    $columns = $structure->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        echo "{$column['Field']} - {$column['Type']}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
