<?php
// Include database connection
require_once("mysql.php");

// Set headers for JSON response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Check if the general_settings table exists
    $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'general_settings'");
    $tableExists = $tableCheckStmt->rowCount() > 0;
    
    if ($tableExists) {
        // Get the API domain setting
        $stmt = $pdo->prepare("SELECT setting_value FROM general_settings WHERE setting_key = 'api_domain' LIMIT 1");
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $apiDomain = $stmt->fetchColumn();
            
            // Return the API domain
            echo json_encode([
                'success' => true,
                'api_domain' => $apiDomain
            ]);
        } else {
            // Default API domain if setting not found
            echo json_encode([
                'success' => true,
                'api_domain' => 'https://test.x-zoneit.ro'
            ]);
        }
    } else {
        // Default API domain if table doesn't exist
        echo json_encode([
            'success' => true,
            'api_domain' => 'https://test.x-zoneit.ro'
        ]);
    }
} catch (Exception $e) {
    // Return error
    echo json_encode([
        'success' => false,
        'error' => 'Failed to get API domain: ' . $e->getMessage()
    ]);
}
?>
