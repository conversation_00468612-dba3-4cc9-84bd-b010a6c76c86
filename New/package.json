{"name": "from0", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.4", "lodash": "^4.17.21", "lucide-react": "^0.479.0", "react": "^19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.3.0", "react-toastify": "^11.0.5", "recharts": "^2.15.1", "web-vitals": "^2.1.4", "express": "^4.18.2", "ssh2": "^1.13.0", "ws": "^8.13.0", "xterm": "^5.5.0", "@xterm/addon-fit": "^0.10.0"}, "scripts": {"start": "react-scripts start --host 0.0.0.0 --allowed-hosts '*'", "build": "react-scripts build", "test": "react-scripts test", "tailwind": "tailwindcss init -p", "start:ssh": "node ssh_server.js", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/postcss7-compat": "^2.2.17", "autoprefixer": "^9.8.8", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "postcss-flexbugs-fixes": "^4.2.1", "postcss-loader": "^4.3.0", "postcss-preset-env": "^6.7.2", "react-scripts": "^5.0.1", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17"}}