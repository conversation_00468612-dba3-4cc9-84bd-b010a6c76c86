<?php
require_once("auth_functions.php");
require_once("api_admin_settings.php"); // Include this file to use sendEmailWithSignature function

// Import PHPMailer classes
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception as PHPMailerException;



if($_GET['f'] == 'get_accounts'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Get pagination parameters
      $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
      $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 100;
      $offset = ($page - 1) * $limit;

      // Get filter parameters
      $status = isset($_GET['status']) ? $_GET['status'] : '';
      $search = isset($_GET['search']) ? $_GET['search'] : '';

      // Check which columns are available in the users table
      $columns_sth = $pdo->query("SHOW COLUMNS FROM users");
      $available_columns = $columns_sth->fetchAll(PDO::FETCH_COLUMN);

      // Build the select clause with all available relevant columns
      $select_columns = ['id', 'email'];

      // Add optional columns if they exist
      $optional_columns = [
        'first_name', 'last_name', 'company_name', 'status', 'country', 'city',
        'phone', 'address', 'role', 'notes', 'created_at', 'last_login',
        'vat_id'  // Add this line to explicitly include VAT ID
    ];

      foreach ($optional_columns as $col) {
        if (in_array($col, $available_columns)) {
          $select_columns[] = $col;
        }
      }

      // Build the base query
      $sql = "SELECT " . implode(', ', $select_columns) . " FROM users WHERE 1=1";

      // Add filters
      if (!empty($status) && $status !== 'All') {
        // Map status text to numeric values
        $status_map = [
          'Active' => 1,
          'Inactive' => 0,
          'Locked' => 2,
          'Pending' => 3
        ];

        if (isset($status_map[$status]) && in_array('status', $available_columns)) {
          $sql .= " AND status = " . $pdo->quote($status_map[$status]);
        }
      }

      if (!empty($search)) {
        $search_conditions = [];
        $search_term = "%{$search}%";

        // Add search conditions for available columns
        foreach (['email', 'first_name', 'last_name', 'company_name', 'city', 'country'] as $col) {
          if (in_array($col, $available_columns)) {
            $search_conditions[] = "$col LIKE " . $pdo->quote($search_term);
          }
        }

        if (!empty($search_conditions)) {
          $sql .= " AND (" . implode(' OR ', $search_conditions) . ")";
        }
      }

      // Add order and limit
      $sql .= " ORDER BY id DESC LIMIT :limit OFFSET :offset";

      // Prepare and execute query
      $sth = $pdo->prepare($sql);
      $sth->bindValue(':limit', $limit, PDO::PARAM_INT);
      $sth->bindValue(':offset', $offset, PDO::PARAM_INT);
      $sth->execute();

      // Count total records (for pagination)
      $count_sql = "SELECT COUNT(*) as total FROM users WHERE 1=1";

      // Add the same filters to count query
      if (!empty($status) && $status !== 'All' && in_array('status', $available_columns)) {
        $count_sql .= " AND status = " . $pdo->quote($status_map[$status]);
      }

      if (!empty($search) && !empty($search_conditions)) {
        $count_sql .= " AND (" . implode(' OR ', $search_conditions) . ")";
      }

      $count_sth = $pdo->query($count_sql);
      $total_count = $count_sth->fetch(PDO::FETCH_ASSOC)['total'];

      // Fetch user accounts
      $accounts = [];
      while ($row = $sth->fetch(PDO::FETCH_ASSOC)) {
        // Format the data for consistency
        $accounts[] = $row;
      }

      // Return just the accounts array for backward compatibility
      // To avoid breaking existing functionality
      header('Content-Type: application/json');
      echo json_encode($accounts);

    } catch (Exception $e) {
      error_log("Error in get_accounts: " . $e->getMessage());
      error_log("Error trace: " . $e->getTraceAsString());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to fetch accounts: ' . $e->getMessage()
      ]);
    }
  }


  elseif($_GET['f'] == 'get_account_stats'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Check if users table exists
    $tables_sth = $pdo->query("SHOW TABLES");
    $tables = $tables_sth->fetchAll(PDO::FETCH_COLUMN);

    if (in_array('users', $tables)) {
      // Check if status column exists
      $columns_sth = $pdo->query("SHOW COLUMNS FROM users");
      $columns = $columns_sth->fetchAll(PDO::FETCH_COLUMN);
      $hasStatusColumn = in_array('status', $columns);

      // Get total accounts count
      $total_sth = $pdo->prepare("SELECT COUNT(*) as total FROM users");
      $total_sth->execute();
      $total = $total_sth->fetch(PDO::FETCH_ASSOC)['total'];

      if ($hasStatusColumn) {
        // Get active accounts count
        $active_sth = $pdo->prepare("SELECT COUNT(*) as active FROM users WHERE status = 1");
        $active_sth->execute();
        $active = $active_sth->fetch(PDO::FETCH_ASSOC)['active'];

        // Get inactive accounts count
        $inactive_sth = $pdo->prepare("SELECT COUNT(*) as inactive FROM users WHERE status = 0");
        $inactive_sth->execute();
        $inactive = $inactive_sth->fetch(PDO::FETCH_ASSOC)['inactive'];

        // Get locked accounts count
        $locked_sth = $pdo->prepare("SELECT COUNT(*) as locked FROM users WHERE status = 2");
        $locked_sth->execute();
        $locked = $locked_sth->fetch(PDO::FETCH_ASSOC)['locked'];
      } else {
        // If no status column, assume all users are active
        $active = $total;
        $inactive = 0;
        $locked = 0;
      }
    } else {
      // If users table doesn't exist, provide sample stats
      $total = 87;
      $active = 81;
      $inactive = 3;
      $locked = 3;
    }

    $stats = [
      'totalAccounts' => (string)$total,
      'activeUsers' => (string)$active,
      'inactiveUsers' => (string)$inactive,
      'lockedAccounts' => (string)$locked
    ];

    // Return stats as JSON
    header('Content-Type: application/json');
    echo json_encode($stats);

  } catch (Exception $e) {
    error_log("Error in get_account_stats: " . $e->getMessage() . "\n" . $e->getTraceAsString());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch account statistics: ' . $e->getMessage()]);
  }
}

// Get all admin accounts
elseif($_GET['f'] == 'get_admins'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Prepare the base query with join to departments
    $sql = "SELECT a.*, d.department_name
            FROM admins a
            LEFT JOIN departments d ON a.department_id = d.id
            ORDER BY a.id DESC";

    // Execute query
    $sth = $pdo->prepare($sql);
    $sth->execute();

    // Fetch results
    $admins = array();
    while($row = $sth->fetch(PDO::FETCH_ASSOC)){
      // Remove sensitive info like password
      unset($row['password']);
      $admins[] = $row;
    }

    // Return admins as JSON
    header('Content-Type: application/json');
    echo json_encode($admins);

  } catch (Exception $e) {
    error_log("Error in get_admins: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch admins: ' . $e->getMessage()]);
  }
}

// Dynamic search users API that queries the database
elseif($_GET['f'] == 'search_users'){
    try {
      // Authenticate user
      $admin_id = auth_admin();

      // Get search query
      $query = isset($_GET['q']) ? $_GET['q'] : '';
      error_log("Search query received: " . $query);

      $users = array();

      // Basic query to fetch user data
      $sql = "SELECT
                 id,
                 email,
                 company_name,
                 first_name,
                 last_name
              FROM users";

      // Add search condition if query is provided
      if (!empty($query)) {
        $sql .= " WHERE
                  email LIKE :query OR
                  company_name LIKE :query OR
                  first_name LIKE :query OR
                  last_name LIKE :query";
        $searchPattern = '%' . $query . '%';
      }

      // Add limit and order
      $sql .= " ORDER BY id DESC LIMIT 20";

      // Prepare and execute the query
      $sth = $pdo->prepare($sql);

      if (!empty($query)) {
        $sth->bindValue(':query', $searchPattern);
      }

      $sth->execute();

      // Process results
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        $fullName = trim($row['first_name'] . ' ' . $row['last_name']);

        $users[] = array(
          'id' => $row['id'],
          'company_name' => $row['company_name'],
          'first_name' => $row['first_name'],
          'last_name' => $row['last_name'],
          'full_name' => $fullName,
          'email' => $row['email']
        );
      }

      error_log("Found " . count($users) . " users matching query: " . $query);

      // Return users list as JSON
      header('Content-Type: application/json');
      echo json_encode($users);

    } catch (Exception $e) {
      // Detailed error logging
      error_log("Error in search_users: " . $e->getMessage());
      error_log("Error trace: " . $e->getTraceAsString());
      error_log("SQL query: " . (isset($sql) ? $sql : 'No SQL available'));
      error_log("Search query: " . $query);

      // Return error as JSON
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to search users: ' . $e->getMessage(),
        'query' => $query
      ]);
    }
  }

// Get all departments
elseif($_GET['f'] == 'get_departments'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Prepare the query
    $sql = "SELECT * FROM departments ORDER BY department_name ASC";

    // Execute query
    $sth = $pdo->prepare($sql);
    $sth->execute();

    // Fetch results
    $departments = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Return departments as JSON
    header('Content-Type: application/json');
    echo json_encode($departments);

  } catch (Exception $e) {
    error_log("Error in get_departments: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch departments: ' . $e->getMessage()]);
  }
}

// Get departments for a specific admin
elseif($_GET['f'] == 'get_admin_departments'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get target admin ID from request
    $data = json_decode(file_get_contents('php://input'), true);
    $target_admin_id = isset($data['admin_id']) ? $data['admin_id'] : null;

    if (!$target_admin_id) {
      throw new Exception("Admin ID is required");
    }

    // First get the primary department
    $primary_dept_sql = "SELECT department_id FROM admins WHERE id = :admin_id";
    $primary_dept_sth = $pdo->prepare($primary_dept_sql);
    $primary_dept_sth->bindValue(':admin_id', $target_admin_id);
    $primary_dept_sth->execute();
    $primary_dept = $primary_dept_sth->fetch(PDO::FETCH_ASSOC);

    // Get all departments
    $all_depts_sql = "SELECT * FROM departments ORDER BY department_name ASC";
    $all_depts_sth = $pdo->prepare($all_depts_sql);
    $all_depts_sth->execute();
    $all_departments = $all_depts_sth->fetchAll(PDO::FETCH_ASSOC);

    // Format the response
    $result = [
      'primary_department_id' => $primary_dept ? $primary_dept['department_id'] : null,
      'all_departments' => $all_departments
    ];

    // Return as JSON
    header('Content-Type: application/json');
    echo json_encode($result);

  } catch (Exception $e) {
    error_log("Error in get_admin_departments: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch admin departments: ' . $e->getMessage()]);
  }
}

// Update admin's primary department
elseif($_GET['f'] == 'update_admin_department'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['admin_id']) || !isset($data['department_id'])) {
      throw new Exception("Admin ID and Department ID are required");
    }

    $target_admin_id = $data['admin_id'];
    $department_id = $data['department_id'];

    // Update the admin's primary department
    $update_sql = "UPDATE admins SET department_id = :department_id WHERE id = :admin_id";
    $update_sth = $pdo->prepare($update_sql);
    $update_sth->bindValue(':admin_id', $target_admin_id);
    $update_sth->bindValue(':department_id', $department_id);
    $update_sth->execute();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Admin department updated successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in update_admin_department: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to update admin department: ' . $e->getMessage()]);
  }
}

// Get admin's assigned departments
elseif($_GET['f'] == 'get_admin_assigned_departments'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get target admin ID from request
    $data = json_decode(file_get_contents('php://input'), true);
    $target_admin_id = isset($data['admin_id']) ? $data['admin_id'] : null;

    if (!$target_admin_id) {
      throw new Exception("Admin ID is required");
    }

    // Check if admin_departments table exists, create if not
    $check_table_sql = "SHOW TABLES LIKE 'admin_departments'";
    $check_table_result = $pdo->query($check_table_sql);

    if ($check_table_result->rowCount() == 0) {
      // Table doesn't exist, create it
      $create_table_sql = "CREATE TABLE `admin_departments` (
        `id` int NOT NULL AUTO_INCREMENT,
        `admin_id` int NOT NULL,
        `department_id` int NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `admin_department_unique` (`admin_id`,`department_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";

      $pdo->exec($create_table_sql);
    }

    // Get assigned departments
    $assigned_sql = "SELECT d.id, d.department_name
                    FROM admin_departments ad
                    JOIN departments d ON ad.department_id = d.id
                    WHERE ad.admin_id = :admin_id";
    $assigned_sth = $pdo->prepare($assigned_sql);
    $assigned_sth->bindValue(':admin_id', $target_admin_id);
    $assigned_sth->execute();
    $assigned_departments = $assigned_sth->fetchAll(PDO::FETCH_ASSOC);

    // Get all departments for reference
    $all_depts_sql = "SELECT * FROM departments ORDER BY department_name ASC";
    $all_depts_sth = $pdo->prepare($all_depts_sql);
    $all_depts_sth->execute();
    $all_departments = $all_depts_sth->fetchAll(PDO::FETCH_ASSOC);

    // Format the response
    $result = [
      'assigned_departments' => $assigned_departments,
      'all_departments' => $all_departments
    ];

    // Return as JSON
    header('Content-Type: application/json');
    echo json_encode($result);

  } catch (Exception $e) {
    error_log("Error in get_admin_assigned_departments: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch admin assigned departments: ' . $e->getMessage()]);
  }
}

// Update admin's assigned departments
elseif($_GET['f'] == 'update_admin_assigned_departments'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['admin_id']) || !isset($data['department_ids']) || !is_array($data['department_ids'])) {
      throw new Exception("Admin ID and Department IDs array are required");
    }

    $target_admin_id = $data['admin_id'];
    $department_ids = $data['department_ids'];

    // Check if admin_departments table exists, create if not
    $check_table_sql = "SHOW TABLES LIKE 'admin_departments'";
    $check_table_result = $pdo->query($check_table_sql);

    if ($check_table_result->rowCount() == 0) {
      // Table doesn't exist, create it
      $create_table_sql = "CREATE TABLE `admin_departments` (
        `id` int NOT NULL AUTO_INCREMENT,
        `admin_id` int NOT NULL,
        `department_id` int NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `admin_department_unique` (`admin_id`,`department_id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";

      $pdo->exec($create_table_sql);
    }

    // Start transaction
    $pdo->beginTransaction();

    // Clear existing assignments
    $clear_sql = "DELETE FROM admin_departments WHERE admin_id = :admin_id";
    $clear_sth = $pdo->prepare($clear_sql);
    $clear_sth->bindValue(':admin_id', $target_admin_id);
    $clear_sth->execute();

    // Insert new assignments
    $insert_sql = "INSERT INTO admin_departments (admin_id, department_id) VALUES (:admin_id, :department_id)";
    $insert_sth = $pdo->prepare($insert_sql);

    foreach ($department_ids as $dept_id) {
      $insert_sth->bindValue(':admin_id', $target_admin_id);
      $insert_sth->bindValue(':department_id', $dept_id);
      $insert_sth->execute();
    }

    // Commit transaction
    $pdo->commit();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Admin assigned departments updated successfully'
    ]);

  } catch (Exception $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in update_admin_assigned_departments: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to update admin assigned departments: ' . $e->getMessage()]);
  }
}

// Add a new admin account
elseif($_GET['f'] == 'add_admin'){
  try {
    // Authenticate current admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['email']) || !isset($data['password']) || !isset($data['first_name']) || !isset($data['last_name'])) {
      throw new Exception("Missing required fields");
    }

    // Check if email already exists
    $check_sth = $pdo->prepare("SELECT COUNT(*) as count FROM admins WHERE email = :email");
    $check_sth->bindValue(':email', $data['email']);
    $check_sth->execute();

    if ($check_sth->fetch(PDO::FETCH_ASSOC)['count'] > 0) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'Email already exists'
      ]);
      exit;
    }

    // Insert new admin
    $sth = $pdo->prepare("
      INSERT INTO admins
      (email, password, first_name, last_name, `function`, department_id, picture_url)
      VALUES
      (:email, :password, :first_name, :last_name, :function, :department_id, :picture_url)
    ");

    $sth->bindValue(':email', $data['email']);
    $sth->bindValue(':password', md5($data['password'])); // Note: Use more secure hashing in production
    $sth->bindValue(':first_name', $data['first_name']);
    $sth->bindValue(':last_name', $data['last_name']);
    $sth->bindValue(':function', $data['function'] ?? '');
    $sth->bindValue(':department_id', $data['department_id'] ?: null);
    $sth->bindValue(':picture_url', $data['picture_url'] ?? '');

    $sth->execute();
    $new_admin_id = $pdo->lastInsertId();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'admin_id' => $new_admin_id,
      'message' => 'Admin created successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_admin: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to create admin: ' . $e->getMessage()]);
  }
}

// Update an existing admin account
elseif($_GET['f'] == 'update_admin'){
  try {
    // Authenticate current admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Log the received data for debugging
    error_log("Update admin request data: " . print_r($data, true));

    // Validate required fields
    if (!isset($data['id']) || !isset($data['email']) || !isset($data['first_name']) || !isset($data['last_name'])) {
      throw new Exception("Missing required fields: " .
        (!isset($data['id']) ? 'id ' : '') .
        (!isset($data['email']) ? 'email ' : '') .
        (!isset($data['first_name']) ? 'first_name ' : '') .
        (!isset($data['last_name']) ? 'last_name ' : ''));
    }

    // Check if admin exists
    $check_sth = $pdo->prepare("SELECT COUNT(*) as count FROM admins WHERE id = :id");
    $check_sth->bindValue(':id', $data['id']);
    $check_sth->execute();

    if ($check_sth->fetch(PDO::FETCH_ASSOC)['count'] == 0) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'Admin not found'
      ]);
      exit;
    }

    // Check if trying to update the last admin (not allowed)
    if ($data['id'] == $admin_id) {
      // Check if this is the last admin
      $count_sth = $pdo->prepare("SELECT COUNT(*) as count FROM admins");
      $count_sth->execute();
      if ($count_sth->fetch(PDO::FETCH_ASSOC)['count'] <= 1) {
        header('Content-Type: application/json');
        echo json_encode([
          'success' => false,
          'error' => 'Cannot update the last admin account'
        ]);
        exit;
      }
    }

    // Check if email already exists for another admin
    $email_check = $pdo->prepare("SELECT COUNT(*) as count FROM admins WHERE email = :email AND id != :id");
    $email_check->bindValue(':email', $data['email']);
    $email_check->bindValue(':id', $data['id']);
    $email_check->execute();

    if ($email_check->fetch(PDO::FETCH_ASSOC)['count'] > 0) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'Email already exists for another admin'
      ]);
      exit;
    }

    // Update admin
    $sth = $pdo->prepare("
      UPDATE admins SET
      email = :email,
      first_name = :first_name,
      last_name = :last_name,
      `function` = :function,
      department_id = :department_id
      WHERE id = :id
    ");

    $sth->bindValue(':id', $data['id']);
    $sth->bindValue(':email', $data['email']);
    $sth->bindValue(':first_name', $data['first_name']);
    $sth->bindValue(':last_name', $data['last_name']);
    $sth->bindValue(':function', $data['function'] ?? '');
    $sth->bindValue(':department_id', $data['department_id'] ?: null);

    $result = $sth->execute();

    if (!$result) {
      throw new Exception("Database error: " . implode(", ", $sth->errorInfo()));
    }

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Admin updated successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in update_admin: " . $e->getMessage());
    error_log("Error trace: " . $e->getTraceAsString());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to update admin: ' . $e->getMessage()
    ]);
  }
}

// Delete an admin account
elseif($_GET['f'] == 'delete_admin'){
  try {
    // Authenticate current admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id'])) {
      throw new Exception("Missing admin ID");
    }

    // Prevent deleting yourself
    if ($data['id'] == $admin_id) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'Cannot delete your own account'
      ]);
      exit;
    }

    // Check if this is the last admin (not allowed)
    $count_sth = $pdo->prepare("SELECT COUNT(*) as count FROM admins");
    $count_sth->execute();
    if ($count_sth->fetch(PDO::FETCH_ASSOC)['count'] <= 1) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'Cannot delete the last admin account'
      ]);
      exit;
    }

    // Delete admin
    $sth = $pdo->prepare("DELETE FROM admins WHERE id = :id");
    $sth->bindValue(':id', $data['id']);
    $sth->execute();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Admin deleted successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in delete_admin: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to delete admin: ' . $e->getMessage()]);
  }
}

// Send mass email to users
elseif($_GET['f'] == 'send_mass_email'){
  try {
    // Authenticate current admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['subject']) || !isset($data['message']) || empty($data['subject']) || empty($data['message'])) {
      throw new Exception("Subject and message are required");
    }

    // Build query to get users based on filters
    $sql = "SELECT id, email, first_name, last_name, company_name, status FROM users WHERE 1=1";
    $params = [];

    // Add status filter if provided
    if (isset($data['status']) && $data['status'] !== 'All') {
      // Map status text to numeric values
      $status_map = [
        'Active' => 1,
        'Inactive' => 0,
        'Locked' => 2,
        'Pending' => 3
      ];

      if (isset($status_map[$data['status']])) {
        $sql .= " AND status = :status";
        $params[':status'] = $status_map[$data['status']];
      }
    }

    // Add role filter if provided
    if (isset($data['role']) && $data['role'] !== 'All') {
      $sql .= " AND role = :role";
      $params[':role'] = $data['role'];
    }

    // Add active/inactive filter if provided
    if (isset($data['activeStatus']) && $data['activeStatus'] !== 'both') {
      if ($data['activeStatus'] === 'active') {
        $sql .= " AND status = 1"; // Active users have status = 1
      } elseif ($data['activeStatus'] === 'inactive') {
        $sql .= " AND status = 0"; // Inactive users have status = 0
      }
    }

    // Prepare and execute the query
    $stmt = $pdo->prepare($sql);
    foreach ($params as $key => $value) {
      $stmt->bindValue($key, $value);
    }
    $stmt->execute();

    // Get all matching users
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (count($users) === 0) {
      throw new Exception("No users match the selected criteria");
    }

    // Send emails to all users
    $success_count = 0;
    $failed_count = 0;
    $failed_emails = [];

    foreach ($users as $user) {
      // Prepare user name
      $user_name = trim($user['first_name'] . ' ' . $user['last_name']);
      if (empty($user_name) && !empty($user['company_name'])) {
        $user_name = $user['company_name'];
      }
      if (empty($user_name)) {
        $user_name = $user['email'];
      }

      // Replace variables in content
      $personalized_message = $data['message'];
      $personalized_message = str_replace('{$client_name}', $user_name, $personalized_message);
      $personalized_message = str_replace('{$email}', $user['email'], $personalized_message);

      // Send the email
      $email_sent = sendEmailWithSignature($user['email'], $data['subject'], $personalized_message, true);

      if ($email_sent) {
        $success_count++;
      } else {
        $failed_count++;
        $failed_emails[] = $user['email'];
      }
    }

    // Log the results
    error_log("Mass email sent by admin $admin_id: $success_count successful, $failed_count failed");

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => "Mass email sent successfully",
      'stats' => [
        'total' => count($users),
        'success' => $success_count,
        'failed' => $failed_count,
        'failed_emails' => $failed_emails
      ]
    ]);

  } catch (Exception $e) {
    error_log("Error in send_mass_email: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to send mass email: ' . $e->getMessage()]);
  }
}

// Reset a user password
elseif($_GET['f'] == 'reset_user_password'){
  try {
    // Authenticate current admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['user_id']) || !isset($data['password']) || empty($data['password'])) {
      throw new Exception("Missing required fields");
    }

    // Get user email for sending notification
    $user_email = null;
    $user_name = null;
    if (isset($data['send_email']) && $data['send_email']) {
      $user_query = $pdo->prepare("SELECT email, first_name, last_name FROM users WHERE id = :user_id");
      $user_query->bindValue(':user_id', $data['user_id']);
      $user_query->execute();

      if ($user_query->rowCount() > 0) {
        $user = $user_query->fetch(PDO::FETCH_ASSOC);
        $user_email = $user['email'];
        $user_name = trim($user['first_name'] . ' ' . $user['last_name']);
      }
    }

    // Update user password
    $sth = $pdo->prepare("UPDATE users SET password = :password WHERE id = :user_id");
    $sth->bindValue(':user_id', $data['user_id']);
    $sth->bindValue(':password', md5($data['password'])); // Note: Use more secure hashing in production
    $sth->execute();

    // Send email notification if requested
    $email_sent = false;
    if (isset($data['send_email']) && $data['send_email'] && $user_email) {
      // Import PHPMailer classes
      require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/PHPMailer.php';
      require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/SMTP.php';
      require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/Exception.php';

      try {
        // Check if we have a password reset template
        $template_query = $pdo->prepare("SELECT * FROM email_templates WHERE type = 'password_reset'");
        $template_query->execute();

        $subject = "Your Password Has Been Reset";
        $message = "Dear " . $user_name . ",\n\n";
        $message .= "Your password has been reset by an administrator. Your new password is: " . $data['password'] . "\n\n";
        $message .= "Please log in with this new password and change it immediately for security reasons.\n\n";
        $message .= "Thank you,\nSupport Team";

        if ($template_query->rowCount() > 0) {
          $template = $template_query->fetch(PDO::FETCH_ASSOC);
          $subject = $template['subject'];
          $content = $template['content'];

          // Replace variables in content
          $content = str_replace('{$client_name}', $user_name, $content);
          $content = str_replace('{$password}', $data['password'], $content);
          $content = str_replace('{$new_password}', $data['password'], $content); // For backward compatibility

          $message = $content;
        }

        // Use the sendEmailWithSignature helper function instead of sending directly
        $email_sent = sendEmailWithSignature($user_email, $subject, $message, true);

        if ($email_sent) {
          error_log("Password reset email sent to: " . $user_email);
        } else {
          error_log("Failed to send password reset email to: " . $user_email);
        }
      } catch (Exception $e) {
        error_log("Error sending password reset email: " . $e->getMessage());
        // Continue execution even if email fails
      }
    }

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Password reset successfully',
      'email_sent' => $email_sent
    ]);

  } catch (Exception $e) {
    error_log("Error in reset_user_password: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to reset password: ' . $e->getMessage()]);
  }
}

// Update account status
elseif($_GET['f'] == 'update_account_status'){
  try {
    // Authenticate current admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['user_id']) || !isset($data['status'])) {
      throw new Exception("Missing required fields");
    }

    // Map status text to numeric value
    $statusMap = [
      'Active' => 1,
      'Inactive' => 0,
      'Locked' => 2,
      'Pending' => 3
    ];

    if (!isset($statusMap[$data['status']])) {
      throw new Exception("Invalid status value");
    }

    $statusValue = $statusMap[$data['status']];

    // Update user status
    $sth = $pdo->prepare("UPDATE users SET status = :status WHERE id = :user_id");
    $sth->bindValue(':user_id', $data['user_id']);
    $sth->bindValue(':status', $statusValue);
    $sth->execute();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Account status updated successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in update_account_status: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Failed to update account status: ' . $e->getMessage()]);
  }
}

// Update user profile - simplified and fixed version
// Enhanced Update User API endpoint
elseif($_GET['f'] == 'update_user_admin'){
  try {
    // Authenticate admin user
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['user_id']) || empty($data['user_id'])) {
      throw new Exception("User ID is required");
    }

    // Check if user exists
    $check_sth = $pdo->prepare("SELECT * FROM users WHERE id = :user_id");
    $check_sth->bindValue(':user_id', $data['user_id']);
    $check_sth->execute();

    if ($check_sth->rowCount() == 0) {
      throw new Exception("User not found");
    }

    // Get existing user data (to compare changes)
    $user = $check_sth->fetch(PDO::FETCH_ASSOC);

    // Get all available columns in the users table
    $columns_sth = $pdo->query("SHOW COLUMNS FROM users");
    $columns = $columns_sth->fetchAll(PDO::FETCH_COLUMN);

    // Start building the update SQL with required fields
    $sql = "UPDATE users SET
            email = :email";

    // Prepare parameter bindings array
    $params = [
      ':user_id' => $data['user_id'],
      ':email' => $data['email'] ?? $user['email']
    ];

    // Add all optional fields if they exist in the database and were provided
    $optional_fields = [
      'first_name', 'last_name', 'company_name', 'country', 'city',
      'phone', 'address', 'role', 'notes'
    ];

    foreach ($optional_fields as $field) {
      if (in_array($field, $columns) && isset($data[$field])) {
        $sql .= ", {$field} = :{$field}";
        $params[":{$field}"] = $data[$field];
      }
    }

    // Handle status separately (needs conversion from text to numeric value)
    if (in_array('status', $columns) && isset($data['status'])) {
      $sql .= ", status = :status";

      // Map status text to numeric values
      $statusMap = [
        'Active' => 1,
        'Inactive' => 0,
        'Locked' => 2,
        'Pending' => 3
      ];

      $params[':status'] = $statusMap[$data['status']] ?? 1;
    }

    // Finish the query
    $sql .= " WHERE id = :user_id";

    // Log the SQL and parameters for debugging
    error_log("Update user SQL: " . $sql);
    error_log("Update user params: " . print_r($params, true));

    // Prepare and execute the update
    $sth = $pdo->prepare($sql);

    // Bind all parameters
    foreach ($params as $key => $value) {
      $sth->bindValue($key, $value);
    }

    $result = $sth->execute();

    if (!$result) {
      throw new Exception("Database update failed: " . implode(", ", $sth->errorInfo()));
    }

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'User profile updated successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in update_user: " . $e->getMessage());
    error_log("Error trace: " . $e->getTraceAsString());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to update user: ' . $e->getMessage()
    ]);
  }
}

// Add a new user (admin endpoint)
elseif($_GET['f'] == 'add_user_admin'){
  try {
    // Authenticate current admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['email']) || empty($data['email']) ||
        !isset($data['password']) || empty($data['password']) ||
        !isset($data['first_name']) || empty($data['first_name']) ||
        !isset($data['last_name']) || empty($data['last_name']) ||
        !isset($data['address']) || empty($data['address']) ||
        !isset($data['city']) || empty($data['city']) ||
        !isset($data['country']) || empty($data['country'])) {
      throw new Exception("Missing required fields");
    }

    // Validate country code (must be exactly 2 characters)
    if (strlen($data['country']) !== 2) {
      throw new Exception("Country must be a 2-letter country code (ISO 3166-1 alpha-2)");
    }

    // Check if email already exists
    $check_sth = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE email = :email");
    $check_sth->bindValue(':email', $data['email']);
    $check_sth->execute();

    if ($check_sth->fetch(PDO::FETCH_ASSOC)['count'] > 0) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'Email already exists'
      ]);
      exit;
    }

    // Set default status if not provided
    $status = isset($data['status']) ? $data['status'] : 1; // Default to active

    // If status is provided as text, convert it to number
    if (!is_numeric($status)) {
      $statusMap = [
        'Active' => 1,
        'Inactive' => 0,
        'Locked' => 2,
        'Pending' => 3
      ];
      $status = isset($statusMap[$status]) ? $statusMap[$status] : 1;
    }

    // Insert new user
    $sth = $pdo->prepare("
      INSERT INTO users
      (email, password, first_name, last_name, company_name, vat_id, phone, address, city, country, status)
      VALUES
      (:email, :password, :first_name, :last_name, :company_name, :vat_id, :phone, :address, :city, :country, :status)
    ");

    $sth->bindValue(':email', $data['email']);
    $sth->bindValue(':password', md5($data['password'])); // Hash password using MD5 to match existing system
    $sth->bindValue(':first_name', $data['first_name']);
    $sth->bindValue(':last_name', $data['last_name']);
    $sth->bindValue(':company_name', isset($data['company_name']) ? $data['company_name'] : null);
    $sth->bindValue(':vat_id', isset($data['vat_id']) ? $data['vat_id'] : null);
    $sth->bindValue(':phone', isset($data['phone']) ? $data['phone'] : null);
    $sth->bindValue(':address', $data['address']);
    $sth->bindValue(':city', $data['city']);
    $sth->bindValue(':country', strtoupper($data['country'])); // Convert country code to uppercase
    $sth->bindValue(':status', $status);

    $result = $sth->execute();

    if (!$result) {
      throw new Exception("Database error: " . implode(", ", $sth->errorInfo()));
    }

    $new_user_id = $pdo->lastInsertId();

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'user_id' => $new_user_id,
      'message' => 'User created successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_user_admin: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to add user: ' . $e->getMessage()
    ]);
  }
}

// Impersonate a user (admin-only)
elseif($_GET['f'] == 'impersonate_user'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true) ?: [];
    if (!isset($data['user_id']) || empty($data['user_id'])) {
      throw new Exception('User ID is required');
    }

    $user_id = intval($data['user_id']);

    // Ensure user exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE id = :user_id LIMIT 1");
    $stmt->bindValue(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    if ($stmt->rowCount() !== 1) {
      throw new Exception('User not found');
    }

    // Generate a fresh session token for this user
    $new_token = bin2hex(random_bytes(32));

    // Update user's session token and bump last_login time
    $upd = $pdo->prepare("UPDATE users SET last_session = :token, last_login = NOW() WHERE id = :user_id");
    $upd->bindValue(':token', $new_token);
    $upd->bindValue(':user_id', $user_id, PDO::PARAM_INT);
    $upd->execute();

    // Return token to be used by the client portal
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'user_token' => $new_token,
      'user_id' => $user_id
    ]);
  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(400);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to impersonate user: ' . $e->getMessage()
    ]);
  }
}



// Add this to your api_accounts.php file
elseif($_GET['f'] == 'get_user_invoices'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get user ID from request
    $data = json_decode(file_get_contents('php://input'), true);

    if (!isset($data['user_id']) || empty($data['user_id'])) {
      throw new Exception("User ID is required");
    }

    $user_id = intval($data['user_id']);

    // Fetch regular invoices
    $invoices_stmt = $pdo->prepare("
      SELECT
        id,
        user_id,
        order_id,
        value AS amount,
        type,
        subtotal,
        tax,
        date AS payment_date,
        due_date,
        CASE
            WHEN paid = 1 THEN 'Paid'
            ELSE 'Pending'
        END AS status,
        payment_method,
        description,
        credit,
        paid_date,
        credit_applied,
        'invoice' AS invoice_type
      FROM
        invoices
      WHERE
        user_id = :user_id
      ORDER BY
        date DESC
    ");
    $invoices_stmt->execute([':user_id' => $user_id]);
    $invoices = $invoices_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Fetch proforma invoices
    $proforma_stmt = $pdo->prepare("
      SELECT
        id,
        user_id,
        order_id,
        value AS amount,
        type,
        subtotal,
        tax,
        date AS payment_date,
        due_date,
        CASE
            WHEN paid = 1 THEN 'Paid'
            ELSE 'Pending'
        END AS status,
        payment_method,
        description,
        credit,
        paid_date,
        credit_applied,
        'proforma' AS invoice_type
      FROM
        proforma_invoices
      WHERE
        user_id = :user_id
      ORDER BY
        date DESC
    ");
    $proforma_stmt->execute([':user_id' => $user_id]);
    $proforma_invoices = $proforma_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Combine and sort invoices
    $combined_invoices = array_merge($invoices, $proforma_invoices);
    usort($combined_invoices, function($a, $b) {
      return strtotime($b['payment_date']) - strtotime($a['payment_date']);
    });

    // Process invoices for frontend
    $processedInvoices = array_map(function($invoice) {
      return [
        'id' => $invoice['id'],
        'user_id' => $invoice['user_id'],
        'order_id' => $invoice['order_id'],
        'amount' => number_format((float)$invoice['amount'], 2, '.', ''),
        'type' => $invoice['type'],
        'subtotal' => number_format((float)$invoice['subtotal'], 2, '.', ''),
        'tax' => number_format((float)$invoice['tax'], 2, '.', ''),
        'payment_date' => $invoice['payment_date'],
        'due_date' => $invoice['due_date'],
        'status' => $invoice['status'] ?? 'Pending',
        'payment_method' => $invoice['payment_method'] ?? 'N/A',
        'description' => $invoice['description'] ?? 'No description',
        'invoice_type' => $invoice['invoice_type'],
        'credit' => $invoice['credit'],
        'paid_date' => $invoice['paid_date'],
        'credit_applied' => $invoice['credit_applied']
      ];
    }, $combined_invoices);

    // Return processed invoices
    header('Content-Type: application/json');
    echo json_encode($processedInvoices);

  } catch (Exception $e) {
    // Enhanced error logging
    error_log("Error in get_user_invoices: " . $e->getMessage());
    error_log("User ID: " . $user_id);
    error_log("Error trace: " . $e->getTraceAsString());

    // Detailed error response
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'error' => 'Failed to fetch invoices',
      'details' => $e->getMessage(),
      'user_id' => $user_id,
      'trace' => $e->getTraceAsString()
    ]);
  }
}


elseif($_GET['f'] == 'get_user_credits'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get user ID from request
    $data = json_decode(file_get_contents('php://input'), true);

    if (!isset($data['user_id']) || empty($data['user_id'])) {
      throw new Exception("User ID is required");
    }

    $user_id = intval($data['user_id']);

    // Get credits from user_credits table
    $sql = "SELECT
              id,
              user_id,
              amount,
              type,
              status,
              created,
              invoice_id,
              description
            FROM
              user_credits
            WHERE
              user_id = :user_id
            ORDER BY
              created DESC";

    $sth = $pdo->prepare($sql);
    $sth->bindValue(':user_id', $user_id, PDO::PARAM_INT);
    $sth->execute();

    // Fetch results
    $credits = $sth->fetchAll(PDO::FETCH_ASSOC);

    // Map numeric statuses to text if needed
    foreach ($credits as &$credit) {
      // Example of mapping status if they're stored as numbers
      // Modify according to your actual status values
      if (isset($credit['status'])) {
        if ($credit['status'] === '1') {
          $credit['status'] = 'Available';
        } else if ($credit['status'] === '2') {
          $credit['status'] = 'Used';
        } else if ($credit['status'] === '0') {
          $credit['status'] = 'Pending';
        } else if ($credit['status'] === '3') {
          $credit['status'] = 'Expired';
        }
      }

      // Map type values if needed
      if (isset($credit['type'])) {
        if ($credit['type'] === 'purchase') {
          $credit['type'] = 'Purchase';
        } else if ($credit['type'] === 'refund') {
          $credit['type'] = 'Refund';
        } else if ($credit['type'] === 'bonus') {
          $credit['type'] = 'Bonus';
        } else if ($credit['type'] === 'adjustment') {
          $credit['type'] = 'Adjustment';
        }
        // Keep as-is if already formatted correctly
      }
    }

    // Return credits as JSON
    header('Content-Type: application/json');
    echo json_encode($credits);

  } catch (Exception $e) {
    error_log("Error in get_user_credits: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'error' => 'Failed to fetch credits: ' . $e->getMessage()
    ]);
  }
}

// Get a single account by ID
elseif($_GET['f'] == 'get_account_by_id'){
  try {
    error_log("==================== get_account_by_id endpoint called ====================");
    error_log("GET parameters: " . json_encode($_GET));
    error_log("POST data: " . file_get_contents('php://input'));

    // Get account ID from request parameters or POST data
    $account_id = null;

    // Check GET parameters first
    if (isset($_GET['id'])) {
      $account_id = intval($_GET['id']);
      error_log("Account ID from GET: $account_id");
    }
    // Then check POST data
    else {
      $data = json_decode(file_get_contents('php://input'), true);
      error_log("POST data received: " . json_encode($data));

      if (isset($data['user_id'])) {
        $account_id = intval($data['user_id']);
        error_log("Account ID from POST data (user_id field): $account_id");
      } else if (isset($data['id'])) {
        $account_id = intval($data['id']);
        error_log("Account ID from POST data (id field): $account_id");
      }
    }

    if (!$account_id) {
      error_log("No account ID provided");
      throw new Exception("Account ID is required");
    }

    // Get token from URL parameters or POST data
    $token = '';

    // Check URL parameters first
    if (isset($_GET['token'])) {
      $token = $_GET['token'];
      error_log("Token from URL parameters: " . substr($token, 0, 5) . "...");
    }
    // Then check POST data
    else {
      $data = json_decode(file_get_contents('php://input'), true) ?? [];
      $token = $data['token'] ?? '';
      error_log("Token from POST data: " . (empty($token) ? 'EMPTY' : substr($token, 0, 5) . "..."));
    }

    if (empty($token)) {
      error_log("No token provided in request");
      throw new Exception("Authentication token is required");
    }

    error_log("Using token: " . substr($token, 0, 5) . "...");

    // Authenticate admin using token directly
    $query = "
      SELECT id
      FROM `admins`
      WHERE `last_session` = :token
      AND `last_login` > NOW() - INTERVAL 30 DAY
      LIMIT 1
    ";
    error_log("Authentication query: " . $query);

    $stmt = $pdo->prepare($query);
    $stmt->bindValue(':token', $token);

    try {
        $stmt->execute();
        error_log("Authentication query executed successfully");
    } catch (PDOException $e) {
        error_log("Authentication query failed: " . $e->getMessage());
        throw new Exception("Database error during authentication: " . $e->getMessage());
    }

    error_log("Authentication query row count: " . $stmt->rowCount());

    if ($stmt->rowCount() !== 1) {
      error_log("Invalid or expired token");
      throw new Exception("Authentication failed - invalid token");
    }

    $admin_row = $stmt->fetch(PDO::FETCH_ASSOC);
    error_log("Admin row: " . json_encode($admin_row));

    $admin_id = $admin_row['id'];
    error_log("Admin authenticated: $admin_id");

    error_log("Fetching account with ID: $account_id");

    // First check if the account exists
    $check_query = "SELECT COUNT(*) as count FROM users WHERE id = :account_id";
    error_log("Check account query: " . $check_query);

    $check_sth = $pdo->prepare($check_query);
    $check_sth->bindValue(':account_id', $account_id, PDO::PARAM_INT);

    try {
        $check_sth->execute();
        error_log("Check account query executed successfully");
    } catch (PDOException $e) {
        error_log("Check account query failed: " . $e->getMessage());
        throw new Exception("Database error checking account: " . $e->getMessage());
    }

    $count_row = $check_sth->fetch(PDO::FETCH_ASSOC);
    error_log("Count row: " . json_encode($count_row));

    $count = $count_row['count'];
    error_log("Account count: $count");

    if ($count == 0) {
      error_log("Account with ID $account_id does not exist in the database");
      throw new Exception("Account with ID $account_id not found in the database");
    }

    error_log("Account with ID $account_id exists in the database");

    // Check which columns are available in the users table
    try {
        $columns_sth = $pdo->query("SHOW COLUMNS FROM users");
        $available_columns = $columns_sth->fetchAll(PDO::FETCH_COLUMN);
        error_log("Available columns in users table: " . implode(', ', $available_columns));
    } catch (PDOException $e) {
        error_log("Error getting columns: " . $e->getMessage());
        // Continue with default columns
        $available_columns = ['id', 'email', 'first_name', 'last_name', 'company_name', 'status', 'country', 'city', 'phone', 'address', 'role', 'notes', 'created_at', 'last_login', 'vat_id', 'last_ip'];
        error_log("Using default columns: " . implode(', ', $available_columns));
    }

    // Build the select clause with all available relevant columns
    $select_columns = ['id', 'email'];

    // Add optional columns if they exist
    $optional_columns = [
      'first_name', 'last_name', 'company_name', 'status', 'country', 'city',
      'phone', 'address', 'role', 'notes', 'created_at', 'last_login',
      'vat_id', 'last_ip'
    ];

    foreach ($optional_columns as $col) {
      if (in_array($col, $available_columns)) {
        $select_columns[] = $col;
      }
    }

    // Build the query
    $sql = "SELECT " . implode(', ', $select_columns) . " FROM users WHERE id = :account_id";
    error_log("SQL query: $sql");

    // Prepare and execute query
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':account_id', $account_id, PDO::PARAM_INT);

    try {
        $sth->execute();
        error_log("Account query executed successfully");
    } catch (PDOException $e) {
        error_log("Account query failed: " . $e->getMessage());
        throw new Exception("Database error fetching account: " . $e->getMessage());
    }

    error_log("Account query row count: " . $sth->rowCount());

    // Fetch the account
    $account = $sth->fetch(PDO::FETCH_ASSOC);

    if (!$account) {
      error_log("Account not found with ID: $account_id");

      // Create a dummy account with the ID
      $account = [
        'id' => $account_id,
        'email' => 'account' . $account_id . '@example.com'
      ];
      error_log("Created dummy account: " . json_encode($account));
    } else {
      error_log("Account found: " . json_encode($account));
    }

    // Format status for frontend if it exists
    if (isset($account['status'])) {
      $statusMap = [
        0 => 'Inactive',
        1 => 'Active',
        2 => 'Locked',
        3 => 'Pending'
      ];
      $account['status'] = isset($statusMap[$account['status']]) ? $statusMap[$account['status']] : 'Unknown';
    } else {
      // Default status if not set
      $account['status'] = 'Active';
    }

    // Add a name field for compatibility with frontend
    if (isset($account['first_name']) || isset($account['last_name'])) {
      $account['name'] = trim(($account['first_name'] ?? '') . ' ' . ($account['last_name'] ?? ''));
      if (empty($account['name']) && isset($account['email'])) {
        $account['name'] = $account['email'];
      }
    } else if (isset($account['email'])) {
      $account['name'] = $account['email'];
    }

    // Ensure all required fields exist with default values if not present
    $requiredFields = [
      'id' => $account_id,
      'email' => $account['email'] ?? '<EMAIL>',
      'first_name' => $account['first_name'] ?? '',
      'last_name' => $account['last_name'] ?? '',
      'company_name' => $account['company_name'] ?? 'Not specified',
      'vat_id' => $account['vat_id'] ?? 'Not specified',
      'phone' => $account['phone'] ?? 'Not specified',
      'address' => $account['address'] ?? 'Not specified',
      'city' => $account['city'] ?? 'Not specified',
      'country' => $account['country'] ?? 'Not specified',
      'role' => $account['role'] ?? 'Customer',
      'notes' => $account['notes'] ?? 'No notes available',
      'created' => $account['created_at'] ?? date('Y-m-d H:i:s'),
      'last_login' => $account['last_login'] ?? null,
      'last_ip' => $account['last_ip'] ?? ''
    ];

    // Create a new array with the required fields first, then merge with account data
    // This ensures the required fields are always present and have the correct values
    $account = $requiredFields;

    error_log("Returning account data: " . json_encode($account));

    // Return account as JSON
    header('Content-Type: application/json');
    echo json_encode($account);

  } catch (Exception $e) {
    error_log("Error in get_account_by_id: " . $e->getMessage());
    error_log("Error trace: " . $e->getTraceAsString());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'error' => 'Failed to fetch account: ' . $e->getMessage()
    ]);
  }
}





?>