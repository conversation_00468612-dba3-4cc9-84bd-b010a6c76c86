<?php
/**
 * Functions for sending invoice-related emails
 */

// Include necessary files
require_once("mysql.php");

// Import PHPMailer classes if not already imported
use PHPMailer\PHPMailer\PHPMailer;
use <PERSON><PERSON>Mailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

/**
 * Send an email notification for a newly generated invoice
 *
 * @param int $invoice_id The ID of the invoice
 * @return bool True if email was sent successfully, false otherwise
 */
function sendInvoiceGeneratedEmail($invoice_id) {
    global $pdo;

    try {
        // Get invoice details
        $invoiceStmt = $pdo->prepare("
            SELECT i.*, u.email, u.first_name, u.last_name, u.company_name
            FROM invoices i
            JOIN users u ON i.user_id = u.id
            WHERE i.id = :invoice_id
        ");
        $invoiceStmt->bindValue(':invoice_id', $invoice_id);
        $invoiceStmt->execute();

        $invoice = $invoiceStmt->fetch(PDO::FETCH_ASSOC);

        if (!$invoice) {
            error_log("Error sending invoice email: Invoice #$invoice_id not found");
            return false;
        }

        // Check if user has an email
        if (empty($invoice['email'])) {
            error_log("Error sending invoice email: No email address found for user ID " . $invoice['user_id']);
            return false;
        }

        // Get invoice items
        $itemsStmt = $pdo->prepare("
            SELECT * FROM invoice_items WHERE invoice_id = :invoice_id
        ");
        $itemsStmt->bindValue(':invoice_id', $invoice_id);
        $itemsStmt->execute();

        $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);

        // Get email template
        $templateStmt = $pdo->prepare("
            SELECT * FROM email_templates WHERE type = 'invoice_generated'
        ");
        $templateStmt->execute();

        if ($templateStmt->rowCount() == 0) {
            error_log("Error sending invoice email: 'invoice_generated' template not found");
            return false;
        }

        $template = $templateStmt->fetch(PDO::FETCH_ASSOC);

        // Prepare template variables
        $clientName = !empty($invoice['company_name']) ?
            $invoice['company_name'] :
            trim($invoice['first_name'] . ' ' . $invoice['last_name']);

        // Format invoice number based on type
        $invoiceNum = !empty($invoice['invoice_number']) ?
            $invoice['invoice_number'] :
            (!empty($invoice['proforma_number']) ? $invoice['proforma_number'] : $invoice_id);

        // For display in the email body, we might want to show the P- prefix for proforma numbers
        $displayInvoiceNum = !empty($invoice['invoice_number']) ?
            $invoice['invoice_number'] :
            (!empty($invoice['proforma_number']) ? 'P-' . $invoice['proforma_number'] : $invoice_id);

        // Format dates
        $invoiceDateCreated = date('Y-m-d', strtotime($invoice['date']));
        $invoiceDateDue = date('Y-m-d', strtotime($invoice['due_date']));

        // Format currency with HTML entity for Euro
        $invoiceTotal = '&euro;' . number_format($invoice['value'], 2);

        // Generate HTML for invoice items
        $invoiceHtmlContents = '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
        $invoiceHtmlContents .= '<tr style="background-color: #f8f9fa;">';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Description</th>';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">Quantity</th>';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">Unit Price</th>';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">Total</th>';
        $invoiceHtmlContents .= '</tr>';

        foreach ($items as $item) {
            $invoiceHtmlContents .= '<tr>';
            $invoiceHtmlContents .= '<td style="padding: 10px; border-bottom: 1px solid #ddd;">' . htmlspecialchars($item['description']) . '</td>';
            $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">' . htmlspecialchars($item['quantity']) . '</td>';
            $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($item['unit_price'], 2) . '</td>';
            $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($item['total'], 2) . '</td>';
            $invoiceHtmlContents .= '</tr>';
        }

        // Add subtotal, tax, and total rows
        $invoiceHtmlContents .= '<tr>';
        $invoiceHtmlContents .= '<td colspan="3" style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;"><strong>Subtotal:</strong></td>';
        $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($invoice['subtotal'], 2) . '</td>';
        $invoiceHtmlContents .= '</tr>';

        $invoiceHtmlContents .= '<tr>';
        $invoiceHtmlContents .= '<td colspan="3" style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;"><strong>Tax:</strong></td>';
        $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($invoice['tax'], 2) . '</td>';
        $invoiceHtmlContents .= '</tr>';

        $invoiceHtmlContents .= '<tr>';
        $invoiceHtmlContents .= '<td colspan="3" style="padding: 10px; text-align: right;"><strong>Total:</strong></td>';
        $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right;"><strong>&euro;' . number_format($invoice['value'], 2) . '</strong></td>';
        $invoiceHtmlContents .= '</tr>';

        $invoiceHtmlContents .= '</table>';

        // Get invoice link from database settings
        try {
            // First check if the general_settings table exists
            $tableCheckStmt = $pdo->query("SHOW TABLES LIKE 'general_settings'");
            $tableExists = $tableCheckStmt->rowCount() > 0;

            if ($tableExists) {
                $invoiceLinkStmt = $pdo->prepare("SELECT setting_value FROM general_settings WHERE setting_key = 'invoice_page_url' LIMIT 1");
                $invoiceLinkStmt->execute();

                if ($invoiceLinkStmt->rowCount() > 0) {
                    $invoiceLink = $invoiceLinkStmt->fetchColumn();
                } else {
                    // Fallback to default if setting not found
                    $invoiceLink = 'https://' . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'example.com') . '/client/invoices';
                }
            } else {
                // Table doesn't exist, use default
                $invoiceLink = 'https://' . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'example.com') . '/client/invoices';
            }
        } catch (Exception $e) {
            // If any error occurs, use default
            error_log("Error fetching invoice link from general_settings: " . $e->getMessage());
            $invoiceLink = 'https://' . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'example.com') . '/client/invoices';
        }

        // Replace template variables
        $subject = $template['subject'];
        $content = $template['content'];

        // Replace variables in subject
        $subject = str_replace('{$invoice_num}', $invoiceNum, $subject);

        // Replace variables in content
        $content = str_replace('{$client_name}', $clientName, $content);
        $content = str_replace('{$invoice_date_created}', $invoiceDateCreated, $content);
        $content = str_replace('{$invoice_payment_method}', $invoice['payment_method'], $content);
        $content = str_replace('{$invoice_num}', $displayInvoiceNum, $content);
        $content = str_replace('{$invoice_total}', $invoiceTotal, $content);
        $content = str_replace('{$invoice_date_due}', $invoiceDateDue, $content);
        $content = str_replace('{$invoice_html_contents}', $invoiceHtmlContents, $content);
        $content = str_replace('{$invoice_link}', $invoiceLink, $content);

        // Send the email - direct implementation for CLI compatibility
        try {
            // Get email settings from database
            $settingsStmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");

            if ($settingsStmt->rowCount() == 0) {
                error_log("Email settings not configured");
                return false;
            }

            $settings = $settingsStmt->fetch(PDO::FETCH_ASSOC);

            // Check if email sending is enabled
            if (!$settings['enabled']) {
                error_log("Email sending is disabled in settings");
                return false;
            }

            // For Google provider, always set the Gmail SMTP settings
            if ($settings['provider'] === 'google') {
                $settings['smtp_host'] = 'smtp.gmail.com';
                $settings['smtp_port'] = '587';
                $settings['smtp_encryption'] = 'tls';
            }

            // Set sender details
            $fromName = $settings['from_name'] ? $settings['from_name'] : 'Admin System';
            $fromEmail = $settings['from_email'] ? $settings['from_email'] : '<EMAIL>';
            $replyTo = !empty($settings['reply_to']) ? $settings['reply_to'] : '';

            // Check if signature is enabled and add it to the message
            $useHtmlMessage = true;
            $htmlMessage = $content;

            if (!empty($settings['use_signature']) && !empty($settings['signature'])) {
                // Add the signature with a divider
                $htmlMessage .= "<hr style='margin-top: 20px; margin-bottom: 20px; border: 0; border-top: 1px solid #eee;'>";
                $htmlMessage .= $settings['signature'];
            }

            // Create a new PHPMailer instance
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/PHPMailer.php';
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/SMTP.php';
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/Exception.php';
            $mail = new PHPMailer(true);

            // Server settings
            $mail->SMTPDebug = SMTP::DEBUG_OFF; // Disable debug output

            // Always use SMTP
            $mail->isSMTP();
            $mail->Host = $settings['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $settings['smtp_username'];
            $mail->Password = $settings['smtp_password'];

            // Set encryption type
            if ($settings['smtp_encryption'] === 'ssl') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // SSL
                $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 465;
            } else {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS; // TLS
                $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 587;
            }

            // Recipients
            $mail->setFrom($fromEmail, $fromName);
            $mail->addAddress($invoice['email']);

            if ($replyTo) {
                $mail->addReplyTo($replyTo);
            }

            // Content
            $mail->isHTML(true);
            $mail->CharSet = 'UTF-8'; // Set UTF-8 character encoding
            $mail->Subject = $subject;
            $mail->Body = $htmlMessage;
            $mail->AltBody = strip_tags(str_replace('<br>', "\n", $content)); // Plain text alternative

            // Send the email
            $mail->send();
            $result = true;
        } catch (Exception $mailException) {
            error_log("Error sending email: " . $mailException->getMessage());
            $result = false;
        }

        if ($result) {
            error_log("Invoice email sent successfully to " . $invoice['email'] . " for invoice #$invoice_id");

            // Log to activity log
            $activityStmt = $pdo->prepare("
                INSERT INTO activity_log (
                    user_id,
                    action,
                    description,
                    user_name,
                    timestamp,
                    activity_type,
                    invoice_number,
                    proforma_number
                ) VALUES (
                    :user_id,
                    'Invoice Email Sent',
                    :description,
                    :user_name,
                    NOW(),
                    'billing',
                    :invoice_number,
                    :proforma_number
                )
            ");

            $activityStmt->bindValue(':user_id', $invoice['user_id']);
            $activityStmt->bindValue(':description', "Invoice notification email sent to " . $invoice['email']);
            $activityStmt->bindValue(':user_name', $clientName);
            $activityStmt->bindValue(':invoice_number', $invoice['invoice_number'] ?? null);
            $activityStmt->bindValue(':proforma_number', $invoice['proforma_number'] ?? null);

            $activityStmt->execute();

            return true;
        } else {
            error_log("Failed to send invoice email to " . $invoice['email'] . " for invoice #$invoice_id");
            return false;
        }

    } catch (Exception $e) {
        error_log("Error in sendInvoiceGeneratedEmail: " . $e->getMessage());
        return false;
    }
}

/**
 * Send an email notification when an invoice is marked as paid
 *
 * @param int $invoice_id The ID of the invoice
 * @return bool True if email was sent successfully, false otherwise
 */
function sendInvoicePaidEmail($invoice_id) {
    global $pdo;

    try {
        // Get invoice details
        $invoiceStmt = $pdo->prepare("
            SELECT i.*, u.email, u.first_name, u.last_name, u.company_name
            FROM invoices i
            JOIN users u ON i.user_id = u.id
            WHERE i.id = :invoice_id
        ");
        $invoiceStmt->bindValue(':invoice_id', $invoice_id);
        $invoiceStmt->execute();

        $invoice = $invoiceStmt->fetch(PDO::FETCH_ASSOC);

        if (!$invoice) {
            error_log("Error sending paid invoice email: Invoice #$invoice_id not found");
            return false;
        }

        // Check if user has an email
        if (empty($invoice['email'])) {
            error_log("Error sending paid invoice email: No email address found for user ID " . $invoice['user_id']);
            return false;
        }

        // Get invoice items
        $itemsStmt = $pdo->prepare("
            SELECT * FROM invoice_items WHERE invoice_id = :invoice_id
        ");
        $itemsStmt->bindValue(':invoice_id', $invoice_id);
        $itemsStmt->execute();

        $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);

        // Get email template
        $templateStmt = $pdo->prepare("
            SELECT * FROM email_templates WHERE type = 'invoice_paid'
        ");
        $templateStmt->execute();

        if ($templateStmt->rowCount() == 0) {
            error_log("Error sending paid invoice email: 'invoice_paid' template not found");
            return false;
        }

        $template = $templateStmt->fetch(PDO::FETCH_ASSOC);

        // Prepare template variables
        $clientName = !empty($invoice['company_name']) ?
            $invoice['company_name'] :
            trim($invoice['first_name'] . ' ' . $invoice['last_name']);

        // Format invoice number based on type
        $invoiceNum = !empty($invoice['invoice_number']) ?
            $invoice['invoice_number'] :
            (!empty($invoice['proforma_number']) ? $invoice['proforma_number'] : $invoice_id);

        // For display in the email body, we might want to show the P- prefix for proforma numbers
        $displayInvoiceNum = !empty($invoice['invoice_number']) ?
            $invoice['invoice_number'] :
            (!empty($invoice['proforma_number']) ? 'P-' . $invoice['proforma_number'] : $invoice_id);

        // Format payment date
        $paymentDate = !empty($invoice['paid_date']) ?
            date('Y-m-d', strtotime($invoice['paid_date'])) :
            date('Y-m-d'); // Use current date if paid_date is not set

        // Format currency with HTML entity for Euro
        $invoiceTotal = '&euro;' . number_format($invoice['value'], 2);

        // Generate HTML for invoice items
        $invoiceHtmlContents = '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
        $invoiceHtmlContents .= '<tr style="background-color: #f8f9fa;">';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">Description</th>';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">Quantity</th>';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">Unit Price</th>';
        $invoiceHtmlContents .= '<th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">Total</th>';
        $invoiceHtmlContents .= '</tr>';

        foreach ($items as $item) {
            $invoiceHtmlContents .= '<tr>';
            $invoiceHtmlContents .= '<td style="padding: 10px; border-bottom: 1px solid #ddd;">' . htmlspecialchars($item['description']) . '</td>';
            $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">' . htmlspecialchars($item['quantity']) . '</td>';
            $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($item['unit_price'], 2) . '</td>';
            $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($item['total'], 2) . '</td>';
            $invoiceHtmlContents .= '</tr>';
        }

        // Add subtotal, tax, and total rows
        $invoiceHtmlContents .= '<tr>';
        $invoiceHtmlContents .= '<td colspan="3" style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;"><strong>Subtotal:</strong></td>';
        $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($invoice['subtotal'], 2) . '</td>';
        $invoiceHtmlContents .= '</tr>';

        $invoiceHtmlContents .= '<tr>';
        $invoiceHtmlContents .= '<td colspan="3" style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;"><strong>Tax:</strong></td>';
        $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">&euro;' . number_format($invoice['tax'], 2) . '</td>';
        $invoiceHtmlContents .= '</tr>';

        $invoiceHtmlContents .= '<tr>';
        $invoiceHtmlContents .= '<td colspan="3" style="padding: 10px; text-align: right;"><strong>Total:</strong></td>';
        $invoiceHtmlContents .= '<td style="padding: 10px; text-align: right;"><strong>&euro;' . number_format($invoice['value'], 2) . '</strong></td>';
        $invoiceHtmlContents .= '</tr>';

        $invoiceHtmlContents .= '</table>';

        // Replace template variables
        $subject = $template['subject'];
        $content = $template['content'];

        // Replace variables in subject
        $subject = str_replace('{$invoice_num}', $invoiceNum, $subject);

        // Replace variables in content
        $content = str_replace('{$client_name}', $clientName, $content);
        $content = str_replace('{$invoice_num}', $displayInvoiceNum, $content);
        $content = str_replace('{$invoice_total}', $invoiceTotal, $content);
        $content = str_replace('{$payment_date}', $paymentDate, $content);
        $content = str_replace('{$invoice_html_contents}', $invoiceHtmlContents, $content);

        // Send the email - direct implementation for CLI compatibility
        try {
            // Get email settings from database
            $settingsStmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");

            if ($settingsStmt->rowCount() == 0) {
                error_log("Email settings not configured");
                return false;
            }

            $settings = $settingsStmt->fetch(PDO::FETCH_ASSOC);

            // Check if email sending is enabled
            if (!$settings['enabled']) {
                error_log("Email sending is disabled in settings");
                return false;
            }

            // For Google provider, always set the Gmail SMTP settings
            if ($settings['provider'] === 'google') {
                $settings['smtp_host'] = 'smtp.gmail.com';
                $settings['smtp_port'] = '587';
                $settings['smtp_encryption'] = 'tls';
            }

            // Set sender details
            $fromName = $settings['from_name'] ? $settings['from_name'] : 'Admin System';
            $fromEmail = $settings['from_email'] ? $settings['from_email'] : '<EMAIL>';
            $replyTo = !empty($settings['reply_to']) ? $settings['reply_to'] : '';

            // Check if signature is enabled and add it to the message
            $useHtmlMessage = true;
            $htmlMessage = $content;

            if (!empty($settings['use_signature']) && !empty($settings['signature'])) {
                // Add the signature with a divider
                $htmlMessage .= "<hr style='margin-top: 20px; margin-bottom: 20px; border: 0; border-top: 1px solid #eee;'>";
                $htmlMessage .= $settings['signature'];
            }

            // Create a new PHPMailer instance
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/PHPMailer.php';
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/SMTP.php';
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/Exception.php';
            $mail = new PHPMailer(true);

            // Server settings
            $mail->SMTPDebug = SMTP::DEBUG_OFF; // Disable debug output

            // Always use SMTP
            $mail->isSMTP();
            $mail->Host = $settings['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $settings['smtp_username'];
            $mail->Password = $settings['smtp_password'];

            // Set encryption type
            if ($settings['smtp_encryption'] === 'ssl') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // SSL
                $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 465;
            } else {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS; // TLS
                $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 587;
            }

            // Recipients
            $mail->setFrom($fromEmail, $fromName);
            $mail->addAddress($invoice['email']);

            if ($replyTo) {
                $mail->addReplyTo($replyTo);
            }

            // Content
            $mail->isHTML(true);
            $mail->CharSet = 'UTF-8'; // Set UTF-8 character encoding
            $mail->Subject = $subject;
            $mail->Body = $htmlMessage;
            $mail->AltBody = strip_tags(str_replace('<br>', "\n", $content)); // Plain text alternative

            // Send the email
            $mail->send();
            $result = true;
        } catch (Exception $mailException) {
            error_log("Error sending paid invoice email: " . $mailException->getMessage());
            $result = false;
        }

        if ($result) {
            error_log("Paid invoice email sent successfully to " . $invoice['email'] . " for invoice #$invoice_id");

            // Log to activity log
            $activityStmt = $pdo->prepare("
                INSERT INTO activity_log (
                    user_id,
                    action,
                    description,
                    user_name,
                    timestamp,
                    activity_type,
                    invoice_number,
                    proforma_number
                ) VALUES (
                    :user_id,
                    'Paid Invoice Email Sent',
                    :description,
                    :user_name,
                    NOW(),
                    'billing',
                    :invoice_number,
                    :proforma_number
                )
            ");

            $activityStmt->bindValue(':user_id', $invoice['user_id']);
            $activityStmt->bindValue(':description', "Payment confirmation email sent to " . $invoice['email']);
            $activityStmt->bindValue(':user_name', $clientName);
            $activityStmt->bindValue(':invoice_number', $invoice['invoice_number'] ?? null);
            $activityStmt->bindValue(':proforma_number', $invoice['proforma_number'] ?? null);

            $activityStmt->execute();

            return true;
        } else {
            error_log("Failed to send paid invoice email to " . $invoice['email'] . " for invoice #$invoice_id");
            return false;
        }

    } catch (Exception $e) {
        error_log("Error in sendInvoicePaidEmail: " . $e->getMessage());
        return false;
    }
}
