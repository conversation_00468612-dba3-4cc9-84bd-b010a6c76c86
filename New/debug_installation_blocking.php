<?php
/**
 * Debug Installation Blocking System
 * 
 * Simple script to test the installation blocking API endpoints
 */

require_once 'mysql.php';
require_once 'auth_functions.php';
require_once 'pxe_api_integration.php';

// Test configuration
$TEST_SERVER_ID = 1; // Change this to match a server in your system

echo "<h2>Debug Installation Blocking System</h2>\n";
echo "<p>Testing server ID: $TEST_SERVER_ID</p>\n";

// Test 1: Direct database check
echo "<h3>Test 1: Direct Database Check</h3>\n";
try {
    $stmt = $pdo->prepare("
        SELECT 
            id, server_id, server_label, os_template, status, started_at
        FROM pxe_reinstall_sessions 
        WHERE server_id = ? 
          AND status IN ('pending', 'active')
        ORDER BY started_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$TEST_SERVER_ID]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($session) {
        echo "<p style='color: orange'>❌ Installation session found in database:</p>\n";
        echo "<pre>" . print_r($session, true) . "</pre>\n";
    } else {
        echo "<p style='color: green'>✅ No active installation sessions found in database</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Database check failed: " . $e->getMessage() . "</p>\n";
}

// Test 2: Test check_installation_status endpoint
echo "<h3>Test 2: API Status Check</h3>\n";
try {
    // Simulate the API call
    $_GET['f'] = 'check_installation_status';
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    $request_data = [
        'server_id' => $TEST_SERVER_ID
    ];
    
    // Mock the request input
    $mock_input = json_encode($request_data);
    
    // Capture output
    ob_start();
    
    // Simulate the API call logic
    $stmt = $pdo->prepare("
        SELECT 
            s.id,
            s.server_id,
            s.server_type,
            s.server_label,
            s.os_template,
            s.status,
            s.started_at,
            s.error_message,
            ro.name as os_name,
            ro.version as os_version
        FROM pxe_reinstall_sessions s
        LEFT JOIN reinstallable_os ro ON CONCAT(ro.name, ' ', ro.version) = s.os_template
        WHERE s.server_id = ? 
          AND s.status IN ('pending', 'active')
        ORDER BY s.started_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$TEST_SERVER_ID]);
    $active_session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($active_session) {
        $os_display_name = $active_session['os_name'] 
            ? $active_session['os_name'] . ' ' . $active_session['os_version']
            : $active_session['os_template'];
            
        $api_response = [
            'installation_in_progress' => true,
            'session_id' => $active_session['id'],
            'os_name' => $os_display_name,
            'status' => $active_session['status'],
            'started_at' => $active_session['started_at'],
            'server_label' => $active_session['server_label'],
            'error_message' => $active_session['error_message']
        ];
        
        echo "<p style='color: orange'>❌ API would return: Installation in progress</p>\n";
        echo "<pre>" . print_r($api_response, true) . "</pre>\n";
    } else {
        $api_response = [
            'installation_in_progress' => false
        ];
        
        echo "<p style='color: green'>✅ API would return: No installation in progress</p>\n";
        echo "<pre>" . print_r($api_response, true) . "</pre>\n";
    }
    
    ob_end_clean();
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<p style='color: red'>❌ API status check failed: " . $e->getMessage() . "</p>\n";
}

// Test 3: Test reserve_installation endpoint
echo "<h3>Test 3: API Reservation Test</h3>\n";
try {
    // Start transaction
    $pdo->beginTransaction();
    
    // Check for existing installations
    $check_stmt = $pdo->prepare("
        SELECT id, server_label, os_template, status, started_at
        FROM pxe_reinstall_sessions 
        WHERE server_id = ? 
          AND status IN ('pending', 'active')
        ORDER BY started_at DESC 
        LIMIT 1
        FOR UPDATE
    ");
    $check_stmt->execute([$TEST_SERVER_ID]);
    $existing_session = $check_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing_session) {
        $pdo->rollBack();
        
        echo "<p style='color: orange'>❌ Reservation would be blocked:</p>\n";
        echo "<pre>Another installation is already in progress for this server</pre>\n";
        echo "<p>Existing session details:</p>\n";
        echo "<pre>" . print_r($existing_session, true) . "</pre>\n";
    } else {
        $pdo->commit();
        echo "<p style='color: green'>✅ Reservation would succeed - no installation in progress</p>\n";
    }
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo "<p style='color: red'>❌ Reservation test failed: " . $e->getMessage() . "</p>\n";
}

// Test 4: Show recent sessions
echo "<h3>Test 4: Recent Sessions</h3>\n";
try {
    $stmt = $pdo->prepare("
        SELECT 
            id, server_id, server_label, os_template, status, started_at, completed_at
        FROM pxe_reinstall_sessions 
        WHERE server_id = ?
        ORDER BY started_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$TEST_SERVER_ID]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($sessions) {
        echo "<p>Recent sessions for server $TEST_SERVER_ID:</p>\n";
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>ID</th><th>OS</th><th>Status</th><th>Started</th><th>Completed</th></tr>\n";
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>" . $session['id'] . "</td>";
            echo "<td>" . $session['os_template'] . "</td>";
            echo "<td>" . $session['status'] . "</td>";
            echo "<td>" . $session['started_at'] . "</td>";
            echo "<td>" . ($session['completed_at'] ?: 'N/A') . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p>No sessions found for server $TEST_SERVER_ID</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red'>❌ Session query failed: " . $e->getMessage() . "</p>\n";
}

echo "<h3>Instructions</h3>\n";
echo "<ol>\n";
echo "<li>If you see active sessions above, the blocking should work</li>\n";
echo "<li>Open browser dev tools and check console when testing the modal</li>\n";
echo "<li>Look for emoji debug messages (🔍, 📡, ✅, ❌) in the console</li>\n";
echo "<li>Check the debug section in the modal UI</li>\n";
echo "</ol>\n";

echo "<h3>To Create Test Session</h3>\n";
echo "<p>Run this SQL to create a test session:</p>\n";
echo "<pre>
INSERT INTO pxe_reinstall_sessions 
(server_id, server_type, server_label, mac_address, ip_address, hostname, os_template, status) 
VALUES ($TEST_SERVER_ID, 'dedicated', 'Test Server', '00:00:00:00:00:00', '*************', 'test', 'Ubuntu 22.04', 'active');
</pre>\n";

echo "<h3>To Clean Up Test Sessions</h3>\n";
echo "<p>Run this SQL to remove test sessions:</p>\n";
echo "<pre>
UPDATE pxe_reinstall_sessions 
SET status = 'completed', completed_at = NOW() 
WHERE server_id = $TEST_SERVER_ID AND status IN ('pending', 'active');
</pre>\n";
?> 