<?php
// Debug script to check user services data
require_once 'auth_functions.php';

$userId = 1; // Test with user ID 1

echo "=== DEBUG USER SERVICES DATA ===\n";
echo "User ID: $userId\n\n";

try {
    // Check if user exists
    $userStmt = $pdo->prepare("SELECT id, first_name, last_name, email FROM users WHERE id = :user_id");
    $userStmt->bindValue(':user_id', $userId);
    $userStmt->execute();
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "✓ User exists: {$user['first_name']} {$user['last_name']} ({$user['email']})\n\n";
    } else {
        echo "✗ User with ID $userId does not exist\n";
        exit;
    }
    
    // Check orders_items table
    echo "--- ORDERS_ITEMS TABLE ---\n";
    $itemsStmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders_items WHERE user_id = :user_id");
    $itemsStmt->bindValue(':user_id', $userId);
    $itemsStmt->execute();
    $itemsCount = $itemsStmt->fetchColumn();
    echo "Total orders_items for user: $itemsCount\n";
    
    if ($itemsCount > 0) {
        $sampleItemsStmt = $pdo->prepare("SELECT * FROM orders_items WHERE user_id = :user_id LIMIT 3");
        $sampleItemsStmt->bindValue(':user_id', $userId);
        $sampleItemsStmt->execute();
        $items = $sampleItemsStmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($items as $item) {
            echo "  Item ID: {$item['id']}, Order ID: {$item['order_id']}, CPU ID: {$item['cpu_id']}, Location ID: {$item['location_id']}\n";
        }
    }
    echo "\n";
    
    // Check orders table
    echo "--- ORDERS TABLE ---\n";
    $ordersStmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE owner_id = :user_id");
    $ordersStmt->bindValue(':user_id', $userId);
    $ordersStmt->execute();
    $ordersCount = $ordersStmt->fetchColumn();
    echo "Total orders for user: $ordersCount\n";
    
    if ($ordersCount > 0) {
        $sampleOrdersStmt = $pdo->prepare("SELECT * FROM orders WHERE owner_id = :user_id LIMIT 3");
        $sampleOrdersStmt->bindValue(':user_id', $userId);
        $sampleOrdersStmt->execute();
        $orders = $sampleOrdersStmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($orders as $order) {
            echo "  Order ID: {$order['id']}, Status: {$order['status']}, Type: {$order['order_type']}, Date: {$order['order_date']}\n";
        }
    }
    echo "\n";
    
    // Check if there are any orders_items at all
    echo "--- TOTAL DATABASE CONTENT ---\n";
    $totalItemsStmt = $pdo->query("SELECT COUNT(*) as count FROM orders_items");
    $totalItems = $totalItemsStmt->fetchColumn();
    echo "Total orders_items in database: $totalItems\n";
    
    $totalOrdersStmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
    $totalOrders = $totalOrdersStmt->fetchColumn();
    echo "Total orders in database: $totalOrders\n";
    
    // If no data exists, let's create some test data
    if ($itemsCount == 0 && $ordersCount == 0) {
        echo "\n--- CREATING TEST DATA ---\n";
        
        // Create a test order
        $orderStmt = $pdo->prepare("INSERT INTO orders (
            label, owner_id, placed_by_id, order_type, order_date, expiration_date,
            initial_price, recurring_price, status
        ) VALUES (
            'Test Server Order', :user_id, :user_id, 'dedicated', NOW(),
            DATE_ADD(NOW(), INTERVAL 1 MONTH), 50.00, 50.00, 'Active'
        )");
        $orderStmt->bindValue(':user_id', $userId);
        $orderStmt->execute();
        $orderId = $pdo->lastInsertId();
        echo "✓ Created test order with ID: $orderId\n";
        
        // Create a test order item
        $itemStmt = $pdo->prepare("INSERT INTO orders_items (
            order_id, invoice_item_id, user_id, cpu_id, bandwidth_id, location_id, 
            storage_id, order_price, requirement_price, payment_period, hostname, 
            date_ordered, due_date, status
        ) VALUES (
            :order_id, 1, :user_id, 1, 1, 1, 1, 50.00, 50.00, 'monthly', 
            'srv-test.example.com', NOW(), DATE_ADD(NOW(), INTERVAL 1 MONTH), 'Active'
        )");
        $itemStmt->bindValue(':order_id', $orderId);
        $itemStmt->bindValue(':user_id', $userId);
        $itemStmt->execute();
        $itemId = $pdo->lastInsertId();
        echo "✓ Created test order item with ID: $itemId\n";
        
        echo "\nTest data created successfully!\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n=== END DEBUG ===\n";
?>
