#!/bin/bash
# PXE Infrastructure Verification Script
# This script checks if all PXE components are properly installed and configured

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_success() {
    echo -e "${GREEN}✓${NC} $1"
}

echo_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

echo_error() {
    echo -e "${RED}✗${NC} $1"
}

echo_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

echo_header() {
    echo ""
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if running as root
check_privileges() {
    if [[ $EUID -eq 0 ]]; then
        echo_success "Running as root"
    else
        echo_warning "Not running as root - some checks may fail"
    fi
}

# Check required packages
check_packages() {
    echo_header "Checking Required Packages"
    
    packages=("isc-dhcp-server" "tftpd-hpa" "apache2" "syslinux-common")
    
    for package in "${packages[@]}"; do
        if dpkg -l | grep -q "^ii  $package "; then
            echo_success "$package is installed"
        elif rpm -q "$package" &>/dev/null; then
            echo_success "$package is installed"
        else
            echo_error "$package is not installed"
        fi
    done
}

# Check services
check_services() {
    echo_header "Checking Services"
    
    services=("isc-dhcp-server" "tftpd-hpa" "apache2")
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            echo_success "$service is running"
        else
            echo_error "$service is not running"
        fi
        
        if systemctl is-enabled --quiet "$service"; then
            echo_success "$service is enabled"
        else
            echo_warning "$service is not enabled"
        fi
    done
}

# Check directories and files
check_directories() {
    echo_header "Checking Directories and Files"
    
    # TFTP directories
    if [[ -d "/var/lib/tftpboot" ]]; then
        echo_success "TFTP root directory exists"
        
        if [[ -f "/var/lib/tftpboot/pxelinux.0" ]]; then
            echo_success "PXE bootloader found"
        else
            echo_error "PXE bootloader (pxelinux.0) not found"
        fi
        
        if [[ -d "/var/lib/tftpboot/pxelinux.cfg" ]]; then
            echo_success "PXE configuration directory exists"
        else
            echo_error "PXE configuration directory not found"
        fi
        
        # Check for OS directories
        for os in ubuntu centos debian; do
            if [[ -d "/var/lib/tftpboot/$os" ]]; then
                echo_success "$os directory exists"
            else
                echo_warning "$os directory not found"
            fi
        done
        
    else
        echo_error "TFTP root directory not found"
    fi
    
    # Web directories
    if [[ -d "/var/www/html/kickstart" ]]; then
        echo_success "Kickstart directory exists"
    else
        echo_error "Kickstart directory not found"
    fi
    
    # Check management scripts
    scripts=("pxe-add-host" "pxe-remove-host" "pxe-create-config")
    for script in "${scripts[@]}"; do
        if [[ -x "/usr/local/bin/$script" ]]; then
            echo_success "Management script $script found"
        else
            echo_error "Management script $script not found"
        fi
    done
}

# Check configuration files
check_configuration() {
    echo_header "Checking Configuration Files"
    
    # DHCP configuration
    if [[ -f "/etc/dhcp/dhcpd.conf" ]]; then
        echo_success "DHCP configuration file exists"
        
        if grep -q "PXE HOST DECLARATIONS" "/etc/dhcp/dhcpd.conf"; then
            echo_success "DHCP configuration has PXE markers"
        else
            echo_warning "DHCP configuration may not be properly set up for PXE"
        fi
        
        # Test DHCP configuration syntax
        if dhcpd -t -cf /etc/dhcp/dhcpd.conf &>/dev/null; then
            echo_success "DHCP configuration syntax is valid"
        else
            echo_error "DHCP configuration has syntax errors"
        fi
    else
        echo_error "DHCP configuration file not found"
    fi
    
    # TFTP configuration
    if [[ -f "/etc/default/tftpd-hpa" ]]; then
        echo_success "TFTP configuration file exists"
        
        if grep -q "/var/lib/tftpboot" "/etc/default/tftpd-hpa"; then
            echo_success "TFTP root directory configured"
        else
            echo_warning "TFTP root directory may not be configured"
        fi
    else
        echo_error "TFTP configuration file not found"
    fi
}

# Check network connectivity
check_network() {
    echo_header "Checking Network Configuration"
    
    # Check if ports are listening
    if netstat -ln | grep -q ":67 "; then
        echo_success "DHCP server is listening on port 67"
    else
        echo_error "DHCP server is not listening on port 67"
    fi
    
    if netstat -ln | grep -q ":69 "; then
        echo_success "TFTP server is listening on port 69"
    else
        echo_error "TFTP server is not listening on port 69"
    fi
    
    if netstat -ln | grep -q ":80 "; then
        echo_success "HTTP server is listening on port 80"
    else
        echo_error "HTTP server is not listening on port 80"
    fi
}

# Check file permissions
check_permissions() {
    echo_header "Checking File Permissions"
    
    # TFTP permissions
    if [[ -d "/var/lib/tftpboot" ]]; then
        tftp_owner=$(stat -c '%U' /var/lib/tftpboot)
        if [[ "$tftp_owner" == "tftp" ]]; then
            echo_success "TFTP directory has correct ownership"
        else
            echo_warning "TFTP directory owner is $tftp_owner (expected: tftp)"
        fi
        
        tftp_perms=$(stat -c '%a' /var/lib/tftpboot)
        if [[ "$tftp_perms" == "755" ]]; then
            echo_success "TFTP directory has correct permissions"
        else
            echo_warning "TFTP directory permissions are $tftp_perms (expected: 755)"
        fi
    fi
    
    # Web permissions
    if [[ -d "/var/www/html/kickstart" ]]; then
        web_owner=$(stat -c '%U' /var/www/html/kickstart)
        if [[ "$web_owner" == "www-data" ]] || [[ "$web_owner" == "apache" ]]; then
            echo_success "Kickstart directory has correct ownership"
        else
            echo_warning "Kickstart directory owner is $web_owner (expected: www-data or apache)"
        fi
    fi
}

# Test basic functionality
test_functionality() {
    echo_header "Testing Basic Functionality"
    
    # Test TFTP access
    if command -v tftp &>/dev/null; then
        if timeout 5 tftp localhost -c get pxelinux.0 /tmp/pxelinux.0.test 2>/dev/null; then
            echo_success "TFTP server is accessible"
            rm -f /tmp/pxelinux.0.test
        else
            echo_error "TFTP server is not accessible"
        fi
    else
        echo_warning "TFTP client not available for testing"
    fi
    
    # Test HTTP access
    if command -v curl &>/dev/null; then
        if curl -s -f "http://localhost/kickstart/" >/dev/null; then
            echo_success "HTTP server kickstart directory is accessible"
        else
            echo_error "HTTP server kickstart directory is not accessible"
        fi
    elif command -v wget &>/dev/null; then
        if wget -q --spider "http://localhost/kickstart/"; then
            echo_success "HTTP server kickstart directory is accessible"
        else
            echo_error "HTTP server kickstart directory is not accessible"
        fi
    else
        echo_warning "No HTTP client available for testing"
    fi
}

# Check database tables
check_database() {
    echo_header "Checking Database Configuration"
    
    # This would require database connection details
    # For now, just check if the schema files exist
    if [[ -f "pxe_reinstall_schema.sql" ]]; then
        echo_success "PXE database schema file found"
    else
        echo_warning "PXE database schema file not found"
    fi
    
    echo_info "Manual database check required - verify server_reinstall_log table exists"
}

# Generate summary report
generate_summary() {
    echo_header "Summary and Recommendations"
    
    echo_info "PXE Infrastructure Verification Complete"
    echo ""
    echo "Next steps:"
    echo "1. Review any errors or warnings above"
    echo "2. Configure DHCP subnet settings for your network"
    echo "3. Download OS installation files to TFTP directories"
    echo "4. Test PXE boot with a test server"
    echo "5. Verify database tables are created"
    echo ""
    echo "For detailed setup instructions, see:"
    echo "- network_infrastructure_setup.sh"
    echo "- COMPLETE_PXE_INFRASTRUCTURE.md"
}

# Main execution
main() {
    echo_info "PXE Infrastructure Verification Script"
    echo_info "======================================"
    
    check_privileges
    check_packages
    check_services
    check_directories
    check_configuration
    check_network
    check_permissions
    test_functionality
    check_database
    generate_summary
}

# Run main function
main "$@" 