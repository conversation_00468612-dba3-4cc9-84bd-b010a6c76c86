# Automatic Invoice Generation System

This system automatically generates invoices for services that are due for renewal in 14 days. It runs daily at 00:01 via a cron job.

## Files

1. `generate_upcoming_invoices.php` - The main script that checks for services due in 14 days and generates invoices
2. `setup_invoice_cron.sh` - Shell script to set up the cron job
3. `test_invoice_generation.php` - Test script to manually generate invoices for testing purposes

## Setup Instructions

### 1. Install the Cron Job

Run the setup script to install the cron job:

```bash
chmod +x setup_invoice_cron.sh
./setup_invoice_cron.sh
```

This will set up a cron job to run the invoice generation script daily at 00:01.

### 2. Test the System

You can test the invoice generation process by running:

```bash
php test_invoice_generation.php
```

This will generate test invoices for up to 5 active services in the system.

## How It Works

1. The script runs daily at 00:01
2. It queries the database for services with due dates exactly 14 days from the current date
3. For each service found, it:
   - Calculates the appropriate VAT based on the user's country
   - Generates a new invoice with the service details
   - Creates an invoice item
   - Logs the action in the activity log
4. All actions are logged to `/var/log/invoice_generation.log`

## Troubleshooting

If you encounter issues:

1. Check the log file at `/var/log/invoice_generation.log`
2. Ensure the database connection details in `mysql.php` are correct
3. Verify that the script has permission to write to the log file
4. Run the test script to see if it can generate invoices successfully

## Customization

You can modify the following aspects of the system:

- Change the number of days in advance (currently 14) by editing the `$due_date_target` calculation in `generate_upcoming_invoices.php`
- Adjust the invoice description format in the script
- Change the default payment method (currently 'Bank Transfer')
- Modify the invoice status (currently 'Pending')

## Security Considerations

- The script contains database credentials, so ensure it's only accessible by authorized users
- Consider implementing additional validation and error handling as needed
- Regularly review the logs to ensure the system is functioning correctly
