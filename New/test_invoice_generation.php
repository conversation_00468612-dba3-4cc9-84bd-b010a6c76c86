<?php
/**
 * Test script to manually run the invoice generation process
 * This script overrides the due date check to generate invoices for testing
 */

// Include database connection and required functions
require_once("mysql.php");
require_once("./invoice_email_functions.php");

// Set error handling
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Starting test invoice generation...\n\n";

// Statistics
$stats = [
    'services_checked' => 0,
    'invoices_generated' => 0,
    'errors' => 0
];

try {
    // For testing, we'll look for any active services
    // You can modify this query to test with specific services
    // Debug: Print the orders_items table structure
    echo "Checking orders_items table structure...\n";
    $tableStmt = $pdo->query("DESCRIBE orders_items");
    $columns = $tableStmt->fetchAll(PDO::FETCH_ASSOC);
    echo "orders_items columns:\n";
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})\n";
    }
    echo "\n";

    // Debug: Check if there are any records in orders_items
    $countStmt = $pdo->query("SELECT COUNT(*) as count FROM orders_items");
    $countResult = $countStmt->fetch(PDO::FETCH_ASSOC);
    echo "Total records in orders_items: {$countResult['count']}\n\n";

    // Debug: Check if there are any active records
    $activeStmt = $pdo->query("SELECT COUNT(*) as count FROM orders_items WHERE status = 'active'");
    $activeResult = $activeStmt->fetch(PDO::FETCH_ASSOC);
    echo "Active records in orders_items: {$activeResult['count']}\n\n";

    // Modified query to check both tables and print more details
    $query = "SELECT oi.*, o.status as order_status, u.id as user_id, u.email as user_email,
              u.first_name, u.last_name, u.company_name, u.country
              FROM orders_items oi
              LEFT JOIN orders o ON oi.order_id = o.id
              LEFT JOIN users u ON oi.user_id = u.id
              WHERE oi.status = 'active' OR o.status = 'active'
              LIMIT 5"; // Limit to 5 services for testing

    $stmt = $pdo->query($query);
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $stats['services_checked'] = count($services);

    echo "Found " . count($services) . " services for testing\n";

    // Process each service
    foreach ($services as $service) {
        echo "Processing service ID: " . $service['id'] . " for user: " . $service['user_email'] . "\n";

        try {
            // Begin transaction
            $pdo->beginTransaction();

            // Generate invoice number
            $invoiceNumberStmt = $pdo->query("SELECT MAX(CAST(invoice_number AS UNSIGNED)) AS last_number FROM invoices");
            $invoiceNumberResult = $invoiceNumberStmt->fetch(PDO::FETCH_ASSOC);
            $nextInvoiceNumber = ($invoiceNumberResult && $invoiceNumberResult['last_number'])
                ? $invoiceNumberResult['last_number'] + 1
                : 1;

            // Generate proforma number
            // First check for numeric proforma numbers
            $proformaNumberStmt = $pdo->query("SELECT MAX(CAST(proforma_number AS UNSIGNED)) AS last_number FROM invoices WHERE proforma_number REGEXP '^[0-9]+$'");
            $proformaNumberResult = $proformaNumberStmt->fetch(PDO::FETCH_ASSOC);
            $lastNumericProforma = ($proformaNumberResult && $proformaNumberResult['last_number'])
                ? $proformaNumberResult['last_number']
                : 0;

            // Then check for PRO-X format
            $proformaProStmt = $pdo->query("SELECT MAX(CAST(SUBSTRING(proforma_number, 5) AS UNSIGNED)) AS last_number FROM invoices WHERE proforma_number LIKE 'PRO-%'");
            $proformaProResult = $proformaProStmt->fetch(PDO::FETCH_ASSOC);
            $lastProFormatNumber = ($proformaProResult && $proformaProResult['last_number'])
                ? $proformaProResult['last_number']
                : 0;

            // Use the highest number + 1
            $nextProformaNumber = max($lastNumericProforma, $lastProFormatNumber) + 1;

            // For consistency with existing format, use numeric format if that's what's being used
            $proformaNumber = $lastNumericProforma > $lastProFormatNumber ? $nextProformaNumber : 'PRO-' . $nextProformaNumber;

            echo "Generated proforma number: {$proformaNumber} (Last numeric: {$lastNumericProforma}, Last PRO-format: {$lastProFormatNumber})\n";

            // Get user's country for VAT calculation
            $country = $service['country'];

            // Get VAT rate based on country
            $vatStmt = $pdo->prepare("SELECT rate FROM vat_rates WHERE country = :country");
            $vatStmt->bindValue(':country', $country);
            $vatStmt->execute();
            $vatResult = $vatStmt->fetch(PDO::FETCH_ASSOC);
            $tax_rate = ($vatResult && $vatResult['rate']) ? $vatResult['rate'] / 100 : 0;

            // Calculate amounts
            $subtotal = $service['order_price'] ?? 0;
            $tax = round($subtotal * $tax_rate, 2);
            $total = $subtotal + $tax;

            // Create description
            $description = "TEST INVOICE - Service renewal: " . ($service['hostname'] ?? "Server") . " - " .
                           ($service['payment_period'] ?? "Monthly") . " period";

            // For testing, set due date to 14 days from now
            $due_date = date('Y-m-d', strtotime('+14 days'));

            // Insert the invoice
            $invoiceStmt = $pdo->prepare("
                INSERT INTO invoices (
                    user_id,
                    order_id,
                    type,
                    value,
                    subtotal,
                    tax,
                    date,
                    due_date,
                    payment_method,
                    status,
                    proforma_number,
                    invoice_number,
                    description
                ) VALUES (
                    :user_id,
                    :order_id,
                    'proforma',
                    :value,
                    :subtotal,
                    :tax,
                    NOW(),
                    :due_date,
                    'Bank Transfer',
                    'Pending',
                    :proforma_number,
                    :invoice_number,
                    :description
                )
            ");

            $invoiceStmt->bindValue(':user_id', $service['user_id']);
            $invoiceStmt->bindValue(':order_id', $service['order_id']);
            $invoiceStmt->bindValue(':value', $total);
            $invoiceStmt->bindValue(':subtotal', $subtotal);
            $invoiceStmt->bindValue(':tax', $tax);
            $invoiceStmt->bindValue(':due_date', $due_date);
            $invoiceStmt->bindValue(':proforma_number', $proformaNumber);
            $invoiceStmt->bindValue(':invoice_number', $nextInvoiceNumber);
            $invoiceStmt->bindValue(':description', $description);

            $invoiceStmt->execute();
            $invoiceId = $pdo->lastInsertId();

            // Create invoice item
            $itemStmt = $pdo->prepare("
                INSERT INTO invoice_items (
                    invoice_id,
                    description,
                    quantity,
                    unit_price,
                    total,
                    period
                ) VALUES (
                    :invoice_id,
                    :description,
                    :quantity,
                    :unit_price,
                    :total,
                    :period
                )
            ");

            $itemStmt->bindValue(':invoice_id', $invoiceId);
            $itemStmt->bindValue(':description', $description);
            $itemStmt->bindValue(':quantity', 1);
            $itemStmt->bindValue(':unit_price', $subtotal);
            $itemStmt->bindValue(':total', $subtotal);
            $itemStmt->bindValue(':period', $service['payment_period'] ?? 'Monthly');

            $itemStmt->execute();

            // Log to activity log
            $activityStmt = $pdo->prepare("
                INSERT INTO activity_log (
                    user_id,
                    action,
                    description,
                    user_name,
                    timestamp,
                    activity_type,
                    invoice_number,
                    proforma_number
                ) VALUES (
                    :user_id,
                    'Test Invoice Generated',
                    :description,
                    :user_name,
                    NOW(),
                    'billing',
                    :invoice_number,
                    :proforma_number
                )
            ");

            $userName = trim($service['first_name'] . ' ' . $service['last_name']);
            if (empty($userName)) {
                $userName = $service['company_name'] ?? $service['user_email'];
            }

            $activityStmt->bindValue(':user_id', $service['user_id']);
            $activityStmt->bindValue(':description', "TEST - Automatic invoice generation");
            $activityStmt->bindValue(':user_name', $userName);
            $activityStmt->bindValue(':invoice_number', $nextInvoiceNumber);
            $activityStmt->bindValue(':proforma_number', $proformaNumber);

            $activityStmt->execute();

            // Commit transaction
            $pdo->commit();

            $stats['invoices_generated']++;
            echo "Successfully generated test invoice #$nextInvoiceNumber (Proforma: $proformaNumber) for service ID: " . $service['id'] . "\n";

            // Ask if user wants to send a test email
            echo "Do you want to send a test email notification for this invoice? (y/n): ";
            $sendEmail = trim(fgets(STDIN));

            if (strtolower($sendEmail) === 'y' || strtolower($sendEmail) === 'yes') {
                try {
                    echo "Sending test email to " . $service['user_email'] . "...\n";

                    // Make sure we have the PHPMailer library
                    if (!file_exists('/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/PHPMailer.php')) {
                        echo "PHPMailer not found. Expected at /var/www/html/New/admin/vendor/phpmailer/\n";
                    } else {
                        $emailResult = sendInvoiceGeneratedEmail($invoiceId);

                        if ($emailResult) {
                            echo "Successfully sent email notification for invoice #$nextInvoiceNumber to " . $service['user_email'] . "\n";
                        } else {
                            echo "Failed to send email notification for invoice #$nextInvoiceNumber to " . $service['user_email'] . "\n";
                        }
                    }
                } catch (Exception $emailError) {
                    echo "Error sending email notification: " . $emailError->getMessage() . "\n";
                    echo "Stack trace: " . $emailError->getTraceAsString() . "\n";
                }
            }

        } catch (Exception $e) {
            // Rollback transaction on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }

            $stats['errors']++;
            echo "Error generating test invoice for service ID " . $service['id'] . ": " . $e->getMessage() . "\n";
        }
    }

    // Summary
    echo "\nTest invoice generation completed.\n";
    echo "Services checked: " . $stats['services_checked'] . "\n";
    echo "Invoices generated: " . $stats['invoices_generated'] . "\n";
    echo "Errors: " . $stats['errors'] . "\n";

} catch (Exception $e) {
    echo "Fatal error in test invoice generation script: " . $e->getMessage() . "\n";
    exit(1);
}

exit(0);
