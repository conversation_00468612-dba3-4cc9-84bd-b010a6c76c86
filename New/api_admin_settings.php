<?php
require_once("auth_functions.php");

// Function to create email_templates table if it doesn't exist
function createEmailTemplatesTable() {
  global $pdo;

  try {
    // Check if the table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'email_templates'");
    if ($stmt->rowCount() == 0) {
      // Create the email_templates table
      $pdo->exec("CREATE TABLE `email_templates` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `type` enum('password_reset', 'server_details', 'invoice_generated', 'invoice_paid', 'ticket_reply') NOT NULL,
        `subject` varchar(255) NOT NULL,
        `content` text NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `type` (`type`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

      // Insert default templates
      $templates = [
        [
          'type' => 'password_reset',
          'subject' => 'Password Reset Request',
          'content' => '<p>Dear {$client_name},</p>
<p><strong>THIS E-MAIL CONTAINS SENSITIVE INFORMATION. PLEASE KEEP IT SAFE.</strong></p>
<p>You have requested to reset your password. Please use the following temporary password to log in:</p>
<p style="font-size: 16px; font-weight: bold; padding: 10px; background-color: #f0f0f0; border: 1px solid #ddd; text-align: center;">{$password}</p>
<p>After logging in, please change your password immediately for security reasons.</p>
<hr>
<p>If you did not request this password reset, please contact our support team immediately.</p>'
        ],
        [
          'type' => 'server_details',
          'subject' => 'Your Server Details',
          'content' => '<p>Dear {$client_name},</p>
<p><strong>THIS E-MAIL CONTAINS SENSITIVE INFORMATION. PLEASE KEEP IT SAFE.<br>
WE RECOMMEND YOU TO CHANGE YOUR ROOT PASSWORD ON FIRST LOGIN.</strong></p>
<p>Your new server is now online and ready to use.</p>
<h3>Server Details</h3>
<hr>
<p>{$service_product_name}</p>
<p><strong>ServerName:</strong> {$service_domain}<br>
<strong>Main IP:</strong> {$service_dedicated_ip}<br>
<strong>Root pass:</strong> {$service_password}</p>
<p><strong>Additional IP addresses:</strong> {$service_assigned_ips}</p>
<p>Please note that under any circumstances you are NOT ALLOWED to use this service for:</p>
<ul>
<li>Anything than can result in Spamhaus listing</li>
<li>Phishing Sites / Scam Sites (ex: Ebay/Paypal,CC/Password Scam sites)</li>
<li>Mailbombers or any sort of spam sites</li>
<li>DDoS originations</li>
<li>Spam email</li>
<li>Netscan / Hack programs and archives</li>
<li>Malicious Scripts (ex: originate DDoS attacks or hack attempts)</li>
<li>Botnet/Doorway/Carding</li>
</ul>
<p>If any illegal activity is detected or reported, your server will be suspended with no refund right.</p>
<h3>Support</h3>
<hr>
<p>If you need assistance in accessing your server or if you have any issues, don\'t hesitate to contact our technical support staff by opening a ticket here:</p>'
        ],
        [
          'type' => 'invoice_generated',
          'subject' => 'Invoice #{$invoice_num} Generated',
          'content' => '<p>Dear {$client_name},</p>
<p>This is a notice that an invoice has been generated on {$invoice_date_created}.</p>
<p>Your payment method is: {$invoice_payment_method}</p>
<p><strong>Invoice #{$invoice_num}</strong><br>
<strong>Amount Due:</strong> {$invoice_total}<br>
<strong>Due Date:</strong> {$invoice_date_due}</p>
<p><strong>Invoice Items</strong></p>
<div>{$invoice_html_contents}</div>
<hr>
<p>You can login to your client area to view and pay the invoice at <a href="{$invoice_link}">{$invoice_link}</a></p>'
        ],
        [
          'type' => 'invoice_paid',
          'subject' => 'Payment Confirmation - Invoice #{$invoice_num}',
          'content' => '<p>Dear {$client_name},</p>
<p>We have received your payment for invoice #{$invoice_num}. Thank you!</p>
<p><strong>Invoice Number:</strong> {$invoice_num}<br>
<strong>Amount Paid:</strong> {$invoice_total}<br>
<strong>Payment Date:</strong> {$payment_date}</p>
<p><strong>Invoice Items</strong></p>
<div>{$invoice_html_contents}</div>
<hr>
<p>If you have any questions about your payment, please contact our support team.</p>'
        ],
        [
          'type' => 'ticket_reply',
          'subject' => 'New Reply to Your Support Ticket #{$ticket_id}',
          'content' => '<p>Dear {$client_name},</p>
<p>A new reply has been added to your support ticket.</p>
<p><strong>Ticket ID:</strong> #{$ticket_id}<br>
<strong>Subject:</strong> {$ticket_subject}<br>
<strong>Status:</strong> {$ticket_status}</p>
<p><strong>Reply from:</strong> {$staff_name}</p>
<div style="padding: 10px; background-color: #f5f5f5; border: 1px solid #ddd; border-radius: 4px; margin: 10px 0;">
{$message_content}
</div>
<hr>
<p>You can view the full ticket and reply by logging into your account.</p>
<p>Please do not reply to this email. To respond to this ticket, please log in to your account and reply through our support system.</p>'
        ]
      ];

      $stmt = $pdo->prepare("INSERT INTO email_templates (type, subject, content) VALUES (:type, :subject, :content)");

      foreach ($templates as $template) {
        $stmt->bindValue(':type', $template['type']);
        $stmt->bindValue(':subject', $template['subject']);
        $stmt->bindValue(':content', $template['content']);
        $stmt->execute();
      }

      error_log("Created email_templates table and inserted default templates");
    }
  } catch (Exception $e) {
    error_log("Error creating email_templates table: " . $e->getMessage());
    throw $e;
  }
}

// Use Composer autoloader if available - try multiple possible paths
$autoloaderPaths = [
  __DIR__ . '/admin/vendor/autoload.php',
  __DIR__ . '/../admin/vendor/autoload.php',
  __DIR__ . '/vendor/autoload.php',
  'admin/vendor/autoload.php',
  'vendor/autoload.php'
];

$autoloaderLoaded = false;
foreach ($autoloaderPaths as $path) {
  if (file_exists($path)) {
    require $path;
    error_log("PHPMailer: Loaded autoloader from $path");
    $autoloaderLoaded = true;
    break;
  }
}

if (!$autoloaderLoaded) {
  error_log("PHPMailer: Autoloader not found, trying manual includes");
  // Manual PHPMailer includes if Composer is not used
  $manualPaths = [
    __DIR__ . '/PHPMailer/src/',
    __DIR__ . '/../PHPMailer/src/',
    'PHPMailer/src/'
  ];

  $manualLoaded = false;
  foreach ($manualPaths as $basePath) {
    if (file_exists($basePath . 'Exception.php')) {
      require $basePath . 'Exception.php';
      require $basePath . 'PHPMailer.php';
      require $basePath . 'SMTP.php';
      error_log("PHPMailer: Loaded manual includes from $basePath");
      $manualLoaded = true;
      break;
    }
  }

  if (!$manualLoaded) {
    error_log("PHPMailer: Could not find PHPMailer files in any location");
  }
}

// Import PHPMailer classes into the global namespace
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Helper function to send emails with signature
function sendEmailWithSignature($to, $subject, $message, $isHtml = false) {
  global $pdo;

  try {
    // Ensure the table exists
    createEmailSettingsTable();

    // Get email settings from database
    $stmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");

    if ($stmt->rowCount() == 0) {
      throw new Exception('Email settings not configured');
    }

    $settings = $stmt->fetch(PDO::FETCH_ASSOC);

    // Check if email sending is enabled
    if (!$settings['enabled']) {
      throw new Exception('Email sending is disabled in settings');
    }

    // For Google provider, always set the Gmail SMTP settings
    if ($settings['provider'] === 'google') {
      $settings['smtp_host'] = 'smtp.gmail.com';
      $settings['smtp_port'] = '587';
      $settings['smtp_encryption'] = 'tls';
    }

    // Set sender details
    $fromName = $settings['from_name'] ? $settings['from_name'] : 'Admin System';
    $fromEmail = $settings['from_email'] ? $settings['from_email'] : '<EMAIL>';
    $replyTo = !empty($settings['reply_to']) ? $settings['reply_to'] : '';

    // Check if signature is enabled and add it to the message
    $useHtmlMessage = $isHtml;
    $htmlMessage = $message;

    if (!empty($settings['use_signature']) && !empty($settings['signature'])) {
      // If we're using a signature, we need to use HTML email
      $useHtmlMessage = true;

      if ($isHtml) {
        // If the message is already HTML, add the signature with a divider
        $htmlMessage .= "<hr style='margin-top: 20px; margin-bottom: 20px; border: 0; border-top: 1px solid #eee;'>";
        $htmlMessage .= $settings['signature'];
      } else {
        // If the message is plain text, convert it to HTML and add the signature
        $htmlMessage = "<p>" . nl2br(htmlspecialchars($message)) . "</p>";
        $htmlMessage .= "<hr style='margin-top: 20px; margin-bottom: 20px; border: 0; border-top: 1px solid #eee;'>";
        $htmlMessage .= $settings['signature'];
      }
    }

    // Create a new PHPMailer instance
    $mail = new PHPMailer(true); // true enables exceptions

    // Server settings
    $mail->SMTPDebug = SMTP::DEBUG_OFF; // Set to DEBUG_SERVER for troubleshooting
    $mail->Debugoutput = function($str, $level) {
      error_log("PHPMailer Debug: $str");
    };

    // Always use SMTP
    $mail->isSMTP();
    $mail->Host = $settings['smtp_host'];
    $mail->SMTPAuth = true;
    $mail->Username = $settings['smtp_username'];
    $mail->Password = $settings['smtp_password'];

    // Set encryption type
    if ($settings['smtp_encryption'] === 'ssl') {
      $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // SSL
      $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 465;
    } else {
      $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS; // TLS
      $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 587;
    }

    // Recipients
    $mail->setFrom($fromEmail, $fromName);
    $mail->addAddress($to);

    if ($replyTo) {
      $mail->addReplyTo($replyTo);
    }

    // Content
    if ($useHtmlMessage) {
      $mail->isHTML(true);
      $mail->Subject = $subject;
      $mail->Body = $htmlMessage;
      $mail->AltBody = strip_tags(str_replace('<br>', "\n", $message)); // Plain text alternative
    } else {
      $mail->isHTML(false);
      $mail->Subject = $subject;
      $mail->Body = $message;
    }

    // Send the email
    $mail->send();
    error_log("Email successfully sent to: $to");

    return true;
  } catch (Exception $e) {
    error_log("Error sending email: " . $e->getMessage());
    return false;
  }
}

// Create email_settings table if it doesn't exist
function createEmailSettingsTable() {
  global $pdo;

  try {
    // Check if the table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'email_settings'");
    if ($stmt->rowCount() == 0) {
      // Create the email_settings table
      $pdo->exec("CREATE TABLE `email_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `enabled` tinyint(1) NOT NULL DEFAULT '0',
        `provider` varchar(50) NOT NULL DEFAULT 'google',
        `client_id` varchar(255) DEFAULT NULL,
        `client_secret` varchar(255) DEFAULT NULL,
        `redirect_uri` varchar(255) DEFAULT NULL,
        `smtp_host` varchar(255) DEFAULT NULL,
        `smtp_port` varchar(10) DEFAULT '587',
        `smtp_username` varchar(255) DEFAULT NULL,
        `smtp_password` varchar(255) DEFAULT NULL,
        `smtp_encryption` varchar(10) DEFAULT 'tls',
        `from_email` varchar(255) DEFAULT NULL,
        `from_name` varchar(255) DEFAULT 'Admin System',
        `reply_to` varchar(255) DEFAULT NULL,
        `use_signature` tinyint(1) NOT NULL DEFAULT '0',
        `signature` text DEFAULT NULL,
        `authorized` tinyint(1) NOT NULL DEFAULT '0',
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

      // Insert default record
      $pdo->exec("INSERT INTO `email_settings`
        (`enabled`, `provider`, `smtp_port`, `smtp_encryption`, `from_name`, `use_signature`, `signature`)
        VALUES (0, 'google', '587', 'tls', 'Admin System', 1, '<p>Best regards,<br/>The Team</p>')");

      error_log("Created email_settings table and inserted default record");
    }
  } catch (Exception $e) {
    error_log("Error creating email_settings table: " . $e->getMessage());
    throw $e;
  }
}

// Get email settings
if($_GET['f'] == 'get_email_settings'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Ensure the table exists
    createEmailSettingsTable();

    // Get the origin from the request headers
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : 'https://test.x-zoneit.ro';
    $redirectUri = $origin . '/admin/email-settings/oauth-callback';

    // Get settings from database
    $stmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");

    if ($stmt->rowCount() > 0) {
      $row = $stmt->fetch(PDO::FETCH_ASSOC);

      $settings = [
        'enabled' => (bool)$row['enabled'],
        'provider' => $row['provider'],
        // Google OAuth settings
        'clientId' => $row['client_id'] ?? '',
        'clientSecret' => $row['client_secret'] ?? '',
        'redirectUri' => $row['redirect_uri'] ?? $redirectUri,
        // SMTP settings
        'smtpHost' => $row['smtp_host'] ?? '',
        'smtpPort' => $row['smtp_port'] ?? '587',
        'smtpUsername' => $row['smtp_username'] ?? '',
        'smtpPassword' => $row['smtp_password'] ?? '',
        'smtpEncryption' => $row['smtp_encryption'] ?? 'tls',
        // Common settings
        'fromEmail' => $row['from_email'] ?? '',
        'fromName' => $row['from_name'] ?? 'Admin System',
        'replyTo' => $row['reply_to'] ?? '',
        // Signature settings
        'useSignature' => (bool)($row['use_signature'] ?? true),
        'signature' => $row['signature'] ?? '<p>Best regards,<br/>The Team</p>',
        'authorized' => (bool)$row['authorized']
      ];
    } else {
      // Fallback to default values if no record exists
      $settings = [
        'enabled' => false,
        'provider' => 'google',
        // Google OAuth settings
        'clientId' => '',
        'clientSecret' => '',
        'redirectUri' => $redirectUri,
        // SMTP settings
        'smtpHost' => '',
        'smtpPort' => '587',
        'smtpUsername' => '',
        'smtpPassword' => '',
        'smtpEncryption' => 'tls',
        // Common settings
        'fromEmail' => '',
        'fromName' => 'Admin System',
        'replyTo' => '',
        // Signature settings
        'useSignature' => true,
        'signature' => '<p>Best regards,<br/>The Team</p>',
        'authorized' => false
      ];
    }

    // Return settings as JSON
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'settings' => $settings
    ]);

  } catch (Exception $e) {
    error_log("Error in get_email_settings: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to fetch email settings: ' . $e->getMessage()
    ]);
  }
}

// Save email settings
if($_GET['f'] == 'save_email_settings'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request body
    $requestBody = json_decode(file_get_contents('php://input'), true);
    $settings = $requestBody['settings'] ?? null;

    if (!$settings) {
      throw new Exception('No settings provided');
    }

    // Ensure the table exists
    createEmailSettingsTable();

    // Debug: Log the incoming settings
    error_log("Saving email settings: " . json_encode($settings));

    // Prepare data for database
    $data = [
      'enabled' => isset($settings['enabled']) ? (int)$settings['enabled'] : 0,
      'provider' => $settings['provider'] ?? 'google',
      'client_id' => $settings['clientId'] ?? null,
      'client_secret' => $settings['clientSecret'] ?? null,
      'redirect_uri' => $settings['redirectUri'] ?? null,
      'smtp_host' => $settings['smtpHost'] ?? null,
      'smtp_port' => $settings['smtpPort'] ?? '587',
      'smtp_username' => $settings['smtpUsername'] ?? null,
      'smtp_password' => $settings['smtpPassword'] ?? null,
      'smtp_encryption' => $settings['smtpEncryption'] ?? 'tls',
      'from_email' => $settings['fromEmail'] ?? null,
      'from_name' => $settings['fromName'] ?? 'Admin System',
      'reply_to' => $settings['replyTo'] ?? null,
      'use_signature' => isset($settings['useSignature']) ? (int)$settings['useSignature'] : 0,
      'signature' => $settings['signature'] ?? '<p>Best regards,<br/>The Team</p>',
      'authorized' => isset($settings['authorized']) ? (int)$settings['authorized'] : 0
    ];

    // Debug: Log the prepared data
    error_log("Prepared data for database: " . json_encode($data));

    // Check if we need to update or insert
    $stmt = $pdo->query("SELECT COUNT(*) FROM email_settings");
    $count = $stmt->fetchColumn();

    if ($count > 0) {
      // Update existing record
      $sql = "UPDATE email_settings SET
              enabled = :enabled,
              provider = :provider,
              client_id = :client_id,
              client_secret = :client_secret,
              redirect_uri = :redirect_uri,
              smtp_host = :smtp_host,
              smtp_port = :smtp_port,
              smtp_username = :smtp_username,
              smtp_password = :smtp_password,
              smtp_encryption = :smtp_encryption,
              from_email = :from_email,
              from_name = :from_name,
              reply_to = :reply_to,
              use_signature = :use_signature,
              signature = :signature,
              authorized = :authorized
              WHERE id = 1";
    } else {
      // Insert new record
      $sql = "INSERT INTO email_settings
              (enabled, provider, client_id, client_secret, redirect_uri,
               smtp_host, smtp_port, smtp_username, smtp_password, smtp_encryption,
               from_email, from_name, reply_to, use_signature, signature, authorized)
              VALUES
              (:enabled, :provider, :client_id, :client_secret, :redirect_uri,
               :smtp_host, :smtp_port, :smtp_username, :smtp_password, :smtp_encryption,
               :from_email, :from_name, :reply_to, :use_signature, :signature, :authorized)";
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($data);

    // Log the success
    error_log("Email settings saved successfully by admin ID: $admin_id");

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Email settings saved successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in save_email_settings: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to save email settings: ' . $e->getMessage()
    ]);
  }
}

// Handle OAuth callback
if($_GET['f'] == 'oauth_callback'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request body
    $requestBody = json_decode(file_get_contents('php://input'), true);
    $code = $requestBody['code'] ?? null;

    if (!$code) {
      throw new Exception('No authorization code provided');
    }

    // Ensure the table exists
    createEmailSettingsTable();

    // Get email settings from database
    $stmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");

    if ($stmt->rowCount() == 0) {
      throw new Exception('Email settings not configured');
    }

    $settings = $stmt->fetch(PDO::FETCH_ASSOC);

    // In a real implementation, you would exchange the code for tokens
    // For now, we'll just mark as authorized

    // Update the authorized status in the database
    $stmt = $pdo->prepare("UPDATE email_settings SET authorized = 1 WHERE id = 1");
    $stmt->execute();

    error_log("Google OAuth authorization successful for admin ID: $admin_id");

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Google OAuth authorization successful'
    ]);

  } catch (Exception $e) {
    error_log("Error in oauth_callback: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to complete OAuth authorization: ' . $e->getMessage()
    ]);
  }
}

// Send test email
if($_GET['f'] == 'send_test_email'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request body
    $requestBody = json_decode(file_get_contents('php://input'), true);
    $email = $requestBody['email'] ?? null;

    if (!$email) {
      throw new Exception('No email address provided');
    }

    // Ensure the table exists
    createEmailSettingsTable();

    // Get email settings from database
    $stmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");

    if ($stmt->rowCount() == 0) {
      throw new Exception('Email settings not configured');
    }

    $settings = $stmt->fetch(PDO::FETCH_ASSOC);

    // For test emails, we'll allow sending even if email is disabled in settings
    // This helps users test their configuration before enabling it system-wide
    $originalEnabledSetting = $settings['enabled'];
    $settings['enabled'] = 1;

    // Temporarily update the settings to allow sending test emails
    $stmt = $pdo->prepare("UPDATE email_settings SET enabled = 1 WHERE id = 1");
    $stmt->execute();

    // Prepare email details
    $subject = "Test Email from Admin System";
    $message = "This is a test email sent from your Admin System.\n\n";
    $message .= "If you received this email, your email settings are configured correctly.";

    // Send the test email using our helper function
    $result = sendEmailWithSignature($email, $subject, $message, false);

    // Restore the original enabled setting
    $stmt = $pdo->prepare("UPDATE email_settings SET enabled = :enabled WHERE id = 1");
    $stmt->bindParam(':enabled', $originalEnabledSetting, PDO::PARAM_INT);
    $stmt->execute();

    if ($result) {
      // Update the database to mark as authorized if not already
      if (!$settings['authorized']) {
        $stmt = $pdo->prepare("UPDATE email_settings SET authorized = 1 WHERE id = 1");
        $stmt->execute();
        error_log("Updated authorized status to true");
      }

      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'Test email sent successfully'
      ]);
    } else {
      throw new Exception('Failed to send test email. Please check your email settings.');
    }
  } catch (Exception $e) {
    error_log("Error in send_test_email: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to send test email: ' . $e->getMessage()
    ]);
  }
}

// Get all email templates
if($_GET['f'] == 'get_email_templates'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Ensure the table exists
    createEmailTemplatesTable();

    // Get templates from database
    $stmt = $pdo->query("SELECT * FROM email_templates ORDER BY type");
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Return templates as JSON
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'templates' => $templates
    ]);

  } catch (Exception $e) {
    error_log("Error in get_email_templates: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to fetch email templates: ' . $e->getMessage()
    ]);
  }
}

// Get a specific email template by type
if($_GET['f'] == 'get_email_template'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request body
    $requestBody = json_decode(file_get_contents('php://input'), true);
    $type = $requestBody['type'] ?? null;

    if (!$type) {
      throw new Exception('No template type provided');
    }

    // Ensure the table exists
    createEmailTemplatesTable();

    // Get template from database
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE type = :type");
    $stmt->bindValue(':type', $type);
    $stmt->execute();

    if ($stmt->rowCount() == 0) {
      throw new Exception('Template not found');
    }

    $template = $stmt->fetch(PDO::FETCH_ASSOC);

    // Return template as JSON
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'template' => $template
    ]);

  } catch (Exception $e) {
    error_log("Error in get_email_template: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to fetch email template: ' . $e->getMessage()
    ]);
  }
}

// Save/update an email template
if($_GET['f'] == 'save_email_template'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request body
    $requestBody = json_decode(file_get_contents('php://input'), true);
    $template = $requestBody['template'] ?? null;

    if (!$template || !isset($template['type']) || !isset($template['subject']) || !isset($template['content'])) {
      throw new Exception('Missing required template fields');
    }

    // Ensure the table exists
    createEmailTemplatesTable();

    // Check if template exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM email_templates WHERE type = :type");
    $stmt->bindValue(':type', $template['type']);
    $stmt->execute();
    $exists = $stmt->fetchColumn() > 0;

    if ($exists) {
      // Update existing template
      $stmt = $pdo->prepare("UPDATE email_templates SET subject = :subject, content = :content WHERE type = :type");
    } else {
      // Insert new template
      $stmt = $pdo->prepare("INSERT INTO email_templates (type, subject, content) VALUES (:type, :subject, :content)");
    }

    $stmt->bindValue(':type', $template['type']);
    $stmt->bindValue(':subject', $template['subject']);
    $stmt->bindValue(':content', $template['content']);
    $stmt->execute();

    // Log the success
    error_log("Email template '{$template['type']}' saved successfully by admin ID: $admin_id");

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Email template saved successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in save_email_template: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to save email template: ' . $e->getMessage()
    ]);
  }
}
?>
