# GLOBAL CONFIGURATION
default-lease-time 600;
max-lease-time 7200;
authoritative;
ddns-update-style none;
option domain-name "x-zoneit.ro";  # Fixed domain format
option domain-name-servers *******, *******;
option netbios-name-servers *************;
next-server *************;
option space pxelinux;
option pxelinux.magic code 208 = string;
option pxelinux.configfile code 209 = text;
option pxelinux.pathprefix code 210 = text;
option pxelinux.reboottime code 211 = unsigned integer 32;
option space ipxe;
option ipxe-encap-opts code 175 = encapsulate ipxe;
option ipxe.priority code 1 = signed integer 8;
option client-architecture code 93 = unsigned integer 16;
option OPT43 code 43 = text;
option OPT150 code 150 = ip-address;

# SERVER'S LOCAL NETWORK (DUMMY CONFIG TO SATISFY DHCPD)
subnet ************* netmask *************** {
    option subnet-mask ***************;
    option routers *************;      # Replace with your actual gateway
    range ************* *************;  # Range that doesn't conflict with server
    ignore booting;                   # Don't serve DHCP on this network
}

# PXE NETWORK CONFIGURATION
shared-network pxe {
    # First PXE subnet (XR-BL1S4)
    subnet ************ netmask *************** {
        option subnet-mask ***************;
        option routers ************;
        option broadcast-address ************;
        range ************ ************;  # Single-IP range for fixed address
        
        host XR-BL1S4 {
            hardware ethernet 00:8c:fa:fb:db:18;
            fixed-address ************;
            option host-name "XR-BL1S4";
            
            # Boot logic based on client architecture and user-class
            if exists user-class and option user-class = "iPXE" {
                filename "http://auto.x-zoneit.ro/servers/XR-BL1S4/boot.ipxe";
            } elsif option client-architecture = 00:07 {  # UEFI x64
                filename "snponly.efi";
            } elsif option client-architecture = 00:00 {  # Legacy BIOS
                filename "undionly.kpxe";
            } else {
                filename "undionly.kpxe";
            }
        }
    }
    
    # Second PXE subnet (XR-BL1S3)
    subnet ************* netmask *************** {
        option subnet-mask ***************;
        option routers *************;
        option broadcast-address *************;
        range ************* *************;  # Safe IP range
        
        host XR-BL1S3 {
            hardware ethernet 00:8c:fa:fb:de:ba;
            fixed-address *************;  # Moved to safe IP
            option host-name "XR-BL1S3";
            
            # Boot logic based on client architecture and user-class
            if exists user-class and option user-class = "iPXE" {
                filename "http://auto.x-zoneit.ro/servers/XR-BL1S3/boot.ipxe";
            } elsif option client-architecture = 00:07 {  # UEFI x64
                filename "snponly.efi";
            } elsif option client-architecture = 00:00 {  # Legacy BIOS
                filename "undionly.kpxe";
            } else {
                filename "undionly.kpxe";
            }
        }
    }
}