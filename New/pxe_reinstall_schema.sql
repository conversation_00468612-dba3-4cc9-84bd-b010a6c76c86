-- SQL Schema for PXE Reinstall Log Table
-- This table logs all PXE reinstall operations for audit purposes

CREATE TABLE IF NOT EXISTS `server_reinstall_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server_id` int(11) NOT NULL,
  `server_type` enum('dedicated','blade') NOT NULL DEFAULT 'dedicated',
  `os_id` int(11) NOT NULL,
  `os_template` varchar(255) DEFAULT NULL,
  `initiated_by` int(11) NOT NULL COMMENT 'Admin user ID who initiated the reinstall',
  `initiated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ipmi_address` varchar(45) NOT NULL,
  `custom_password_used` tinyint(1) NOT NULL DEFAULT 0,
  `status` enum('initiated','dhcp_configured','pxe_configured','in_progress','completed','failed') NOT NULL DEFAULT 'initiated',
  `completion_time` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `dhcp_config` json DEFAULT NULL COMMENT 'DHCP reservation configuration',
  `network_config` json DEFAULT NULL COMMENT 'Network configuration for installation',
  `pxe_config` json DEFAULT NULL COMMENT 'PXE server configuration',
  `kickstart_content` longtext DEFAULT NULL COMMENT 'Generated kickstart/preseed file content',
  PRIMARY KEY (`id`),
  KEY `idx_server_id_type` (`server_id`, `server_type`),
  KEY `idx_initiated_by` (`initiated_by`),
  KEY `idx_initiated_at` (`initiated_at`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add index for faster queries by server
ALTER TABLE `server_reinstall_log` 
  ADD INDEX `idx_server_reinstall` (`server_id`, `server_type`, `initiated_at` DESC);

-- Optional: Add foreign key constraints if your admin users table exists
-- ALTER TABLE `server_reinstall_log` 
--   ADD CONSTRAINT `fk_reinstall_admin` 
--   FOREIGN KEY (`initiated_by`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE;

-- Add columns to server tables for tracking last reinstall (optional)
-- These columns help track when a server was last reinstalled

-- For dedicated servers table
-- ALTER TABLE `inventory_dedicated_servers` 
--   ADD COLUMN `last_reinstall` timestamp NULL DEFAULT NULL,
--   ADD COLUMN `reinstall_count` int(11) NOT NULL DEFAULT 0;

-- For blade servers table  
-- ALTER TABLE `blade_server_inventory` 
--   ADD COLUMN `last_reinstall` timestamp NULL DEFAULT NULL,
--   ADD COLUMN `reinstall_count` int(11) NOT NULL DEFAULT 0;

-- Update status options to include 'Installing' if not already present
-- ALTER TABLE `inventory_dedicated_servers` 
--   MODIFY COLUMN `status` enum('Available','In Use','Defect','Installing') NOT NULL DEFAULT 'Available';

-- ALTER TABLE `blade_server_inventory` 
--   MODIFY COLUMN `status` enum('Available','In Use','Defect','Installing') NOT NULL DEFAULT 'Available'; 

-- PXE Reinstall Schema
-- This file contains the database schema needed for PXE reinstall functionality

-- Update the operating_systems_vps table if it doesn't have all required columns
ALTER TABLE `operating_systems_vps` 
ADD COLUMN IF NOT EXISTS `template_file` varchar(100) DEFAULT NULL COMMENT 'Template file identifier for PXE boot',
ADD COLUMN IF NOT EXISTS `iso_filename` varchar(255) DEFAULT NULL COMMENT 'ISO filename in autoinstall/os directory',
ADD COLUMN IF NOT EXISTS `kernel_path` varchar(255) DEFAULT 'vmlinuz' COMMENT 'Kernel file path relative to OS directory',
ADD COLUMN IF NOT EXISTS `initrd_path` varchar(255) DEFAULT 'initrd' COMMENT 'Initrd file path relative to OS directory';

-- Insert default Ubuntu 22.04 OS if it doesn't exist
INSERT IGNORE INTO `operating_systems_vps` 
(`id`, `solus_os_id`, `name`, `category`, `is_active`, `display_order`, `template_file`, `iso_filename`, `kernel_path`, `initrd_path`) 
VALUES 
(1, 'ubuntu-22.04-server', 'Ubuntu Server 22.04', 'Linux', 1, 1, 'ubuntu-22.04-server', 'ubuntu-22.04.5-live-server-amd64.iso', 'vmlinuz', 'initrd');

-- Table to track PXE reinstall sessions
CREATE TABLE IF NOT EXISTS `pxe_reinstall_sessions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `server_id` int NOT NULL,
  `server_type` enum('dedicated','blade') NOT NULL DEFAULT 'dedicated',
  `server_label` varchar(255) NOT NULL,
  `mac_address` varchar(17) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `hostname` varchar(255) NOT NULL,
  `os_template` varchar(100) NOT NULL,
  `status` enum('pending','active','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
  `dhcp_configured` tinyint(1) DEFAULT 0,
  `files_created` tinyint(1) DEFAULT 0,
  `started_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  `error_message` text,
  `network_config` json COMMENT 'Network configuration used for reinstall',
  `notes` text,
  PRIMARY KEY (`id`),
  KEY `idx_server` (`server_id`, `server_type`),
  KEY `idx_status` (`status`),
  KEY `idx_started` (`started_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table to track DHCP entries for cleanup
CREATE TABLE IF NOT EXISTS `pxe_dhcp_entries` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_id` int NOT NULL,
  `mac_address` varchar(17) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `hostname` varchar(255) NOT NULL,
  `server_label` varchar(255) NOT NULL,
  `dhcp_entry_added` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `dhcp_entry_removed` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `fk_session` (`session_id`),
  KEY `idx_mac` (`mac_address`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add foreign key constraint after both tables are created
ALTER TABLE `pxe_dhcp_entries` 
ADD CONSTRAINT `fk_dhcp_session` 
FOREIGN KEY (`session_id`) REFERENCES `pxe_reinstall_sessions` (`id`) ON DELETE CASCADE;

-- Add indexes to blade_server_inventory for better performance
ALTER TABLE `blade_server_inventory` 
ADD INDEX IF NOT EXISTS `idx_label` (`label`),
ADD INDEX IF NOT EXISTS `idx_mac` (`mac`),
ADD INDEX IF NOT EXISTS `idx_ipmi` (`ipmi`),
ADD INDEX IF NOT EXISTS `idx_main_ip` (`main_ip`),
ADD INDEX IF NOT EXISTS `idx_status` (`status`);

-- Add indexes to dedicated_server_inventory if it exists
-- Note: This assumes you have a similar table for dedicated servers
CREATE TABLE IF NOT EXISTS `dedicated_server_inventory` (
  `id` int NOT NULL AUTO_INCREMENT,
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `hostname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cpu` int DEFAULT NULL COMMENT 'Reference to dedicated_cpu.id',
  `ram` int DEFAULT NULL COMMENT 'RAM configuration ID',
  `switch_id` int DEFAULT NULL,
  `port1` int DEFAULT NULL,
  `port1_speed` int DEFAULT NULL,
  `port2` int DEFAULT NULL,
  `port2_speed` int DEFAULT NULL,
  `port3` int DEFAULT NULL,
  `port3_speed` int DEFAULT NULL,
  `port4` int DEFAULT NULL,
  `port4_speed` int DEFAULT NULL,
  `bay1` int DEFAULT NULL,
  `bay2` int DEFAULT NULL,
  `bay3` int DEFAULT NULL,
  `bay4` int DEFAULT NULL,
  `bay5` int DEFAULT NULL,
  `bay6` int DEFAULT NULL,
  `bay7` int DEFAULT NULL,
  `bay8` int DEFAULT NULL,
  `bay9` int DEFAULT NULL,
  `bay10` int DEFAULT NULL,
  `bay11` int DEFAULT NULL,
  `bay12` int DEFAULT NULL,
  `bay13` int DEFAULT NULL,
  `bay14` int DEFAULT NULL,
  `bay15` int DEFAULT NULL,
  `bay16` int DEFAULT NULL,
  `bay17` int DEFAULT NULL,
  `bay18` int DEFAULT NULL,
  `bay19` int DEFAULT NULL,
  `bay20` int DEFAULT NULL,
  `bay21` int DEFAULT NULL,
  `bay22` int DEFAULT NULL,
  `bay23` int DEFAULT NULL,
  `bay24` int DEFAULT NULL,
  `bay25` int DEFAULT NULL,
  `bay26` int DEFAULT NULL,
  `mac` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ipmi` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `root` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'Active',
  `rack_id` int DEFAULT NULL,
  `position` varchar(10) DEFAULT NULL,
  `city_id` int DEFAULT NULL,
  `country_id` int DEFAULT NULL,
  `order_id` int DEFAULT NULL,
  `main_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `additional_ips` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `ipmi_root_pass` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ipmi_user_pass` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `idrac_version` int DEFAULT NULL,
  `password` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `auto_renewal` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `size` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_dedicated_cpu` (`cpu`) USING BTREE,
  KEY `fk_dedicated_ram` (`ram`) USING BTREE,
  KEY `fk_dedicated_switch` (`switch_id`) USING BTREE,
  KEY `fk_dedicated_rack` (`rack_id`) USING BTREE,
  KEY `fk_dedicated_city` (`city_id`) USING BTREE,
  KEY `fk_dedicated_country` (`country_id`) USING BTREE,
  KEY `idx_dedicated_label` (`label`),
  KEY `idx_dedicated_mac` (`mac`),
  KEY `idx_dedicated_ipmi` (`ipmi`),
  KEY `idx_dedicated_main_ip` (`main_ip`),
  KEY `idx_dedicated_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- View to unify server data from both blade and dedicated servers
CREATE OR REPLACE VIEW `unified_server_view` AS
SELECT 
    'blade' as server_type,
    id,
    label,
    hostname,
    mac,
    ipmi,
    ipmi_root_pass,
    ipmi_user_pass,
    main_ip,
    additional_ips,
    status,
    password,
    notes
FROM blade_server_inventory
UNION ALL
SELECT 
    'dedicated' as server_type,
    id,
    label,
    hostname,
    mac,
    ipmi,
    ipmi_root_pass,
    ipmi_user_pass,
    main_ip,
    additional_ips,
    status,
    password,
    notes
FROM dedicated_server_inventory
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'dedicated_server_inventory'); 