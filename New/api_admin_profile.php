<?php
require_once("auth_functions.php");

// Get current admin profile
if($_GET['f'] == 'get_current_admin'){
  try {
    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Log the received data
    error_log("Received get_current_admin data: " . json_encode($data));

    // Manually authenticate admin
    if (empty($data['token'])) {
      throw new Exception('Admin authentication required - no token provided');
    }

    // Additional security: token should be at least 32 chars
    if (strlen($data['token']) < 32) {
      throw new Exception('Admin authentication required - invalid token format');
    }

    // Use a prepared statement to prevent SQL injection
    $auth_stmt = $pdo->prepare("
      SELECT id, last_login, last_ip
      FROM `admins`
      WHERE `last_session` = :token
      AND `last_login` > NOW() - INTERVAL 30 DAY
      LIMIT 1
    ");

    $auth_stmt->bindValue(':token', $data['token']);
    $auth_stmt->execute();

    if ($auth_stmt->rowCount() !== 1) {
      throw new Exception('Admin authentication required - invalid token');
    }

    $row = $auth_stmt->fetch(PDO::FETCH_ASSOC);
    $admin_id = $row['id'];

    // Prepare the query with join to departments
    $sql = "SELECT a.id, a.first_name, a.last_name, a.function, a.picture_url, a.department_id, d.department_name,
            a.email, a.location, a.phone, a.last_login
            FROM admins a
            LEFT JOIN departments d ON a.department_id = d.id
            WHERE a.id = :admin_id";

    // Execute query
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':admin_id', $admin_id);
    $sth->execute();

    // Fetch result
    $admin = $sth->fetch(PDO::FETCH_ASSOC);

    if (!$admin) {
      throw new Exception('Admin not found');
    }

    // Handle picture_url properly
    if (!isset($admin['picture_url']) || empty(trim($admin['picture_url']))) {
      $admin['picture_url'] = '/admin/src/assets/default.png';
      error_log("Admin {$admin_id} has no picture URL, setting to default");
    } else {
      // Ensure the path is properly formatted
      $picture_url = trim($admin['picture_url']);
      error_log("Admin {$admin_id} original picture URL: {$picture_url}");

      // If it doesn't start with /, add it
      if (!str_starts_with($picture_url, '/')) {
        $admin['picture_url'] = '/' . $picture_url;
        error_log("Added leading slash: {$admin['picture_url']}");
      }

      // Check if the file exists on the server
      $full_path = '/var/www/html/New' . $admin['picture_url'];
      $file_exists = file_exists($full_path);

      error_log("Checking if file exists: {$full_path} - Exists: " . ($file_exists ? 'YES' : 'NO'));

      // If file doesn't exist, fallback to default
      if (!$file_exists) {
        error_log("File doesn't exist, falling back to default for admin {$admin_id}");
        $admin['picture_url'] = '/admin/src/assets/default.png';
        
        // Update database to reflect the fallback
        $update_stmt = $pdo->prepare("UPDATE admins SET picture_url = '/admin/src/assets/default.png' WHERE id = :admin_id");
        $update_stmt->bindValue(':admin_id', $admin_id);
        $update_stmt->execute();
      }
    }

    // Add debug info for fields
    error_log("Admin data: " . json_encode($admin));

    // Return admin as JSON
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'admin' => $admin
    ]);

  } catch (Exception $e) {
    error_log("Error in get_current_admin: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to fetch admin profile: ' . $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'delete_profile_picture'){
  try {
    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Log the received data
    error_log("Received delete_profile_picture data: " . json_encode($data));

    // Manually authenticate admin
    if (empty($data['token'])) {
      throw new Exception('Admin authentication required - no token provided');
    }

    // Additional security: token should be at least 32 chars
    if (strlen($data['token']) < 32) {
      throw new Exception('Admin authentication required - invalid token format');
    }

    // Use a prepared statement to prevent SQL injection
    $auth_stmt = $pdo->prepare("
      SELECT id, last_login, last_ip
      FROM `admins`
      WHERE `last_session` = :token
      AND `last_login` > NOW() - INTERVAL 30 DAY
      LIMIT 1
    ");

    $auth_stmt->bindValue(':token', $data['token']);
    $auth_stmt->execute();

    if ($auth_stmt->rowCount() !== 1) {
      throw new Exception('Admin authentication required - invalid token');
    }

    $row = $auth_stmt->fetch(PDO::FETCH_ASSOC);
    $admin_id = $row['id'];

    // Get current picture URL to delete the file
    $get_stmt = $pdo->prepare("SELECT picture_url FROM admins WHERE id = :admin_id");
    $get_stmt->bindValue(':admin_id', $admin_id);
    $get_stmt->execute();
    $current_admin = $get_stmt->fetch(PDO::FETCH_ASSOC);

    if ($current_admin && !empty($current_admin['picture_url'])) {
      $current_picture = $current_admin['picture_url'];
      
      // Only delete if it's not the default image
      if (strpos($current_picture, 'default.png') === false) {
        // Construct the full file path
        $full_path = '/var/www/html/New' . $current_picture;
        
        if (file_exists($full_path)) {
          $delete_result = @unlink($full_path);
          error_log("Deleted old profile picture: $full_path - Result: " . ($delete_result ? 'success' : 'failed'));
        }
      }
    }

    // Update admin profile to use default picture
    $update_stmt = $pdo->prepare("UPDATE admins SET picture_url = '/admin/src/assets/default.png' WHERE id = :admin_id");
    $update_stmt->bindValue(':admin_id', $admin_id);
    $update_stmt->execute();

    error_log("Profile picture reset to default for admin ID: $admin_id");

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Profile picture reset to default',
      'picture_url' => '/admin/src/assets/default.png'
    ]);

  } catch (Exception $e) {
    error_log("Error in delete_profile_picture: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to delete profile picture: ' . $e->getMessage()
    ]);
  }
}

// Update admin profile picture
elseif($_GET['f'] == 'update_profile_picture'){
  try {
    // Get token from POST data for file uploads
    $token = $_POST['token'] ?? '';

    if (empty($token)) {
      error_log("No token provided for profile picture upload");
      throw new Exception('Authentication required');
    }

    // Authenticate admin using token
    $stmt = $pdo->prepare("
      SELECT id
      FROM `admins`
      WHERE `last_session` = :token
      AND `last_login` > NOW() - INTERVAL 30 DAY
      LIMIT 1
    ");

    $stmt->bindValue(':token', $token);
    $stmt->execute();

    if ($stmt->rowCount() !== 1) {
      error_log("Invalid token for profile picture upload: $token");
      throw new Exception('Invalid authentication token');
    }

    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    $admin_id = $row['id'];

    error_log("Authenticated admin ID: $admin_id for profile picture upload");

    // Get current picture URL to delete the old file
    $get_stmt = $pdo->prepare("SELECT picture_url FROM admins WHERE id = :admin_id");
    $get_stmt->bindValue(':admin_id', $admin_id);
    $get_stmt->execute();
    $current_admin = $get_stmt->fetch(PDO::FETCH_ASSOC);

    // Check if file was uploaded
    if (!isset($_FILES['profile_picture']) || $_FILES['profile_picture']['error'] !== UPLOAD_ERR_OK) {
      error_log("File upload error: " . ($_FILES['profile_picture']['error'] ?? 'No file'));
      throw new Exception('No file uploaded or upload error');
    }

    // Get file info
    $file = $_FILES['profile_picture'];
    $file_name = $file['name'];
    $file_tmp = $file['tmp_name'];
    $file_size = $file['size'];
    $file_type = $file['type'];

    error_log("File info: name=$file_name, size=$file_size, type=$file_type");

    // Validate file type
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    if (!in_array($file_type, $allowed_types)) {
      throw new Exception('Invalid file type. Only JPEG, PNG, and GIF are allowed.');
    }

    // Validate file size (max 2MB)
    if ($file_size > 2 * 1024 * 1024) {
      throw new Exception('File size exceeds the limit of 2MB');
    }

    // Set the upload directory path
    $upload_dir = '/var/www/html/New/admin/src/assets/';
    error_log("Using upload directory: $upload_dir");

    // Generate unique filename
    $extension = pathinfo($file_name, PATHINFO_EXTENSION);
    $unique_name = 'admin_' . $admin_id . '_' . time() . '.' . $extension;

    // Create directory structure if it doesn't exist
    if (!file_exists($upload_dir)) {
      $mkdir_result = @mkdir($upload_dir, 0755, true);
      if (!$mkdir_result) {
        error_log("Failed to create directory: $upload_dir");
        throw new Exception('Failed to create upload directory');
      }
    }

    // Check if directory is writable
    if (!is_writable($upload_dir)) {
      @chmod($upload_dir, 0755);
      if (!is_writable($upload_dir)) {
        throw new Exception('Upload directory is not writable');
      }
    }

    // Set full file path
    $full_file_path = $upload_dir . $unique_name;
    error_log("Attempting to save file to: $full_file_path");

    // Try to move the uploaded file
    if (@move_uploaded_file($file_tmp, $full_file_path)) {
      error_log("File saved successfully to: $full_file_path");

      // Set the web-accessible path
      $web_path = '/admin/src/assets/' . $unique_name;
      error_log("Web-accessible path: $web_path");

      // Delete old profile picture if it exists and is not default
      if ($current_admin && !empty($current_admin['picture_url'])) {
        $old_picture = $current_admin['picture_url'];
        
        // Only delete if it's not the default image
        if (strpos($old_picture, 'default.png') === false) {
          $old_full_path = '/var/www/html/New' . $old_picture;
          
          if (file_exists($old_full_path)) {
            $delete_result = @unlink($old_full_path);
            error_log("Deleted old profile picture: $old_full_path - Result: " . ($delete_result ? 'success' : 'failed'));
          }
        }
      }

      // Update admin profile with new picture URL
      $sth = $pdo->prepare("UPDATE admins SET picture_url = :picture_url WHERE id = :admin_id");
      $sth->bindValue(':picture_url', $web_path);
      $sth->bindValue(':admin_id', $admin_id);
      $sth->execute();

      error_log("Database updated with new picture URL for admin ID: $admin_id with path: $web_path");

      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'Profile picture updated successfully',
        'picture_url' => $web_path
      ]);

    } else {
      error_log("Failed to move uploaded file to: $full_file_path");
      throw new Exception('Failed to save the uploaded file');
    }

  } catch (Exception $e) {
    error_log("Error in update_profile_picture: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to update profile picture: ' . $e->getMessage()
    ]);
  }
}


elseif($_GET['f'] == 'get_profile_image'){
  try {
    $image_path = $_GET['path'] ?? '';
    
    if (empty($image_path)) {
      throw new Exception('Image path not provided');
    }

    // Security: only allow images from the assets directory
    if (strpos($image_path, '/admin/src/assets/') !== 0) {
      throw new Exception('Invalid image path');
    }

    $full_path = '/var/www/html/New' . $image_path;
    
    if (!file_exists($full_path)) {
      // Return default image if requested image doesn't exist
      $full_path = '/var/www/html/New/admin/src/assets/default.png';
    }

    // Get file info
    $file_info = pathinfo($full_path);
    $extension = strtolower($file_info['extension']);

    // Set appropriate content type
    switch ($extension) {
      case 'png':
        header('Content-Type: image/png');
        break;
      case 'jpg':
      case 'jpeg':
        header('Content-Type: image/jpeg');
        break;
      case 'gif':
        header('Content-Type: image/gif');
        break;
      default:
        throw new Exception('Unsupported image type');
    }

    // Set cache headers
    header('Cache-Control: public, max-age=3600');
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
    
    // Output the image
    readfile($full_path);
    exit;

  } catch (Exception $e) {
    error_log("Error in get_profile_image: " . $e->getMessage());
    
    // Return default image on error
    $default_path = '/var/www/html/New/admin/src/assets/default.png';
    if (file_exists($default_path)) {
      header('Content-Type: image/png');
      readfile($default_path);
    } else {
      http_response_code(404);
      echo 'Image not found';
    }
    exit;
  }
}


// Update admin profile
elseif($_GET['f'] == 'update_admin_profile'){
  try {
    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Log the received data
    error_log("Received update data: " . json_encode($data));

    // Manually authenticate admin
    if (empty($data['token'])) {
      throw new Exception('Admin authentication required - no token provided');
    }

    // Additional security: token should be at least 32 chars
    if (strlen($data['token']) < 32) {
      throw new Exception('Admin authentication required - invalid token format');
    }

    // Use a prepared statement to prevent SQL injection
    $stmt = $pdo->prepare("
      SELECT id, last_login, last_ip
      FROM `admins`
      WHERE `last_session` = :token
      AND `last_login` > NOW() - INTERVAL 30 DAY
      LIMIT 1
    ");

    $stmt->bindValue(':token', $data['token']);
    $stmt->execute();

    if ($stmt->rowCount() !== 1) {
      throw new Exception('Admin authentication required - invalid token');
    }

    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    $admin_id = $row['id'];

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('Admin ID is required');
    }

    // Ensure the admin can only update their own profile
    if ($data['id'] != $admin_id) {
      throw new Exception('You can only update their own profile');
    }

    // Update admin profile
    error_log("Preparing SQL query to update admin profile with ID: $admin_id");

    // Build the SQL query - escape function as it's a reserved keyword
    $sql = "
      UPDATE admins SET
      first_name = :first_name,
      last_name = :last_name,
      email = :email,
      `function` = :function,
      location = :location,
      phone = :phone
      WHERE id = :admin_id
    ";

    error_log("SQL Query: $sql");

    // Prepare the statement
    $sth = $pdo->prepare($sql);

    $sth->bindValue(':first_name', $data['first_name']);
    $sth->bindValue(':last_name', $data['last_name']);
    $sth->bindValue(':email', $data['email']);
    $sth->bindValue(':function', $data['function']);
    $sth->bindValue(':location', $data['location'] ?? null);
    $sth->bindValue(':phone', $data['phone'] ?? null);
    $sth->bindValue(':admin_id', $admin_id);

    // Log the values being set
    error_log("Updating admin profile with values: " .
              "first_name=" . $data['first_name'] . ", " .
              "last_name=" . $data['last_name'] . ", " .
              "email=" . $data['email'] . ", " .
              "function=" . $data['function'] . ", " .
              "location=" . ($data['location'] ?? 'null') . ", " .
              "phone=" . ($data['phone'] ?? 'null') . ", " .
              "admin_id=" . $admin_id);

    // Dump the entire data array for debugging
    error_log("Full data array: " . json_encode($data));

    // Try to execute the query directly for debugging
    try {
        // Execute the update query
        $result = $sth->execute();

        // Check if the update was successful
        if ($result) {
            error_log("Admin profile updated successfully. Rows affected: " . $sth->rowCount());

            // Double-check the updated data
            $check_sql = "SELECT first_name, last_name, email, `function`, location, phone FROM admins WHERE id = :admin_id";
            $check_sth = $pdo->prepare($check_sql);
            $check_sth->bindValue(':admin_id', $admin_id);
            $check_sth->execute();
            $updated_data = $check_sth->fetch(PDO::FETCH_ASSOC);
            error_log("Updated data in database: " . json_encode($updated_data));

            // If no rows were affected, try a direct query as a fallback
            if ($sth->rowCount() == 0) {
                error_log("No rows affected by prepared statement. Trying direct query as fallback.");

                // Escape values for direct query
                $first_name = $pdo->quote($data['first_name']);
                $last_name = $pdo->quote($data['last_name']);
                $email = $pdo->quote($data['email']);
                $function = $pdo->quote($data['function']);
                $location = isset($data['location']) ? $pdo->quote($data['location']) : "NULL";
                $phone = isset($data['phone']) ? $pdo->quote($data['phone']) : "NULL";

                // Build direct query
                $direct_sql = "UPDATE admins SET
                               first_name = $first_name,
                               last_name = $last_name,
                               email = $email,
                               `function` = $function,
                               location = $location,
                               phone = $phone
                               WHERE id = $admin_id";

                error_log("Direct SQL query: $direct_sql");

                // Execute direct query
                $direct_result = $pdo->exec($direct_sql);
                error_log("Direct query result: $direct_result rows affected");

                // Check updated data again
                $check_sql = "SELECT first_name, last_name, email, `function`, location, phone FROM admins WHERE id = :admin_id";
                $check_sth = $pdo->prepare($check_sql);
                $check_sth->bindValue(':admin_id', $admin_id);
                $check_sth->execute();
                $updated_data = $check_sth->fetch(PDO::FETCH_ASSOC);
                error_log("Updated data after direct query: " . json_encode($updated_data));
            }
        } else {
            error_log("Failed to update admin profile. Error: " . implode(", ", $sth->errorInfo()));
        }
    } catch (Exception $e) {
        error_log("Exception during update: " . $e->getMessage());
        throw $e;
    }

    // As a last resort, try a completely direct SQL update
    try {
        error_log("Attempting direct SQL update as final fallback");

        // Sanitize values
        $first_name = addslashes($data['first_name']);
        $last_name = addslashes($data['last_name']);
        $email = addslashes($data['email']);
        $function = addslashes($data['function']);
        $location = isset($data['location']) ? addslashes($data['location']) : "NULL";
        $phone = isset($data['phone']) ? addslashes($data['phone']) : "NULL";

        // Build direct query with proper NULL handling
        $location_part = isset($data['location']) ? "'$location'" : "NULL";
        $phone_part = isset($data['phone']) ? "'$phone'" : "NULL";

        $direct_sql = "UPDATE admins SET
                       first_name = '$first_name',
                       last_name = '$last_name',
                       email = '$email',
                       `function` = '$function',
                       location = $location_part,
                       phone = $phone_part
                       WHERE id = $admin_id";

        error_log("Final direct SQL query: $direct_sql");

        // Execute direct query
        $direct_result = $pdo->exec($direct_sql);
        error_log("Final direct query result: $direct_result rows affected");
    } catch (Exception $e) {
        error_log("Final direct SQL update failed: " . $e->getMessage());
    }

    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Profile updated successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in update_admin_profile: " . $e->getMessage());
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to update profile: ' . $e->getMessage()
    ]);
  }
}
?>
