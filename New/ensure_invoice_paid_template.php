<?php
/**
 * <PERSON><PERSON>t to ensure the invoice_paid email template exists in the database
 */

// Include necessary files
require_once("auth_functions.php");

try {
    echo "Checking for invoice_paid email template...\n";

    // Check if email_templates table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'email_templates'");
    if ($tableCheck->rowCount() == 0) {
        echo "Email templates table does not exist. Creating it...\n";
        
        // Create the email_templates table
        $pdo->exec("CREATE TABLE `email_templates` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `type` enum('password_reset', 'server_details', 'invoice_generated', 'invoice_paid') NOT NULL,
            `subject` varchar(255) NOT NULL,
            `content` text NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            <PERSON>IMAR<PERSON> KEY (`id`),
            UNIQUE KEY `type` (`type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
        
        echo "Email templates table created successfully.\n";
    }

    // Check if invoice_paid template exists
    $templateCheck = $pdo->prepare("SELECT COUNT(*) FROM email_templates WHERE type = 'invoice_paid'");
    $templateCheck->execute();
    $templateExists = $templateCheck->fetchColumn() > 0;

    if (!$templateExists) {
        echo "Invoice paid template does not exist. Creating it...\n";
        
        // Insert the invoice_paid template
        $stmt = $pdo->prepare("INSERT INTO email_templates (type, subject, content) VALUES (:type, :subject, :content)");
        $stmt->bindValue(':type', 'invoice_paid');
        $stmt->bindValue(':subject', 'Payment Confirmation - Invoice #{$invoice_num}');
        $stmt->bindValue(':content', '<p>Dear {$client_name},</p>
<p>We have received your payment for invoice #{$invoice_num}. Thank you!</p>
<p><strong>Invoice Number:</strong> {$invoice_num}<br>
<strong>Amount Paid:</strong> {$invoice_total}<br>
<strong>Payment Date:</strong> {$payment_date}</p>
<p><strong>Invoice Items</strong></p>
<div>{$invoice_html_contents}</div>
<hr>
<p>If you have any questions about your payment, please contact our support team.</p>');
        $stmt->execute();
        
        echo "Invoice paid template created successfully.\n";
    } else {
        echo "Invoice paid template already exists.\n";
    }

    echo "Done!\n";

} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
