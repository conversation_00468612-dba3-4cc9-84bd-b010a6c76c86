#!/bin/bash

# Download and apply Proxmox answer file
# Usage: ./download_proxmox_answer.sh <servername>

if [[ $# -ne 1 ]]; then
    echo "Usage: $0 <servername>"
    echo "Example: $0 server123"
    exit 1
fi

SERVERNAME="$1"
BASE_URL="http://auto.x-zoneit.ro/servers"
ANSWER_FILE_URL="$BASE_URL/$SERVERNAME/proxmox-answer.toml"
LOCAL_FILE="./proxmox-answer-$SERVERNAME.toml"

echo "Downloading Proxmox answer file for server: $SERVERNAME"
echo "URL: $ANSWER_FILE_URL"

# Download the file using wget
if wget -O "$LOCAL_FILE" "$ANSWER_FILE_URL"; then
    echo "Successfully downloaded: $LOCAL_FILE"
    echo ""
    echo "File contents:"
    echo "=============="
    cat "$LOCAL_FILE"
    echo ""
    echo "=============="
    echo ""
    echo "To use this file with Proxmox installation:"
    echo "1. For PXE boot: File is automatically fetched during installation"
    echo "2. For manual installation: Use as auto-installer answer file"
    echo "3. For ISO creation: Include in custom Proxmox ISO"
    echo ""
    echo "Manual installation command:"
    echo "proxmox-auto-installer --answer-file $LOCAL_FILE"
    echo ""
    echo "Or place the file at /tmp/proxmox-answer.toml during installation"
else
    echo "Failed to download the file from: $ANSWER_FILE_URL"
    echo "Please check:"
    echo "1. Server name is correct"
    echo "2. Network connectivity"
    echo "3. File exists on the server"
    exit 1
fi 