# Automatic Switch Configuration

## Overview

This system now includes automatic switch interface configuration when allocating subnets to servers. When a subnet is assigned to a server, the system will automatically attempt to configure the corresponding switch interface with the assigned IP address.

## How It Works

### 1. **Subnet Assignment Trigger**
When you assign a subnet to a server through the subnet management interface, the system:
- Completes the subnet assignment first
- Then attempts automatic switch configuration if server has switch information

### 2. **Server Requirements**
For automatic configuration to work, the server must have:
- **Switch ID**: The server must be associated with a switch in the inventory
- **Port Information**: The server must have `port1` (primary port) configured
- **Switch Credentials**: The switch must have:
  - Switch IP address configured
  - SNMP community string (for SNMP operations)
  - Optionally: Root password (for SSH fallback)

### 3. **Configuration Process**

#### **Step 1: SNMP Discovery**
- Connects to the switch via SNMP using configured credentials
- Walks interface descriptions and names to find the correct port
- Maps the server's port number to the switch's interface index

#### **Step 2: Interface Configuration**
- Enables the interface administratively (`ifAdminStatus = 1`)
- Attempts to configure the IP address via SNMP
- Verifies the interface operational status

#### **Step 3: SSH Fallback (if needed)**
If SNMP IP configuration fails:
- Falls back to SSH connection using switch root password
- Executes Cisco-style CLI commands to configure the interface
- Supports standard interface configuration commands

## SNMP Requirements

### **Switch Configuration**
```sql
-- Example switch record
UPDATE inventory_switches SET 
  switch_ip = '*************',
  snmp_community = 'private',
  snmp_version = 2,
  root_password = 'switch_admin_password'
WHERE id = 1;
```

### **Server Configuration**
```sql
-- Example server with switch association
UPDATE inventory_dedicated_servers SET 
  switch_id = 1,
  port1 = '24'  -- Physical port number
WHERE id = 1;
```

### **SNMP MIBs Used**
- **Interface Discovery**: `.*******.*******.1.2` (ifDescr)
- **Interface Names**: `.*******.********.1.1.1` (ifName)
- **Admin Status**: `.*******.*******.1.7` (ifAdminStatus)
- **Operational Status**: `.*******.*******.1.8` (ifOperStatus)
- **IP Configuration**: `.*******.********.1.1` (ipAdEntAddr)

## Usage Examples

### **1. Automatic Configuration (Success)**
```javascript
// When subnet is assigned successfully
{
  "success": true,
  "message": "Subnet assigned to server successfully. Switch interface configured automatically",
  "subnet_assigned": "**********/24",
  "switch_configuration": {
    "success": true,
    "configured": true,
    "switch_ip": "*************",
    "port": "24",
    "configured_ip": "**********"
  }
}
```

### **2. Configuration Warning**
```javascript
// When subnet is assigned but switch config fails
{
  "success": true,
  "message": "Subnet assigned to server successfully. Warning: Could not configure switch interface automatically - Switch IP address not found",
  "subnet_assigned": "**********/24",
  "switch_configuration": {
    "success": false,
    "configured": false,
    "error": "Switch IP address not found"
  }
}
```

### **3. Manual Configuration**
```javascript
// API endpoint for manual configuration
POST /api_admin_subnets.php?f=configure_switch_interface
{
  "token": "admin_token",
  "server_id": 123,
  "server_type": "dedicated",
  "ip_address": "**********"
}
```

## Frontend Integration

### **SelectSubnetModal Enhancement**
The subnet selection modal now shows:
- **Information Banner**: Explains automatic configuration capability
- **Configuration Status**: Shows success/failure after assignment
- **Manual Trigger**: Button to retry configuration if needed

### **Configuration Status Display**
```jsx
<SwitchConfigurationStatus
  switchConfiguration={result.switch_configuration}
  serverId={serverId}
  serverType={serverType}
  ipAddress={assignedIP}
  onConfigureSwitch={handleConfigureSwitch}
/>
```

## Error Handling

### **Common Issues**
1. **No Switch Configured**: Server not associated with a switch
2. **No Port Information**: Server missing port1 configuration
3. **SNMP Connectivity**: Switch not responding to SNMP queries
4. **Interface Not Found**: Port number doesn't match switch interfaces
5. **SSH Fallback Failed**: Switch password incorrect or SSH disabled

### **Error Messages**
- `"Server has no switch configured - interface configuration skipped"`
- `"Server has no primary port configured - interface configuration skipped"`
- `"Could not find interface index for port 24"`
- `"SNMP connectivity test failed: No response"`
- `"SSH authentication failed"`

## Configuration Steps

### **1. Setup Switch in Inventory**
1. Add switch to inventory with IP address
2. Configure SNMP community string
3. Optionally set root password for SSH fallback

### **2. Associate Server with Switch**
1. Edit server in inventory
2. Set Switch ID to link server to switch
3. Configure port1 with physical port number

### **3. Assign Subnet**
1. Use subnet management interface
2. Select server for assignment
3. System automatically attempts configuration

### **4. Verify Configuration**
1. Check configuration status in UI
2. Verify switch interface is up
3. Test network connectivity

## Security Considerations

### **SNMP Security**
- Use strong community strings
- Consider SNMPv3 for enhanced security
- Restrict SNMP access to management networks

### **SSH Security**
- Use strong passwords for switch access
- Consider SSH key authentication
- Limit SSH access to management networks

### **Network Segmentation**
- Keep management interfaces on separate VLANs
- Use firewall rules to restrict access
- Monitor configuration changes

## Troubleshooting

### **Enable Debug Logging**
Configuration attempts are logged to PHP error log:
```bash
tail -f /var/log/php_errors.log | grep "switch interface"
```

### **Test SNMP Connectivity**
```bash
snmpwalk -v2c -c private ************* *******.*******.0
```

### **Test SSH Connectivity**
```bash
ssh admin@*************
```

### **Manual Interface Configuration**
If automatic configuration fails, configure manually:
```
enable
configure terminal
interface gigabitethernet 1/0/24
ip address ********** *************
no shutdown
exit
write memory
```

## API Endpoints

### **Automatic Configuration**
- **Endpoint**: `api_admin_subnets.php?f=assign_subnet_to_server`
- **Method**: POST
- **Auto-triggers**: Switch configuration after subnet assignment

### **Manual Configuration**
- **Endpoint**: `api_admin_subnets.php?f=configure_switch_interface`
- **Method**: POST
- **Parameters**: server_id, server_type, ip_address

## Database Schema

### **Required Fields**
```sql
-- inventory_switches table
ALTER TABLE inventory_switches ADD COLUMN IF NOT EXISTS switch_ip VARCHAR(15);
ALTER TABLE inventory_switches ADD COLUMN IF NOT EXISTS snmp_community VARCHAR(50);
ALTER TABLE inventory_switches ADD COLUMN IF NOT EXISTS snmp_version INT DEFAULT 2;
ALTER TABLE inventory_switches ADD COLUMN IF NOT EXISTS root_password VARCHAR(255);

-- Server tables (both dedicated and blade)
ALTER TABLE inventory_dedicated_servers ADD COLUMN IF NOT EXISTS switch_id INT;
ALTER TABLE inventory_dedicated_servers ADD COLUMN IF NOT EXISTS port1 VARCHAR(10);
ALTER TABLE blade_server_inventory ADD COLUMN IF NOT EXISTS switch_id INT;
ALTER TABLE blade_server_inventory ADD COLUMN IF NOT EXISTS port1 VARCHAR(10);
```

This automatic configuration feature significantly reduces manual network configuration overhead while maintaining flexibility for complex network setups. 