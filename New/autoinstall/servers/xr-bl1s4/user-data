#cloud-config
autoinstall:
  version: 1
  identity:
    hostname: xr-bl1s4.zetservers.com
    username: user
    password: $2y$10$ymJFFrD8ZKGN1t/zU2xvAurfbo6BI.ovLz1IGigkJvpKRbkX/pwe2
  early-commands:
    - systemctl stop ssh
  network:
    network:
      version: 2
      ethernets:
        eth0:
          addresses:
            - ************/30
          gateway4: ************
          nameservers:
            addresses: [*******]
  ssh:
    install-server: true
    allow-pw: true
  storage:
    layout:
      name: lvm
    swap:
      size: 0
  packages:
    - openssh-server
    - net-tools
    - curl
    - wget
  late-commands:
    - echo 'user ALL=(ALL) NOPASSWD:ALL' > /target/etc/sudoers.d/user
    - chmod 440 /target/etc/sudoers.d/user
    - sed -i 's/GRUB_CMDLINE_LINUX_DEFAULT="quiet splash"/GRUB_CMDLINE_LINUX_DEFAULT="quiet splash net.ifnames=0 biosdevname=0"/' /target/etc/default/grub
    - curtin in-target --target=/target -- update-grub
    - curtin in-target --target=/target -- apt-get update
    - curtin in-target --target=/target -- apt-get upgrade -y