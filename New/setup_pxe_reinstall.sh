#!/bin/bash

# PXE Reinstall Setup Script
# This script sets up the database schema and directory structure for PXE reinstall functionality



# Create required directories
echo "Setting up directory structure..."

# Create autoinstall directories
mkdir -p /var/www/html/New/autoinstall/servers
mkdir -p /var/www/html/New/autoinstall/os/ubuntu-22.04

# Set proper permissions
chown -R www-data:www-data /var/www/html/New/autoinstall/
chmod -R 755 /var/www/html/New/autoinstall/

# Create OS directory structure if it doesn't exist
if [ ! -f "/var/www/html/New/autoinstall/os/ubuntu-22.04/ubuntu-22.04.5-live-server-amd64.iso" ]; then
    echo "⚠️  Warning: Ubuntu 22.04 ISO not found in /var/www/html/New/autoinstall/os/ubuntu-22.04/"
    echo "   Please download and place the following files:"
    echo "   - ubuntu-22.04.5-live-server-amd64.iso"
    echo "   - vm<PERSON>uz"
    echo "   - initrd"
fi

# Check DHCP configuration
if [ ! -f "/etc/dhcp/dhcpd.conf" ]; then
    echo "⚠️  Warning: DHCP configuration file not found at /etc/dhcp/dhcpd.conf"
    echo "   Please ensure ISC DHCP server is installed and configured"
else
    echo "✅ DHCP configuration file found"
fi

# Check if www-data can write to DHCP config (for automated DHCP updates)
if [ -w "/etc/dhcp/dhcpd.conf" ]; then
    echo "✅ DHCP configuration is writable by web server"
else
    echo "⚠️  Warning: DHCP configuration is not writable by web server"
    echo "   Run: chmod 664 /etc/dhcp/dhcpd.conf"
    echo "   And: chgrp www-data /etc/dhcp/dhcpd.conf"
fi

# Check systemctl access for DHCP restart
if sudo -u www-data systemctl status isc-dhcp-server >/dev/null 2>&1; then
    echo "✅ Web server can check DHCP service status"
else
    echo "⚠️  Warning: Web server cannot control DHCP service"
    echo "   Add to /etc/sudoers:"
    echo "   www-data ALL=(ALL) NOPASSWD: /bin/systemctl restart isc-dhcp-server"
    echo "   www-data ALL=(ALL) NOPASSWD: /bin/systemctl status isc-dhcp-server"
fi

echo ""
echo "🎉 PXE Reinstall setup completed!"
echo ""
echo "Next steps:"
echo "1. Download Ubuntu 22.04 server ISO and extract vmlinuz/initrd to /var/www/html/New/autoinstall/os/ubuntu-22.04/"
echo "2. Configure DHCP server permissions if warnings were shown above"
echo "3. Test PXE reinstall functionality from the web interface"
echo ""
echo "Files created/modified:"
echo "- Database tables: pxe_reinstall_sessions, pxe_dhcp_entries"
echo "- Directory: /var/www/html/New/autoinstall/servers/"
echo "- Directory: /var/www/html/New/autoinstall/os/"

sudo visudo 