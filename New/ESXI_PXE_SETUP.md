# VMware ESXi PXE Installation Setup

This document describes how to set up ESXi 8 and 9 for PXE network installation using the integrated PXE API system.

## Prerequisites

1. ESXi installation ISO files for versions 8.0 and 9.0
2. Web server with proper directory structure
3. PXE/DHCP server configured
4. Sufficient disk space for extracted ESXi files

## Directory Structure

Create the following directory structure under your web server's document root (`/var/www/html/New/os/`):

```
/var/www/html/New/os/
├── esxi-8.0/
│   ├── boot.cfg
│   ├── mboot.c32
│   ├── efi/
│   │   └── boot/
│   │       ├── bootx64.efi
│   │       └── boot.cfg
│   ├── isolinux/
│   │   ├── isolinux.bin
│   │   ├── isolinux.cfg
│   │   └── mboot.c32
│   └── [other extracted ESXi files]
└── esxi-9.0/
    ├── boot.cfg
    ├── mboot.c32
    ├── efi/
    │   └── boot/
    │       ├── bootx64.efi
    │       └── boot.cfg
    ├── isolinux/
    │   ├── isolinux.bin
    │   ├── isolinux.cfg
    │   └── mboot.c32
    └── [other extracted ESXi files]
```

## ESXi ISO Extraction

### For ESXi 8.0:

```bash
# Create directory
mkdir -p /var/www/html/New/os/esxi-8.0
cd /var/www/html/New/os/esxi-8.0

# Mount and extract ESXi ISO
sudo mount -o loop /path/to/VMware-VMvisor-Installer-8.0.x-xxxxxxxx.x86_64.iso /mnt
sudo cp -r /mnt/* .
sudo umount /mnt

# Set proper permissions
sudo chown -R www-data:www-data /var/www/html/New/os/esxi-8.0
sudo chmod -R 755 /var/www/html/New/os/esxi-8.0
```

### For ESXi 9.0:

```bash
# Create directory
mkdir -p /var/www/html/New/os/esxi-9.0
cd /var/www/html/New/os/esxi-9.0

# Mount and extract ESXi ISO
sudo mount -o loop /path/to/VMware-VMvisor-Installer-9.0.x-xxxxxxxx.x86_64.iso /mnt
sudo cp -r /mnt/* .
sudo umount /mnt

# Set proper permissions
sudo chown -R www-data:www-data /var/www/html/New/os/esxi-9.0
sudo chmod -R 755 /var/www/html/New/os/esxi-9.0
```

## Boot Configuration

The system will automatically generate the appropriate `boot.cfg` modifications for kickstart installation. The key components are:

1. **mboot.c32**: The ESXi multiboot loader
2. **boot.cfg**: Contains module loading information
3. **Kickstart file**: Auto-generated with network and password configuration

## Supported ESXi Templates

The following template names are supported:

- `esxi-8.0` or `esxi-8`
- `esxi-9.0` or `esxi-9`
- `vmware-esxi-8`
- `vmware-esxi-9`

## ESXi Installation Features

### Automatic Configuration

The ESXi kickstart file automatically configures:

- Root password (auto-generated and stored in database)
- Network configuration (static IP, gateway, DNS)
- Additional IP addresses (if configured in server inventory)
- SSH service enablement
- ESXi Shell enablement
- NTP configuration
- Firewall rules for management access

### Post-Installation

After installation, ESXi will be configured with:

- SSH enabled for remote access
- ESXi Shell enabled
- Root password as generated during installation
- Network properly configured with all assigned IPs
- Post-installation cleanup callback to remove PXE configuration

## Network Requirements

### DHCP Configuration

The system automatically creates DHCP reservations with:
- PXE boot configuration
- Static IP assignment
- Proper boot file selection based on client architecture

### Example DHCP Entry

```
subnet ************ netmask *************** {
    option subnet-mask ***************;
    option routers ************;
    option broadcast-address ************;
    range ************ ************;
    
    host esx-server-01 {
        hardware ethernet 00:11:22:33:44:55;
        fixed-address ************;
        option host-name "esx-server-01.domain.com";
        
        if exists user-class and option user-class = "iPXE" {
            filename "http://auto.x-zoneit.ro/servers/esx-server-01/boot.ipxe";
        } elsif option client-architecture = 00:07 {
            filename "snponly.efi";
        } elsif option client-architecture = 00:00 {
            filename "undionly.kpxe";
        } else {
            filename "undionly.kpxe";
        }
    }
}
```

## Usage

### Via API

```bash
curl -X POST https://your-domain.com/pxe_api_integration.php?action=execute_server_reinstall \
  -H "Content-Type: application/json" \
  -d '{
    "server_id": 1000001,
    "server_type": "dedicated",
    "os_template": "esxi-8.0",
    "ipmi_address": "**************",
    "ipmi_username": "root",
    "ipmi_password": "password123"
  }'
```

### Via Web Interface

1. Navigate to server inventory
2. Select the server to reinstall
3. Choose "ESXi 8.0" or "ESXi 9.0" from the OS template dropdown
4. Provide IPMI credentials
5. Click "Start Reinstall"

## Troubleshooting

### Common Issues

1. **Boot.cfg not found**: Ensure ESXi files are properly extracted
2. **Network timeout**: Check firewall rules and network connectivity
3. **IPMI issues**: Verify IPMI credentials and network access
4. **Permission errors**: Ensure web server has read access to ESXi files

### Log Files

Monitor these log files for troubleshooting:

- `/var/log/dhcp/dhcpd.log` - DHCP server logs
- `auto.logs` - PXE API operation logs
- ESXi installation logs (available via iDRAC/IPMI console)

### Verification

After setup, verify ESXi files are accessible:

```bash
# Test ESXi 8.0 accessibility
curl -I http://auto.x-zoneit.ro/os/esxi-8.0/mboot.c32

# Test ESXi 9.0 accessibility  
curl -I http://auto.x-zoneit.ro/os/esxi-9.0/mboot.c32
```

## Security Considerations

1. **Root Password**: Auto-generated passwords are stored securely in the database
2. **Network Access**: ESXi installations enable SSH by default for management
3. **Firewall**: Configure appropriate firewall rules for ESXi management
4. **IPMI Security**: Use strong IPMI passwords and secure network access

## Additional Notes

- ESXi installations typically take 15-30 minutes depending on hardware
- The system automatically cleans up PXE configuration after successful installation
- Multiple IP addresses are configured if available in server inventory
- Post-installation, ESXi will be accessible via web interface (HTTPS) and SSH 