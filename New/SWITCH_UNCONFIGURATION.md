# Switch Unconfiguration Implementation

## Overview

I've enhanced the system to automatically unconfigure switch interfaces when IP subnets are unallocated from servers. This ensures that when you remove an IP allocation from the Inventory page, the corresponding switch configuration is also cleaned up.

## Changes Made

### 1. **New PHP Functions in `api_admin_subnets.php`**

#### `unconfigureSwitchInterface($pdo, $serverId, $serverType, $ipAddress)`
- Mirrors the configuration function but for removal
- Gets server and switch information
- Resolves port numbers 
- Calls SSH unconfiguration function
- Returns detailed status information

#### `unconfigureSwitchViaSsh($switchIp, $password, $portNumber, $ipAddress)`
- Connects to switch via SSH
- Executes commands to remove IP configuration:
  - `no ip address` - removes IP from interface
  - `shutdown` - disables the interface
  - `description Unconfigured by API - IP removed` - updates description
- Uses same authentication and connection logic as configuration

### 2. **Enhanced `unallocate_subnet_server_side` Endpoint**

**Before:** Only updated database to remove subnet allocation

**After:** 
- Returns immediate success response to user (no waiting)
- Runs everything in background:
  - Gets server information before removing allocation
  - Updates database (commits transaction)
  - Calculates gateway IP that was previously configured
  - Runs switch unconfiguration
  - Logs all unconfiguration attempts

### 3. **New API Endpoint: `unconfigure_switch_interface`**

- Manual unconfiguration endpoint
- Same parameters as configuration endpoint
- For manual cleanup or troubleshooting
- Usage: `POST /api_admin_subnets.php?f=unconfigure_switch_interface`

### 4. **Updated InventoryPage.js**

- Enhanced success message to inform users about background switch unconfiguration
- Maintains same user experience but with better feedback

## How It Works

### Automatic Unconfiguration Flow

1. **User clicks "Unallocate" on a subnet in Inventory page**
2. **Frontend calls `unallocate_subnet_server_side`**
3. **Backend process:**
   ```
   a. Return success to user immediately (no waiting)
   b. Start background process:
      - Get server info before removing allocation
      - Update database (remove subnet assignment)
      - Calculate gateway IP that was configured (subnet + 1)
      - Call unconfigureSwitchInterface()
      - SSH to switch and remove IP configuration
   ```

### Switch Commands Executed

When unconfiguring, the system runs these commands on the switch:

```bash
enable
configure terminal
interface <port_number>       # e.g., "Ethernet35"
description Unconfigured by API - IP removed
no ip address                 # Remove IP configuration
shutdown                      # Disable interface
exit
exit
write memory                  # Save configuration
```

## Benefits

1. **Complete Cleanup**: No orphaned IP configurations left on switches
2. **Background Processing**: Users don't wait for switch operations
3. **Consistent State**: Database and switch configuration stay in sync
4. **Error Handling**: Failures are logged but don't block user operations
5. **Manual Override**: `unconfigure_switch_interface` endpoint for manual cleanup

## Requirements

### Server Configuration
- Server must have `switch_id` and `port1` configured
- Switch must have `switch_ip` and `root_password` set
- SSH2 PHP extension must be available

### Error Handling
- If switch unconfiguration fails, database operation still succeeds
- All failures are logged for troubleshooting
- Manual unconfiguration endpoint available as fallback

## API Usage Examples

### Automatic (happens during subnet unallocation)
```javascript
// This now runs everything in background (immediate response)
fetch('/api_admin_subnets.php?f=unallocate_subnet_server_side', {
  method: 'POST',
  body: JSON.stringify({
    token: 'admin_token',
    subnet_cidr: '**********/24'
  })
});

// Response (immediate):
{
  "success": true,
  "message": "Subnet unallocation started in background. Database update and switch unconfiguration will complete shortly.",
  "subnet_cidr": "**********/24",
  "database_update": "background",
  "switch_unconfiguration": "background"
}
```

### Manual Switch Unconfiguration
```javascript
fetch('/api_admin_subnets.php?f=unconfigure_switch_interface', {
  method: 'POST', 
  body: JSON.stringify({
    token: 'admin_token',
    server_id: 123,
    server_type: 'dedicated', 
    ip_address: '**********'
  })
});
```

## Logging

All unconfiguration attempts are logged:

```bash
# Success
Background switch unconfiguration completed successfully for server 123 - removed IP **********

# Failure  
Background switch unconfiguration failed for server 123: SSH authentication failed

# No server info
No server information found for subnet **********/24, skipping switch unconfiguration
```

## Testing

To test the unconfiguration:

1. **Allocate a subnet to a server** (should configure switch)
2. **Unallocate the subnet from Inventory page** 
3. **Check switch configuration** - IP should be removed
4. **Check logs** - should show unconfiguration success/failure

This implementation ensures that your switch configurations stay clean and consistent with your IP allocations, providing a complete infrastructure management solution. 