<?php
require_once("auth_functions.php");
require_once("invoice_email_functions.php");


function generateNextInvoiceNumber($pdo) {
  try {
      // Get the highest invoice number overall
      $stmt = $pdo->prepare("
          SELECT MAX(CAST(invoice_number AS UNSIGNED)) AS last_number
          FROM invoices
      ");
      $stmt->execute();
      $result = $stmt->fetch(PDO::FETCH_ASSOC);

      // Calculate the next number, defaulting to 1 if no existing invoices
      $next_number = ($result && $result['last_number']) ? $result['last_number'] + 1 : 1;

      // Return simple sequential number as string
      return (string)$next_number;
  } catch (PDOException $e) {
      // Optional: Add error handling
      error_log('Error generating invoice number: ' . $e->getMessage());
      return '1'; // Fallback to 1 in case of any database error
  }
}

function insertInvoiceItems($pdo, $invoiceId, $items) {
  foreach ($items as $index => $item) {
    $description = isset($item['description']) && !empty($item['description'])
      ? $item['description']
      : "Item #" . ($index + 1);

    $quantity = isset($item['quantity']) ? floatval($item['quantity']) : 1;
    $unitPrice = isset($item['unitPrice']) ? floatval(str_replace(['€', ','], ['', '.'], $item['unitPrice'])) : 0;
    $itemTotal = $quantity * $unitPrice;

    $item_sth = $pdo->prepare("
    INSERT INTO invoice_items
    (invoice_id, description, quantity, unit_price, total, period)
    VALUES
    (:invoice_id, :description, :quantity, :unit_price, :total, :period)
    ");

    $item_sth->bindValue(':invoice_id', $invoiceId);
    $item_sth->bindValue(':description', $description);
    $item_sth->bindValue(':quantity', $quantity);
    $item_sth->bindValue(':unit_price', $unitPrice);
    $item_sth->bindValue(':total', $itemTotal);
    $item_sth->bindValue(':period', $item['period'] ?? '');

    $item_sth->execute();
  }
 }

function generateNextProformaNumber($pdo) {
  // Get the highest proforma number overall
  $stmt = $pdo->prepare("
      SELECT MAX(CAST(proforma_number AS UNSIGNED)) AS last_number
      FROM invoices
  ");
  $stmt->execute();

  $result = $stmt->fetch(PDO::FETCH_ASSOC);
  $last_number = $result && $result['last_number'] ? $result['last_number'] : 0;
  error_log("Last proforma number found: " . $last_number);

  $next_number = $last_number + 1;
  error_log("Generated new proforma number: " . $next_number);

  // Return simple sequential number as string
  return (string)$next_number;
}


function convertProformaToInvoice($pdo, $adminId, $invoiceId) {
  try {
      // Begin transaction
      $pdo->beginTransaction();

      // Get proforma details
      $proforma_sth = $pdo->prepare("SELECT * FROM invoices WHERE id = :id AND type = 'proforma'");
      $proforma_sth->bindValue(':id', $invoiceId);
      $proforma_sth->execute();

      $proforma = $proforma_sth->fetch(PDO::FETCH_ASSOC);

      if (!$proforma) {
          throw new Exception("Proforma invoice not found");
      }

      // Generate new invoice number
      $invoiceNumber = generateNextInvoiceNumber($pdo);

      // Update the record to convert it to an invoice
      $update_sth = $pdo->prepare("
          UPDATE invoices
          SET type = 'invoice',
              invoice_number = :invoice_number,
              status = 'Paid',
              Paid_date = NOW()
          WHERE id = :id
      ");
      $update_sth->bindValue(':invoice_number', $invoiceNumber);
      $update_sth->bindValue(':id', $invoiceId);
      $update_sth->execute();

      // Find associated orders_items and extend their due dates by one month
      error_log("Finding associated orders_items to extend due dates for proforma conversion");

      // First check if this invoice is associated with an order
      if (!empty($proforma['order_id'])) {
        $orderItemsQuery = $pdo->prepare("SELECT id, due_date FROM orders_items WHERE order_id = :order_id");
        $orderItemsQuery->bindValue(':order_id', $proforma['order_id']);
        $orderItemsQuery->execute();
        $orderItems = $orderItemsQuery->fetchAll(PDO::FETCH_ASSOC);

        if (count($orderItems) > 0) {
          error_log("Found " . count($orderItems) . " order items to update for proforma conversion");

          foreach ($orderItems as $item) {
            // Calculate new due date (one month from current due date)
            $currentDueDate = $item['due_date'] ? $item['due_date'] : date('Y-m-d H:i:s');
            $newDueDate = date('Y-m-d H:i:s', strtotime($currentDueDate . ' +1 month'));

            // Update the due date
            $updateItemQuery = $pdo->prepare("UPDATE orders_items SET due_date = :new_due_date WHERE id = :item_id");
            $updateItemQuery->bindValue(':new_due_date', $newDueDate);
            $updateItemQuery->bindValue(':item_id', $item['id']);
            $updateItemQuery->execute();

            error_log("Updated order item ID: " . $item['id'] . " due date from " . $currentDueDate . " to " . $newDueDate);
          }
        } else {
          error_log("No order items found for order ID: " . $proforma['order_id'] . " during proforma conversion");
        }
      } else {
        error_log("Proforma invoice is not associated with an order, no order items to update");
      }

      // Check if the proforma number matches any invoice_id in user_credits
      $credit_sth = $pdo->prepare("
          SELECT id FROM user_credits
          WHERE invoice_id = :proforma_number AND status != 'available'
      ");
      $credit_sth->bindValue(':proforma_number', $proforma['proforma_number']);
      $credit_sth->execute();

      $creditFound = $credit_sth->fetch(PDO::FETCH_ASSOC);
      if ($creditFound) {
          // Update the credit status to available
          $update_credit_sth = $pdo->prepare("
              UPDATE user_credits
              SET status = 'Available'
              WHERE invoice_id = :proforma_number
          ");
          $update_credit_sth->bindValue(':proforma_number', $proforma['proforma_number']);
          $update_credit_sth->execute();

          // Log the credit activation
          logInvoiceActivity(
              $pdo,
              $adminId,
              'credit_activated',
              "Credits activated for invoice {$invoiceNumber}",
              $invoiceId,
              $invoiceNumber,
              $proforma['proforma_number']
          );
      }

      logInvoiceActivity(
        $pdo,
        $adminId,
        'proforma_converted',
        "Converted proforma {$proforma['proforma_number']} to invoice {$invoiceNumber}",
        $invoiceId,
        $invoiceNumber,
        $proforma['proforma_number']
      );

      logInvoiceActivity(
        $pdo,
        $adminId,
        'payment_received',
        "Payment received for invoice {$invoiceNumber}",
        $invoiceId,
        $invoiceNumber,
        null
      );

      // Commit transaction
      $pdo->commit();

      return [
          'success' => true,
          'invoice_id' => $invoiceId,
          'invoice_number' => $invoiceNumber,
          'proforma_number' => $proforma['proforma_number'],
          'credit_activated' => $creditFound ? true : false,
          'message' => 'Proforma invoice converted to invoice successfully'
      ];

  } catch (Exception $e) {
      // Rollback on error
      if ($pdo->inTransaction()) {
          $pdo->rollBack();
      }

      error_log("Error converting proforma: " . $e->getMessage());
      return [
          'success' => false,
          'error' => 'Failed to convert proforma: ' . $e->getMessage()
      ];
  }
}


/**
* Update the createProformaInvoice function to handle combined descriptions
*/
function createProformaInvoice($pdo, $adminId, $data) {
  try {
    // Begin transaction
    $pdo->beginTransaction();

    // Identify user
    $userId = identifyUser($data, $pdo);

    // Get user's country for VAT calculation
    $country = getUserCountry($userId);

    // Get appropriate VAT rate
    $tax_rate = getVatRateByCountry($country);

    // Calculate subtotal from items
    $subtotal = calculateSubtotal($data['items']);
    $tax = $subtotal * $tax_rate;
    $total = $subtotal + $tax;

    // Generate proforma number
    $proformaNumber = generateNextProformaNumber($pdo);

    // Set due date to tomorrow if not provided
    $due_date = isset($data['dueDate']) && !empty($data['dueDate']) ?
      $data['dueDate'] :
      date('Y-m-d', strtotime('+1 day'));

    // Insert the proforma invoice
    $invoice_sth = $pdo->prepare("
    INSERT INTO invoices
    (user_id, type, proforma_number, value, subtotal, tax, status, date, due_date, payment_method, description)
    VALUES
    (:user_id, 'proforma', :proforma_number, :value, :subtotal, :tax, 'Draft', NOW(), :due_date, :payment_method, :description)
    ");

    $invoice_sth->bindValue(':user_id', $userId, PDO::PARAM_INT);
    $invoice_sth->bindValue(':proforma_number', $proformaNumber);
    $invoice_sth->bindValue(':value', $total);
    $invoice_sth->bindValue(':subtotal', $subtotal);
    $invoice_sth->bindValue(':tax', $tax);
    $invoice_sth->bindValue(':due_date', $due_date);
    $invoice_sth->bindValue(':payment_method', $data['paymentMethod'] ?? 'Bank Transfer');
    $invoice_sth->bindValue(':description', $data['notes'] ?? null);

    $invoice_sth->execute();
    $invoiceId = $pdo->lastInsertId();

    // Insert invoice items
    insertInvoiceItems($pdo, $invoiceId, $data['items']);

    // Log the creation
    logInvoiceActivity(
      $pdo,
      $adminId,
      'proforma_created',
      "Created proforma invoice {$proformaNumber} for user {$userId}",
      $invoiceId,
      null,
      $proformaNumber
  );

    // Commit the transaction
    $pdo->commit();

    // Send email notification
    try {
      $emailResult = sendInvoiceGeneratedEmail($invoiceId);
      if ($emailResult) {
        error_log("Successfully sent email notification for invoice #$invoiceId");
      } else {
        error_log("Failed to send email notification for invoice #$invoiceId");
      }
    } catch (Exception $emailError) {
      error_log("Error sending email notification: " . $emailError->getMessage());
      // Don't throw the exception - we don't want to fail the invoice creation if email fails
    }

    return [
      'success' => true,
      'invoice_id' => $invoiceId,
      'proforma_number' => $proformaNumber,
      'type' => 'proforma',
      'message' => 'Proforma invoice created successfully'
    ];

  } catch (Exception $e) {
    // Rollback the transaction in case of error
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    error_log("Error in createProformaInvoice: " . $e->getMessage());

    return [
      'success' => false,
      'error' => 'Failed to create proforma invoice: ' . $e->getMessage()
    ];
  }
}



function getInvoiceActivity($pdo, $invoiceId) {
  try {
      // Clean invoice ID if needed
      if (is_string($invoiceId) && strpos($invoiceId, '#') !== false) {
          $invoiceId = str_replace('#', '', $invoiceId);
          $invoiceId = trim($invoiceId, '0');
      }

      // Get all activity logs for this invoice
      $activity_sth = $pdo->prepare("
          SELECT * FROM activity_log
          WHERE invoice_id = :invoice_id
          ORDER BY timestamp DESC
      ");
      $activity_sth->bindValue(':invoice_id', $invoiceId);
      $activity_sth->execute();

      $activities = [];
      while ($row = $activity_sth->fetch(PDO::FETCH_ASSOC)) {
          // Parse JSON details if available
          $details = null;
          if (!empty($row['details']) && $row['details'] !== 'null') {
              try {
                  $details = json_decode($row['details'], true);
              } catch (Exception $e) {
                  $details = null;
              }
          }

          // Format the timestamp for display
          $timestamp = formatTimestamp($row['timestamp']);

          // Determine activity type for frontend display
          $type = determineActivityType($row['action_type'], $row['action']);

          $activities[] = [
              'id' => $row['id'],
              'invoice_id' => $row['invoice_id'],
              'user_id' => $row['user_id'],
              'action' => $row['action'],
              'description' => $row['description'],
              'user_name' => $row['user_name'],
              'timestamp' => $timestamp,
              'raw_timestamp' => $row['timestamp'],
              'type' => $type,
              'details' => $details
          ];
      }

      return [
          'success' => true,
          'activities' => $activities
      ];

  } catch (Exception $e) {
      error_log("Error getting invoice activity: " . $e->getMessage());
      return [
          'success' => false,
          'error' => 'Failed to get invoice activity: ' . $e->getMessage()
      ];
  }
}


/**
* Helper function to determine activity type for frontend
*/
function determineActivityType($actionType, $action) {
  switch ($actionType) {
      case 'create':
          return 'system';
      case 'update':
          return 'edit';
      case 'conversion':
          return 'system';
      case 'payment':
          return 'payment';
      case 'status':
          return 'status';
      case 'items':
          return 'edit';
      case 'send':
          return 'notification';
      default:
          // Fallback based on action text
          if (stripos($action, 'payment') !== false) {
              return 'payment';
          } elseif (stripos($action, 'edit') !== false) {
              return 'edit';
          } elseif (stripos($action, 'status') !== false) {
              return 'status';
          } elseif (stripos($action, 'sent') !== false || stripos($action, 'reminder') !== false) {
              return 'notification';
          }
          return 'system';
  }
}



/**
 * Helper function to get user name (admin or customer)
 */
function getUserName($pdo, $userId) {
    if ($userId === 'system') {
        return 'System';
    }

    try {
        // First try admin table
        $admin_sth = $pdo->prepare("SELECT CONCAT(first_name, ' ', last_name) as name FROM admins WHERE id = :user_id");
        $admin_sth->bindValue(':user_id', $userId);
        $admin_sth->execute();
        $admin = $admin_sth->fetch(PDO::FETCH_ASSOC);

        if ($admin && !empty($admin['name'])) {
            return $admin['name'] . ' (Admin)';
        }

        // Then try user table
        $user_sth = $pdo->prepare("SELECT CONCAT(first_name, ' ', last_name) as name, company_name FROM users WHERE id = :user_id");
        $user_sth->bindValue(':user_id', $userId);
        $user_sth->execute();
        $user = $user_sth->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            if (!empty($user['company_name'])) {
                return $user['company_name'];
            } elseif (!empty($user['name'])) {
                return $user['name'];
            }
        }

        return 'User #' . $userId;
    } catch (Exception $e) {
        error_log("Error getting user name: " . $e->getMessage());
        return 'User #' . $userId;
    }
}





function calculateItemsTotal($items) {
    $total = 0;
    foreach ($items as $item) {
        if (isset($item['total'])) {
            // Extract numeric value from total if it contains currency symbol
            if (is_string($item['total']) && preg_match('/([€$£])?(\d+(?:\.\d+)?)/', $item['total'], $matches)) {
                $total += floatval($matches[2]);
            } else {
                $total += floatval($item['total']);
            }
        } else if (isset($item['quantity']) && isset($item['unit_price'])) {
            $quantity = floatval($item['quantity']);
            $unitPrice = floatval(str_replace(['€', '$', '£', ','], '', $item['unit_price']));
            $total += $quantity * $unitPrice;
        }
    }
    return $total;
}


function getCustomerName($pdo, $userId) {
    $customer_name = 'Unknown Customer';
    try {
        $stmt = $pdo->prepare("SELECT first_name, last_name, company_name FROM users WHERE id = :user_id");
        $stmt->bindValue(':user_id', $userId);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            if (!empty($user['company_name'])) {
                $customer_name = $user['company_name'];
            } else if (!empty($user['first_name']) || !empty($user['last_name'])) {
                $customer_name = trim($user['first_name'] . ' ' . $user['last_name']);
            }
        }
    } catch (Exception $e) {
        error_log("Failed to get customer name: " . $e->getMessage());
    }

    return $customer_name;
}

function logInvoiceActivity($pdo, $adminId, $actionType, $description, $invoiceId = null, $invoiceNumber = null, $proformaNumber = null) {
  try {
      $adminName = getUserName($pdo, $adminId);

      $stmt = $pdo->prepare("
          INSERT INTO activity_log
          (user_id, action, description, user_name, activity_type, invoice_number, proforma_number, timestamp)
          VALUES
          (:user_id, :action, :description, :user_name, :activity_type, :invoice_number, :proforma_number, NOW())
      ");

      $stmt->execute([
          ':user_id' => $adminId,
          ':action' => $actionType,
          ':description' => $description,
          ':user_name' => $adminName,
          ':activity_type' => $actionType,
          ':invoice_number' => $invoiceNumber,
          ':proforma_number' => $proformaNumber
      ]);

      return true;
  } catch (Exception $e) {
      error_log("Activity log error: " . $e->getMessage());
      return false;
  }
}

function getReadableStatus($isPaid, $dueDate = null) {
  if ($isPaid == 1) {
    return 'Paid';
  } else if ($dueDate && strtotime($dueDate) < time()) {
    return 'Overdue';
  } else {
    return 'Pending';
  }
}


function getVatRateByCountry($countryCode) {
  global $pdo;

  // Default VAT rate if country is not found
  $defaultRate = 0.19;

  if (empty($countryCode)) {
    return $defaultRate;
  }

  try {
    $stmt = $pdo->prepare("SELECT rate FROM zet.vat_rates WHERE country = :country");
    $stmt->bindValue(':country', $countryCode);
    $stmt->execute();

    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($result && isset($result['rate'])) {
      return floatval($result['rate']) / 100; // Convert percentage to decimal
    }

    return $defaultRate;
  } catch (Exception $e) {
    error_log("Error fetching VAT rate: " . $e->getMessage());
    return $defaultRate;
  }
}

function getUserCountry($userId) {
  global $pdo;

  try {
    $stmt = $pdo->prepare("SELECT country FROM users WHERE id = :userId");
    $stmt->bindValue(':userId', $userId);
    $stmt->execute();

    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($result && isset($result['country'])) {
      return $result['country'];
    }

    return null;
  } catch (Exception $e) {
    error_log("Error fetching user country: " . $e->getMessage());
    return null;
  }
}

function identifyUser($data, $pdo) {
  $user_id = null;

  // Method 0: Direct user_id from payload (highest priority)
  if (isset($data['user_id']) && !empty($data['user_id'])) {
    $user_id = $data['user_id'];
  }

  // Method 1: Try to extract from clientId field
  if (!$user_id && isset($data['clientId']) && !empty($data['clientId'])) {
    if (is_numeric($data['clientId'])) {
      $user_id = $data['clientId'];
    } else {
      $extracted = preg_replace('/[^0-9]/', '', $data['clientId']);
      if (!empty($extracted)) {
        $user_id = $extracted;
      }
    }
  }

  // Method 2: Try extracting from the client string
  if (!$user_id && isset($data['client']) && !empty($data['client'])) {
    if (preg_match('/\(([^)]*\d+[^)]*)\)/', $data['client'], $matches)) {
      $extracted = preg_replace('/[^0-9]/', '', $matches[1]);
      if (!empty($extracted)) {
        $user_id = $extracted;
      }
    }
  }

  // Method 3: Try finding by email
  if (!$user_id && isset($data['clientEmail']) && !empty($data['clientEmail'])) {
    $user_search_sth = $pdo->prepare("SELECT id FROM users WHERE email = :email");
    $user_search_sth->bindValue(':email', $data['clientEmail']);
    $user_search_sth->execute();

    $existing_user = $user_search_sth->fetch(PDO::FETCH_ASSOC);

    if ($existing_user) {
      $user_id = $existing_user['id'];
    } else {
      try {
        $create_user_sth = $pdo->prepare("
          INSERT INTO users
          (email, first_name, last_name, company_name, status, created_at)
          VALUES
          (:email, :first_name, :last_name, :company_name, 1, NOW())
        ");

        $nameParts = explode(' ', $data['client'] ?? 'New Client', 2);
        $first_name = $nameParts[0];
        $last_name = isset($nameParts[1]) ? $nameParts[1] : '';

        $create_user_sth->bindValue(':email', $data['clientEmail']);
        $create_user_sth->bindValue(':first_name', $first_name);
        $create_user_sth->bindValue(':last_name', $last_name);
        $create_user_sth->bindValue(':company_name', $data['client'] ?? null);

        $create_user_sth->execute();
        $user_id = $pdo->lastInsertId();
      } catch (Exception $create_ex) {
        error_log("Failed to create user: " . $create_ex->getMessage());
      }
    }
  }

  // Method 4: Direct database search by name
  if (!$user_id && isset($data['client']) && !empty($data['client'])) {
    $client_str = $data['client'];

    $user_search_sth = $pdo->prepare("
      SELECT id FROM users
      WHERE company_name = :client
      OR CONCAT(first_name, ' ', last_name) = :client
      OR first_name = :client
      LIMIT 1
    ");
    $user_search_sth->bindValue(':client', $client_str);
    $user_search_sth->execute();

    $existing_user = $user_search_sth->fetch(PDO::FETCH_ASSOC);

    if ($existing_user) {
      $user_id = $existing_user['id'];
    }
  }

  // Ultimate fallback - create a temporary user
  if (!$user_id) {
    try {
      $temp_email = 'temp_' . time() . '@example.com';
      $temp_name = $data['client'] ?? 'Temporary Client';

      $create_temp_sth = $pdo->prepare("
        INSERT INTO users
        (email, first_name, last_name, status, created_at, notes)
        VALUES
        (:email, :first_name, 'User', 1, NOW(), 'Temporary user created during invoice creation')
      ");

      $create_temp_sth->bindValue(':email', $temp_email);
      $create_temp_sth->bindValue(':first_name', $temp_name);
      $create_temp_sth->execute();

      $user_id = $pdo->lastInsertId();
    } catch (Exception $temp_ex) {
      error_log("Failed to create temporary user: " . $temp_ex->getMessage());
      throw new Exception("Unable to determine user for invoice: " . $temp_ex->getMessage());
    }
  }

  // Verify user exists
  $user_verify_sth = $pdo->prepare("SELECT id FROM users WHERE id = :user_id");
  $user_verify_sth->bindValue(':user_id', $user_id);
  $user_verify_sth->execute();

  if ($user_verify_sth->rowCount() == 0) {
    throw new Exception("Selected user ID {$user_id} does not exist in the database.");
  }

  return $user_id;
}


function calculateSubtotal($items) {
  $subtotal = 0;
  foreach ($items as $item) {
    $quantity = isset($item['quantity']) ? floatval($item['quantity']) : 1;
    $unitPrice = isset($item['unitPrice']) ? floatval(str_replace(['€', ','], ['', '.'], $item['unitPrice'])) : 0;
    $subtotal += $quantity * $unitPrice;
  }
  return $subtotal;
}


function logActivity($pdo, $admin_id, $action, $description) {
  try {
    // Get admin name
    $admin_sth = $pdo->prepare("SELECT first_name, last_name FROM admins WHERE id = :admin_id");
    $admin_sth->bindValue(':admin_id', $admin_id);
    $admin_sth->execute();
    $admin = $admin_sth->fetch(PDO::FETCH_ASSOC);
    $admin_name = ($admin['first_name'] ?? '') . ' ' . ($admin['last_name'] ?? '');

    $sql = "INSERT INTO activity_log
            (user_id, action, description, user_name, timestamp)
            VALUES
            (:user_id, :action, :description, :user_name, NOW())";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':user_id', $admin_id);
    $stmt->bindValue(':action', $action);
    $stmt->bindValue(':description', $description);
    $stmt->bindValue(':user_name', $admin_name);
    $stmt->execute();
  } catch (Exception $e) {
    error_log("Failed to log activity: " . $e->getMessage());
  }
}

function recordInvoiceTransaction($pdo, $invoice_id, $user_id, $amount, $type, $description, $status = 'Completed', $processor = 'System') {
  try {
      // Generate a unique transaction ID
      $transaction_id = 'TX' . time() . rand(1000, 9999);

      // Get current timestamp using our standard function
      $current_time = getCurrentTimestamp();

      // Log what we're about to insert
      error_log("TRANSACTION: Recording transaction at time: " . $current_time);

      // Insert transaction record with explicit timestamp
      $sql = "INSERT INTO invoice_transactions
              (id, invoice_id, user_id, date, type, amount, description, status, processor)
              VALUES
              (:id, :invoice_id, :user_id, :date, :type, :amount, :description, :status, :processor)";

      $stmt = $pdo->prepare($sql);
      $stmt->bindValue(':id', $transaction_id);
      $stmt->bindValue(':invoice_id', $invoice_id);
      $stmt->bindValue(':user_id', $user_id);
      $stmt->bindValue(':date', $current_time); // Explicit timestamp
      $stmt->bindValue(':type', $type);
      $stmt->bindValue(':amount', $amount);
      $stmt->bindValue(':description', $description);
      $stmt->bindValue(':status', $status);
      $stmt->bindValue(':processor', $processor);

      $stmt->execute();

      return true;
  } catch (Exception $e) {
      error_log("Error recording invoice transaction: " . $e->getMessage());
      return false;
  }
}


/**
 * Update invoice content (details and items)
 *
 * @param PDO $pdo Database connection
 * @param int $adminId ID of admin making the change
 * @param mixed $invoiceId Invoice ID or number to update
 * @param array $data Updated invoice data
 *
 * @return array Result of the update operation
 */
function updateInvoiceContent($pdo, $adminId, $invoiceId, $data) {
  try {
      // Begin transaction
      $pdo->beginTransaction();

      // Clean invoice ID if it contains non-numeric characters
      if (is_string($invoiceId)) {
          $invoiceId = preg_replace('/\D/', '', $invoiceId);
      }

      // Log the update request details for debugging
      error_log("updateInvoiceContent: Processing ID {$invoiceId}");

      // Find the invoice using multiple identifiers
      $sql = "SELECT * FROM invoices WHERE id = :id OR proforma_number = :id_alt OR invoice_number = :id_num LIMIT 1";
      $invoice_sth = $pdo->prepare($sql);
      $invoice_sth->bindValue(':id', $invoiceId, PDO::PARAM_INT);
      $invoice_sth->bindValue(':id_alt', $invoiceId, PDO::PARAM_STR);
      $invoice_sth->bindValue(':id_num', $invoiceId, PDO::PARAM_INT);
      $invoice_sth->execute();

      $invoice = $invoice_sth->fetch(PDO::FETCH_ASSOC);

      if (!$invoice) {
          throw new Exception("Invoice not found with ID: $invoiceId");
      }

      // Get the actual invoice ID for updating
      $actualInvoiceId = $invoice['id'];

      // Get client info
      $clientName = getCustomerName($pdo, $invoice['user_id']);

      // Arrays to track changes for logging
      $changedFields = [];

      // Update invoice core details if provided
      $fieldsToUpdate = [];
      $updateParams = [];

      // Normalize date formats for comparison
      $normalizedDueDate = isset($invoice['due_date']) ?
          date('Y-m-d', strtotime($invoice['due_date'])) : null;

      $newDueDate = isset($data['dueDate']) ?
          date('Y-m-d', strtotime($data['dueDate'])) : $normalizedDueDate;

      // Check and prepare field updates (with proper date comparison)
      if (isset($data['dueDate']) && $newDueDate !== $normalizedDueDate) {
          $fieldsToUpdate[] = "due_date = :due_date";
          $updateParams[':due_date'] = $data['dueDate'];
          $changedFields[] = "Due date changed from {$normalizedDueDate} to {$newDueDate}";
      }

      if (isset($data['paymentMethod']) && $data['paymentMethod'] != $invoice['payment_method']) {
          $fieldsToUpdate[] = "payment_method = :payment_method";
          $updateParams[':payment_method'] = $data['paymentMethod'];
          $changedFields[] = "Payment method changed from '{$invoice['payment_method']}' to '{$data['paymentMethod']}'";
      }

      if (isset($data['notes']) && $data['notes'] != $invoice['description']) {
          $fieldsToUpdate[] = "description = :description";
          $updateParams[':description'] = $data['notes'];
          $changedFields[] = "Notes updated from '{$invoice['description']}' to '{$data['notes']}'";
      }

      // Handle items update if provided
      $itemsChanged = false;
      $itemChanges = [];
      $oldSubtotal = $invoice['subtotal'];
      $oldTax = $invoice['tax'];
      $oldTotal = $invoice['value'];

      if (isset($data['items']) && is_array($data['items'])) {
          // First, get existing items
          $items_sth = $pdo->prepare("SELECT * FROM invoice_items WHERE invoice_id = :invoice_id");
          $items_sth->bindValue(':invoice_id', $actualInvoiceId);
          $items_sth->execute();
          $existingItems = $items_sth->fetchAll(PDO::FETCH_ASSOC);

          // Track changes in items for better logging
          if (count($existingItems) != count($data['items'])) {
              $itemChanges[] = "Number of items changed from " . count($existingItems) . " to " . count($data['items']);
          } else {
              // Compare each item for changes
              foreach ($data['items'] as $index => $newItem) {
                  if (isset($existingItems[$index])) {
                      $oldItem = $existingItems[$index];

                      // Check for description changes
                      if (isset($newItem['description']) && $newItem['description'] !== $oldItem['description']) {
                          $itemChanges[] = "Item #" . ($index + 1) . " description changed from '{$oldItem['description']}' to '{$newItem['description']}'";
                      }

                      // Check for quantity changes
                      $oldQuantity = floatval($oldItem['quantity']);
                      $newQuantity = isset($newItem['quantity']) ? floatval($newItem['quantity']) : 1;

                      if ($oldQuantity != $newQuantity) {
                          $itemChanges[] = "Item #" . ($index + 1) . " quantity changed from {$oldQuantity} to {$newQuantity}";
                      }

                      // Check for price changes
                      $oldPrice = floatval($oldItem['unit_price']);
                      $newPrice = 0;

                      if (isset($newItem['unitPrice'])) {
                          $newPrice = floatval(str_replace(['€', ',', '$', '£'], ['', '.', '', ''], $newItem['unitPrice']));
                      } else if (isset($newItem['unit_price'])) {
                          $newPrice = floatval(str_replace(['€', ',', '$', '£'], ['', '.', '', ''], $newItem['unit_price']));
                      }

                      if (abs($oldPrice - $newPrice) > 0.01) { // Use a small epsilon for float comparison
                          $itemChanges[] = "Item #" . ($index + 1) . " price changed from €" .
                              number_format($oldPrice, 2) . " to €" . number_format($newPrice, 2);
                      }
                  }
              }
          }

          // Only consider items changed if we have specific changes logged
          $itemsChanged = !empty($itemChanges);

          // Delete all existing items (we'll reinsert them)
          $delete_sth = $pdo->prepare("DELETE FROM invoice_items WHERE invoice_id = :invoice_id");
          $delete_sth->bindValue(':invoice_id', $actualInvoiceId);
          $delete_sth->execute();

          // Insert updated items
          $subtotal = 0;
          foreach ($data['items'] as $index => $item) {
              $description = isset($item['description']) && !empty($item['description'])
                  ? $item['description']
                  : "Item #" . ($index + 1);

              $quantity = isset($item['quantity']) ? floatval($item['quantity']) : 1;

              // Handle different formats of unit price
              $unitPrice = 0;
              if (isset($item['unitPrice'])) {
                  $unitPrice = floatval(str_replace(['€', ',', '$', '£'], ['', '.', '', ''], $item['unitPrice']));
              } else if (isset($item['unit_price'])) {
                  $unitPrice = floatval(str_replace(['€', ',', '$', '£'], ['', '.', '', ''], $item['unit_price']));
              }

              $itemTotal = $quantity * $unitPrice;
              $subtotal += $itemTotal;

              $item_sth = $pdo->prepare("
              INSERT INTO invoice_items
              (invoice_id, description, quantity, unit_price, total, period)
              VALUES
              (:invoice_id, :description, :quantity, :unit_price, :total, :period)
              ");

              $item_sth->bindValue(':invoice_id', $actualInvoiceId);
              $item_sth->bindValue(':description', $description);
              $item_sth->bindValue(':quantity', $quantity);
              $item_sth->bindValue(':unit_price', $unitPrice);
              $item_sth->bindValue(':total', $itemTotal);
              $item_sth->bindValue(':period', $item['period'] ?? '');

              $item_sth->execute();
          }

          // Get user's country for VAT calculation
          $country = getUserCountry($invoice['user_id']);

          // Get appropriate VAT rate
          $tax_rate = getVatRateByCountry($country);

          // Calculate tax and total
          $tax = $subtotal * $tax_rate;
          $total = $subtotal + $tax;

          // Update invoice totals
          $fieldsToUpdate[] = "subtotal = :subtotal";
          $updateParams[':subtotal'] = $subtotal;

          $fieldsToUpdate[] = "tax = :tax";
          $updateParams[':tax'] = $tax;

          $fieldsToUpdate[] = "value = :value";
          $updateParams[':value'] = $total;

          // Only add to changedFields if there's an actual value change
          if (abs($oldSubtotal - $subtotal) > 0.01) {
              $changedFields[] = "Subtotal changed from €" . number_format($oldSubtotal, 2) . " to €" . number_format($subtotal, 2);
          }

          if (abs($oldTotal - $total) > 0.01) {
              $changedFields[] = "Total amount changed from €" . number_format($oldTotal, 2) . " to €" . number_format($total, 2);
          }


      }

      // Update the invoice if there are any fields to update
      if (!empty($fieldsToUpdate)) {
          $updateSql = "UPDATE invoices SET " . implode(", ", $fieldsToUpdate) . " WHERE id = :id";
          $updateParams[':id'] = $actualInvoiceId;

          $update_sth = $pdo->prepare($updateSql);

          // Bind each parameter explicitly
          foreach ($updateParams as $param => $value) {
              if (is_int($value)) {
                  $update_sth->bindValue($param, $value, PDO::PARAM_INT);
              } else if (is_float($value)) {
                  $update_sth->bindValue($param, $value);
              } else {
                  $update_sth->bindValue($param, $value, PDO::PARAM_STR);
              }
          }

          $update_sth->execute();
      }



      // If items were changed, create a more specific log for that
      if ($itemsChanged && !empty($itemChanges)) {
          // Create a detailed item changes log
          $itemChangeDescription = "Invoice items updated: " . implode("; ", $itemChanges);

          logInvoiceActivity(
              $pdo,
              $adminId,
              'items_updated',
              $itemChangeDescription,
              $actualInvoiceId,
              $invoice['invoice_number'],
              $invoice['proforma_number']
          );
      }


      if (!empty($changedFields)) {
        logInvoiceActivity(
            $pdo,
            $adminId,
            'invoice_updated',
            "Invoice details updated: " . implode("; ", $changedFields),
            $actualInvoiceId,
            $invoice['invoice_number'],
            $invoice['proforma_number']
        );
    }

      // Commit transaction
      $pdo->commit();

      // Get updated invoice details to return
      $updated_sth = $pdo->prepare("SELECT * FROM invoices WHERE id = :id");
      $updated_sth->bindValue(':id', $actualInvoiceId);
      $updated_sth->execute();
      $updatedInvoice = $updated_sth->fetch(PDO::FETCH_ASSOC);

      // Get updated items
      $items_sth = $pdo->prepare("SELECT * FROM invoice_items WHERE invoice_id = :invoice_id");
      $items_sth->bindValue(':invoice_id', $actualInvoiceId);
      $items_sth->execute();
      $updatedItems = $items_sth->fetchAll(PDO::FETCH_ASSOC);

      // Format response
      $response = [
          'success' => true,
          'invoice_id' => $actualInvoiceId,
          'type' => $updatedInvoice['type'],
          'isProforma' => ($updatedInvoice['type'] === 'proforma'),
          'proforma_number' => $updatedInvoice['proforma_number'],
          'invoice_number' => $updatedInvoice['invoice_number'],
          'client' => $clientName,
          'clientId' => $updatedInvoice['user_id'],
          'date' => $updatedInvoice['date'],
          'dueDate' => $updatedInvoice['due_date'],
          'amount' => '€' . number_format($updatedInvoice['value'], 2),
          'status' => $updatedInvoice['status'] ?? 'Pending',
          'paymentMethod' => $updatedInvoice['payment_method'],
          'notes' => $updatedInvoice['description'],
          'items' => array_map(function($item) {
              return [
                  'description' => $item['description'],
                  'quantity' => floatval($item['quantity']),
                  'unitPrice' => '€' . number_format(floatval($item['unit_price']), 2, '.', ','),
                  'total' => '€' . number_format(floatval($item['total']), 2, '.', ','),
                  'period' => $item['period'] ?? ''
              ];
          }, $updatedItems),
          'message' => !empty($changedFields) || !empty($itemChanges) ?
                        'Invoice updated successfully' :
                        'No changes were made to the invoice'
      ];

      return $response;

  } catch (Exception $e) {
      // Rollback transaction on error
      if ($pdo->inTransaction()) {
          $pdo->rollBack();
          error_log("Transaction rolled back due to error");
      }

      error_log("Error in updateInvoiceContent: " . $e->getMessage());
      error_log("Stack trace: " . $e->getTraceAsString());

      throw $e; // Re-throw the exception for the endpoint handler to catch
  }
}

// Replace the create_invoice endpoint handler in api_admin_invoices.php
if ($_GET['f'] == 'create_invoice') {
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Log the incoming data for debugging
    error_log("create_invoice incoming data: " . print_r($data, true));

    // Validate required fields
    if (!isset($data['client']) || empty($data['client'])) {
      throw new Exception("Client name is required");
    }

    if (!isset($data['items']) || empty($data['items']) || !is_array($data['items'])) {
      throw new Exception("Invoice items are required and must be an array");
    }

    // Make sure clientId is properly formatted if provided
    if (isset($data['clientId']) && !empty($data['clientId'])) {
      // Extract numeric part if it contains non-numeric characters
      if (!is_numeric($data['clientId'])) {
        $data['clientId'] = preg_replace('/\D/', '', $data['clientId']);
      }
    }

    // Begin transaction
    $pdo->beginTransaction();
    $clientName = isset($data['client']) ? $data['client'] : getCustomerName($pdo, $userId);
    try {
      // Identify user from client information
      $userId = identifyUser($data, $pdo);

      // Get user's country for VAT calculation
      $country = getUserCountry($userId);

      // Get appropriate VAT rate
      $tax_rate = getVatRateByCountry($country);

      // Calculate subtotal from items
      $subtotal = calculateSubtotal($data['items']);
      $tax = $subtotal * $tax_rate;
      $total = $subtotal + $tax;

      // Generate proforma number
      $proformaNumber = generateNextProformaNumber($pdo);

      // Set due date to tomorrow if not provided
      $due_date = isset($data['dueDate']) && !empty($data['dueDate']) ?
        $data['dueDate'] :
        date('Y-m-d', strtotime('+1 day'));

      // Insert the proforma invoice
      $invoice_sth = $pdo->prepare("
      INSERT INTO invoices
      (user_id, type, proforma_number, value, subtotal, tax, status, date, due_date, payment_method, description)
      VALUES
      (:user_id, 'proforma', :proforma_number, :value, :subtotal, :tax, 'Draft', NOW(), :due_date, :payment_method, :description)
      ");

      $invoice_sth->bindValue(':user_id', $userId, PDO::PARAM_INT);
      $invoice_sth->bindValue(':proforma_number', $proformaNumber);
      $invoice_sth->bindValue(':value', $total);
      $invoice_sth->bindValue(':subtotal', $subtotal);
      $invoice_sth->bindValue(':tax', $tax);
      $invoice_sth->bindValue(':due_date', $due_date);
      $invoice_sth->bindValue(':payment_method', $data['paymentMethod'] ?? 'Bank Transfer');
      $invoice_sth->bindValue(':description', $data['notes'] ?? null);

      $invoice_sth->execute();
      $invoiceId = $pdo->lastInsertId();

      // Insert invoice items
      foreach ($data['items'] as $index => $item) {
        $description = isset($item['description']) && !empty($item['description'])
          ? $item['description']
          : "Item #" . ($index + 1);

        $quantity = isset($item['quantity']) ? floatval($item['quantity']) : 1;
        $unitPrice = isset($item['unitPrice']) ? floatval(str_replace(['€', ','], ['', '.'], $item['unitPrice'])) : 0;
        $itemTotal = $quantity * $unitPrice;

        $item_sth = $pdo->prepare("
        INSERT INTO invoice_items
        (invoice_id, description, quantity, unit_price, total, period)
        VALUES
        (:invoice_id, :description, :quantity, :unit_price, :total, :period)
        ");

        $item_sth->bindValue(':invoice_id', $invoiceId);
        $item_sth->bindValue(':description', $description);
        $item_sth->bindValue(':quantity', $quantity);
        $item_sth->bindValue(':unit_price', $unitPrice);
        $item_sth->bindValue(':total', $itemTotal);
        $item_sth->bindValue(':period', $item['period'] ?? '');

        $item_sth->execute();
      }

      // Log the invoice creation
      logInvoiceActivity(
        $pdo,
        $admin_id,
        'proforma_created',
        "Proforma #$proformaNumber created for {$clientName}",
        $invoiceId,
        null,
        $proformaNumber
    );

      // Commit the transaction
      $pdo->commit();

      // Get the client name for the response


      // Prepare response data
      $responseData = [
        'success' => true,
        'invoice_id' => $invoiceId,
        'proforma_number' => $proformaNumber,
        'id' => "#$proformaNumber",
        'type' => 'proforma',
        'isProforma' => true,
        'client' => $clientName,
        'clientId' => $userId,
        'clientEmail' => $data['clientEmail'] ?? '',
        'date' => date('Y-m-d'),
        'dueDate' => $due_date,
        'amount' => '€' . number_format($total, 2),
        'status' => 'Draft',
        'items' => array_map(function($item) {
          $quantity = isset($item['quantity']) ? floatval($item['quantity']) : 1;
          $unitPrice = isset($item['unitPrice']) ? floatval(str_replace(['€', ','], ['', '.'], $item['unitPrice'])) : 0;
          $total = $quantity * $unitPrice;

          return [
            'description' => $item['description'],
            'quantity' => $quantity,
            'unitPrice' => '€' . number_format($unitPrice, 2),
            'total' => '€' . number_format($total, 2)
          ];
        }, $data['items']),
        'message' => 'Invoice created successfully'
      ];

      // Return response
      header('Content-Type: application/json');
      echo json_encode($responseData);

    } catch (Exception $inner_e) {
      // Rollback the transaction in case of error
      if ($pdo->inTransaction()) {
        $pdo->rollBack();
      }
      throw $inner_e;
    }

  } catch (Exception $e) {
    error_log("Error in create_invoice: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to create invoice: ' . $e->getMessage()
    ]);
  }
}


elseif ($_GET['f'] == 'update_invoice_status') {
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Detailed logging - dump the entire request
    error_log("update_invoice_status: Full request data: " . print_r($data, true));

    // Validate required fields
    if (!isset($data['id']) || !isset($data['status'])) {
      throw new Exception("Invoice ID and status are required");
    }

    $invoiceId = $data['id'];
    $newStatus = $data['status'];

    // Clean invoice ID if it contains non-numeric characters
    if (is_string($invoiceId)) {
      $invoiceId = preg_replace('/\D/', '', $invoiceId);
    }

    error_log("Processing invoice ID: $invoiceId with new status: $newStatus");

    // Begin transaction
    $pdo->beginTransaction();

    // First, find the invoice
    $invoice_query = "SELECT * FROM invoices WHERE id = ? OR proforma_number = ? OR invoice_number = ? LIMIT 1";
    $invoice_stmt = $pdo->prepare($invoice_query);
    $invoice_stmt->execute([$invoiceId, $invoiceId, $invoiceId]);
    $invoice = $invoice_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$invoice) {
      throw new Exception("Invoice not found with ID: $invoiceId");
    }

    error_log("Found invoice: " . print_r($invoice, true));

    // Get the actual invoice ID and current status
    $actualInvoiceId = $invoice['id'];
    $oldStatus = $invoice['status'] ?? 'unknown';
    $isProforma = ($invoice['type'] === 'proforma');

    // Response to return
    $response = [];

    // CASE 1: Proforma being marked as Paid - convert to invoice
    if ($newStatus === 'Paid' && $isProforma) {
      error_log("Converting proforma to Paid invoice");

      // Generate new invoice number
      $invoiceNumber = generateNextInvoiceNumber($pdo);

      error_log("Generated new invoice number: $invoiceNumber");

      // Update the invoice
      $update_query = $pdo->prepare("
        UPDATE invoices SET
          type = 'invoice',
          status = :new_status,
          invoice_number = :invoice_number,
          Paid_date = NOW()
        WHERE id = :invoice_id
      ");

      $update_query->bindValue(':new_status', $newStatus);
      $update_query->bindValue(':invoice_number', $invoiceNumber);
      $update_query->bindValue(':invoice_id', $actualInvoiceId);
      $update_query->execute();

      // Find associated orders_items and extend their due dates by one month
      error_log("Finding associated orders_items to extend due dates for proforma conversion");

      // First check if this invoice is associated with an order
      if (!empty($invoice['order_id'])) {
        $orderItemsQuery = $pdo->prepare("SELECT id, due_date FROM orders_items WHERE order_id = :order_id");
        $orderItemsQuery->bindValue(':order_id', $invoice['order_id']);
        $orderItemsQuery->execute();
        $orderItems = $orderItemsQuery->fetchAll(PDO::FETCH_ASSOC);

        if (count($orderItems) > 0) {
          error_log("Found " . count($orderItems) . " order items to update for proforma conversion");

          foreach ($orderItems as $item) {
            // Calculate new due date (one month from current due date)
            $currentDueDate = $item['due_date'] ? $item['due_date'] : date('Y-m-d H:i:s');
            $newDueDate = date('Y-m-d H:i:s', strtotime($currentDueDate . ' +1 month'));

            // Update the due date
            $updateItemQuery = $pdo->prepare("UPDATE orders_items SET due_date = :new_due_date WHERE id = :item_id");
            $updateItemQuery->bindValue(':new_due_date', $newDueDate);
            $updateItemQuery->bindValue(':item_id', $item['id']);
            $updateItemQuery->execute();

            error_log("Updated order item ID: " . $item['id'] . " due date from " . $currentDueDate . " to " . $newDueDate);
          }
        } else {
          error_log("No order items found for order ID: " . $invoice['order_id'] . " during proforma conversion");
        }
      } else {
        error_log("Proforma invoice is not associated with an order, no order items to update");
      }

      // Check if the proforma number matches any invoice_id in user_credits
      $credit_query = $pdo->prepare("
        SELECT id FROM user_credits
        WHERE invoice_id = :proforma_number AND status != 'available'
      ");
      $credit_query->bindValue(':proforma_number', $invoice['proforma_number']);
      $credit_query->execute();

      $creditFound = $credit_query->fetch(PDO::FETCH_ASSOC);
      $creditActivated = false;

      if ($creditFound) {
        // Update the credit status to available
        $update_credit = $pdo->prepare("
          UPDATE user_credits
          SET status = 'Available'
          WHERE invoice_id = :proforma_number
        ");
        $update_credit->bindValue(':proforma_number', $invoice['proforma_number']);
        $update_credit->execute();
        $creditActivated = true;

        // Log credit activation
        logInvoiceActivity(
          $pdo,
          $admin_id,
          'credit_activated',
          "Credits activated for invoice {$invoiceNumber}",
          $actualInvoiceId,
          $invoiceNumber,
          $invoice['proforma_number']
        );
      }

      logInvoiceActivity(
        $pdo,
        $admin_id,
        'proforma_converted',
        "Proforma #{$invoice['proforma_number']} has been converted to invoice #{$invoiceNumber}",
        $actualInvoiceId,
        $invoiceNumber,
        $invoice['proforma_number']
      );

      $response = [
        'success' => true,
        'type' => 'conversion',
        'old_id' => $invoice['proforma_number'],
        'new_id' => $invoiceNumber,
        'credit_activated' => $creditActivated,
        'message' => 'Proforma invoice has been Paid and converted to invoice #' . $invoiceNumber
      ];

      // Send payment confirmation email
      try {
        error_log("Attempting to send payment confirmation email for invoice #$invoiceNumber");
        $emailResult = sendInvoicePaidEmail($actualInvoiceId);
        if ($emailResult) {
          error_log("Successfully sent payment confirmation email for invoice #$invoiceNumber");
        } else {
          error_log("Failed to send payment confirmation email for invoice #$invoiceNumber");
        }
      } catch (Exception $emailError) {
        error_log("Error sending payment confirmation email: " . $emailError->getMessage());
        // Don't throw the exception - we don't want to fail the status update if email fails
      }
    }
    // CASE 2: Regular status update - non-proforma or not being marked Paid
    else {
      error_log("Performing regular status update to $newStatus");

      // Build the update query based on whether it's being marked as Paid
      if ($newStatus === 'Paid') {
        $update_query = "UPDATE invoices SET status = '$newStatus', Paid_date = NOW() WHERE id = $actualInvoiceId";

        // Execute the direct query
        $success = $pdo->exec($update_query);

        if ($success === false) {
          throw new Exception("Error executing status update: " . print_r($pdo->errorInfo(), true));
        }

        error_log("Status update successful, rows affected: $success");

        // Find associated orders_items and extend their due dates by one month
        error_log("Finding associated orders_items to extend due dates");

        // First check if this invoice is associated with an order
        if (!empty($invoice['order_id'])) {
          $orderItemsQuery = $pdo->prepare("SELECT id, due_date FROM orders_items WHERE order_id = :order_id");
          $orderItemsQuery->bindValue(':order_id', $invoice['order_id']);
          $orderItemsQuery->execute();
          $orderItems = $orderItemsQuery->fetchAll(PDO::FETCH_ASSOC);

          if (count($orderItems) > 0) {
            error_log("Found " . count($orderItems) . " order items to update");

            foreach ($orderItems as $item) {
              // Calculate new due date (one month from current due date)
              $currentDueDate = $item['due_date'] ? $item['due_date'] : date('Y-m-d H:i:s');
              $newDueDate = date('Y-m-d H:i:s', strtotime($currentDueDate . ' +1 month'));

              // Update the due date
              $updateItemQuery = $pdo->prepare("UPDATE orders_items SET due_date = :new_due_date WHERE id = :item_id");
              $updateItemQuery->bindValue(':new_due_date', $newDueDate);
              $updateItemQuery->bindValue(':item_id', $item['id']);
              $updateItemQuery->execute();

              error_log("Updated order item ID: " . $item['id'] . " due date from " . $currentDueDate . " to " . $newDueDate);
            }
          } else {
            error_log("No order items found for order ID: " . $invoice['order_id']);
          }
        } else {
          error_log("Invoice is not associated with an order, no order items to update");
        }

        // Send payment confirmation email
        try {
          error_log("Attempting to send payment confirmation email for invoice #$actualInvoiceId");
          $emailResult = sendInvoicePaidEmail($actualInvoiceId);
          if ($emailResult) {
            error_log("Successfully sent payment confirmation email for invoice #$actualInvoiceId");
          } else {
            error_log("Failed to send payment confirmation email for invoice #$actualInvoiceId");
          }
        } catch (Exception $emailError) {
          error_log("Error sending payment confirmation email: " . $emailError->getMessage());
          // Don't throw the exception - we don't want to fail the status update if email fails
        }
      } else {
        $update_query = "UPDATE invoices SET status = '$newStatus' WHERE id = $actualInvoiceId";

        // Execute the direct query
        $success = $pdo->exec($update_query);

        if ($success === false) {
          throw new Exception("Error executing status update: " . print_r($pdo->errorInfo(), true));
        }

        error_log("Status update successful, rows affected: $success");
      }


      // Only log activity if there's an actual status change
      if (strtolower($oldStatus) !== strtolower($newStatus)) {
        error_log("Logging status change from {$oldStatus} to {$newStatus}");
        logInvoiceActivity(
          $pdo,
          $admin_id,
          'status_change',
          "Updated invoice status from {$oldStatus} to {$newStatus}",
          $actualInvoiceId,
          $invoice['invoice_number'],
          $invoice['proforma_number']
        );
      } else {
        error_log("No status change to log: {$oldStatus} is the same as {$newStatus}");
      }

      $response = [
        'success' => true,
        'invoice_id' => $invoiceId,
        'message' => 'Invoice status updated successfully'
      ];
    }

    // Commit the transaction
    $pdo->commit();
    error_log("Transaction committed successfully");

    // Return success response
    header('Content-Type: application/json');
    echo json_encode($response);

  } catch (Exception $e) {
    // Roll back transaction if active
    if (isset($pdo) && $pdo->inTransaction()) {
      $pdo->rollBack();
      error_log("Transaction rolled back due to error");
    }

    error_log("Error in update_invoice_status: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    // Return error response
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to update invoice status: ' . $e->getMessage()
    ]);
  }
}


elseif ($_GET['f'] == 'update_invoice_content') {
  try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Get request data
      $data = json_decode(file_get_contents('php://input'), true);

      // Log the incoming request for debugging
      error_log("update_invoice_content request data: " . print_r($data, true));

      // Validate required fields
      if (!isset($data['id']) || empty($data['id'])) {
          throw new Exception("Invoice ID is required");
      }

      // Update invoice content
      $result = updateInvoiceContent($pdo, $admin_id, $data['id'], $data);

      // Return response
      header('Content-Type: application/json');
      echo json_encode($result);

  } catch (Exception $e) {
      error_log("Error in update_invoice_content: " . $e->getMessage());
      error_log("Stack trace: " . $e->getTraceAsString());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
          'success' => false,
          'error' => 'Failed to update invoice: ' . $e->getMessage()
      ]);
  }
}

elseif($_GET['f'] == 'get_invoices'){
  try {
      // Authenticate user
      $admin_id = auth_admin();

      // Get request body parameters
      $json_body = json_decode(file_get_contents('php://input'), true);

      // Get sorting parameters
      $sortField = isset($_GET['sortField']) ? $_GET['sortField'] :
                  (isset($json_body['sortField']) ? $json_body['sortField'] : 'date');
      $sortDirection = isset($_GET['sortDirection']) ? $_GET['sortDirection'] :
                      (isset($json_body['sortDirection']) ? $json_body['sortDirection'] : 'desc');

      // Get filter parameters
      $status = isset($_GET['status']) ? $_GET['status'] :
               (isset($json_body['status']) ? $json_body['status'] : '');
      $client = isset($_GET['client']) ? $_GET['client'] :
               (isset($json_body['client']) ? $json_body['client'] : '');
      $month = isset($_GET['month']) ? $_GET['month'] :
              (isset($json_body['month']) ? $json_body['month'] : '');
      $search = isset($_GET['search']) ? $_GET['search'] :
               (isset($json_body['search']) ? $json_body['search'] : '');

      // Add debug logging
      error_log("API Debug - Get Invoices Request: " . json_encode([
          'sortField' => $sortField,
          'sortDirection' => $sortDirection,
          'status' => $status,
          'client' => $client,
          'month' => $month,
          'search' => $search
      ]));

      // Initialize array to hold all invoices
      $invoices = array();

      // Build SQL query
      $sql = "SELECT i.*, u.first_name, u.last_name, u.company_name, u.email,
             COALESCE(u.company_name, CONCAT(u.first_name, ' ', u.last_name)) as client_name,
             o.label as order_label
             FROM invoices i
             LEFT JOIN users u ON i.user_id = u.id
             LEFT JOIN orders o ON i.order_id = o.id
             WHERE 1=1";

      // Add status filter
      if ($status && $status != 'All') {
          if ($status == 'Paid') {
              $sql .= " AND (i.status = 'Paid')";
          } else if ($status == 'Pending') {
              $sql .= " AND (i.status = 'Pending' AND i.due_date >= NOW())";
          } else if ($status == 'Overdue') {
              $sql .= " AND ((i.status = 'Pending' OR i.status = 'Overdue') AND i.due_date < NOW())";
          } else if ($status == 'Draft') {
              $sql .= " AND i.status = 'Draft'";
          } else if ($status == 'Disputed') {
              $sql .= " AND i.status = 'Disputed'";
          }
      }

      // Add search filter
      if ($search) {
          $sql .= " AND (i.id LIKE " . $pdo->quote("%$search%") .
                 " OR i.invoice_number LIKE " . $pdo->quote("%$search%") .
                 " OR i.proforma_number LIKE " . $pdo->quote("%$search%") .
                 " OR i.description LIKE " . $pdo->quote("%$search%") .
                 " OR i.order_id LIKE " . $pdo->quote("%$search%") .
                 " OR u.company_name LIKE " . $pdo->quote("%$search%") .
                 " OR CONCAT(u.first_name, ' ', u.last_name) LIKE " . $pdo->quote("%$search%") . ")";
      }

      // Add client filter
      if ($client && $client != 'All') {
          $sql .= " AND (u.company_name = " . $pdo->quote($client) .
                 " OR CONCAT(u.first_name, ' ', u.last_name) = " . $pdo->quote($client) . ")";
      }

      // Add month filter
      if ($month && $month != 'All') {
          // Parse month string like "March 2025"
          $date = strtotime($month);
          if ($date) {
              $month_num = date('m', $date);
              $year_num = date('Y', $date);
              $sql .= " AND MONTH(i.date) = " . $pdo->quote($month_num) .
                     " AND YEAR(i.date) = " . $pdo->quote($year_num);
          }
      }

      // Add sorting
      if ($sortField == 'client') {
          $sql .= " ORDER BY client_name " . ($sortDirection === 'asc' ? 'ASC' : 'DESC');
      } else if ($sortField == 'amount') {
          $sql .= " ORDER BY i.value " . ($sortDirection === 'asc' ? 'ASC' : 'DESC');
      } else if ($sortField == 'dueDate') {
          $sql .= " ORDER BY i.due_date " . ($sortDirection === 'asc' ? 'ASC' : 'DESC');
      } else if ($sortField == 'status') {
          $sql .= " ORDER BY i.status " . ($sortDirection === 'asc' ? 'ASC' : 'DESC');
      } else {
          // Default to date sort if field not recognized
          $sql .= " ORDER BY i.date " . ($sortDirection === 'asc' ? 'ASC' : 'DESC');
      }

      // Log the query for debugging
      error_log("API Debug - Get Invoices SQL: $sql");

      // Execute query
      $sth = $pdo->prepare($sql);
      $sth->execute();

      // Fetch invoices
      while ($row = $sth->fetch(PDO::FETCH_ASSOC)) {
          // Get client name
          $clientName = !empty($row['company_name']) ? $row['company_name'] :
                       trim($row['first_name'] . ' ' . $row['last_name']);
          if (empty($clientName)) {
              $clientName = 'Customer #' . $row['user_id'];
          }

          // Format email
          $clientEmail = !empty($row['email']) ? $row['email'] : 'customer' . $row['user_id'] . '@example.com';

          // Status mapping to match old format
          $statusMapping = [
              'Draft' => 'Draft',
              'Pending' => 'Pending',
              'Paid' => 'Paid',
              'Overdue' => 'Overdue',
              'Disputed' => 'Disputed'
          ];

          // Determine display status
          $displayStatus = $statusMapping[$row['status']] ?? ucfirst($row['status']);

          // Override status for Overdue invoices
          if ($row['status'] == 'Pending' && $row['due_date'] && strtotime($row['due_date']) < time()) {
              $displayStatus = 'Overdue';
          }

          // Determine invoice number to display
          $displayNumber = $row['type'] === 'proforma' ? $row['proforma_number'] : $row['invoice_number'];
          if (empty($displayNumber)) {
              $displayNumber = '#' . str_pad($row['id'], 3, '0', STR_PAD_LEFT);
          }

          // Fetch invoice items
          $items_sth = $pdo->prepare("SELECT * FROM invoice_items WHERE invoice_id = :invoice_id");
          $items_sth->bindValue(':invoice_id', $row['id']);
          $items_sth->execute();
          $invoiceItems = $items_sth->fetchAll(PDO::FETCH_ASSOC);

          // Format invoice items
          $formattedItems = [];
          foreach ($invoiceItems as $item) {
              $formattedItems[] = [
                  'description' => $item['description'],
                  'quantity' => floatval($item['quantity']),
                  'period' => $item['period'] ?? '',
                  'unitPrice' => '€' . number_format(floatval($item['unit_price']), 2, '.', ','),
                  'total' => '€' . number_format(floatval($item['total']), 2, '.', ',')
              ];
          }

          // Fallback to single item if no items found
          if (empty($formattedItems)) {
              $formattedItems = [
                  [
                      'description' => $row['description'],
                      'quantity' => 1,
                      'unitPrice' => '€' . number_format($row['subtotal'], 2, '.', ','),
                      'total' => '€' . number_format($row['subtotal'], 2, '.', ',')
                  ]
              ];
          }

          // Format data for frontend - matching the old expected format
          $invoices[] = [
              'id' => $displayNumber,
              'client' => $clientName,
              'clientId' => 'CLI-' . str_pad($row['user_id'], 4, '0', STR_PAD_LEFT),
              'clientEmail' => $clientEmail,
              'date' => $row['date'],
              'dueDate' => $row['due_date'],
              'amount' => '€' . number_format($row['value'], 2, '.', ','),
              'status' => $displayStatus,
              'paymentDate' => $row['Paid_date'],
              'items' => $formattedItems,
              'subtotal' => '€' . number_format($row['subtotal'], 2, '.', ','),
              'tax' => '€' . number_format($row['tax'], 2, '.', ','),
              'total' => '€' . number_format($row['value'], 2, '.', ','),
              'paymentMethod' => $row['payment_method'] ?: 'Bank Transfer',
              'notes' => $row['description'],
              'orderId' => $row['order_id'],
              'orderLabel' => $row['order_label'],
              'type' => ucfirst($row['type']),
              'isProforma' => ($row['type'] === 'proforma'),
              'rawId' => $row['id'] // Internal ID for API calls
          ];
      }

      error_log("API Debug - Found " . count($invoices) . " invoices");

      // Return invoices as a plain JSON array - not wrapped in another object
      header('Content-Type: application/json');
      echo json_encode($invoices);

  } catch (Exception $e) {
      error_log("Error in get_invoices: " . $e->getMessage());
      error_log("Stack trace: " . $e->getTraceAsString());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode(['error' => 'Failed to fetch invoices: ' . $e->getMessage()]);
  }
}





elseif($_GET['f'] == 'search_users'){
    try {
      // Enable detailed error reporting for debugging
      error_log("Starting search_users function");

      // Authenticate admin
      $admin_id = auth_admin();
      error_log("Admin authenticated with ID: $admin_id");

      // Get search query
      $query = isset($_GET['q']) ? $_GET['q'] : '';
      error_log("Search query: $query");

      if (empty($query) || strlen($query) < 2) {
        error_log("Query too short, returning empty array");
        // Return empty array if query is too short
        header('Content-Type: application/json');
        echo json_encode([]);
        return;
      }

      // First check if users table exists
      $tablesResult = $pdo->query("SHOW TABLES");
      $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);
      error_log("Database tables found: " . implode(", ", $tables));

      if (!in_array('users', $tables)) {
        error_log("Users table does not exist, returning test data");
        // Return test data if table doesn't exist
        $testUsers = [
          ['id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>'],
          ['id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>'],
          ['id' => 3, 'name' => 'Michael Johnson', 'email' => '<EMAIL>']
        ];

        // Filter test data based on query
        $filteredUsers = array_filter($testUsers, function($user) use ($query) {
          return (stripos($user['name'], $query) !== false ||
                  stripos($user['email'], $query) !== false);
        });

        header('Content-Type: application/json');
        echo json_encode(array_values($filteredUsers));
        return;
      }

      // Check columns in users table
      $columnsResult = $pdo->query("DESCRIBE users");
      $columns = [];
      while ($row = $columnsResult->fetch(PDO::FETCH_ASSOC)) {
        $columns[] = $row['Field'];
      }
      error_log("Users table columns: " . implode(", ", $columns));

      // Build query based on available columns
      $conditions = [];
      $params = [];

      if (in_array('first_name', $columns)) {
        $conditions[] = "first_name LIKE :first_name";
        $params[':first_name'] = '%' . $query . '%';
      }

      if (in_array('last_name', $columns)) {
        $conditions[] = "last_name LIKE :last_name";
        $params[':last_name'] = '%' . $query . '%';
      }

      if (in_array('email', $columns)) {
        $conditions[] = "email LIKE :email";
        $params[':email'] = '%' . $query . '%';
      }

      if (in_array('username', $columns)) {
        $conditions[] = "username LIKE :username";
        $params[':username'] = '%' . $query . '%';
      }

      if (in_array('first_name', $columns) && in_array('last_name', $columns)) {
        $conditions[] = "CONCAT(first_name, ' ', last_name) LIKE :full_name";
        $params[':full_name'] = '%' . $query . '%';
      }

      if (empty($conditions)) {
        error_log("No searchable columns found in users table");
        header('Content-Type: application/json');
        echo json_encode([]);
        return;
      }

      // Build and execute the query
      $sql = "SELECT id";

      // Add select fields based on available columns
      if (in_array('first_name', $columns)) $sql .= ", first_name";
      if (in_array('last_name', $columns)) $sql .= ", last_name";
      if (in_array('email', $columns)) $sql .= ", email";
      if (in_array('username', $columns) && !in_array('email', $columns)) $sql .= ", username as email";

      $sql .= " FROM users WHERE " . implode(" OR ", $conditions) . " LIMIT 10";

      error_log("Executing SQL: $sql");
      $sth = $pdo->prepare($sql);

      // Bind parameters
      foreach ($params as $key => $value) {
        $sth->bindValue($key, $value);
      }

      $sth->execute();

      // Process results
      $users = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        $fullName = trim($row['first_name'] . ' ' . $row['last_name']);

        // Create a display name that includes full name and email
        $displayName = $fullName;
        if (!empty($row['company_name'])) {
          $displayName .= " (" . $row['company_name'] . ")";
        }
        $displayName .= " - " . $row['email'];

        $users[] = array(
          'id' => $row['id'],
          'company_name' => $row['company_name'],
          'first_name' => $row['first_name'],
          'last_name' => $row['last_name'],
          'full_name' => $fullName,
          'display_name' => $displayName,
          'email' => $row['email']
        );
      }

      error_log("Found " . count($users) . " matching users");

      // Return users list as JSON
      header('Content-Type: application/json');
      echo json_encode($users);

    } catch (Exception $e) {
      error_log("Error in search_users: " . $e->getMessage());
      error_log("Error trace: " . $e->getTraceAsString());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to search users: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
      ]);
    }
  }


  elseif($_GET['f'] == 'get_vat_rate'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Get country code from request
      $countryCode = isset($_GET['country']) ? $_GET['country'] : '';

      if (empty($countryCode)) {
        throw new Exception("Country code is required");
      }

      // Get VAT rate
      $vatRate = getVatRateByCountry($countryCode);

      // Return VAT rate as JSON
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'country' => $countryCode,
        'rate' => $vatRate,
        'rate_percent' => $vatRate * 100
      ]);

    } catch (Exception $e) {
      error_log("Error in get_vat_rate: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'success' => false,
        'error' => 'Failed to get VAT rate: ' . $e->getMessage()
      ]);
    }
  }


  // Add this endpoint to your api_admin_invoices.php file

  elseif ($_GET['f'] == 'get_activity_logs') {
    try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Get request data
      $data = json_decode(file_get_contents('php://input'), true);

      // Log the incoming request
      error_log("get_activity_logs request data: " . print_r($data, true));

      // Get invoice ID from request
      $invoiceId = isset($data['invoice_id']) ? $data['invoice_id'] : null;

      if (empty($invoiceId)) {
        throw new Exception("Invoice ID is required");
      }

      // Clean any non-numeric characters from invoice ID
      if (is_string($invoiceId)) {
        $invoiceId = preg_replace('/\D/', '', $invoiceId);
      }

      error_log("Searching for activity logs with invoice ID: $invoiceId");

      // First, try to find the actual invoice to determine if it's proforma or regular
      $invoice_query = "SELECT * FROM invoices WHERE id = ? OR proforma_number = ? OR invoice_number = ? LIMIT 1";
      $invoice_stmt = $pdo->prepare($invoice_query);
      $invoice_stmt->execute([$invoiceId, $invoiceId, $invoiceId]);
      $invoice = $invoice_stmt->fetch(PDO::FETCH_ASSOC);

      $proforma_number = null;
      $invoice_number = null;

      if ($invoice) {
        error_log("Found invoice: " . print_r($invoice, true));
        $proforma_number = $invoice['proforma_number'];
        $invoice_number = $invoice['invoice_number'];
      } else {
        // If we couldn't find the invoice, just use the ID for both searches
        error_log("Could not find invoice, using ID for both fields");
        $proforma_number = $invoiceId;
        $invoice_number = $invoiceId;
      }

      // Query the activity log
      $activity_query = "SELECT * FROM activity_log WHERE
                        (invoice_number = :invoice_number OR proforma_number = :proforma_number)
                        ORDER BY timestamp DESC";

      $activity_stmt = $pdo->prepare($activity_query);
      $activity_stmt->bindValue(':invoice_number', $invoice_number);
      $activity_stmt->bindValue(':proforma_number', $proforma_number);
      $activity_stmt->execute();

      // Fetch all results
      $activities = [];
      while ($row = $activity_stmt->fetch(PDO::FETCH_ASSOC)) {
        // Map database columns to frontend expected format
        $activities[] = [
          'id' => $row['id'],
          'invoice_id' => $invoiceId,
          'user_id' => $row['user_id'],
          'action' => $row['action'],
          'description' => $row['description'],
          'user_name' => $row['user_name'],
          'timestamp' => $row['timestamp'],
          'type' => $row['activity_type'] ?? 'system'
        ];
      }

      error_log("Found " . count($activities) . " activity records");

      // If no activities found, return empty array
      if (empty($activities)) {
        error_log("No activity records found, returning empty array");
        header('Content-Type: application/json');
        echo json_encode([]);
        return;
      }

      // Return the activities as JSON
      header('Content-Type: application/json');
      echo json_encode($activities);

    } catch (Exception $e) {
      error_log("Error in get_activity_logs: " . $e->getMessage());
      error_log("Stack trace: " . $e->getTraceAsString());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'success' => false,
        'error' => 'Failed to get activity logs: ' . $e->getMessage()
      ]);
    }
  }

  elseif ($_GET['f'] == 'generate_credit_invoice') {
    try {
      // Enable detailed error reporting
      ini_set('display_errors', 1);
      ini_set('display_startup_errors', 1);
      error_reporting(E_ALL);

      // Log the start of the function
      error_log("Starting generate_credit_invoice function");

      // Get request data
      $data = json_decode(file_get_contents('php://input'), true);

      // Log the incoming request for debugging
      error_log("generate_credit_invoice request data: " . print_r($data, true));

      // Authenticate admin - pass token from request data
      error_log("Setting token for authentication: " . ($data['token'] ?? 'null'));
      $_POST['token'] = $data['token'] ?? null;

      try {
        $admin_id = auth_admin();
        error_log("Authentication successful, admin_id: " . $admin_id);
      } catch (Exception $auth_e) {
        error_log("Authentication error: " . $auth_e->getMessage());
        throw new Exception("Authentication failed: " . $auth_e->getMessage());
      }

      // Validate required fields
      if (!isset($data['user_id']) || empty($data['user_id'])) {
        throw new Exception("User ID is required");
      }

      if (!isset($data['amount']) || !is_numeric($data['amount']) || $data['amount'] <= 0) {
        throw new Exception("Amount is required and must be a positive number");
      }

      $userId = $data['user_id'];
      $amount = floatval($data['amount']);
      $description = isset($data['description']) ? $data['description'] : 'Add funds to account';

      error_log("Validated input: userId=$userId, amount=$amount, description=$description");

      // Begin transaction
      error_log("Beginning database transaction");
      $pdo->beginTransaction();

      try {
        // For add funds/credit invoices, we don't apply VAT
        error_log("Add funds invoice - no VAT will be applied");
        $country = getUserCountry($userId);
        error_log("User country: $country (for reference only)");

        // Set tax rate to 0 for add funds
        $tax_rate = 0;
        error_log("VAT rate set to 0 for add funds");

        // Calculate tax and total (tax will be 0)
        $subtotal = $amount;
        $tax = 0;
        $total = $subtotal; // Total equals subtotal since tax is 0
        error_log("Calculated amounts: subtotal=$subtotal, tax=$tax, total=$total");

        // Generate proforma number
        error_log("Generating proforma number");
        $proformaNumber = generateNextProformaNumber($pdo);
        error_log("Generated proforma number: $proformaNumber");

        // Set due date to tomorrow
        $due_date = date('Y-m-d', strtotime('+1 day'));
        error_log("Due date set to: $due_date");

        // Insert the proforma invoice
        error_log("Preparing to insert proforma invoice");
        $invoice_sth = $pdo->prepare("
        INSERT INTO invoices
        (user_id, type, proforma_number, value, subtotal, tax, status, date, due_date, payment_method, description)
        VALUES
        (:user_id, 'proforma', :proforma_number, :value, :subtotal, :tax, 'Pending', NOW(), :due_date, :payment_method, :description)
        ");

        $invoice_sth->bindValue(':user_id', $userId, PDO::PARAM_INT);
        $invoice_sth->bindValue(':proforma_number', $proformaNumber);
        $invoice_sth->bindValue(':value', $total);
        $invoice_sth->bindValue(':subtotal', $subtotal);
        $invoice_sth->bindValue(':tax', $tax);
        $invoice_sth->bindValue(':due_date', $due_date);
        $invoice_sth->bindValue(':payment_method', 'Bank Transfer');
        $invoice_sth->bindValue(':description', $description);

        error_log("Executing invoice insert query");
        $invoice_sth->execute();
        $invoiceId = $pdo->lastInsertId();
        error_log("Invoice inserted with ID: $invoiceId");

        // Create a single invoice item
        error_log("Creating invoice item");
        $item = [
          'description' => $description,
          'quantity' => 1,
          'unitPrice' => $amount
        ];

        // Insert invoice item
        error_log("Preparing to insert invoice item");
        $item_sth = $pdo->prepare("
        INSERT INTO invoice_items
        (invoice_id, description, quantity, unit_price, total)
        VALUES
        (:invoice_id, :description, :quantity, :unit_price, :total)
        ");

        $item_sth->bindValue(':invoice_id', $invoiceId, PDO::PARAM_INT);
        $item_sth->bindValue(':description', $item['description']);
        $item_sth->bindValue(':quantity', $item['quantity']);
        $item_sth->bindValue(':unit_price', $item['unitPrice']);
        $item_sth->bindValue(':total', $item['quantity'] * $item['unitPrice']);
        error_log("Executing invoice item insert");
        $item_sth->execute();

        // Add a Pending credit record
        error_log("Preparing to insert user credit record");
        $credit_sth = $pdo->prepare("
        INSERT INTO user_credits
        (user_id, amount, type, status, created, invoice_id, description)
        VALUES
        (:user_id, :amount, 'Purchase', 'Pending', NOW(), :invoice_id, :description)
        ");

        $credit_sth->bindValue(':user_id', $userId, PDO::PARAM_INT);
        $credit_sth->bindValue(':amount', $amount);
        $credit_sth->bindValue(':invoice_id', $proformaNumber);
        $credit_sth->bindValue(':description', $description);
        error_log("Executing user credit insert");
        $credit_sth->execute();

        // Log the creation
        error_log("Logging invoice activity");
        logInvoiceActivity(
          $pdo,
          $admin_id,
          'proforma_created',
          "Created proforma invoice {$proformaNumber} for user {$userId}",
          $invoiceId,
          null,
          $proformaNumber
        );

        // Commit transaction
        error_log("Committing transaction");
        $pdo->commit();

        // Send email notification
        try {
          error_log("Sending email notification for invoice #$invoiceId");
          $emailResult = sendInvoiceGeneratedEmail($invoiceId);
          if ($emailResult) {
            error_log("Successfully sent email notification for invoice #$invoiceId");
          } else {
            error_log("Failed to send email notification for invoice #$invoiceId");
          }
        } catch (Exception $emailError) {
          error_log("Error sending email notification: " . $emailError->getMessage());
          // Don't throw the exception - we don't want to fail the invoice creation if email fails
        }

        // Get user name for response
        error_log("Getting user name for response");
        $user_query = $pdo->prepare("SELECT CONCAT(first_name, ' ', last_name) as name FROM users WHERE id = ?");
        $user_query->execute([$userId]);
        $user = $user_query->fetch(PDO::FETCH_ASSOC);
        $clientName = $user ? $user['name'] : 'Client #' . $userId;

        // Prepare response data
        error_log("Preparing response data");
        $responseData = [
          'success' => true,
          'invoice_id' => $proformaNumber,
          'proforma_number' => $proformaNumber,
          'id' => "#{$proformaNumber}",
          'type' => 'proforma',
          'isProforma' => true,
          'client' => $clientName,
          'clientId' => $userId,
          'date' => date('Y-m-d'),
          'dueDate' => $due_date,
          'amount' => '€' . number_format($total, 2),
          'status' => 'Pending',
          'items' => [
            [
              'description' => $description,
              'quantity' => 1,
              'unitPrice' => '€' . number_format($amount, 2),
              'total' => '€' . number_format($amount, 2)
            ]
          ],
          'subtotal' => '€' . number_format($subtotal, 2),
          'tax' => '€' . number_format($tax, 2),
          'total' => '€' . number_format($total, 2),
          'paymentMethod' => 'Bank Transfer',
          'notes' => $description
        ];

        // Return response
        error_log("Sending JSON response");
        header('Content-Type: application/json');
        echo json_encode($responseData);

      } catch (Exception $inner_e) {
        // Rollback transaction on error
        $pdo->rollBack();
        error_log("Transaction rolled back due to error: " . $inner_e->getMessage());
        error_log("Inner exception stack trace: " . $inner_e->getTraceAsString());
        throw $inner_e;
      }

    } catch (Exception $e) {
      error_log("Error in generate_credit_invoice: " . $e->getMessage());
      error_log("Stack trace: " . $e->getTraceAsString());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'success' => false,
        'error' => 'Failed to generate credit invoice: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString() // Include stack trace in response for debugging
      ]);
    }
  }


/**
* Update create_order_invoice endpoint to handle combined descriptions
*/
elseif ($_GET['f'] == 'create_order_invoice') {
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);

    // Log the incoming data for debugging
    error_log("create_order_invoice incoming data: " . print_r($data, true));

    // Validate required fields
    if (!isset($data['customerName']) || empty($data['customerName'])) {
      throw new Exception("Customer name is required");
    }

    // Validate customer_id
    if (!isset($data['customer_id']) || empty($data['customer_id'])) {
      throw new Exception("Customer information is required");
    }

    // Begin transaction
    $pdo->beginTransaction();

    try {
      // Identify user from customer information
      $user_data = [
        'client' => $data['customerName'],
        'clientId' => $data['customer_id'] ?? '',
        'clientEmail' => $data['clientEmail'] ?? ''
      ];

      $userId = identifyUser($user_data, $pdo);
      $clientName = $data['customerName'];

      // Get user's country for VAT calculation
      $country = getUserCountry($userId);

      // Get appropriate VAT rate
      $tax_rate = getVatRateByCountry($country);

      // Handle multi-device order
      if (isset($data['devices']) && is_array($data['devices']) && count($data['devices']) > 0) {
        // Generate proforma number
        $proformaNumber = generateNextProformaNumber($pdo);

        // Set due date to tomorrow if not provided
        $due_date = isset($data['dueDate']) && !empty($data['dueDate']) ?
          $data['dueDate'] :
          date('Y-m-d', strtotime('+1 day'));

        // Calculate total from all devices
        $subtotal = 0;
        $invoice_items = [];

        foreach ($data['devices'] as $index => $device) {
          // Get device price
          $device_price = floatval(str_replace(['€', ','], ['', '.'], $device['recurringPrice']));
          $subtotal += $device_price;

          // Check if we have a combined description
          $description = isset($device['combinedDescription']) && !empty($device['combinedDescription'])
            ? $device['combinedDescription']
            : "Server " . ($index + 1) . ": " . ($device['cpuName'] ?? $device['cpuModel'] ?? "Server");

          // Create invoice item for this device - as a SINGLE item
          $invoice_items[] = [
            'description' => $description,
            'quantity' => 1,
            'unitPrice' => $device_price,
            'period' => 'Monthly'
          ];
        }

        // Calculate tax and total
        $tax = $subtotal * $tax_rate;
        $total = $subtotal + $tax;

        // Insert the proforma invoice
        $invoice_sth = $pdo->prepare("
        INSERT INTO invoices
        (user_id, type, proforma_number, value, subtotal, tax, status, date, due_date, payment_method, description)
        VALUES
        (:user_id, 'proforma', :proforma_number, :value, :subtotal, :tax, 'Draft', NOW(), :due_date, :payment_method, :description)
        ");

        $invoice_sth->bindValue(':user_id', $userId, PDO::PARAM_INT);
        $invoice_sth->bindValue(':proforma_number', $proformaNumber);
        $invoice_sth->bindValue(':value', $total);
        $invoice_sth->bindValue(':subtotal', $subtotal);
        $invoice_sth->bindValue(':tax', $tax);
        $invoice_sth->bindValue(':due_date', $due_date);
        $invoice_sth->bindValue(':payment_method', $data['paymentMethod'] ?? 'Bank Transfer');
        $invoice_sth->bindValue(':description', $data['orderNote'] ?? 'Multi-Device Server Order');

        $invoice_sth->execute();
        $invoiceId = $pdo->lastInsertId();

        // Insert invoice items
        insertInvoiceItems($pdo, $invoiceId, $invoice_items);

        // Log the creation
        logInvoiceActivity(
          $pdo,
          $admin_id,
          'proforma_created',
          "Created proforma invoice {$proformaNumber} for {$clientName}",
          $invoiceId,
          null,
          $proformaNumber
        );

      } else {
        // Single device order
        // Make sure we have the required fields
        if (!isset($data['initialPrice']) || !isset($data['recurringPrice'])) {
          throw new Exception("Price information is required");
        }

        // Format prices
        $initialPrice = floatval(str_replace(['€', ','], ['', '.'], $data['initialPrice']));
        $recurringPrice = floatval(str_replace(['€', ','], ['', '.'], $data['recurringPrice']));

        // Generate proforma number
        $proformaNumber = generateNextProformaNumber($pdo);

        // Set due date to tomorrow if not provided
        $due_date = isset($data['dueDate']) && !empty($data['dueDate']) ?
          $data['dueDate'] :
          date('Y-m-d', strtotime('+1 day'));

        // Calculate tax on recurring price
        $subtotal = $recurringPrice;
        $tax = $subtotal * $tax_rate;
        $total = $subtotal + $tax;

        // Insert the proforma invoice
        $invoice_sth = $pdo->prepare("
        INSERT INTO invoices
        (user_id, type, proforma_number, value, subtotal, tax, status, date, due_date, payment_method, description)
        VALUES
        (:user_id, 'proforma', :proforma_number, :value, :subtotal, :tax, 'Draft', NOW(), :due_date, :payment_method, :description)
        ");

        $invoice_sth->bindValue(':user_id', $userId, PDO::PARAM_INT);
        $invoice_sth->bindValue(':proforma_number', $proformaNumber);
        $invoice_sth->bindValue(':value', $total);
        $invoice_sth->bindValue(':subtotal', $subtotal);
        $invoice_sth->bindValue(':tax', $tax);
        $invoice_sth->bindValue(':due_date', $due_date);
        $invoice_sth->bindValue(':payment_method', $data['paymentMethod'] ?? 'Bank Transfer');
        $invoice_sth->bindValue(':description', $data['orderNote'] ?? 'Server Order');

        $invoice_sth->execute();
        $invoiceId = $pdo->lastInsertId();

        // Create a comprehensive description using the combinedDescription if available
        $description = isset($data['combinedDescription']) && !empty($data['combinedDescription'])
          ? $data['combinedDescription']
          : "{$data['orderType']} Order";

        // Create a single invoice item for the entire server
        $invoice_items = [
          [
            'description' => $description,
            'quantity' => 1,
            'unitPrice' => $recurringPrice,
            'period' => 'Monthly'
          ]
        ];

        // Add any setup fee if different from recurring
        if ($initialPrice > $recurringPrice) {
          $setup_fee = $initialPrice - $recurringPrice;
          $invoice_items[] = [
            'description' => "Setup Fee",
            'quantity' => 1,
            'unitPrice' => $setup_fee,
            'period' => 'One-time'
          ];
        }

        // Insert the invoice items
        insertInvoiceItems($pdo, $invoiceId, $invoice_items);

        // Log the creation
        logInvoiceActivity(
          $pdo,
          $admin_id,
          'proforma_created',
          "Created proforma invoice {$proformaNumber} for {$clientName}",
          $invoiceId,
          null,
          $proformaNumber
        );
      }

      // Commit the transaction
      $pdo->commit();

      // Send email notification
      try {
        error_log("Sending email notification for invoice #$invoiceId");
        $emailResult = sendInvoiceGeneratedEmail($invoiceId);
        if ($emailResult) {
          error_log("Successfully sent email notification for invoice #$invoiceId");
        } else {
          error_log("Failed to send email notification for invoice #$invoiceId");
        }
      } catch (Exception $emailError) {
        error_log("Error sending email notification: " . $emailError->getMessage());
        // Don't throw the exception - we don't want to fail the invoice creation if email fails
      }

      // Format invoice items for response
      $formatted_items = array_map(function($item) {
        return [
          'description' => $item['description'],
          'quantity' => $item['quantity'],
          'unitPrice' => '€' . number_format($item['unitPrice'], 2),
          'total' => '€' . number_format($item['quantity'] * $item['unitPrice'], 2),
          'period' => $item['period'] ?? ''
        ];
      }, $invoice_items);

      // Prepare response data
      $responseData = [
        'success' => true,
        'invoice_id' => $invoiceId,
        'proforma_number' => $proformaNumber,
        'id' => "#$proformaNumber",
        'type' => 'proforma',
        'isProforma' => true,
        'client' => $clientName,
        'clientId' => $userId,
        'clientEmail' => $data['clientEmail'] ?? '',
        'date' => date('Y-m-d'),
        'dueDate' => $due_date,
        'amount' => '€' . number_format($total, 2),
        'status' => 'Draft',
        'items' => $formatted_items,
        'message' => 'Invoice created successfully from order'
      ];

      // Return response
      header('Content-Type: application/json');
      echo json_encode($responseData);

    } catch (Exception $inner_e) {
      // Rollback the transaction in case of error
      if ($pdo->inTransaction()) {
        $pdo->rollBack();
      }
      throw $inner_e;
    }

  } catch (Exception $e) {
    error_log("Error in create_order_invoice: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to create order: ' . $e->getMessage()
    ]);
  }
}



?>