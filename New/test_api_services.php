<?php
// Test the get_user_services API endpoint directly
require_once 'auth_functions.php';

echo "=== TESTING GET_USER_SERVICES API ===\n";

$userId = 1;
echo "Testing with User ID: $userId\n\n";

// Simulate the API call
$_GET['f'] = 'get_user_services';
$data = ['user_id' => $userId];

// Include the API file to test the endpoint
ob_start();

try {
    // Manually call the getUserServices function
    require_once 'api_admin_orders.php';
    
    echo "Calling getUserServices function directly...\n";
    $services = getUserServices($pdo, $userId);
    
    echo "Function returned " . count($services) . " services\n";
    
    if (count($services) > 0) {
        echo "\nFirst service data:\n";
        print_r($services[0]);
    } else {
        echo "\nNo services returned\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

$output = ob_get_clean();
echo $output;

echo "\n=== END API TEST ===\n";
?>
