#!/bin/bash

# Script to set up cron job for invoice generation

# Get the current directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
INVOICE_SCRIPT="$SCRIPT_DIR/generate_upcoming_invoices.php"

# Make sure the invoice script exists and is executable
if [ ! -f "$INVOICE_SCRIPT" ]; then
    echo "Error: Invoice script not found at $INVOICE_SCRIPT"
    exit 1
fi

chmod +x "$INVOICE_SCRIPT"

# Create a temporary file for the crontab
TEMP_CRON=$(mktemp)

# Export current crontab
crontab -l > "$TEMP_CRON" 2>/dev/null || echo "# New crontab" > "$TEMP_CRON"

# Check if the cron job already exists
if grep -q "generate_upcoming_invoices.php" "$TEMP_CRON"; then
    echo "Cron job already exists. No changes made."
else
    # Add the new cron job to run at 00:01 every day
    echo "1 0 * * * php $INVOICE_SCRIPT >> /var/log/invoice_generation.log 2>&1" >> "$TEMP_CRON"
    
    # Install the new crontab
    crontab "$TEMP_CRON"
    echo "Cron job installed successfully. It will run daily at 00:01."
fi

# Clean up
rm "$TEMP_CRON"

echo "Setup complete!"
