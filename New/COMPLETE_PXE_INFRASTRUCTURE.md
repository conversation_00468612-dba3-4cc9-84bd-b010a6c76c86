# Complete PXE Infrastructure Documentation

## Overview

This document describes the complete PXE (Preboot eXecution Environment) infrastructure implementation for automated server reinstallation. The system integrates real network infrastructure components including DHCP servers, TFTP servers, and HTTP servers to provide complete automated OS installation capabilities.

## Architecture Components

### 1. Network Infrastructure
- **DHCP Server**: ISC DHCP server for IP allocation and PXE boot options
- **TFTP Server**: Trivial File Transfer Protocol server for boot files
- **HTTP Server**: Apache web server for kickstart/preseed files
- **PXE Boot Files**: SYSLINUX/PXELINUX boot loader components

### 2. Web Interface Components
- **PXE Reinstall Modal**: React component for OS selection and installation initiation
- **PXE Network Config Modal**: React component for complete network infrastructure setup
- **Server View Integration**: Integrated PXE reinstall button in server management

### 3. Backend API Components
- **DHCP Integration**: Real DHCP server configuration and reservation management
- **PXE Server Setup**: Automated PXE boot menu and installation file generation
- **Database Integration**: Comprehensive logging and configuration storage

## Infrastructure Setup

### Prerequisites
- Ubuntu/Debian or CentOS/RHEL server with root access
- Dedicated network interface for PXE services
- Proper firewall configuration for DHCP (67/68), TFTP (69), and HTTP (80)

### Installation Steps

#### 1. Run Infrastructure Setup Script
```bash
# Make the script executable
chmod +x network_infrastructure_setup.sh

# Run as root
sudo ./network_infrastructure_setup.sh
```

#### 2. Configure Network Settings
Edit `/etc/dhcp/dhcpd.conf` to match your network:
```
subnet *********** netmask ************* {
    range ************* *************;
    option routers ***********;
    option domain-name-servers *******, *******;
    next-server ***********0;  # Your TFTP server IP
    filename "pxelinux.0";
}
```

#### 3. Download OS Installation Files
Create directories and download installation media:
```bash
# Ubuntu/Debian
mkdir -p /var/lib/tftpboot/ubuntu
cd /var/lib/tftpboot/ubuntu
wget http://archive.ubuntu.com/ubuntu/dists/focal/main/installer-amd64/current/legacy-images/netboot/ubuntu-installer/amd64/linux
wget http://archive.ubuntu.com/ubuntu/dists/focal/main/installer-amd64/current/legacy-images/netboot/ubuntu-installer/amd64/initrd.gz

# CentOS/RHEL
mkdir -p /var/lib/tftpboot/centos
cd /var/lib/tftpboot/centos
wget http://mirror.centos.org/centos/8/BaseOS/x86_64/os/images/pxeboot/vmlinuz
wget http://mirror.centos.org/centos/8/BaseOS/x86_64/os/images/pxeboot/initrd.img
```

#### 4. Set Permissions
```bash
chown -R tftp:tftp /var/lib/tftpboot
chmod -R 755 /var/lib/tftpboot
chown -R www-data:www-data /var/www/html/kickstart
chmod -R 755 /var/www/html/kickstart
```

## Database Schema

### Enhanced Server Reinstall Log Table
```sql
CREATE TABLE server_reinstall_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    server_id INT NOT NULL,
    server_type ENUM('dedicated', 'blade') DEFAULT 'dedicated',
    os_id INT,
    os_template VARCHAR(100),
    initiated_by INT,
    initiated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ipmi_address VARCHAR(45),
    status ENUM('initiated', 'dhcp_configured', 'pxe_configured', 'completed', 'failed') DEFAULT 'initiated',
    
    -- Network Configuration
    network_config JSON,
    dhcp_config JSON,
    pxe_config JSON,
    
    -- Installation Content
    kickstart_content TEXT,
    
    -- Timestamps
    completed_at TIMESTAMP NULL,
    error_message TEXT,
    
    INDEX idx_server_id (server_id),
    INDEX idx_status (status),
    INDEX idx_initiated_at (initiated_at)
);

-- DHCP Operations Log
CREATE TABLE dhcp_operations_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    server_id INT NOT NULL,
    operation VARCHAR(50) NOT NULL,
    details TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_server_id (server_id),
    INDEX idx_timestamp (timestamp)
);

-- PXE Operations Log  
CREATE TABLE pxe_operations_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    server_id INT NOT NULL,
    operation VARCHAR(50) NOT NULL,
    details JSON,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_server_id (server_id),
    INDEX idx_timestamp (timestamp)
);
```

## API Endpoints

### 1. DHCP Configuration
**Endpoint**: `api_admin_inventory.php?f=setup_pxe_dhcp`

**Request**:
```json
{
  "token": "admin_token",
  "server_id": 123,
  "server_type": "dedicated",
  "mac_address": "aa:bb:cc:dd:ee:ff",
  "network_config": {
    "ip_address": "*************",
    "subnet_mask": "*************",
    "gateway": "***********",
    "dns_primary": "*******",
    "dns_secondary": "*******",
    "hostname": "server123"
  },
  "dhcp_config": {
    "tftp_server": "***********0",
    "pxe_filename": "pxelinux.0",
    "next_server": "***********0"
  }
}
```

**Response**:
```json
{
  "success": true,
  "message": "DHCP reservation created successfully",
  "infrastructure_status": "active",
  "dhcp_config": {
    "mac_address": "aa:bb:cc:dd:ee:ff",
    "ip_address": "*************",
    "hostname": "server123",
    "dhcp_reservation_created": true
  }
}
```

### 2. PXE Server Configuration
**Endpoint**: `api_admin_inventory.php?f=setup_pxe_server`

**Request**:
```json
{
  "token": "admin_token",
  "server_id": 123,
  "mac_address": "aa:bb:cc:dd:ee:ff",
  "network_config": {
    "ip_address": "*************",
    "hostname": "server123"
  },
  "os_template": "Ubuntu 20.04 LTS"
}
```

**Response**:
```json
{
  "success": true,
  "message": "PXE server configuration created successfully",
  "infrastructure_status": "active",
  "pxe_config": {
    "kickstart_url": "http://server.com/kickstart/install-server123-20240101-120000.preseed",
    "pxe_config_file": "01-aa-bb-cc-dd-ee-ff",
    "installation_ready": true
  },
  "next_step": "server_power_cycle"
}
```

### 3. PXE Reinstall Execution
**Endpoint**: `api_admin_inventory.php?f=pxe_reinstall`

**Request**:
```json
{
  "token": "admin_token",
  "server_id": 123,
  "server_type": "dedicated",
  "os_id": 1,
  "os_template": "Ubuntu 20.04 LTS",
  "ipmi_address": "************",
  "ipmi_username": "root",
  "ipmi_password": "password123",
  "network_config": { /* network config */ },
  "dhcp_config": { /* dhcp config */ },
  "mac_address": "aa:bb:cc:dd:ee:ff"
}
```

## Management Scripts

### 1. Add DHCP Host Reservation
```bash
/usr/local/bin/pxe-add-host <hostname> <mac_address> <ip_address> <server_id>
```

### 2. Remove DHCP Host Reservation
```bash
/usr/local/bin/pxe-remove-host <hostname> <server_id>
```

### 3. Create PXE Configuration
```bash
/usr/local/bin/pxe-create-config <mac_address> <hostname> <os_template> <kickstart_url>
```

## Installation Files

### Ubuntu/Debian Preseed Example
```
# Debian/Ubuntu Preseed Configuration
d-i debian-installer/locale string en_US
d-i keyboard-configuration/xkb-keymap select us

# Network configuration
d-i netcfg/choose_interface select auto
d-i netcfg/disable_autoconfig boolean true
d-i netcfg/get_ipaddress string *************
d-i netcfg/get_netmask string *************
d-i netcfg/get_gateway string ***********
d-i netcfg/get_nameservers string *******
d-i netcfg/get_hostname string server123
d-i netcfg/confirm_static boolean true

# User configuration
d-i passwd/root-login boolean true
d-i passwd/root-password password changeme123
d-i passwd/root-password-again password changeme123

# Disk partitioning
d-i partman-auto/method string regular
d-i partman-auto/choose_recipe select atomic
d-i partman/confirm boolean true
d-i partman/confirm_nooverwrite boolean true

# Package selection
tasksel tasksel/first multiselect standard, ssh-server
d-i pkgsel/include string openssh-server curl wget vim htop
```

### CentOS/RHEL Kickstart Example
```
# CentOS/RHEL Kickstart Configuration
install
text
reboot

# System configuration
lang en_US.UTF-8
keyboard us
timezone UTC

# Network configuration
network --bootproto=static --ip=************* --netmask=************* --gateway=*********** --nameserver=******* --hostname=server123

# Security configuration
rootpw --plaintext changeme123
firewall --disabled
selinux --disabled

# Disk configuration
bootloader --location=mbr
zerombr
clearpart --all --initlabel
autopart --type=lvm

# Package selection
%packages
@core
@base
openssh-server
curl
wget
vim
htop
%end

# Post-installation
%post --log=/root/ks-post.log
systemctl enable sshd
systemctl start sshd
%end
```

## PXE Boot Configuration

### MAC-Specific Configuration
File: `/var/lib/tftpboot/pxelinux.cfg/01-aa-bb-cc-dd-ee-ff`
```
# PXE Configuration for server123 (aa:bb:cc:dd:ee:ff)
DEFAULT install
PROMPT 0
TIMEOUT 30

LABEL install
    MENU LABEL Install Ubuntu 20.04 LTS on server123
    KERNEL ubuntu/linux
    APPEND initrd=ubuntu/initrd.gz auto=true url=http://server.com/kickstart/install-server123.preseed hostname=server123

LABEL local
    MENU LABEL Boot from Hard Drive
    LOCALBOOT 0
```

## Process Flow

### Complete Installation Workflow

1. **OS Selection**: User selects operating system and options
2. **Network Configuration**: 
   - Configure IP address, hostname, DNS settings
   - Set up DHCP server with PXE boot options
   - Configure TFTP server with boot files
3. **Installation File Generation**:
   - Generate kickstart/preseed file with network settings
   - Save to web-accessible directory
   - Create MAC-specific PXE boot configuration
4. **Server Boot Configuration**:
   - Configure server IPMI for PXE boot
   - Power cycle server to initiate network boot
5. **Automated Installation**:
   - Server boots from network via PXE
   - DHCP assigns configured IP address
   - Downloads and executes installation files
   - Installs OS with specified configuration
   - Reboots to installed system

### Infrastructure Status Indicators

- **Active**: Real network infrastructure is configured and operational
- **Simulated**: API endpoints work but no real network configuration (for development/testing)

## Troubleshooting

### Common Issues

1. **DHCP Service Failed to Reload**
   - Check DHCP configuration syntax: `dhcpd -t`
   - Review DHCP logs: `journalctl -u isc-dhcp-server`

2. **TFTP Files Not Accessible**
   - Check file permissions: `ls -la /var/lib/tftpboot`
   - Verify TFTP service: `systemctl status tftpd-hpa`

3. **Kickstart Files Not Found**
   - Check web server: `systemctl status apache2`
   - Verify file accessibility: `curl http://server/kickstart/file.cfg`

4. **PXE Boot Fails**
   - Verify network interface configuration
   - Check switch configuration for PXE boot support
   - Confirm server MAC address in DHCP reservations

### Log Files

- DHCP Server: `/var/log/syslog` (Ubuntu) or `/var/log/messages` (CentOS)
- TFTP Server: `/var/log/daemon.log`
- Apache: `/var/log/apache2/access.log` and `/var/log/apache2/error.log`
- PXE Operations: `/var/log/pxe_operations.log`

## Security Considerations

1. **Network Isolation**: Use dedicated management network for PXE operations
2. **Access Control**: Restrict access to DHCP and TFTP services
3. **File Permissions**: Ensure proper permissions on installation files
4. **Password Security**: Use strong default passwords and change them post-installation
5. **Audit Trail**: Comprehensive logging of all PXE operations

## Performance Optimization

1. **TFTP Caching**: Configure TFTP server for optimal file serving
2. **HTTP Compression**: Enable compression for kickstart file serving
3. **Network Bandwidth**: Monitor and optimize network traffic during installations
4. **Parallel Installations**: Manage concurrent installations to avoid resource conflicts

## Maintenance

### Regular Tasks

1. **Clean Old Files**: Remove old kickstart and PXE configuration files
2. **Update OS Images**: Regularly update kernel and initrd files
3. **Monitor Logs**: Review operation logs for errors or issues
4. **Backup Configuration**: Backup DHCP and PXE configurations
5. **Test Functionality**: Regularly test PXE boot process

### Backup and Recovery

1. **Configuration Backup**:
   ```bash
   # Backup DHCP configuration
   cp /etc/dhcp/dhcpd.conf /backup/dhcpd.conf.$(date +%Y%m%d)
   
   # Backup PXE files
   tar -czf /backup/pxe-files-$(date +%Y%m%d).tar.gz /var/lib/tftpboot
   ```

2. **Database Backup**:
   ```bash
   mysqldump -u root -p database_name server_reinstall_log dhcp_operations_log pxe_operations_log > pxe_tables_backup.sql
   ```

## Integration with Existing Infrastructure

### Network Integration
- Coordinate with network team for VLAN configuration
- Ensure proper routing for PXE traffic
- Configure firewall rules for required ports

### Monitoring Integration
- Integrate with existing monitoring systems
- Set up alerts for failed installations
- Monitor DHCP lease usage and conflicts

### Inventory Integration
- Sync with existing CMDB systems
- Update asset management with installation status
- Integrate with change management processes

This comprehensive PXE infrastructure provides automated, reliable, and scalable server reinstallation capabilities with full network integration and proper audit trails. 