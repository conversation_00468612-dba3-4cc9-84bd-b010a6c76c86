<?php
/**
 * Debug script for PXE reinstall functionality
 * This script simulates the API call to identify the 500 error
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Debug: PXE Reinstall API Call\n";
echo "=============================\n\n";

try {
    // Test if mysql.php exists and loads
    echo "1. Testing mysql.php inclusion...\n";
    if (file_exists('mysql.php')) {
        require_once 'mysql.php';
        echo "✅ mysql.php loaded successfully\n";
        
        // Test database connection
        if (isset($pdo)) {
            echo "✅ PDO connection available\n";
        } else {
            echo "❌ PDO connection not available\n";
        }
    } else {
        echo "❌ mysql.php not found\n";
    }
    
    // Test if auth_functions.php exists and loads
    echo "\n2. Testing auth_functions.php inclusion...\n";
    if (file_exists('auth_functions.php')) {
        require_once 'auth_functions.php';
        echo "✅ auth_functions.php loaded successfully\n";
    } else {
        echo "❌ auth_functions.php not found\n";
    }
    
    // Test if pxe_api_integration.php exists and loads
    echo "\n3. Testing pxe_api_integration.php inclusion...\n";
    if (file_exists('pxe_api_integration.php')) {
        require_once 'pxe_api_integration.php';
        echo "✅ pxe_api_integration.php loaded successfully\n";
        
        // Test PXENetworkManager class
        if (class_exists('PXENetworkManager')) {
            echo "✅ PXENetworkManager class available\n";
            
            // Test creating an instance
            try {
                $pxe_manager = new PXENetworkManager($pdo);
                echo "✅ PXENetworkManager instance created successfully\n";
            } catch (Exception $e) {
                echo "❌ Failed to create PXENetworkManager: " . $e->getMessage() . "\n";
            }
        } else {
            echo "❌ PXENetworkManager class not found\n";
        }
    } else {
        echo "❌ pxe_api_integration.php not found\n";
    }
    
    // Test the specific function logic
    echo "\n4. Testing PXE reinstall logic...\n";
    
    // Simulate the data that would come from the API call
    $test_data = [
        'server_id' => 'test-123',
        'server_type' => 'dedicated',
        'os_id' => '1',
        'ipmi_address' => '*************',
        'ipmi_username' => 'root',
        'ipmi_password' => 'password123',
        'network_config' => [
            'hostname' => 'xr-bl41s4.zetservers.com',
            'ip_address' => '************',
            'subnet_mask' => '***************',
            'gateway' => '************',
            'dns_primary' => '*******'
        ],
        'mac_address' => '00:8C:FA:FB:DB:18'
    ];
    
    echo "Test data prepared\n";
    
    // Test server table query
    $server_table = 'inventory_dedicated_servers';
    $server_query = "SELECT * FROM $server_table WHERE id = :server_id LIMIT 1";
    echo "Testing query: $server_query\n";
    
    try {
        $server_sth = $pdo->prepare($server_query);
        echo "✅ Server query prepared successfully\n";
    } catch (Exception $e) {
        echo "❌ Server query preparation failed: " . $e->getMessage() . "\n";
    }
    
    // Test OS table query
    $os_query = "SELECT * FROM dedicated_os WHERE id = :os_id LIMIT 1";
    echo "Testing query: $os_query\n";
    
    try {
        $os_sth = $pdo->prepare($os_query);
        echo "✅ OS query prepared successfully\n";
    } catch (Exception $e) {
        echo "❌ OS query preparation failed: " . $e->getMessage() . "\n";
    }
    
    // Test directory permissions
    echo "\n5. Testing directory permissions...\n";
    $autoinstall_dir = '/var/www/html/New/autoinstall';
    if (is_dir($autoinstall_dir)) {
        echo "✅ Autoinstall directory exists: $autoinstall_dir\n";
        if (is_writable($autoinstall_dir)) {
            echo "✅ Autoinstall directory is writable\n";
        } else {
            echo "❌ Autoinstall directory is not writable\n";
        }
    } else {
        echo "❌ Autoinstall directory does not exist: $autoinstall_dir\n";
    }
    
    $dhcp_file = '/etc/dhcp/dhcpd.conf';
    if (file_exists($dhcp_file)) {
        echo "✅ DHCP config file exists: $dhcp_file\n";
        if (is_writable($dhcp_file)) {
            echo "✅ DHCP config file is writable\n";
        } else {
            echo "❌ DHCP config file is not writable\n";
        }
    } else {
        echo "❌ DHCP config file does not exist: $dhcp_file\n";
    }
    
    // Test PXE setup with safe data
    echo "\n6. Testing PXE setup with test data...\n";
    if (isset($pxe_manager)) {
        try {
            $server_data_for_pxe = [
                'mac' => $test_data['mac_address'],
                'id' => $test_data['server_id'],
                'label' => 'test-server'
            ];
            
            // Only test if we have write permissions
            if (is_writable('/var/www/html/New/autoinstall') && is_writable('/etc/dhcp/dhcpd.conf')) {
                echo "⚠️  Skipping actual PXE setup to avoid modifying system files\n";
                echo "✅ All prerequisites for PXE setup are met\n";
            } else {
                echo "⚠️  Cannot test PXE setup due to permission issues\n";
            }
            
        } catch (Exception $e) {
            echo "❌ PXE setup test failed: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n7. Testing individual PXE functions...\n";
    if (isset($pxe_manager)) {
        try {
                         // Test MAC address formatting (using reflection since method is private)
             $reflection = new ReflectionClass($pxe_manager);
             $method = $reflection->getMethod('formatMacAddress');
             $method->setAccessible(true);
             $mac_clean = $method->invoke($pxe_manager, $test_data['mac_address']);
             if ($mac_clean) {
                 echo "✅ MAC address formatting works: " . $mac_clean . "\n";
             } else {
                 echo "❌ MAC address formatting failed\n";
             }
            
            // Test hostname extraction
            $servername = $pxe_manager->extractServerName($test_data['network_config']['hostname']);
            echo "✅ Server name extraction works: " . $servername . "\n";
            
        } catch (Exception $e) {
            echo "❌ Individual function test failed: " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Critical error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nDebug completed.\n";
?> 