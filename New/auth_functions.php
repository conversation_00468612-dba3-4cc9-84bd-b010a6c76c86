<?php
require_once("mysql.php");

// Define allowed origins - update with your actual frontend domain
$allowed_origins = [
    'https://test.x-zoneit.ro',       // Main site
    'http://test.x-zoneit.ro',        // Main site (http)
    'http://localhost:3000',          // Local development
    'http://localhost:5173',          // Vite dev server
    'http://127.0.0.1:5173',          // Development via IP address
    'https://admin.test.x-zoneit.ro', // Admin site
    'null',                           // Allow null origin for local file testing
    '*'                               // Allow all origins temporarily for debugging
];

// Debug: Log the request origin
error_log("Request origin: " . ($_SERVER['HTTP_ORIGIN'] ?? 'none'));
error_log("Request method: " . ($_SERVER['REQUEST_METHOD'] ?? 'none'));
error_log("Request headers: " . json_encode(function_exists('getallheaders') ? getallheaders() : []));

date_default_timezone_set('Europe/Bucharest');

function getCurrentTimestamp() {
  // Create timestamp in a consistent format
  return date('Y-m-d H:i:s');
}

function formatTimestampForDisplay($timestamp) {
  if (empty($timestamp)) return '';

  // First convert the timestamp to the correct timezone if needed
  $date = new DateTime($timestamp);
  $date->setTimezone(new DateTimeZone(date_default_timezone_get()));

  // Format consistently
  return $date->format('Y-m-d H:i:s');
}

// Get the origin from the request headers
$origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';

// Set CORS headers - allow all origins temporarily for debugging
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, Origin");
header("Access-Control-Allow-Credentials: true");
header("Access-Control-Max-Age: 86400");

// Other security headers (relaxed for debugging)
header("X-Content-Type-Options: nosniff");
header("X-Frame-Options: SAMEORIGIN");
// Temporarily disable CSP for debugging
// header("Content-Security-Policy: default-src 'self'");
header("Strict-Transport-Security: max-age=31536000; includeSubDomains");

// Fallback implementation of getallheaders() if it doesn't exist
if (!function_exists('getallheaders')) {
  function getallheaders() {
    $headers = [];
    foreach ($_SERVER as $name => $value) {
      if (substr($name, 0, 5) === 'HTTP_') {
        $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
      }
    }
    return $headers;
  }
}

// Debug: Log the request method and headers
error_log("Request method: " . ($_SERVER['REQUEST_METHOD'] ?? 'none'));
error_log("Request headers: " . json_encode(function_exists('getallheaders') ? getallheaders() : []));

// Handle preflight requests
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
  http_response_code(200);
  exit;
}

// Note: JSON header will be set by individual endpoints as needed

// Get client IP - updated to prioritize Cloudflare headers
function getClientIP() {
  // First check for Cloudflare IP header
  if (isset($_SERVER['HTTP_CF_CONNECTING_IP']) && !empty($_SERVER['HTTP_CF_CONNECTING_IP'])) {
    $ip = trim($_SERVER['HTTP_CF_CONNECTING_IP']);
    // Basic validation
    if (filter_var($ip, FILTER_VALIDATE_IP) !== false) {
      error_log("Using Cloudflare IP: " . $ip);
      return $ip;
    }
  }

  // Log Cloudflare headers for debugging
  error_log("CF Headers: " . json_encode(array_filter($_SERVER, function($key) {
    return strpos($key, 'HTTP_CF_') === 0;
  }, ARRAY_FILTER_USE_KEY)));

  // Fall back to regular IP detection
  $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];

  foreach ($ip_keys as $key) {
    if (array_key_exists($key, $_SERVER) === true) {
      foreach (explode(',', $_SERVER[$key]) as $ip) {
        $ip = trim($ip);
        // Validate IP format - removed the FILTER_FLAG_NO_PRIV_RANGE flag to allow private IPs behind Cloudflare
        if (filter_var($ip, FILTER_VALIDATE_IP) !== false) {
          error_log("Using fallback IP from {$key}: {$ip}");
          return $ip;
        }
      }
    }
  }

  $default_ip = $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
  error_log("Using default IP: " . $default_ip);
  return $default_ip; // Default fallback
}

$client_ip = getClientIP();
error_log("Final client IP determined: " . $client_ip);

// Generate cryptographically secure random string
function generateRandom($length = 32) {
  return bin2hex(random_bytes($length / 2));
}

// Authentication helper function
function getRequestData() {
  // Check if data is sent as JSON or as form data
  $input = file_get_contents('php://input');

  // Debug: Log the raw input
  error_log("Raw request input: " . $input);

  // Try to parse as JSON
  $data = json_decode($input, true);
  $jsonError = json_last_error();

  // Debug: Log JSON parsing result
  error_log("JSON parse result: " . ($jsonError === JSON_ERROR_NONE ? "Success" : "Error: " . json_last_error_msg()));

  if ($jsonError !== JSON_ERROR_NONE) {
    // Debug: Log POST data if JSON parsing failed
    error_log("Falling back to POST data: " . json_encode($_POST));
    return $_POST;
  }

  return $data;
}


// Admin authentication function - updated to handle tokens without strict IP binding
function auth_admin() {
  global $pdo;
  global $client_ip;

  $data = getRequestData();
  $token = $data['token'] ?? '';

  if (empty($token)) {
    return respondWithError(5, 'Admin authentication required - no token provided');
  }

  // Additional security: token should be at least 32 chars
  if (strlen($token) < 32) {
    return respondWithError(5, 'Admin authentication required - invalid token format');
  }

  // Use a prepared statement to prevent SQL injection
  // Changed from 24 HOUR to 30 DAY for extended session
  // IMPORTANT: Removed IP check to allow for Cloudflare IP changes
  $stmt = $pdo->prepare("
    SELECT id, last_login, last_ip
    FROM `admins`
    WHERE `last_session` = :token
    AND `last_login` > NOW() - INTERVAL 30 DAY
    LIMIT 1
  ");

  $stmt->bindValue(':token', $token);
  $stmt->execute();

  if ($stmt->rowCount() !== 1) {
    return respondWithError(5, 'Admin authentication required - invalid token');
  }

  $row = $stmt->fetch(PDO::FETCH_ASSOC);

  // Log IP mismatch but still allow authentication
  if ($row['last_ip'] !== $client_ip) {
    error_log("IP mismatch - Token IP: {$row['last_ip']}, Current IP: {$client_ip}");

    // Optional: Update the IP to the current one
    try {
      $update_ip = $pdo->prepare("UPDATE `admins` SET `last_ip` = :ip WHERE `id` = :id");
      $update_ip->bindValue(':ip', $client_ip);
      $update_ip->bindValue(':id', $row['id']);
      $update_ip->execute();
    } catch (Exception $e) {
      error_log("Failed to update IP: " . $e->getMessage());
    }
  }

  return $row['id'];
}

// Helper function for consistent error responses
function respondWithError($code, $message = '') {
  $response = [
    'error' => $code,
    'message' => $message,
    'success' => false
  ];

  echo json_encode($response);
  exit;
}

// Helper function for success responses
function respondWithSuccess($data = []) {
  $response = array_merge(['error' => 0, 'success' => true], $data);
  echo json_encode($response);
  exit;
}

// One-time function to migrate existing MD5 hashes to password_hash (run ONCE)
function migratePasswordHashes() {
  global $pdo;

  // Select all existing users with their MD5 passwords
  $stmt = $pdo->query("SELECT id, email, password FROM `admins`");

  while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    // Hash the existing MD5 password using PASSWORD_DEFAULT
    $newPasswordHash = password_hash($row['password'], PASSWORD_DEFAULT);

    // Update the password hash in the database
    $updateStmt = $pdo->prepare("UPDATE `admins` SET password = :new_hash WHERE id = :id");
    $updateStmt->bindValue(':new_hash', $newPasswordHash);
    $updateStmt->bindValue(':id', $row['id']);
    $updateStmt->execute();
  }

  echo "Password migration complete.";
  exit;
}


function trackLoginAttempt($username, $ip_address, $success) {
  global $pdo;

  try {
      $stmt = $pdo->prepare("
          INSERT INTO `login_attempts`
          (ip_address, username, is_successful)
          VALUES (:ip, :username, :success)
      ");

      $stmt->execute([
          ':ip' => $ip_address,
          ':username' => $username,
          ':success' => $success ? 1 : 0
      ]);
  } catch (Exception $e) {
      error_log("Login attempt tracking error: " . $e->getMessage());
  }
}

// Enhanced audit logging for admin switches
function trackAdminSwitch($from_admin_id, $from_admin_email, $to_admin_id, $to_admin_email, $switch_type, $ip_address, $success, $details = '') {
  global $pdo;

  try {
      $stmt = $pdo->prepare("
          INSERT INTO `admin_switches`
          (`from_admin_id`, `from_admin_email`, `to_admin_id`, `to_admin_email`, `switch_type`, `ip_address`, `success`, `details`, `switch_time`)
          VALUES (:from_admin_id, :from_admin_email, :to_admin_id, :to_admin_email, :switch_type, :ip_address, :success, :details, NOW())
      ");

      $stmt->bindValue(':from_admin_id', $from_admin_id);
      $stmt->bindValue(':from_admin_email', $from_admin_email);
      $stmt->bindValue(':to_admin_id', $to_admin_id);
      $stmt->bindValue(':to_admin_email', $to_admin_email);
      $stmt->bindValue(':switch_type', $switch_type);
      $stmt->bindValue(':ip_address', $ip_address);
      $stmt->bindValue(':success', $success ? 1 : 0);
      $stmt->bindValue(':details', $details);
      $stmt->execute();

  } catch (Exception $e) {
      error_log("Admin switch tracking error: " . $e->getMessage());
      // If table doesn't exist, try to create it
      try {
          $pdo->exec("
              CREATE TABLE IF NOT EXISTS `admin_switches` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `from_admin_id` int(11) NOT NULL,
                  `from_admin_email` varchar(255) NOT NULL,
                  `to_admin_id` int(11) NOT NULL,
                  `to_admin_email` varchar(255) NOT NULL,
                  `switch_type` enum('authenticate', 'quick_switch', 'return_primary', 'remove_remembered') NOT NULL,
                  `ip_address` varchar(45) NOT NULL,
                  `success` tinyint(1) NOT NULL,
                  `details` text,
                  `switch_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  KEY `from_admin_id` (`from_admin_id`),
                  KEY `to_admin_id` (`to_admin_id`),
                  KEY `switch_time` (`switch_time`),
                  KEY `ip_address` (`ip_address`)
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
          ");
          // Try the insert again
          $stmt->execute();
      } catch (Exception $e2) {
          error_log("Admin switch tracking table creation error: " . $e2->getMessage());
      }
  }
}

// Rate limiting specifically for admin switches
function validateAdminSwitchAttempts($from_admin_id, $ip_address) {
  global $pdo;

  // Configuration for admin switches - more restrictive
  $max_attempts = 5;
  $lockout_duration_minutes = 15;

  try {
      // First ensure the table exists
      $pdo->exec("
          CREATE TABLE IF NOT EXISTS `admin_switches` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `from_admin_id` int(11) NOT NULL,
              `from_admin_email` varchar(255) NOT NULL,
              `to_admin_id` int(11) NOT NULL,
              `to_admin_email` varchar(255) NOT NULL,
              `switch_type` enum('authenticate', 'quick_switch', 'return_primary', 'remove_remembered') NOT NULL,
              `ip_address` varchar(45) NOT NULL,
              `success` tinyint(1) NOT NULL,
              `details` text,
              `switch_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `from_admin_id` (`from_admin_id`),
              KEY `to_admin_id` (`to_admin_id`),
              KEY `switch_time` (`switch_time`),
              KEY `ip_address` (`ip_address`)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ");

      // Check recent failed admin switch attempts
      $stmt = $pdo->prepare("
          SELECT COUNT(*) as failed_attempts
          FROM `admin_switches`
          WHERE (`from_admin_id` = :from_admin_id OR `ip_address` = :ip_address)
          AND `success` = 0
          AND `switch_time` > NOW() - INTERVAL :lockout_duration MINUTE
      ");

      $stmt->bindValue(':from_admin_id', $from_admin_id);
      $stmt->bindValue(':ip_address', $ip_address);
      $stmt->bindValue(':lockout_duration', $lockout_duration_minutes);
      $stmt->execute();

      $result = $stmt->fetch(PDO::FETCH_ASSOC);

      // Check if attempts exceed maximum
      if ($result['failed_attempts'] >= $max_attempts) {
          error_log("Admin switch blocked - too many attempts. Admin ID: $from_admin_id, IP: $ip_address, Failed attempts: " . $result['failed_attempts']);
          return false;
      }

      return true;
  } catch (Exception $e) {
      error_log("Admin switch attempts validation error: " . $e->getMessage());
      // If there's an error with the validation, allow the attempt but log it
      return true;
  }
}

// Enhanced Login Attempt Validation - modified to be less strict on IP
function validateLoginAttempts($username, $ip_address) {
  global $pdo;

  // Configuration
  $max_attempts = 5;
  $lockout_duration_minutes = 15;

  try {
      // Delete old attempts
      $cleanup_stmt = $pdo->prepare("
          DELETE FROM `login_attempts`
          WHERE attempt_time < NOW() - INTERVAL :duration MINUTE
      ");
      $cleanup_stmt->bindValue(':duration', $lockout_duration_minutes);
      $cleanup_stmt->execute();

      // Count recent failed attempts - check username only, not IP
      $check_stmt = $pdo->prepare("
          SELECT COUNT(*) AS failed_attempts
          FROM `login_attempts`
          WHERE username = :username
          AND is_successful = 0
          AND attempt_time > NOW() - INTERVAL :duration MINUTE
      ");

      $check_stmt->bindValue(':username', $username);
      $check_stmt->bindValue(':duration', $lockout_duration_minutes);
      $check_stmt->execute();

      $result = $check_stmt->fetch(PDO::FETCH_ASSOC);

      // Check if attempts exceed maximum
      if ($result['failed_attempts'] >= $max_attempts) {
          error_log("Login blocked - too many attempts. IP: $ip_address, Username: $username");
          return false;
      }

      return true;
  } catch (Exception $e) {
      error_log("Login attempts validation error: " . $e->getMessage());
      return false;
  }
}

// Parse request and route to the appropriate handler
$function = $_GET['f'] ?? '';

// Admin login endpoint

if ($function === 'admin_login') {
  global $client_ip;

  // Debug: Log the admin login attempt
  error_log("Admin login attempt from IP: $client_ip");

  // Validate input method and content
  $data = getRequestData();

  // Debug: Log the received data
  error_log("Login data received: " . json_encode($data));

  // Strict input validation
  if (!isset($data['username']) || !isset($data['password'])) {
      error_log("Login attempt with missing credentials");
      respondWithError(1, 'Invalid login credentials');
  }

  $username = trim($data['username']);
  $password = $data['password'];

  // Validate username format (basic email validation)
  if (!filter_var($username, FILTER_VALIDATE_EMAIL)) {
      error_log("Invalid email format: $username");
      respondWithError(1, 'Invalid email format');
  }

  // Check login attempts before processing
  if (!validateLoginAttempts($username, $client_ip)) {
      respondWithError(3, "Too many failed attempts. Please try again later.");
  }

  try {
      // Secure admin lookup
      $stmt = $pdo->prepare("
          SELECT id, email, password
          FROM `admins`
          WHERE LOWER(email) = LOWER(:username)
      ");

      $stmt->bindValue(':username', $username);
      $stmt->execute();

      $admin = $stmt->fetch(PDO::FETCH_ASSOC);

      // Verify credentials
      if (!$admin || !password_verify($password, $admin['password'])) {
          // Log failed attempt
          trackLoginAttempt($username, $client_ip, false);

          error_log("Failed login attempt for username: $username");
          respondWithError(1, 'Invalid credentials');
      }

      // Successful login
      // Generate new session token
      $token = generateRandom(32);

      // Update admin session
      $updateStmt = $pdo->prepare("
          UPDATE `admins`
          SET `last_session` = :token,
              `last_ip` = :last_ip,
              `last_login` = NOW()
          WHERE `id` = :admin_id
      ");

      $updateStmt->bindValue(':admin_id', $admin['id']);
      $updateStmt->bindValue(':last_ip', $client_ip);
      $updateStmt->bindValue(':token', $token);
      $updateStmt->execute();

      // Log successful attempt
      trackLoginAttempt($username, $client_ip, true);

      // Return success response
      respondWithSuccess([
          'token' => $token,
          'is_admin' => true
      ]);

  } catch (Exception $e) {
      error_log("Admin login process error: " . $e->getMessage());
      respondWithError(4, 'Server error during login');
  }
}


// Admin Login Check endpoint
elseif ($function === 'admin_login_check') {
  global $client_ip;
  $data = getRequestData();

  if (empty($data['token'])) {
    respondWithError(1, 'No token provided');
  }

  try {
    // Changed from 24 HOUR to 30 DAY for extended session
    // IMPORTANT: Removed IP check to allow for Cloudflare IP changes
    $stmt = $pdo->prepare("
      SELECT 1
      FROM `admins`
      WHERE `last_session` = :token
      AND `last_login` > NOW() - INTERVAL 30 DAY
      LIMIT 1
    ");

    $stmt->bindValue(':token', $data['token']);
    $stmt->execute();

    if ($stmt->rowCount() === 1) {
      respondWithSuccess(['is_admin' => true]);
    } else {
      respondWithError(1, 'Invalid or expired token');
    }

  } catch (Exception $e) {
    error_log("Admin token check error: " . $e->getMessage());
    respondWithError(4, 'Server error');
  }
}

// Admin Switch Authentication endpoint - authenticate new admin for switching
elseif ($function === 'authenticate_admin_switch') {
  global $client_ip;
  $data = getRequestData();

  // Debug logging
  error_log("Admin switch attempt - Data received: " . json_encode($data));

  // Validate required fields
  if (empty($data['email']) || empty($data['password']) || empty($data['current_token'])) {
    error_log("Admin switch - Missing required fields");
    respondWithError(1, 'Missing required fields: email, password, current_token');
  }

  $email = trim($data['email']);
  $password = $data['password'];
  $current_token = $data['current_token'];

  // Validate email format
  if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    error_log("Invalid email format for admin switch: $email");
    respondWithError(1, 'Invalid email format');
  }

  try {
    // First verify the current admin token is valid
    $current_admin_stmt = $pdo->prepare("
      SELECT id, email
      FROM `admins`
      WHERE `last_session` = :current_token
      AND `last_login` > NOW() - INTERVAL 30 DAY
      LIMIT 1
    ");
    $current_admin_stmt->bindValue(':current_token', $current_token);
    $current_admin_stmt->execute();

    if ($current_admin_stmt->rowCount() !== 1) {
      error_log("Invalid current admin token for switch attempt");
      respondWithError(1, 'Current admin session invalid');
    }

    $current_admin = $current_admin_stmt->fetch(PDO::FETCH_ASSOC);

    // Check admin switch rate limiting (temporarily disabled for testing)
    // if (!validateAdminSwitchAttempts($current_admin['id'], $client_ip)) {
    //   trackAdminSwitch($current_admin['id'], $current_admin['email'], 0, $email, 'authenticate', $client_ip, false, 'Rate limited');
    //   respondWithError(3, "Too many admin switch attempts. Please try again later.");
    // }

    // Check login attempts for the target admin
    if (!validateLoginAttempts($email, $client_ip)) {
      trackAdminSwitch($current_admin['id'], $current_admin['email'], 0, $email, 'authenticate', $client_ip, false, 'Login rate limited');
      respondWithError(3, "Too many failed attempts for target admin. Please try again later.");
    }

    // Authenticate the target admin
    $target_admin_stmt = $pdo->prepare("
      SELECT a.id, a.email, a.password, a.first_name, a.last_name, a.function,
             a.picture_url, a.department_id, d.department_name
      FROM `admins` a
      LEFT JOIN departments d ON a.department_id = d.id
      WHERE LOWER(a.email) = LOWER(:email)
    ");
    $target_admin_stmt->bindValue(':email', $email);
    $target_admin_stmt->execute();

    $target_admin = $target_admin_stmt->fetch(PDO::FETCH_ASSOC);

    // Verify target admin credentials
    if (!$target_admin || !password_verify($password, $target_admin['password'])) {
      // Log failed attempt
      trackLoginAttempt($email, $client_ip, false);
      trackAdminSwitch($current_admin['id'], $current_admin['email'], $target_admin ? $target_admin['id'] : 0, $email, 'authenticate', $client_ip, false, 'Invalid credentials');
      error_log("Failed admin switch attempt from {$current_admin['email']} to $email");
      respondWithError(1, 'Invalid target admin credentials');
    }

    // Prevent switching to the same admin
    if ($current_admin['id'] === $target_admin['id']) {
      trackAdminSwitch($current_admin['id'], $current_admin['email'], $target_admin['id'], $target_admin['email'], 'authenticate', $client_ip, false, 'Same admin switch attempt');
      respondWithError(1, 'Cannot switch to the same admin account');
    }

    // Generate new session token for the target admin
    $switch_token = generateRandom(32);

    // For admin switching, we'll create a temporary session token
    // We could either update their main session or create a separate switching session
    // For now, let's update their session but keep track that this is a switch
    $update_stmt = $pdo->prepare("
      UPDATE `admins`
      SET `last_session` = :token,
          `last_ip` = :last_ip,
          `last_login` = NOW()
      WHERE `id` = :admin_id
    ");
    $update_stmt->bindValue(':admin_id', $target_admin['id']);
    $update_stmt->bindValue(':token', $switch_token);
    $update_stmt->bindValue(':last_ip', $client_ip);
    $update_stmt->execute();

    // Log successful switch attempt
    trackLoginAttempt($email, $client_ip, true);
    trackAdminSwitch($current_admin['id'], $current_admin['email'], $target_admin['id'], $target_admin['email'], 'authenticate', $client_ip, true, 'Successful authentication switch');

    // Log the admin switch for audit purposes
    error_log("Admin switch: {$current_admin['email']} (ID: {$current_admin['id']}) switched to {$target_admin['email']} (ID: {$target_admin['id']}) from IP: $client_ip");

    // Return success with admin data and new token
    respondWithSuccess([
      'token' => $switch_token,
      'admin' => [
        'id' => $target_admin['id'],
        'email' => $target_admin['email'],
        'first_name' => $target_admin['first_name'],
        'last_name' => $target_admin['last_name'],
        'function' => $target_admin['function'],
        'department_name' => $target_admin['department_name'],
        'picture_url' => $target_admin['picture_url']
      ]
    ]);

  } catch (Exception $e) {
    error_log("Admin switch authentication error: " . $e->getMessage());
    respondWithError(4, 'Server error during admin switch');
  }
}

// Switch to Remembered Admin endpoint - quick switch using stored session
elseif ($function === 'switch_to_remembered_admin') {
  global $client_ip;
  $data = getRequestData();

  // Validate required fields
  if (empty($data['admin_id']) || empty($data['admin_token']) || empty($data['current_token'])) {
    respondWithError(1, 'Missing required fields: admin_id, admin_token, current_token');
  }

  $admin_id = $data['admin_id'];
  $admin_token = $data['admin_token'];
  $current_token = $data['current_token'];

  try {
    // First verify the current admin token is valid
    $current_admin_stmt = $pdo->prepare("
      SELECT id, email
      FROM `admins`
      WHERE `last_session` = :current_token
      AND `last_login` > NOW() - INTERVAL 30 DAY
      LIMIT 1
    ");
    $current_admin_stmt->bindValue(':current_token', $current_token);
    $current_admin_stmt->execute();

    if ($current_admin_stmt->rowCount() !== 1) {
      error_log("Invalid current admin token for remembered admin switch");
      respondWithError(1, 'Current admin session invalid');
    }

    $current_admin = $current_admin_stmt->fetch(PDO::FETCH_ASSOC);

    // Verify the target admin session is still valid
    // Note: For remembered admins, we validate the token but don't require it to be the main session
    $target_admin_stmt = $pdo->prepare("
      SELECT a.id, a.email, a.first_name, a.last_name, a.function,
             a.picture_url, a.department_id, d.department_name, a.last_login
      FROM `admins` a
      LEFT JOIN departments d ON a.department_id = d.id
      WHERE a.id = :admin_id
      AND a.last_login > NOW() - INTERVAL 30 DAY
    ");
    $target_admin_stmt->bindValue(':admin_id', $admin_id);
    $target_admin_stmt->execute();

    $target_admin = $target_admin_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$target_admin) {
      trackAdminSwitch($current_admin['id'], $current_admin['email'], $admin_id, 'Unknown', 'quick_switch', $client_ip, false, 'Target admin not found or expired');
      error_log("Target admin not found or session expired for remembered switch: $admin_id");
      respondWithError(1, 'Target admin session expired or not found');
    }

    // Prevent switching to the same admin
    if ($current_admin['id'] === $target_admin['id']) {
      trackAdminSwitch($current_admin['id'], $current_admin['email'], $target_admin['id'], $target_admin['email'], 'quick_switch', $client_ip, false, 'Same admin quick switch attempt');
      respondWithError(1, 'Cannot switch to the same admin account');
    }

    // Update last access time for audit
    $update_stmt = $pdo->prepare("
      UPDATE `admins`
      SET `last_ip` = :last_ip
      WHERE `id` = :admin_id
    ");
    $update_stmt->bindValue(':admin_id', $admin_id);
    $update_stmt->bindValue(':last_ip', $client_ip);
    $update_stmt->execute();

    // Log the admin switch for audit purposes
    trackAdminSwitch($current_admin['id'], $current_admin['email'], $target_admin['id'], $target_admin['email'], 'quick_switch', $client_ip, true, 'Successful quick switch');
    error_log("Admin quick switch: {$current_admin['email']} (ID: {$current_admin['id']}) switched to {$target_admin['email']} (ID: {$target_admin['id']}) from IP: $client_ip");

    // Return success with admin data
    respondWithSuccess([
      'admin' => [
        'id' => $target_admin['id'],
        'email' => $target_admin['email'],
        'first_name' => $target_admin['first_name'],
        'last_name' => $target_admin['last_name'],
        'function' => $target_admin['function'],
        'department_name' => $target_admin['department_name'],
        'picture_url' => $target_admin['picture_url']
      ]
    ]);

  } catch (Exception $e) {
    error_log("Remembered admin switch error: " . $e->getMessage());
    respondWithError(4, 'Server error during remembered admin switch');
  }
}

// Remove Remembered Admin endpoint - remove admin from remembered list
elseif ($function === 'remove_remembered_admin') {
  global $client_ip;
  $data = getRequestData();

  // Validate required fields
  if (empty($data['admin_id']) || empty($data['current_token'])) {
    respondWithError(1, 'Missing required fields: admin_id, current_token');
  }

  $admin_id = $data['admin_id'];
  $current_token = $data['current_token'];

  try {
    // Verify the current admin token is valid
    $current_admin_stmt = $pdo->prepare("
      SELECT id, email
      FROM `admins`
      WHERE `last_session` = :current_token
      AND `last_login` > NOW() - INTERVAL 30 DAY
      LIMIT 1
    ");
    $current_admin_stmt->bindValue(':current_token', $current_token);
    $current_admin_stmt->execute();

    if ($current_admin_stmt->rowCount() !== 1) {
      error_log("Invalid current admin token for remove remembered admin");
      respondWithError(1, 'Current admin session invalid');
    }

    $current_admin = $current_admin_stmt->fetch(PDO::FETCH_ASSOC);

    // Get target admin info for logging
    $target_admin_stmt = $pdo->prepare("
      SELECT email
      FROM `admins`
      WHERE id = :admin_id
    ");
    $target_admin_stmt->bindValue(':admin_id', $admin_id);
    $target_admin_stmt->execute();
    $target_admin = $target_admin_stmt->fetch(PDO::FETCH_ASSOC);

    // Log the removal for audit purposes
    $target_email = $target_admin ? $target_admin['email'] : "ID:$admin_id";
    trackAdminSwitch($current_admin['id'], $current_admin['email'], $admin_id, $target_email, 'remove_remembered', $client_ip, true, 'Removed from remembered admins');
    error_log("Admin removed from remembered: {$current_admin['email']} (ID: {$current_admin['id']}) removed $target_email from remembered admins from IP: $client_ip");

    // Return success (client handles the actual removal from localStorage)
    respondWithSuccess(['removed' => true]);

  } catch (Exception $e) {
    error_log("Remove remembered admin error: " . $e->getMessage());
    respondWithError(4, 'Server error during remove remembered admin');
  }
}

// Get Remembered Admins endpoint - validate and return admin info for remembered admins
elseif ($function === 'get_remembered_admins') {
  global $client_ip;
  $data = getRequestData();

  // Validate required fields
  if (empty($data['current_token'])) {
    respondWithError(1, 'Missing required field: current_token');
  }

  $current_token = $data['current_token'];
  $admin_ids = $data['admin_ids'] ?? [];

  try {
    // Verify the current admin token is valid
    $current_admin_stmt = $pdo->prepare("
      SELECT id, email
      FROM `admins`
      WHERE `last_session` = :current_token
      AND `last_login` > NOW() - INTERVAL 30 DAY
      LIMIT 1
    ");
    $current_admin_stmt->bindValue(':current_token', $current_token);
    $current_admin_stmt->execute();

    if ($current_admin_stmt->rowCount() !== 1) {
      error_log("Invalid current admin token for get remembered admins");
      respondWithError(1, 'Current admin session invalid');
    }

    $remembered_admins = [];

    if (!empty($admin_ids) && is_array($admin_ids)) {
      // Create placeholders for the IN clause
      $placeholders = str_repeat('?,', count($admin_ids) - 1) . '?';

      $stmt = $pdo->prepare("
        SELECT a.id, a.email, a.first_name, a.last_name, a.function,
               a.picture_url, a.department_id, d.department_name, a.last_login
        FROM `admins` a
        LEFT JOIN departments d ON a.department_id = d.id
        WHERE a.id IN ($placeholders)
        AND a.last_login > NOW() - INTERVAL 30 DAY
      ");

      $stmt->execute($admin_ids);
      $remembered_admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Return the validated admin list
    respondWithSuccess(['admins' => $remembered_admins]);

  } catch (Exception $e) {
    error_log("Get remembered admins error: " . $e->getMessage());
    respondWithError(4, 'Server error during get remembered admins');
  }
}
?>