# ESXi CPU Compatibility Fix for Older Hardware

## Problem
ESXi 8.0+ shows CPU compatibility errors on older CPUs like **Xeon E5-2630 v3** (Haswell generation):

```
CPU_SUPPORT OVERRIDEWARNING: The CPUs in this host are not supported by ESXi 8.0.0. 
You can override and force install, but it is not officially supported nor recommended.
```

## Root Cause
- **ESXi 8.0+** removed support for pre-Skylake CPUs (2014 and older)
- **Xeon E5 v3 series** (Haswell, 2014) is no longer officially supported
- VMware enforces CPU compatibility checks during installation

## Fixes Applied

### 1. Enhanced Boot Parameters ✅
Updated `boot.ipxe` generation with CPU override parameters:

```
kernel ${base-url}/efi/boot/bootx64.efi -c ${base-url}/boot.cfg ks=${ks-url} allowLegacyCPU=true cpuUniformityHardCheckPanic=FALSE ignoreHeadless=TRUE systemMediaSize=min
```

**Parameters explained:**
- `allowLegacyCPU=true` - Allows installation on legacy/older CPUs
- `cpuUniformityHardCheckPanic=FALSE` - Prevents hard CPU check failures
- `ignoreHeadless=TRUE` - Ignores headless hardware warnings
- `systemMediaSize=min` - Uses minimal system media size

### 2. Kickstart CPU Override ✅
Added `allowUnsupportedCpu` directive to ESXi kickstart files:

```bash
# Override CPU compatibility checks for older hardware
# This allows installation on unsupported CPUs like Xeon E5 v3 series
allowUnsupportedCpu
```

### 3. ESXi 7.0 Alternative ✅
Added **ESXi 7.0** support as it has better compatibility with older CPUs:
- Uses `mboot.c32` bootloader (more compatible)
- No CPU restrictions like ESXi 8.0+
- Officially supports Xeon E5 v3 series

## Available ESXi Templates

| Template | Version | CPU Compatibility | Recommended For |
|----------|---------|-------------------|-----------------|
| `esxi-7.0`, `esxi-7` | ESXi 7.0 | Excellent (Xeon E5 v3+) | **Older hardware** |
| `esxi-8.0`, `esxi-8` | ESXi 8.0 | Override required | Newer features needed |
| `esxi-9.0`, `esxi-9` | ESXi 9.0 | Override required | Latest features |

## Recommended Approach

### For Xeon E5 v3 and older hardware:
```bash
# Use ESXi 7.0 (best compatibility)
curl -X POST https://test.x-zoneit.ro/pxe_api_integration.php?action=execute_server_reinstall \
  -H "Content-Type: application/json" \
  -d '{
    "server_id": 1000001,
    "server_type": "dedicated",
    "os_template": "esxi-7.0",
    "ipmi_address": "*************",
    "ipmi_username": "root",
    "ipmi_password": "your_password"
  }'
```

### If ESXi 8.0+ features are required:
```bash
# Use ESXi 8.0 with CPU overrides
curl -X POST https://test.x-zoneit.ro/pxe_api_integration.php?action=execute_server_reinstall \
  -H "Content-Type: application/json" \
  -d '{
    "server_id": 1000001,
    "server_type": "dedicated", 
    "os_template": "esxi-8.0",
    "ipmi_address": "*************",
    "ipmi_username": "root",
    "ipmi_password": "your_password"
  }'
```

## Generated Files

### ESXi 7.0 boot.ipxe
```
#!ipxe
imgfree
set base-url http://auto.x-zoneit.ro/os/esxi-7.0/
set ks-url http://auto.x-zoneit.ro/servers/XR-BL1S3/ks.cfg

# ESXi 7.0 boot configuration (better compatibility with older hardware)
kernel ${base-url}/mboot.c32 -c ${base-url}/boot.cfg ks=${ks-url}
boot
```

### ESXi 8.0+ boot.ipxe
```
#!ipxe
imgfree
set base-url http://auto.x-zoneit.ro/os/esxi-8.0/
set ks-url http://auto.x-zoneit.ro/servers/XR-BL1S3/ks.cfg

# ESXi 8.0 boot configuration with CPU compatibility overrides
kernel ${base-url}/efi/boot/bootx64.efi -c ${base-url}/boot.cfg ks=${ks-url} allowLegacyCPU=true cpuUniformityHardCheckPanic=FALSE ignoreHeadless=TRUE systemMediaSize=min
boot
```

### ESXi Kickstart (ks.cfg)
```bash
# VMware ESXi Kickstart Configuration
vmaccepteula

# Override CPU compatibility checks for older hardware
# This allows installation on unsupported CPUs like Xeon E5 v3 series
allowUnsupportedCpu

rootpw [generated_password]
install --firstdisk --overwritevmfs
network --bootproto=static --device=vmnic0 --ip=... --netmask=... --gateway=... --nameserver=... --hostname=...
reboot
```

## Setting Up ESXi 7.0

If you want to use ESXi 7.0 for better compatibility:

```bash
# Download ESXi 7.0 ISO from VMware
# https://customerconnect.vmware.com/downloads/details?downloadGroup=ESXI70U3P&productId=974

# Create directory
sudo mkdir -p /var/www/html/New/os/esxi-7.0

# Extract ISO
sudo mount -o loop VMware-VMvisor-Installer-7.0.3-xxxxx.iso /mnt
sudo cp -r /mnt/* /var/www/html/New/os/esxi-7.0/
sudo umount /mnt

# Set permissions
sudo chown -R www-data:www-data /var/www/html/New/os/esxi-7.0
sudo chmod -R 755 /var/www/html/New/os/esxi-7.0
```

## Supported CPU Generations

| CPU Generation | ESXi 7.0 | ESXi 8.0 | ESXi 9.0 |
|----------------|-----------|----------|----------|
| Xeon E5 v3 (Haswell) | ✅ Native | ⚠️ Override | ⚠️ Override |
| Xeon E5 v4 (Broadwell) | ✅ Native | ⚠️ Override | ⚠️ Override |
| Xeon Scalable 1st Gen (Skylake) | ✅ Native | ✅ Native | ✅ Native |
| Xeon Scalable 2nd Gen+ | ✅ Native | ✅ Native | ✅ Native |

## Troubleshooting

### If CPU error persists:
1. **Try ESXi 7.0** first (best compatibility)
2. Check BIOS settings (disable legacy mode if possible)
3. Verify UEFI boot is enabled
4. Check server logs for additional errors

### Performance Considerations:
- **ESXi 7.0**: Full performance on older CPUs
- **ESXi 8.0+ with overrides**: May have minor performance impacts
- **Security**: ESXi 7.0 gets security updates until 2025

## Recommendation Summary

**For Xeon E5 v3 (like your E5-2630 v3):**
- **Primary choice**: ESXi 7.0 (full compatibility, no overrides needed)
- **If 8.0+ required**: Use override fixes provided
- **Production environments**: ESXi 7.0 recommended for stability 