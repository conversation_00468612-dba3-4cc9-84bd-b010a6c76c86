<?php
require_once("auth_functions.php");
require_once("mysql.php");

// Set headers for CORS and JSON response
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
  http_response_code(200);
  exit();
}

// Authenticate admin user
try {
  $admin_id = auth_admin();
} catch (Exception $e) {
  http_response_code(401);
  echo json_encode(['error' => 'Authentication failed: ' . $e->getMessage()]);
  exit();
}

// Get request data
$requestBody = file_get_contents('php://input');
$data = json_decode($requestBody, true) ?: [];

// Handle different report endpoints
if (isset($_GET['f'])) {
  
  // Get report stats (summary metrics)
  if ($_GET['f'] == 'get_report_stats') {
    try {
      $stats = getReportStats($pdo);
      echo json_encode($stats);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get report stats: ' . $e->getMessage()]);
    }
  }
  
  // Get revenue data
  elseif ($_GET['f'] == 'get_revenue_data') {
    try {
      $timeFrame = isset($_GET['timeFrame']) ? $_GET['timeFrame'] : 'monthly';
      $revenueData = getRevenueData($pdo, $timeFrame);
      echo json_encode($revenueData);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get revenue data: ' . $e->getMessage()]);
    }
  }
  
  // Get order status distribution
  elseif ($_GET['f'] == 'get_order_status') {
    try {
      $period = isset($_GET['period']) ? $_GET['period'] : 'thisMonth';
      $orderStatusData = getOrderStatusData($pdo, $period);
      echo json_encode($orderStatusData);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get order status data: ' . $e->getMessage()]);
    }
  }
  
  // Get customer growth data
  elseif ($_GET['f'] == 'get_customer_growth') {
    try {
      $period = isset($_GET['period']) ? $_GET['period'] : 'thisYear';
      $customerGrowthData = getCustomerGrowthData($pdo, $period);
      echo json_encode($customerGrowthData);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get customer growth data: ' . $e->getMessage()]);
    }
  }
  
  // Get top locations data
  elseif ($_GET['f'] == 'get_top_locations') {
    try {
      $metric = isset($_GET['metric']) ? $_GET['metric'] : 'byRevenue';
      $topLocationsData = getTopLocationsData($pdo, $metric);
      echo json_encode($topLocationsData);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get top locations data: ' . $e->getMessage()]);
    }
  }
  
  // Get performance metrics data
  elseif ($_GET['f'] == 'get_performance_metrics') {
    try {
      $period = isset($_GET['period']) ? $_GET['period'] : 'thisYear';
      $performanceData = getPerformanceMetricsData($pdo, $period);
      echo json_encode($performanceData);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get performance metrics data: ' . $e->getMessage()]);
    }
  }
  
  // Invalid endpoint
  else {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid endpoint']);
  }
} else {
  http_response_code(400);
  echo json_encode(['error' => 'Missing function parameter']);
}

/**
 * Get report stats (summary metrics)
 */
function getReportStats($pdo) {
  // Get total revenue
  $revenueSql = "SELECT SUM(value) as total_revenue FROM invoices WHERE paid = 1";
  $revenueStmt = $pdo->query($revenueSql);
  $revenueData = $revenueStmt->fetch(PDO::FETCH_ASSOC);
  $totalRevenue = $revenueData['total_revenue'] ?: 0;
  
  // Get last year's revenue for comparison
  $lastYearSql = "SELECT SUM(value) as last_year_revenue FROM invoices 
                  WHERE paid = 1 AND date BETWEEN DATE_SUB(NOW(), INTERVAL 2 YEAR) AND DATE_SUB(NOW(), INTERVAL 1 YEAR)";
  $lastYearStmt = $pdo->query($lastYearSql);
  $lastYearData = $lastYearStmt->fetch(PDO::FETCH_ASSOC);
  $lastYearRevenue = $lastYearData['last_year_revenue'] ?: 1; // Avoid division by zero
  
  // Calculate revenue change percentage
  $revenueChange = (($totalRevenue - $lastYearRevenue) / $lastYearRevenue) * 100;
  
  // Get total orders
  $ordersSql = "SELECT COUNT(*) as total_orders FROM orders";
  $ordersStmt = $pdo->query($ordersSql);
  $ordersData = $ordersStmt->fetch(PDO::FETCH_ASSOC);
  $totalOrders = $ordersData['total_orders'] ?: 0;
  
  // Get last month's orders for comparison
  $lastMonthOrdersSql = "SELECT COUNT(*) as last_month_orders FROM orders 
                         WHERE order_date BETWEEN DATE_SUB(DATE_SUB(NOW(), INTERVAL DAYOFMONTH(NOW()) DAY), INTERVAL 1 MONTH) 
                         AND DATE_SUB(NOW(), INTERVAL DAYOFMONTH(NOW()) DAY)";
  $lastMonthOrdersStmt = $pdo->query($lastMonthOrdersSql);
  $lastMonthOrdersData = $lastMonthOrdersStmt->fetch(PDO::FETCH_ASSOC);
  $lastMonthOrders = $lastMonthOrdersData['last_month_orders'] ?: 1; // Avoid division by zero
  
  // Calculate orders change percentage
  $ordersChange = (($totalOrders - $lastMonthOrders) / $lastMonthOrders) * 100;
  
  // Get total customers
  $customersSql = "SELECT COUNT(*) as total_customers FROM users WHERE status = 1";
  $customersStmt = $pdo->query($customersSql);
  $customersData = $customersStmt->fetch(PDO::FETCH_ASSOC);
  $totalCustomers = $customersData['total_customers'] ?: 0;
  
  // Get last month's customers for comparison
  $lastMonthCustomersSql = "SELECT COUNT(*) as last_month_customers FROM users 
                           WHERE status = 1 AND created <= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
  $lastMonthCustomersStmt = $pdo->query($lastMonthCustomersSql);
  $lastMonthCustomersData = $lastMonthCustomersStmt->fetch(PDO::FETCH_ASSOC);
  $lastMonthCustomers = $lastMonthCustomersData['last_month_customers'] ?: 1; // Avoid division by zero
  
  // Calculate customers change percentage
  $customersChange = (($totalCustomers - $lastMonthCustomers) / $lastMonthCustomers) * 100;
  
  // Calculate average order value
  $avgOrderValue = $totalOrders > 0 ? $totalRevenue / $totalOrders : 0;
  
  // Get last month's average order value for comparison
  $lastMonthAvgSql = "SELECT AVG(value) as last_month_avg FROM invoices 
                      WHERE date BETWEEN DATE_SUB(DATE_SUB(NOW(), INTERVAL DAYOFMONTH(NOW()) DAY), INTERVAL 1 MONTH) 
                      AND DATE_SUB(NOW(), INTERVAL DAYOFMONTH(NOW()) DAY)";
  $lastMonthAvgStmt = $pdo->query($lastMonthAvgSql);
  $lastMonthAvgData = $lastMonthAvgStmt->fetch(PDO::FETCH_ASSOC);
  $lastMonthAvg = $lastMonthAvgData['last_month_avg'] ?: 1; // Avoid division by zero
  
  // Calculate average order value change percentage
  $avgOrderValueChange = (($avgOrderValue - $lastMonthAvg) / $lastMonthAvg) * 100;
  
  // Format values
  $formattedTotalRevenue = '€' . number_format($totalRevenue, 0, '.', ',');
  $formattedAvgOrderValue = '€' . number_format($avgOrderValue, 0, '.', ',');
  
  // Return stats array
  return [
    [
      'title' => 'Total Revenue',
      'value' => $formattedTotalRevenue,
      'change' => ($revenueChange >= 0 ? '+' : '') . number_format($revenueChange, 1) . '%',
      'period' => 'vs last year',
      'iconClass' => 'icon-dropshadow-info'
    ],
    [
      'title' => 'Orders',
      'value' => $totalOrders,
      'change' => ($ordersChange >= 0 ? '+' : '') . number_format($ordersChange, 1) . '%',
      'period' => 'vs last month',
      'iconClass' => 'icon-dropshadow-success'
    ],
    [
      'title' => 'Customers',
      'value' => $totalCustomers,
      'change' => ($customersChange >= 0 ? '+' : '') . number_format($customersChange, 1) . '%',
      'period' => 'vs last month',
      'iconClass' => 'icon-dropshadow-warning'
    ],
    [
      'title' => 'Avg. Order Value',
      'value' => $formattedAvgOrderValue,
      'change' => ($avgOrderValueChange >= 0 ? '+' : '') . number_format($avgOrderValueChange, 1) . '%',
      'period' => 'vs last month',
      'iconClass' => 'icon-dropshadow-danger'
    ]
  ];
}

/**
 * Get revenue data based on time frame - FIXED VERSION
 */
function getRevenueData($pdo, $timeFrame) {
  $result = [];
  
  switch ($timeFrame) {
    case 'daily':
      // Get revenue data for the last 24 hours in 4-hour intervals
      // Fixed version that doesn't use GROUP BY
      $timeSlots = [
        '12AM' => ['start_hour' => 0, 'end_hour' => 3],
        '4AM' => ['start_hour' => 4, 'end_hour' => 7],
        '8AM' => ['start_hour' => 8, 'end_hour' => 11],
        '12PM' => ['start_hour' => 12, 'end_hour' => 15],
        '4PM' => ['start_hour' => 16, 'end_hour' => 19],
        '8PM' => ['start_hour' => 20, 'end_hour' => 23]
      ];
      
      foreach ($timeSlots as $name => $hours) {
        $sql = "SELECT SUM(value) as revenue
                FROM invoices
                WHERE date >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                AND HOUR(date) BETWEEN :start_hour AND :end_hour";
        
        $stmt = $pdo->prepare($sql);
        $stmt->bindValue(':start_hour', $hours['start_hour']);
        $stmt->bindValue(':end_hour', $hours['end_hour']);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $result[] = [
          'name' => $name,
          'revenue' => floatval($row['revenue'] ?: 0)
        ];
      }
      break;
      
    case 'weekly':
      // Get revenue data for the last 7 days
      // Fixed version that doesn't use GROUP BY
      $days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      
      foreach ($days as $dayIndex => $dayName) {
        // MySQL DAYOFWEEK: 1 = Sunday, 2 = Monday, etc.
        // We need to convert our index (0 = Monday) to MySQL's format
        $mysqlDayIndex = $dayIndex + 2;
        if ($mysqlDayIndex > 7) $mysqlDayIndex = 1; // Handle Sunday
        
        $sql = "SELECT SUM(value) as revenue
                FROM invoices
                WHERE date >= DATE_SUB(NOW(), INTERVAL 1 WEEK)
                AND DAYOFWEEK(date) = :day_index";
        
        $stmt = $pdo->prepare($sql);
        $stmt->bindValue(':day_index', $mysqlDayIndex);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $result[] = [
          'name' => $dayName,
          'revenue' => floatval($row['revenue'] ?: 0)
        ];
      }
      break;
      
    case 'yearly':
      // Get revenue data for the last 6 years
      // Fixed version that doesn't use GROUP BY
      $currentYear = date('Y');
      for ($i = 5; $i >= 0; $i--) {
        $year = $currentYear - $i;
        
        $sql = "SELECT SUM(value) as revenue
                FROM invoices
                WHERE YEAR(date) = :year";
        
        $stmt = $pdo->prepare($sql);
        $stmt->bindValue(':year', $year);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $result[] = [
          'name' => (string)$year,
          'revenue' => floatval($row['revenue'] ?: 0)
        ];
      }
      break;
      
    case 'monthly':
    default:
      // Get revenue data for the last 12 months
      // Fixed version that doesn't use GROUP BY
      $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      
      foreach ($months as $monthIndex => $monthName) {
        // Month index in MySQL is 1-based
        $mysqlMonthIndex = $monthIndex + 1;
        
        $sql = "SELECT SUM(value) as revenue
                FROM invoices
                WHERE date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                AND MONTH(date) = :month_index";
        
        $stmt = $pdo->prepare($sql);
        $stmt->bindValue(':month_index', $mysqlMonthIndex);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $result[] = [
          'name' => $monthName,
          'revenue' => floatval($row['revenue'] ?: 0)
        ];
      }
      break;
  }
  
  return $result;
}

/**
 * Get order status distribution data
 */
function getOrderStatusData($pdo, $period) {
  // Define time period condition
  $timeCondition = "1=1"; // Default: all time
  switch ($period) {
    case 'thisMonth':
      $timeCondition = "MONTH(order_date) = MONTH(NOW()) AND YEAR(order_date) = YEAR(NOW())";
      break;
    case 'lastMonth':
      $timeCondition = "MONTH(order_date) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH)) AND YEAR(order_date) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH))";
      break;
    case 'thisQuarter':
      $timeCondition = "QUARTER(order_date) = QUARTER(NOW()) AND YEAR(order_date) = YEAR(NOW())";
      break;
    case 'lastQuarter':
      $timeCondition = "QUARTER(order_date) = QUARTER(DATE_SUB(NOW(), INTERVAL 3 MONTH)) AND YEAR(order_date) = YEAR(DATE_SUB(NOW(), INTERVAL 3 MONTH))";
      break;
    case 'thisYear':
      $timeCondition = "YEAR(order_date) = YEAR(NOW())";
      break;
  }
  
  // Query order status distribution
  $sql = "SELECT 
            CASE 
              WHEN status = 'completed' THEN 'Completed'
              WHEN status = 'processing' THEN 'Processing'
              WHEN status = 'pending' THEN 'Pending'
              WHEN status IN ('cancelled', 'canceled') THEN 'Cancelled'
              ELSE status
            END as name,
            COUNT(*) as value
          FROM orders
          WHERE $timeCondition
          GROUP BY name
          ORDER BY value DESC";
  
  $stmt = $pdo->query($sql);
  $result = [];
  
  if ($stmt) {
    // Define colors for each status
    $colors = [
      'Completed' => '#4CD471',
      'Processing' => '#6366F1',
      'Pending' => '#F7C948',
      'Cancelled' => '#F87171'
    ];
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
      $status = $row['name'];
      $result[] = [
        'name' => $status,
        'value' => intval($row['value']),
        'color' => isset($colors[$status]) ? $colors[$status] : '#9CA3AF'
      ];
    }
  }
  
  // If no data, return default values
  if (empty($result)) {
    $result = [
      ['name' => 'Completed', 'value' => 0, 'color' => '#4CD471'],
      ['name' => 'Processing', 'value' => 0, 'color' => '#6366F1'],
      ['name' => 'Pending', 'value' => 0, 'color' => '#F7C948'],
      ['name' => 'Cancelled', 'value' => 0, 'color' => '#F87171']
    ];
  }
  
  return $result;
}

/**
 * Get customer growth data - FIXED VERSION
 */
function getCustomerGrowthData($pdo, $period) {
  $result = [];
  $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  
  // Initialize result array with all months
  foreach ($months as $month) {
    $result[$month] = [
      'name' => $month,
      'newCustomers' => 0,
      'existingCustomers' => 0
    ];
  }
  
  // Determine the year based on period
  $year = date('Y'); // Default to current year
  if ($period === 'lastYear') {
    $year = date('Y') - 1;
  }
  
  // For each month, count new customers created in that month
  foreach ($months as $index => $month) {
    $monthNumber = $index + 1;
    
    // Query for new customers in this month
    $sql = "SELECT COUNT(*) as count
            FROM users
            WHERE YEAR(created) = :year
            AND MONTH(created) = :month";
    
    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':year', $year);
    $stmt->bindValue(':month', $monthNumber);
    $stmt->execute();
    
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    $result[$month]['newCustomers'] = intval($row['count']);
    
    // Query for existing customers (created before this month)
    $sql = "SELECT COUNT(*) as count
            FROM users
            WHERE created < :date";
    
    $date = "$year-$monthNumber-01";
    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':date', $date);
    $stmt->execute();
    
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    $result[$month]['existingCustomers'] = intval($row['count']);
  }
  
  // Convert to indexed array
  return array_values($result);
}

/**
 * Get top locations data
 */
function getTopLocationsData($pdo, $metric) {
  // Define query based on metric
  switch ($metric) {
    case 'byRevenue':
      $sql = "SELECT 
                c.city as name,
                SUM(i.value) as value
              FROM invoices i
              JOIN users u ON i.user_id = u.id
              JOIN cities c ON u.city = c.id
              WHERE i.paid = 1
              GROUP BY c.city
              ORDER BY value DESC
              LIMIT 4";
      break;
    case 'byOrders':
      $sql = "SELECT 
                c.city as name,
                COUNT(o.id) as value
              FROM orders o
              JOIN users u ON o.owner_id = u.id
              JOIN cities c ON u.city = c.id
              GROUP BY c.city
              ORDER BY value DESC
              LIMIT 4";
      break;
    case 'byCustomers':
      $sql = "SELECT 
                c.city as name,
                COUNT(u.id) as value
              FROM users u
              JOIN cities c ON u.city = c.id
              WHERE u.status = 1
              GROUP BY c.city
              ORDER BY value DESC
              LIMIT 4";
      break;
    default:
      $sql = "SELECT 
                c.city as name,
                SUM(i.value) as value
              FROM invoices i
              JOIN users u ON i.user_id = u.id
              JOIN cities c ON u.city = c.id
              WHERE i.paid = 1
              GROUP BY c.city
              ORDER BY value DESC
              LIMIT 4";
  }
  
  $stmt = $pdo->query($sql);
  $result = [];
  
  if ($stmt) {
    // Define colors for top locations
    $colors = ['#6366F1', '#4CD471', '#F7C948', '#F87171'];
    $index = 0;
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
      $result[] = [
        'name' => $row['name'],
        'value' => intval($row['value']),
        'color' => $colors[$index % count($colors)]
      ];
      $index++;
    }
  }
  
  // If no data, return default values
  if (empty($result)) {
    $result = [
      ['name' => 'Frankfurt', 'value' => 0, 'color' => '#6366F1'],
      ['name' => 'Berlin', 'value' => 0, 'color' => '#4CD471'],
      ['name' => 'Munich', 'value' => 0, 'color' => '#F7C948'],
      ['name' => 'Hamburg', 'value' => 0, 'color' => '#F87171']
    ];
  }
  
  return $result;
}

/**
 * Get performance metrics data
 */
function getPerformanceMetricsData($pdo, $period) {
  // Define time period
  $timeCondition = "";
  switch ($period) {
    case 'thisYear':
      $timeCondition = "YEAR(timestamp) = YEAR(NOW())";
      break;
    case 'lastYear':
      $timeCondition = "YEAR(timestamp) = YEAR(DATE_SUB(NOW(), INTERVAL 1 YEAR))";
      break;
    case 'last3Years':
      $timeCondition = "timestamp >= DATE_SUB(NOW(), INTERVAL 3 YEAR)";
      break;
    default:
      $timeCondition = "YEAR(timestamp) = YEAR(NOW())";
  }
  
  // For this example, we'll generate synthetic performance data
  // In a real application, you would query from a monitoring system or logs
  $result = [];
  $monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  
  // Generate synthetic data with slight variations
  foreach ($monthNames as $month) {
    $result[] = [
      'name' => $month,
      'network' => 99.95 + (mt_rand(0, 5) / 100),
      'server' => 99.96 + (mt_rand(0, 4) / 100),
      'service' => 99.94 + (mt_rand(0, 6) / 100)
    ];
  }
  
  return $result;
}
?>
