# Automatic ACL (Access Control List) Configuration

## Overview

When an IP subnet is allocated to a server and configured on the switch, the system now automatically creates an Access Control List (ACL) to secure the network interface.

## ACL Structure

For each configured interface, the system creates an ACL with the following format:

```
ip access-list extended EthernetX
  1 permit ip x.x.x.x/x any
  999 deny ip any any
```

Where:
- `EthernetX` is the interface name (e.g., Ethernet1/1)
- `x.x.x.x/x` is the allocated subnet CIDR (e.g., ***********/24)

## How It Works

### 1. **Automatic ACL Creation**
When a subnet is assigned to a server:
- Interface IP configuration is applied first
- ACL is created automatically with the subnet's CIDR notation
- ACL is applied to the interface in the inbound direction

### 2. **ACL Rules**
- **Rule 1**: `permit ip [subnet]/[mask] any` - Allows traffic from the allocated subnet to anywhere
- **Rule 999**: `deny ip any any` - Denies all other traffic (default deny)

### 3. **Configuration Methods**
ACLs are created through the same methods as interface configuration:
- **SSH Connection**: Uses Cisco-style CLI commands
- **Expect Scripts**: For environments without SSH2 PHP extension
- **Automatic Fallback**: If one method fails, system tries alternatives

## Example Configuration

For a server assigned subnet `********/24` on interface `Ethernet1/5`:

```bash
configure terminal
ip access-list extended Ethernet1/5
  1 permit ip ********/24 any
  999 deny ip any any
exit
interface Ethernet1/5
  ip access-group Ethernet1/5 in
exit
exit
write memory
```

## Security Benefits

- **Network Isolation**: Each server's subnet is isolated from others
- **Traffic Control**: Only traffic from the allocated subnet is permitted
- **Default Deny**: All unauthorized traffic is blocked by default
- **Consistent Security**: Automatic application ensures no human error

## Requirements

- Switch must support Cisco-style ACL commands
- SSH access to switch with administrative credentials
- Interface must be properly configured with IP address

## Logs and Monitoring

ACL creation is logged in the system logs:
- Successful ACL creation: `Created ACL commands for interface X with subnet Y`
- Errors are logged with specific failure reasons
- Background configuration status is reported to the user

## Integration Points

ACL creation is integrated into:
- `configureSwitchViaSsh()` function
- `configureSwitchViaShellExec()` function  
- Subnet assignment workflow
- Switch interface configuration process

The feature works seamlessly with existing switch configuration and requires no additional user intervention. 