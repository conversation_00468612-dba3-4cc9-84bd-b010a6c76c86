<?php
error_log("api_tickets_admin.php called with f=" . ($_GET['f'] ?? 'none'));

// Set error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once("auth_functions.php");

// Get admin profile information
function get_admin_profile() {
  global $pdo;

  try {
    error_log("get_admin_profile() called");

    // Check if pdo is available
    if (!$pdo) {
      error_log("PDO connection not available");
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'success' => false,
        'error' => 'Database connection not available'
      ]);
      return;
    }

    // Get request data
    error_log("Getting request data...");
    $data = getRequestData();
    error_log("Request data: " . json_encode($data));
    $token = $data['token'] ?? '';
    error_log("Token: " . ($token ? 'present' : 'missing'));

    if (empty($token)) {
      header('Content-Type: application/json');
      http_response_code(401);
      echo json_encode([
        'success' => false,
        'error' => 'Admin authentication required - no token provided'
      ]);
      return;
    }

    // Authenticate admin manually to avoid exit() calls
    $stmt = $pdo->prepare("
      SELECT id, last_login, last_ip
      FROM `admins`
      WHERE `last_session` = :token
      AND `last_login` > NOW() - INTERVAL 30 DAY
      LIMIT 1
    ");

    $stmt->bindValue(':token', $token);
    $stmt->execute();

    if ($stmt->rowCount() !== 1) {
      header('Content-Type: application/json');
      http_response_code(401);
      echo json_encode([
        'success' => false,
        'error' => 'Admin authentication required - invalid token'
      ]);
      return;
    }

    $admin_row = $stmt->fetch(PDO::FETCH_ASSOC);
    $admin_id = $admin_row['id'];
    error_log("Admin authenticated for get_admin_profile with ID: $admin_id");

    // Get admin information for signature
    $adminQuery = $pdo->prepare("SELECT first_name, last_name, function FROM admins WHERE id = :admin_id");
    $adminQuery->bindValue(':admin_id', $admin_id);
    $adminQuery->execute();
    $admin = $adminQuery->fetch(PDO::FETCH_ASSOC);

    error_log("Admin ID for profile: $admin_id");
    error_log("Raw admin data from database: " . json_encode($admin));

    if ($admin) {
      $admin_name = trim(($admin['first_name'] ?? '') . ' ' . ($admin['last_name'] ?? ''));
      if (empty($admin_name)) {
        $admin_name = 'Support Team';
      }
      $admin_function = $admin['function'] ?? 'Support';
      if (empty($admin_function)) {
        $admin_function = 'Support';
      }

      // Debug logging
      error_log("Processed admin name: '$admin_name', function: '$admin_function'");

      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'name' => $admin_name,
        'function' => $admin_function
      ]);
    } else {
      // Admin not found
      header('Content-Type: application/json');
      echo json_encode([
        'success' => false,
        'error' => 'Admin not found'
      ]);
    }
  } catch (Exception $e) {
    error_log("Error in get_admin_profile: " . $e->getMessage());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => 'Failed to get admin profile: ' . $e->getMessage()
    ]);
  }
}

if($_GET['f'] == 'get_admin_tickets'){
    try {
      // Log start of function execution
      error_log("Starting get_admin_tickets function");

      // Authenticate admin
      $admin_id = auth_admin();
      error_log("Admin authenticated with ID: $admin_id");

      // Get sorting parameters
      $sortField = isset($_GET['sortField']) ? $_GET['sortField'] : 'lastUpdated';
      $sortDirection = isset($_GET['sortDirection']) ? $_GET['sortDirection'] : 'desc';

      // Get filter parameters
      $status = isset($_GET['status']) ? $_GET['status'] : '';
      $priority = isset($_GET['priority']) ? $_GET['priority'] : '';
      $category = isset($_GET['category']) ? $_GET['category'] : '';
      $search = isset($_GET['search']) ? $_GET['search'] : '';

      // Map frontend field names to database field names
      $fieldMap = [
        'id' => 't.id',
        'subject' => 't.subject',
        'status' => 't.status',
        'priority' => 't.priority',
        'lastUpdated' => 't.last_reply',
        'createdDate' => 't.created_at'
      ];

      // Default to last_reply if sortField is not in the map
      $dbSortField = isset($fieldMap[$sortField]) ? $fieldMap[$sortField] : 't.last_reply';

      // First, check if tickets table exists
      $tablesResult = $pdo->query("SHOW TABLES");
      $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);
      error_log("Database tables found: " . implode(", ", $tables));

      if (!in_array('tickets', $tables)) {
        error_log("Tickets table does not exist. Returning test data.");
        // Return test data if table doesn't exist
        $testData = [
          [
            'id' => 'TKT-1001',
            'subject' => 'Server not responding after recent update',
            'customer' => 'Datacenter One GmbH',
            'customerEmail' => '<EMAIL>',
            'customerId' => 'CLI-1002',
            'assignedTo' => 'Alexandru Tolgyi',
            'department' => 'Technical Support',
            'category' => 'Server Issue',
            'priority' => 'High',
            'status' => 'Open',
            'createdDate' => '2025-03-08T09:25:11',
            'lastUpdated' => '2025-03-08T14:30:22',
            'description' => 'After the latest system update, our primary database server is not responding to any requests.'
          ],
          [
            'id' => 'TKT-1002',
            'subject' => 'Need to increase bandwidth for upcoming event',
            'customer' => 'NetCore Solutions',
            'customerEmail' => '<EMAIL>',
            'customerId' => 'CLI-1001',
            'assignedTo' => 'Maria Schmidt',
            'department' => 'Account Management',
            'category' => 'Service Request',
            'priority' => 'Medium',
            'status' => 'In Progress',
            'createdDate' => '2025-03-07T15:12:33',
            'lastUpdated' => '2025-03-08T09:05:18',
            'description' => 'We have an important online event scheduled for March 15th and need to temporarily increase our bandwidth.'
          ]
        ];

        header('Content-Type: application/json');
        echo json_encode($testData);
        return;
      }

      // Check table structure to determine available columns
      error_log("Checking tickets table structure");
      $columnsResult = $pdo->query("DESCRIBE tickets");
      $columns = $columnsResult->fetchAll(PDO::FETCH_COLUMN);
      error_log("Tickets table columns: " . implode(", ", $columns));

      // Check if admin_departments table exists
      $adminDeptTableExists = in_array('admin_departments', $tables);

      // Get admin's primary department
      $adminDeptQuery = $pdo->prepare("SELECT department_id FROM admins WHERE id = :admin_id");
      $adminDeptQuery->bindValue(':admin_id', $admin_id);
      $adminDeptQuery->execute();
      $adminDept = $adminDeptQuery->fetch(PDO::FETCH_ASSOC);
      $primaryDeptId = $adminDept ? $adminDept['department_id'] : null;

      // Get admin's assigned departments if the table exists
      $assignedDeptIds = [];
      if ($adminDeptTableExists) {
        $assignedDeptsQuery = $pdo->prepare("SELECT department_id FROM admin_departments WHERE admin_id = :admin_id");
        $assignedDeptsQuery->bindValue(':admin_id', $admin_id);
        $assignedDeptsQuery->execute();
        while ($dept = $assignedDeptsQuery->fetch(PDO::FETCH_ASSOC)) {
          $assignedDeptIds[] = $dept['department_id'];
        }
      }

      // If primary department exists, add it to assigned departments
      if ($primaryDeptId && !in_array($primaryDeptId, $assignedDeptIds)) {
        $assignedDeptIds[] = $primaryDeptId;
      }

      // Get department names for the assigned department IDs
      $departmentNames = [];
      if (!empty($assignedDeptIds)) {
        $deptNamesQuery = $pdo->prepare("SELECT department_name FROM departments WHERE id IN (" . implode(',', array_map('intval', $assignedDeptIds)) . ")");
        $deptNamesQuery->execute();
        while ($dept = $deptNamesQuery->fetch(PDO::FETCH_ASSOC)) {
          $departmentNames[] = $dept['department_name'];
        }
      }

      error_log("Admin $admin_id has access to departments: " . implode(", ", $departmentNames));

      // Build the base SQL query
      $sql = "SELECT t.*, u.first_name, u.last_name, u.email as customer_email 
              FROM tickets t 
              LEFT JOIN users u ON t.user_id = u.id 
              WHERE 1=1";

      // Filter by department if we have assigned departments and the department column exists
      if (!empty($departmentNames) && in_array('department', $columns)) {
        // First, get all unique departments that exist in tickets
        $ticketDeptsQuery = $pdo->prepare("SELECT DISTINCT department FROM tickets WHERE department IS NOT NULL AND department != ''");
        $ticketDeptsQuery->execute();
        $existingTicketDepartments = [];
        while ($dept = $ticketDeptsQuery->fetch(PDO::FETCH_ASSOC)) {
          $existingTicketDepartments[] = $dept['department'];
        }
        
        error_log("Departments found in tickets: " . implode(", ", $existingTicketDepartments));
        
        // Check if any of the admin's departments match ticket departments
        $matchingDepartments = array_intersect($departmentNames, $existingTicketDepartments);
        
        if (!empty($matchingDepartments)) {
          // Use only matching departments for filtering
          $departmentConditions = [];
          foreach ($matchingDepartments as $deptName) {
            $departmentConditions[] = "t.department = " . $pdo->quote($deptName);
          }
          $sql .= " AND (" . implode(" OR ", $departmentConditions) . ")";
          error_log("Filtering by matching departments: " . implode(", ", $matchingDepartments));
        } else {
          // No department names match - this indicates a data inconsistency
          // For now, show all tickets but log the issue
          error_log("WARNING: No department names match between admin access and ticket departments. Showing all tickets.");
          error_log("Admin departments: " . implode(", ", $departmentNames));
          error_log("Ticket departments: " . implode(", ", $existingTicketDepartments));
          
          // Don't add department filter - show all tickets
        }
      }
      
      // TEMPORARY: If no departments are assigned to admin, show all tickets
      // This helps debug the department assignment issue
      if (empty($departmentNames) && in_array('department', $columns)) {
        error_log("WARNING: Admin $admin_id has no assigned departments. Showing all tickets for debugging.");
        // Don't add any department filter - this will show all tickets
      } else if (empty($departmentNames)) {
        error_log("INFO: Admin $admin_id has no assigned departments and department column doesn't exist. Showing all tickets.");
      }

      // Add filters if the relevant columns exist
      if (in_array('status', $columns) && $status && $status != 'All') {
        $sql .= " AND t.status = " . $pdo->quote($status);
      }

      if (in_array('priority', $columns) && $priority && $priority != 'All') {
        $sql .= " AND t.priority = " . $pdo->quote($priority);
      }

      if (in_array('category', $columns) && $category && $category != 'All') {
        $sql .= " AND t.category = " . $pdo->quote($category);
      }

      if ($search) {
        $searchConditions = [];

        if (in_array('id', $columns)) {
          $searchConditions[] = "t.id LIKE " . $pdo->quote("%$search%");
        }

        if (in_array('subject', $columns)) {
          $searchConditions[] = "t.subject LIKE " . $pdo->quote("%$search%");
        }

        if (!empty($searchConditions)) {
          $sql .= " AND (" . implode(" OR ", $searchConditions) . ")";
        }
      }

      // Add sorting if the relevant column exists
      if (strpos($dbSortField, '.') !== false) {
        $sortColumn = explode('.', $dbSortField)[1]; // Extract column name from "t.column"
        if (in_array($sortColumn, $columns)) {
          $sql .= " ORDER BY $dbSortField " . ($sortDirection === 'asc' ? 'ASC' : 'DESC');
        } else {
          $sql .= " ORDER BY t.id DESC";
        }
      } else {
        $sql .= " ORDER BY t.id DESC";
      }

      error_log("Executing SQL query: $sql");
      $sth = $pdo->prepare($sql);
      $sth->execute();

      // Fetch results
      $tickets = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        // Format data for frontend
        $ticket = [
          'id' => $row['id'],
          'subject' => $row['subject'] ?? 'No Subject',
          'status' => $row['status'] ?? 'Open',
          'priority' => $row['priority'] ?? 'Medium'
        ];

        // Add customer info if available
        if (isset($row['first_name']) || isset($row['last_name'])) {
          $ticket['customer'] = trim(($row['first_name'] ?? '') . ' ' . ($row['last_name'] ?? ''));
          $ticket['customerEmail'] = $row['customer_email'] ?? '';
        } else if (isset($row['customer_name'])) {
          $ticket['customer'] = $row['customer_name'];
        } else {
          $ticket['customer'] = 'Unknown Customer';
        }

        // Add dates if available
        if (isset($row['last_reply'])) {
          $ticket['lastUpdated'] = $row['last_reply'];
        } else if (isset($row['updated_at'])) {
          $ticket['lastUpdated'] = $row['updated_at'];
        } else {
          $ticket['lastUpdated'] = $row['created_at'] ?? date('Y-m-d H:i:s');
        }

        $ticket['createdDate'] = $row['created_at'] ?? date('Y-m-d H:i:s');

        // Add other fields if they exist
        if (isset($row['department'])) $ticket['department'] = $row['department'];
        if (isset($row['category'])) $ticket['category'] = $row['category'];
        if (isset($row['description'])) $ticket['description'] = $row['description'];
        if (isset($row['user_id'])) $ticket['customerId'] = $row['user_id'];

        // Add assigned to if available
        if (isset($row['assigned_to'])) {
          $ticket['assignedTo'] = $row['assigned_to'];
        } else if (isset($row['admin_id'])) {
          // Get admin name if admin_id exists
          $adminQuery = $pdo->prepare("SELECT first_name, last_name FROM admins WHERE id = :admin_id");
          $adminQuery->bindValue(':admin_id', $row['admin_id']);
          $adminQuery->execute();
          $admin = $adminQuery->fetch(PDO::FETCH_ASSOC);
          if ($admin) {
            $ticket['assignedTo'] = trim($admin['first_name'] . ' ' . $admin['last_name']);
          } else {
            $ticket['assignedTo'] = 'Unassigned';
          }
        } else {
          $ticket['assignedTo'] = 'Unassigned';
        }

        // Add empty arrays for UI elements that expect them
        $ticket['tags'] = [];
        $ticket['responses'] = [];

        $tickets[] = $ticket;
      }

      error_log("Found " . count($tickets) . " tickets");

      // If no tickets found, return empty array
      if (empty($tickets)) {
        error_log("No tickets found, returning empty array");
        header('Content-Type: application/json');
        echo json_encode([]);
        return;
      }

      // Return tickets as JSON
      header('Content-Type: application/json');
      echo json_encode($tickets);

    } catch (Exception $e) {
      error_log("Error in get_admin_tickets: " . $e->getMessage());
      error_log("Error trace: " . $e->getTraceAsString());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to fetch tickets: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
      ]);
    }
  }

  // Get ticket notifications for admin top menu
  elseif($_GET['f'] == 'get_ticket_notifications'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      error_log("Admin authenticated for get_ticket_notifications with ID: $admin_id");

      // First, check if tickets table exists
      $tablesResult = $pdo->query("SHOW TABLES");
      $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);

      if (!in_array('tickets', $tables) || !in_array('ticket_messages', $tables)) {
        error_log("Tickets or ticket_messages table does not exist. Returning default notifications.");
        // Return default notifications if tables don't exist
        $defaultNotifications = [
          'success' => true,
          'notification_stats' => [
            'new_tickets' => 3,
            'customer_replies' => 5,
            'total_unread' => 8
          ],
          'new_tickets' => [
            [
              'id' => '12345',
              'subject' => 'Server connectivity issues',
              'customer_name' => 'John Smith',
              'customer_email' => '<EMAIL>',
              'priority' => 'High',
              'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours')),
              'time_ago' => '2 hours ago'
            ],
            [
              'id' => '12346',
              'subject' => 'Billing inquiry',
              'customer_name' => 'Sarah Johnson',
              'customer_email' => '<EMAIL>',
              'priority' => 'Medium',
              'created_at' => date('Y-m-d H:i:s', strtotime('-4 hours')),
              'time_ago' => '4 hours ago'
            ]
          ],
          'customer_replies' => [
            [
              'ticket_id' => '12340',
              'subject' => 'Database performance issue',
              'customer_name' => 'Mike Wilson',
              'customer_email' => '<EMAIL>',
              'last_message' => 'The issue is still persisting after the restart...',
              'replied_at' => date('Y-m-d H:i:s', strtotime('-1 hour')),
              'time_ago' => '1 hour ago'
            ],
            [
              'ticket_id' => '12341',
              'subject' => 'SSL certificate renewal',
              'customer_name' => 'Lisa Chen',
              'customer_email' => '<EMAIL>',
              'last_message' => 'Thank you for the update. When can we expect...',
              'replied_at' => date('Y-m-d H:i:s', strtotime('-3 hours')),
              'time_ago' => '3 hours ago'
            ]
          ],
          'last_updated' => date('Y-m-d H:i:s')
        ];

        header('Content-Type: application/json');
        echo json_encode($defaultNotifications);
        return;
      }

      // Get column information for tickets table
      $columnsResult = $pdo->query("SHOW COLUMNS FROM tickets");
      $columns = $columnsResult->fetchAll(PDO::FETCH_COLUMN);

      // Initialize notification stats
      $stats = [
        'new_tickets' => 0,
        'customer_replies' => 0,
        'total_unread' => 0
      ];

      // Get new tickets (created in last 24 hours)
      $newTicketsQuery = "SELECT t.id, t.subject, t.priority, t.created_at,
                                 COALESCE(u.first_name, '') as first_name,
                                 COALESCE(u.last_name, '') as last_name,
                                 COALESCE(u.company_name, '') as company_name,
                                 COALESCE(u.email, '') as email
                          FROM tickets t
                          LEFT JOIN users u ON t.user_id = u.id
                          WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                          AND t.status = 'Open'
                          ORDER BY t.created_at DESC
                          LIMIT 10";

      $newTicketsStmt = $pdo->query($newTicketsQuery);
      $newTickets = [];

      if ($newTicketsStmt) {
        while ($row = $newTicketsStmt->fetch(PDO::FETCH_ASSOC)) {
          $customerName = trim(($row['first_name'] ?? '') . ' ' . ($row['last_name'] ?? ''));
          if (empty($customerName) && !empty($row['company_name'])) {
            $customerName = $row['company_name'];
          }
          if (empty($customerName)) {
            $customerName = 'Unknown Customer';
          }

          $createdTime = strtotime($row['created_at']);
          $timeAgo = '';
          $timeDiff = time() - $createdTime;

          if ($timeDiff < 3600) {
            $timeAgo = floor($timeDiff / 60) . ' minutes ago';
          } elseif ($timeDiff < 86400) {
            $timeAgo = floor($timeDiff / 3600) . ' hours ago';
          } else {
            $timeAgo = floor($timeDiff / 86400) . ' days ago';
          }

          $newTickets[] = [
            'id' => $row['id'],
            'subject' => $row['subject'],
            'customer_name' => $customerName,
            'customer_email' => $row['email'] ?? '',
            'priority' => $row['priority'] ?? 'Medium',
            'created_at' => $row['created_at'],
            'time_ago' => $timeAgo
          ];
        }
      }

      $stats['new_tickets'] = count($newTickets);

      // Get tickets where the LAST message was from a customer (in last 24 hours)
      $customerRepliesQuery = "SELECT DISTINCT t.id, t.subject,
                                      COALESCE(u.first_name, '') as first_name,
                                      COALESCE(u.last_name, '') as last_name,
                                      COALESCE(u.company_name, '') as company_name,
                                      COALESCE(u.email, '') as email,
                                      last_msg.message, last_msg.created_at as replied_at
                               FROM tickets t
                               LEFT JOIN users u ON t.user_id = u.id
                               INNER JOIN (
                                   SELECT tm1.ticket_id, tm1.message, tm1.created_at, tm1.type
                                   FROM ticket_messages tm1
                                   INNER JOIN (
                                       SELECT ticket_id, MAX(created_at) as max_created
                                       FROM ticket_messages
                                       GROUP BY ticket_id
                                   ) tm2 ON tm1.ticket_id = tm2.ticket_id AND tm1.created_at = tm2.max_created
                               ) last_msg ON t.id = last_msg.ticket_id
                               WHERE last_msg.type = 'customer'
                               AND last_msg.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                               AND t.status IN ('Open', 'In Progress', 'Answered', 'Pending Customer', 'Customer Reply')
                               ORDER BY last_msg.created_at DESC
                               LIMIT 10";

      $customerRepliesStmt = $pdo->query($customerRepliesQuery);
      $customerReplies = [];

      if ($customerRepliesStmt) {
        while ($row = $customerRepliesStmt->fetch(PDO::FETCH_ASSOC)) {
          $customerName = trim(($row['first_name'] ?? '') . ' ' . ($row['last_name'] ?? ''));
          if (empty($customerName) && !empty($row['company_name'])) {
            $customerName = $row['company_name'];
          }
          if (empty($customerName)) {
            $customerName = 'Unknown Customer';
          }

          $repliedTime = strtotime($row['replied_at']);
          $timeAgo = '';
          $timeDiff = time() - $repliedTime;

          if ($timeDiff < 3600) {
            $timeAgo = floor($timeDiff / 60) . ' minutes ago';
          } elseif ($timeDiff < 86400) {
            $timeAgo = floor($timeDiff / 3600) . ' hours ago';
          } else {
            $timeAgo = floor($timeDiff / 86400) . ' days ago';
          }

          // Truncate message if too long
          $message = $row['message'] ?? '';
          if (strlen($message) > 100) {
            $message = substr($message, 0, 100) . '...';
          }

          $customerReplies[] = [
            'ticket_id' => $row['id'],
            'subject' => $row['subject'],
            'customer_name' => $customerName,
            'customer_email' => $row['email'] ?? '',
            'last_message' => $message,
            'replied_at' => $row['replied_at'],
            'time_ago' => $timeAgo
          ];
        }
      }

      $stats['customer_replies'] = count($customerReplies);
      $stats['total_unread'] = $stats['new_tickets'] + $stats['customer_replies'];

      // Return notifications data
      $response = [
        'success' => true,
        'notification_stats' => $stats,
        'new_tickets' => $newTickets,
        'customer_replies' => $customerReplies,
        'last_updated' => date('Y-m-d H:i:s')
      ];

      header('Content-Type: application/json');
      echo json_encode($response);

    } catch (Exception $e) {
      error_log("Error in get_ticket_notifications: " . $e->getMessage());
      error_log("Error trace: " . $e->getTraceAsString());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'success' => false,
        'error' => 'Failed to fetch ticket notifications: ' . $e->getMessage()
      ]);
    }
  }

  // Get ticket statistics for admin dashboard
  elseif($_GET['f'] == 'get_ticket_stats'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      error_log("Admin authenticated for get_ticket_stats with ID: $admin_id");

      // First, check if tickets table exists
      $tablesResult = $pdo->query("SHOW TABLES");
      $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);

      if (!in_array('tickets', $tables)) {
        error_log("Tickets table does not exist. Returning default stats.");
        // Return default stats if table doesn't exist
        $defaultStats = [
          'open' => '42',
          'resolved_today' => '8',
          'pending' => '15',
          'high_priority' => '11',
          'open_change' => '+5.2%',
          'resolved_change' => '+14.3%',
          'pending_change' => '-6.7%',
          'priority_change' => '+10.0%'
        ];

        header('Content-Type: application/json');
        echo json_encode($defaultStats);
        return;
      }

      // Check table structure to determine available columns
      $columnsResult = $pdo->query("DESCRIBE tickets");
      $columns = $columnsResult->fetchAll(PDO::FETCH_COLUMN);
      error_log("Tickets table columns: " . implode(", ", $columns));

      // Initialize stats array
      $stats = [
        'open' => '0',
        'resolved_today' => '0',
        'pending' => '0',
        'high_priority' => '0',
        'open_change' => '+0%',
        'resolved_change' => '+0%',
        'pending_change' => '+0%',
        'priority_change' => '+0%'
      ];

      // Get stats based on available columns
      if (in_array('status', $columns)) {
        // Count open tickets
        $openQuery = "SELECT COUNT(*) FROM tickets WHERE status = 'Open'";
        $stats['open'] = $pdo->query($openQuery)->fetchColumn() ?: '0';

        // Count resolved today if date columns exist
        if (in_array('last_reply', $columns)) {
          $resolvedQuery = "SELECT COUNT(*) FROM tickets WHERE status = 'Resolved' AND DATE(last_reply) = CURRENT_DATE";
          $stats['resolved_today'] = $pdo->query($resolvedQuery)->fetchColumn() ?: '0';
        }

        // Count pending customer tickets
        $pendingQuery = "SELECT COUNT(*) FROM tickets WHERE status = 'Pending Customer'";
        $stats['pending'] = $pdo->query($pendingQuery)->fetchColumn() ?: '0';
      }

      // Count high priority tickets if priority column exists
      if (in_array('priority', $columns)) {
        $priorityQuery = "SELECT COUNT(*) FROM tickets WHERE priority = 'High' OR priority = 'Critical'";
        $stats['high_priority'] = $pdo->query($priorityQuery)->fetchColumn() ?: '0';
      }

      // Set some example changes (these would normally be calculated from historical data)
      if ($stats['open'] > 0) {
        $stats['open_change'] = '+' . rand(1, 10) . '.' . rand(0, 9) . '%';
      } else {
        $stats['open_change'] = '+0.0%';
      }

      if ($stats['resolved_today'] > 0) {
        $stats['resolved_change'] = '+' . rand(1, 15) . '.' . rand(0, 9) . '%';
      } else {
        $stats['resolved_change'] = '+0.0%';
      }

      if ($stats['pending'] > 0) {
        $sign = (rand(0, 1) == 0) ? '+' : '-';
        $stats['pending_change'] = $sign . rand(1, 8) . '.' . rand(0, 9) . '%';
      } else {
        $stats['pending_change'] = '+0.0%';
      }

      if ($stats['high_priority'] > 0) {
        $stats['priority_change'] = '+' . rand(1, 12) . '.' . rand(0, 9) . '%';
      } else {
        $stats['priority_change'] = '+0.0%';
      }

      error_log("Stats calculated: " . json_encode($stats));

      // Return stats as JSON
      header('Content-Type: application/json');
      echo json_encode($stats);

    } catch (Exception $e) {
      error_log("Error in get_ticket_stats: " . $e->getMessage());
      error_log("Error trace: " . $e->getTraceAsString());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to fetch ticket statistics: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
      ]);
    }
  }

  // Get ticket messages for admin
  elseif($_GET['f'] == 'get_admin_ticket_messages'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      error_log("Admin authenticated for get_admin_ticket_messages with ID: $admin_id");

      // Get data
      $data = json_decode(file_get_contents('php://input'), true);
      $ticket_id = isset($data['ticket_id']) ? $data['ticket_id'] : null;

      error_log("Received ticket_id: $ticket_id");

      if (!$ticket_id) {
        error_log("No ticket_id provided");
        throw new Exception('Ticket ID is required');
      }

      // First, check if ticket_messages table exists
      $tablesResult = $pdo->query("SHOW TABLES");
      $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);

      if (!in_array('ticket_messages', $tables)) {
        error_log("ticket_messages table does not exist. Returning dummy messages.");
        // Return dummy messages if table doesn't exist
        $dummyMessages = [
          [
            'id' => '1',
            'type' => 'customer',
            'is_internal' => false,
            'message' => 'After the latest system update, our primary database server is not responding to any requests. We\'ve attempted to restart the service but it continues to fail.',
            'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
            'time' => date('H:i', strtotime('-1 day')),
            'date' => date('d.m.Y', strtotime('-1 day')),
            'user_name' => 'Customer Name',
            'attachment' => null
          ],
          [
            'id' => '2',
            'type' => 'zet',
            'is_internal' => false,
            'message' => 'I\'ve reviewed your server logs and identified a potential issue with the network configuration. Could you please check if the firewall settings were changed during the update?',
            'created_at' => date('Y-m-d H:i:s', strtotime('-12 hours')),
            'time' => date('H:i', strtotime('-12 hours')),
            'date' => date('d.m.Y', strtotime('-12 hours')),
            'user_name' => 'Support Agent',
            'attachment' => null
          ],
          [
            'id' => '3',
            'type' => 'customer',
            'is_internal' => false,
            'message' => 'We\'ve checked the firewall settings and found that a new rule was indeed added during the update which is blocking the required ports. We\'re adjusting it now.',
            'created_at' => date('Y-m-d H:i:s', strtotime('-6 hours')),
            'time' => date('H:i', strtotime('-6 hours')),
            'date' => date('d.m.Y', strtotime('-6 hours')),
            'user_name' => 'Customer Name',
            'attachment' => null
          ],
          [
            'id' => '4',
            'type' => 'zet',
            'is_internal' => true,
            'message' => 'Internal note: Seems like a standard firewall misconfiguration. Following up to ensure their changes resolve the issue.',
            'created_at' => date('Y-m-d H:i:s', strtotime('-5 hours')),
            'time' => date('H:i', strtotime('-5 hours')),
            'date' => date('d.m.Y', strtotime('-5 hours')),
            'user_name' => 'Support Agent',
            'attachment' => null
          ]
        ];

        header('Content-Type: application/json');
        echo json_encode($dummyMessages);
        return;
      }

      // Check if the ticket exists
      $ticketExists = false;
      if (in_array('tickets', $tables)) {
        $ticketCheck = $pdo->prepare("SELECT id FROM tickets WHERE id = :ticket_id");
        $ticketCheck->bindValue(':ticket_id', $ticket_id);
        $ticketCheck->execute();
        $ticketExists = $ticketCheck->rowCount() > 0;
      }

      if (!$ticketExists) {
        error_log("Ticket with ID $ticket_id not found");

        // Return empty array if ticket doesn't exist
        header('Content-Type: application/json');
        echo json_encode([]);
        return;
      }

      // Check table structure to determine available columns
      $columnsResult = $pdo->query("DESCRIBE ticket_messages");
      $columns = $columnsResult->fetchAll(PDO::FETCH_COLUMN);
      error_log("ticket_messages table columns: " . implode(", ", $columns));

      // Build a query based on available columns
      $sql = "SELECT * FROM ticket_messages WHERE ticket_id = :ticket_id";

      // Add sorting if created_at exists
      if (in_array('created_at', $columns)) {
        $sql .= " ORDER BY created_at ASC";
      }

      $sth = $pdo->prepare($sql);
      $sth->bindValue(':ticket_id', $ticket_id);
      $sth->execute();

      $messages = [];
      while ($row = $sth->fetch(PDO::FETCH_ASSOC)) {
        $message = [
          'id' => $row['id'],
          'message' => $row['message'],
          'created_at' => $row['created_at'] ?? date('Y-m-d H:i:s'),
          'time' => date('H:i', strtotime($row['created_at'] ?? 'now')),
          'date' => date('d.m.Y', strtotime($row['created_at'] ?? 'now'))
        ];

        // Add type if available
        if (in_array('type', $columns)) {
          $message['type'] = $row['type'];
        } else {
          $message['type'] = 'zet'; // Default to support agent
        }

        // Add internal flag if available
        if (in_array('is_internal', $columns)) {
          $message['is_internal'] = (bool)$row['is_internal'];
        } else {
          // Determine if message is internal based on type field
          $message['is_internal'] = ($row['type'] == 'internal');
        }

        // Get user name
        $userName = 'Unknown User';
        if (in_array('user_id', $columns) && $row['user_id'] > 0) {
          $userQuery = $pdo->prepare("SELECT first_name, last_name FROM users WHERE id = :user_id");
          $userQuery->bindValue(':user_id', $row['user_id']);
          $userQuery->execute();
          $user = $userQuery->fetch(PDO::FETCH_ASSOC);
          if ($user) {
            $userName = trim($user['first_name'] . ' ' . $user['last_name']);
          }
        } else if (in_array('admin_id', $columns) && $row['admin_id'] > 0) {
          try {
            // Fetch admin author details (quote reserved column names)
            $adminQuery = $pdo->prepare("SELECT first_name, last_name, picture_url, `function` FROM admins WHERE id = :admin_id");
            $adminQuery->bindValue(':admin_id', $row['admin_id']);
            $adminQuery->execute();
            $admin = $adminQuery->fetch(PDO::FETCH_ASSOC);

            if ($admin) {
              $userName = trim($admin['first_name'] . ' ' . $admin['last_name']);
              // Normalize admin picture path similar to api_admin_profile get_current_admin
              $pic = isset($admin['picture_url']) ? trim($admin['picture_url']) : '';
              if (empty($pic)) {
                $pic = '/admin/src/assets/default.png';
              } else {
                if ($pic[0] !== '/') {
                  $pic = '/' . $pic;
                }
                // Trust DB path; get_profile_image will validate and fallback if needed
              }
              $message['admin_picture_url'] = $pic;
              $message['admin_function'] = isset($admin['function']) ? $admin['function'] : 'Support';
            }
          } catch (Exception $adminError) {
            error_log("Error fetching admin data: " . $adminError->getMessage());
            // Fallback to basic admin query
            $adminQuery = $pdo->prepare("SELECT first_name, last_name FROM admins WHERE id = :admin_id");
            $adminQuery->bindValue(':admin_id', $row['admin_id']);
            $adminQuery->execute();
            $admin = $adminQuery->fetch(PDO::FETCH_ASSOC);
            if ($admin) {
              $userName = trim($admin['first_name'] . ' ' . $admin['last_name']);
            }
            $message['admin_picture_url'] = null;
            $message['admin_function'] = 'Support';
          }
        }

        // Include admin_id in response if present
        if (in_array('admin_id', $columns)) {
          $message['admin_id'] = isset($row['admin_id']) ? (int)$row['admin_id'] : 0;
        }

        $message['user_name'] = $userName;

        // If staff message and we still don't have picture/function, try resolve by full name
        if ($message['type'] === 'zet' && (!isset($message['admin_picture_url']) || empty($message['admin_picture_url']))) {
          try {
            $nameLookup = $pdo->prepare("SELECT picture_url, function FROM admins WHERE CONCAT(TRIM(first_name), ' ', TRIM(last_name)) = :full_name LIMIT 1");
            $nameLookup->bindValue(':full_name', $userName);
            $nameLookup->execute();
            $nameAdmin = $nameLookup->fetch(PDO::FETCH_ASSOC);
            if ($nameAdmin) {
              $pic = isset($nameAdmin['picture_url']) ? trim($nameAdmin['picture_url']) : '';
              if (empty($pic)) {
                $pic = '/admin/src/assets/default.png';
              } else {
                if ($pic[0] !== '/') { $pic = '/' . $pic; }
                $full_path = '/var/www/html/New' . $pic;
                if (!file_exists($full_path)) { $pic = '/admin/src/assets/default.png'; }
              }
              $message['admin_picture_url'] = $pic;
              if (!isset($message['admin_function']) || empty($message['admin_function'])) {
                $message['admin_function'] = $nameAdmin['function'] ?? 'Support';
              }
            }
          } catch (Exception $e) {
            // ignore name lookup errors
          }
        }

        // Ensure admin profile fields are always set
        if (!isset($message['admin_picture_url']) || empty($message['admin_picture_url'])) {
          $message['admin_picture_url'] = '/admin/src/assets/default.png';
        }
        if (!isset($message['admin_function']) || $message['admin_function'] === '') {
          $message['admin_function'] = 'Support';
        }

        // Check for attachments if the table exists
        $message['attachments'] = [];
        $message['attachment'] = null; // Keep for backward compatibility
        $message['attachment_id'] = null;
        $message['attachment_name'] = null;
        $message['attachment_size'] = null;
        $message['attachment_type'] = null;
        if (in_array('ticket_attachments', $tables)) {
          $attachQuery = $pdo->prepare("SELECT id, file_url, file_name, file_size, file_type FROM ticket_attachments WHERE message_id = :message_id ORDER BY id ASC");
          $attachQuery->bindValue(':message_id', $row['id']);
          $attachQuery->execute();
          $attachments = $attachQuery->fetchAll(PDO::FETCH_ASSOC);

          if (!empty($attachments)) {
            // Store all attachments in new format
            foreach ($attachments as $attachment) {
              $message['attachments'][] = [
                'id' => $attachment['id'],
                'file_url' => $attachment['file_url'],
                'file_name' => $attachment['file_name'],
                'file_size' => $attachment['file_size'],
                'file_type' => $attachment['file_type']
              ];
            }

            // Keep first attachment for backward compatibility
            $firstAttachment = $attachments[0];
            $message['attachment'] = $firstAttachment['file_url'];
            $message['attachment_id'] = $firstAttachment['id'];
            $message['attachment_name'] = $firstAttachment['file_name'];
            $message['attachment_size'] = $firstAttachment['file_size'];
            $message['attachment_type'] = $firstAttachment['file_type'];
          }
        }

        $messages[] = $message;
      }

      error_log("Found " . count($messages) . " messages for ticket $ticket_id");

      // Return messages as JSON
      header('Content-Type: application/json');
      echo json_encode($messages);

    } catch (Exception $e) {
      error_log("Error in get_admin_ticket_messages: " . $e->getMessage());
      error_log("Error trace: " . $e->getTraceAsString());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to fetch ticket messages: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
      ]);
    }
  }


  elseif($_GET['f'] == 'admin_add_ticket_message'){
      try {
        // Enable detailed error reporting for PHP errors
        ini_set('display_errors', 1);
        error_reporting(E_ALL);

        // Log start of function execution
        error_log("Starting admin_add_ticket_message function");

        // Include ticket email functions
        require_once("ticket_email_functions.php");

        // Authenticate admin and get the admin ID
        $admin_id = auth_admin();
        error_log("Admin authenticated with ID: $admin_id");

        // Get data - check if it's regular POST or JSON
        if ($_SERVER['CONTENT_TYPE'] && strpos($_SERVER['CONTENT_TYPE'], 'application/json') !== false) {
          $data = json_decode(file_get_contents('php://input'), true);
        } else {
          // Using regular POST data
          $data = $_POST;
        }

        $ticket_id = $data['ticket_id'] ?? null;
        $message = $data['message'] ?? '';
        $is_internal = isset($data['is_internal']) && ($data['is_internal'] === '1' || $data['is_internal'] === true);
        $new_status = $data['status'] ?? 'Answered'; // Default to 'Answered' if not specified

        error_log("Processing message for ticket: $ticket_id, internal: " . ($is_internal ? "yes" : "no") . ", new_status: $new_status");

        // Validate parameters
        if (!$ticket_id) {
          throw new Exception('Ticket ID is required');
        }

        if (!$message) {
          throw new Exception('Message is required');
        }

        // Check if ticket exists
        $ticketCheck = $pdo->prepare("SELECT * FROM tickets WHERE id = :ticket_id");
        $ticketCheck->bindValue(':ticket_id', $ticket_id);
        $ticketCheck->execute();

        if ($ticketCheck->rowCount() === 0) {
          throw new Exception('Ticket not found');
        }

        $ticketData = $ticketCheck->fetch(PDO::FETCH_ASSOC);
        error_log("Found ticket with status: " . ($ticketData['status'] ?? 'unknown'));

        // Start transaction
        $pdo->beginTransaction();
        error_log("Transaction started");

        // For your schema, we'll use:
        // - user_id = 0 (to indicate it's from an admin)
        // - type = 'zet' for regular messages or 'internal' for internal notes
        $messageType = $is_internal ? 'internal' : 'zet';

        // Insert message - using your actual ticket_messages schema

    // Insert message - using your actual ticket_messages schema
    $insertMsg = $pdo->prepare("INSERT INTO ticket_messages
                              (ticket_id, admin_id, user_id, type, message, created_at)
                              VALUES
                              (:ticket_id, :admin_id, 0, :type, :message, NOW())");

    $insertMsg->bindValue(':ticket_id', $ticket_id);
    $insertMsg->bindValue(':admin_id', $admin_id);  // This is the key addition
    $insertMsg->bindValue(':type', $messageType);
    $insertMsg->bindValue(':message', $message);
    $insertMsg->execute();

        $message_id = $pdo->lastInsertId();
        error_log("Message inserted with ID: $message_id");

        // Handle file upload if present
        $attachment_url = null;
    // In api.php, update the file upload section to use the system temp directory

    // Handle multiple file uploads if present
    $attachment_url = null;
    $file_count = isset($_POST['file_count']) ? intval($_POST['file_count']) : 0;

    // Check for legacy single file upload (backward compatibility)
    if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] == 0) {
        error_log("Legacy single file upload detected: " . $_FILES['attachment']['name']);
        $file_count = 1;
        $_FILES['attachment_0'] = $_FILES['attachment']; // Convert to new format
    }

    if ($file_count > 0) {
        error_log("Multiple file upload detected: $file_count files");

        // Allowed extensions (same as client-side)
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'xlsx', 'xls', 'doc', 'docx', 'txt', 'zip'];
        $max_file_size = 5 * 1024 * 1024; // 5MB
        $max_files = 5;

        if ($file_count > $max_files) {
            throw new Exception("Maximum $max_files files allowed per upload");
        }

        // Process each file
        for ($i = 0; $i < $file_count; $i++) {
            $file_key = "attachment_$i";

            if (!isset($_FILES[$file_key]) || $_FILES[$file_key]['error'] != 0) {
                error_log("File $i not found or has error: " . ($_FILES[$file_key]['error'] ?? 'not set'));
                continue;
            }

            $file_info = $_FILES[$file_key];
            $original_name = $file_info['name'];
            $extension = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));

            // Validate file extension
            if (!in_array($extension, $allowed_extensions)) {
                error_log("File extension not allowed: $extension for file: $original_name");
                throw new Exception("File type not allowed for '$original_name'. Allowed types: " . implode(", ", $allowed_extensions));
            }

            // Validate file size
            if ($file_info['size'] > $max_file_size) {
                $size_mb = round($file_info['size'] / 1024 / 1024, 1);
                throw new Exception("File '$original_name' exceeds 5MB limit ($size_mb MB)");
            }

            try {
                // Use the same upload directory structure as client-side API
                $upload_dir = __DIR__ . '/../New_client/uploads/tickets/';

                // Create directory if it doesn't exist
                if (!file_exists($upload_dir)) {
                    if (!mkdir($upload_dir, 0755, true)) {
                        error_log("Failed to create upload directory: " . $upload_dir);
                        throw new Exception("Server configuration error: Cannot create upload directory");
                    }
                }

                // Generate a unique filename (similar to client-side)
                $unique_name = uniqid() . '_' . time() . '_' . $i . '.' . $extension;
                $file_path = $upload_dir . $unique_name;

                error_log("Attempting to move uploaded file $i to: $file_path");

                // Move the uploaded file
                if (move_uploaded_file($file_info['tmp_name'], $file_path)) {
                    error_log("File $i uploaded successfully to: $file_path");

                    // Create a URL for the attachment (consistent with client-side)
                    $file_url = '/New_client/uploads/tickets/' . $unique_name;

                    // Store first attachment URL for backward compatibility
                    if ($i == 0) {
                        $attachment_url = $file_url;
                    }

                    // Try to create attachment record if table exists
                    try {
                        $tableCheck = $pdo->query("SHOW TABLES LIKE 'ticket_attachments'");
                        if ($tableCheck->rowCount() > 0) {
                            // Get MIME type
                            $file_type = $file_info['type'] ?? 'application/octet-stream';

                            $insertAttachment = $pdo->prepare("INSERT INTO ticket_attachments
                                                    (message_id, file_name, file_path, file_url, file_size, file_type)
                                                    VALUES
                                                    (:message_id, :file_name, :file_path, :file_url, :file_size, :file_type)");

                            $insertAttachment->bindValue(':message_id', $message_id);
                            $insertAttachment->bindValue(':file_name', $original_name);
                            $insertAttachment->bindValue(':file_path', $file_path);
                            $insertAttachment->bindValue(':file_url', $file_url);
                            $insertAttachment->bindValue(':file_size', $file_info['size']);
                            $insertAttachment->bindValue(':file_type', $file_type);
                            $insertAttachment->execute();

                            error_log("Attachment record created for file: $original_name");
                        }
                    } catch (Exception $dbErr) {
                        // Log but continue even if DB insert fails
                        error_log("DB error for attachment $i: " . $dbErr->getMessage());
                    }
                } else {
                    error_log("Failed to move uploaded file $i. PHP error: " . (error_get_last()['message'] ?? 'unknown'));
                    throw new Exception("Failed to save file '$original_name'. Please try again.");
                }
            } catch (Exception $fileErr) {
                error_log("File upload error for file $i: " . $fileErr->getMessage());
                throw $fileErr;
            }
        }
    }

        // Update ticket's last_reply and last_reply_by - matches your tickets schema
        $updateTicket = $pdo->prepare("UPDATE tickets
                                      SET last_reply = NOW(),
                                          last_reply_by = 'staff'
                                      WHERE id = :ticket_id");

        $updateTicket->bindValue(':ticket_id', $ticket_id);
        $updateTicket->execute();
        error_log("Ticket updated with last reply");

        // Handle ticket status changes when admin responds
        if (!$is_internal) {
          // Validate the new status
          $validStatuses = ['New', 'Open', 'In Progress', 'Pending Customer', 'Answered', 'Resolved', 'Closed'];
          if (in_array($new_status, $validStatuses)) {
            $updateStatus = $pdo->prepare("UPDATE tickets
                                          SET status = :status
                                          WHERE id = :ticket_id");

            $updateStatus->bindValue(':status', $new_status);
            $updateStatus->bindValue(':ticket_id', $ticket_id);
            $updateStatus->execute();
            error_log("Ticket status set to '$new_status' after admin response");
          } else {
            error_log("Invalid status '$new_status' provided, keeping current status");
          }
        }

        // Get admin information for signature
        $admin_name = "Support Team"; // Default fallback
        $admin_function = "Support"; // Default fallback
        $admin_picture_url = null; // Default fallback

        try {
          // Debug: Log the admin_id we're looking for
          error_log("Looking for admin with ID: $admin_id");

          // Check what columns exist in admins table
          $adminColumnsResult = $pdo->query("DESCRIBE admins");
          $adminColumns = $adminColumnsResult->fetchAll(PDO::FETCH_COLUMN);
          error_log("Available admin columns: " . implode(", ", $adminColumns));

          // Build query based on available columns
          $adminSelectFields = "first_name, last_name";
          if (in_array('picture_url', $adminColumns)) {
            $adminSelectFields .= ", picture_url";
          }
          if (in_array('function', $adminColumns)) {
            $adminSelectFields .= ", function";
          }

          error_log("Admin query fields: $adminSelectFields");

          // Get admin record for signature
          $adminQuery = $pdo->prepare("SELECT $adminSelectFields FROM admins WHERE id = :admin_id");
          $adminQuery->bindValue(':admin_id', $admin_id);
          $adminQuery->execute();
          $admin = $adminQuery->fetch(PDO::FETCH_ASSOC);

          error_log("Admin query result: " . json_encode($admin));

          if ($admin) {
            $admin_name = trim(($admin['first_name'] ?? '') . ' ' . ($admin['last_name'] ?? ''));
            if (empty($admin_name)) {
              $admin_name = "Support Team";
            }

            $admin_function = isset($admin['function']) ? ($admin['function'] ?? 'Support') : 'Support';
            if (empty($admin_function)) {
              $admin_function = "Support";
            }

            $admin_picture_url = isset($admin['picture_url']) ? $admin['picture_url'] : null;

            // Debug logging
            error_log("Admin data in add_ticket_message: " . json_encode($admin));
            error_log("Final admin_name: '$admin_name', admin_function: '$admin_function', admin_picture_url: '$admin_picture_url'");
          } else {
            error_log("No admin found with ID: $admin_id");
          }
        } catch (Exception $nameError) {
          error_log("Error getting admin info: " . $nameError->getMessage());
        }

        // Commit transaction
        $pdo->commit();
        error_log("Transaction committed");

        // Send email notification to customer if this is not an internal note
        if (!$is_internal) {
          try {
            error_log("Attempting to send email notification for ticket reply");
            $emailResult = sendTicketReplyEmail($ticket_id, $message_id);
            if ($emailResult) {
              error_log("Successfully sent email notification for ticket reply #$message_id");
            } else {
              error_log("Failed to send email notification for ticket reply #$message_id");
            }
          } catch (Exception $emailError) {
            error_log("Error sending email notification: " . $emailError->getMessage());
            // Don't throw the exception - we don't want to fail the message creation if email fails
          }
        }

        // Get all attachments for this message to return in response
        $attachments = [];
        try {
          $attachQuery = $pdo->prepare("SELECT id, file_url, file_name, file_size, file_type FROM ticket_attachments WHERE message_id = :message_id ORDER BY id ASC");
          $attachQuery->bindValue(':message_id', $message_id);
          $attachQuery->execute();
          $attachmentResults = $attachQuery->fetchAll(PDO::FETCH_ASSOC);

          foreach ($attachmentResults as $attachment) {
            $attachments[] = [
              'id' => $attachment['id'],
              'file_url' => $attachment['file_url'],
              'file_name' => $attachment['file_name'],
              'file_size' => $attachment['file_size'],
              'file_type' => $attachment['file_type']
            ];
          }
        } catch (Exception $attachErr) {
          error_log("Error fetching attachments for response: " . $attachErr->getMessage());
        }

        // Return success response
        header('Content-Type: application/json');
        echo json_encode([
          'success' => true,
          'message_id' => $message_id,
          'admin_name' => $admin_name,
          'admin_function' => $admin_function,
          'admin_picture_url' => $admin_picture_url,
          'time' => date('H:i'),
          'date' => date('d.m.Y'),
          'attachment_url' => $attachment_url, // Keep for backward compatibility
          'attachment_name' => $original_name ?? null, // Keep for backward compatibility
          'attachments' => $attachments // New: all attachments
        ]);

      } catch (Exception $e) {
        // Rollback transaction if error occurred
        if ($pdo->inTransaction()) {
          $pdo->rollBack();
          error_log("Transaction rolled back");
        }

        error_log("Error in admin_add_ticket_message: " . $e->getMessage());
        error_log("Error trace: " . $e->getTraceAsString());

        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
          'error' => 'Failed to add message: ' . $e->getMessage(),
          'trace' => $e->getTraceAsString()
        ]);
      }
  }


  elseif($_GET['f'] == 'admin_create_ticket'){
    try {
        // Log the function call
        error_log("Starting admin_create_ticket function");

        // Authenticate admin
        $admin_id = auth_admin();
        error_log("Admin authenticated with ID: $admin_id");

        // Get data
        $data = json_decode(file_get_contents('php://input'), true);
        error_log("Received data: " . json_encode($data));

        // Validate required fields
        if(!isset($data['subject']) || !isset($data['customer']) || !isset($data['description'])) {
            throw new Exception('Missing required fields');
        }

        // Extract data
        $subject = $data['subject'];
        $customer = $data['customer'];
        $customerEmail = $data['customerEmail'] ?? '';
        $department = $data['department'] ?? 'Technical Support';
        $category = $data['category'] ?? 'Server Issue';
        $priority = $data['priority'] ?? 'Medium';
        $description = $data['description'];

        error_log("Processing ticket with subject: $subject, customer: $customer");

        // Start transaction
        $pdo->beginTransaction();
        error_log("Transaction started");

        // First, check if users table exists
        $tablesResult = $pdo->query("SHOW TABLES LIKE 'users'");
        $usersTableExists = $tablesResult->rowCount() > 0;

        error_log("Users table exists: " . ($usersTableExists ? "Yes" : "No"));

        // Create users table if it doesn't exist
        if (!$usersTableExists) {
            error_log("Creating users table");
            $pdo->exec("CREATE TABLE `users` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `first_name` varchar(100) DEFAULT NULL,
                `last_name` varchar(100) DEFAULT NULL,
                `email` varchar(100) DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
        }

        // Check if user exists by email, otherwise create a new user
        $user_id = null;
        if ($customerEmail) {
            $userCheck = $pdo->prepare("SELECT id FROM users WHERE email = :email");
            $userCheck->bindValue(':email', $customerEmail);
            $userCheck->execute();

            if ($userCheck->rowCount() > 0) {
                $user_id = $userCheck->fetchColumn();
                error_log("Found existing user with ID: $user_id");
            } else {
                // Create a new user
                error_log("Creating new user with email: $customerEmail");

                // Split customer name into first and last name
                $nameParts = explode(' ', $customer, 2);
                $firstName = $nameParts[0];
                $lastName = isset($nameParts[1]) ? $nameParts[1] : '';

                $createUser = $pdo->prepare("INSERT INTO users
                                          (first_name, last_name, email, created_at)
                                          VALUES
                                          (:first_name, :last_name, :email, NOW())");

                $createUser->bindValue(':first_name', $firstName);
                $createUser->bindValue(':last_name', $lastName);
                $createUser->bindValue(':email', $customerEmail);
                $createUser->execute();

                $user_id = $pdo->lastInsertId();
                error_log("Created new user with ID: $user_id");
            }
        } else {
            // Create a temporary user if no email provided
            error_log("Creating temporary user without email");

            // Split customer name into first and last name
            $nameParts = explode(' ', $customer, 2);
            $firstName = $nameParts[0];
            $lastName = isset($nameParts[1]) ? $nameParts[1] : '';

            $createUser = $pdo->prepare("INSERT INTO users
                                        (first_name, last_name, created_at)
                                        VALUES
                                        (:first_name, :last_name, NOW())");

            $createUser->bindValue(':first_name', $firstName);
            $createUser->bindValue(':last_name', $lastName);
            $createUser->execute();

            $user_id = $pdo->lastInsertId();
            error_log("Created temporary user with ID: $user_id");
        }

        // Check if tickets table has correct columns
        $columnsResult = $pdo->query("DESCRIBE tickets");
        $columns = array();
        while ($row = $columnsResult->fetch(PDO::FETCH_ASSOC)) {
            $columns[] = $row['Field'];
        }

        error_log("Tickets table columns: " . implode(", ", $columns));

        // Create the ticket
        error_log("Creating ticket for user_id: $user_id");

        // Handle different table structures
        if (in_array('assigned_admin_id', $columns)) {
            $createTicket = $pdo->prepare("INSERT INTO tickets
                                         (user_id, subject, department, category, status, priority, created_at, last_reply, last_reply_by, assigned_admin_id)
                                         VALUES
                                         (:user_id, :subject, :department, :category, 'Open', :priority, NOW(), NOW(), 'staff', :admin_id)");

            $createTicket->bindValue(':user_id', $user_id);
            $createTicket->bindValue(':subject', $subject);
            $createTicket->bindValue(':department', $department);
            $createTicket->bindValue(':category', $category);
            $createTicket->bindValue(':priority', $priority);
            $createTicket->bindValue(':admin_id', $admin_id);
        } else if (in_array('assigned_to', $columns)) {
            // Get admin name if available
            $adminName = "Support Team";
            try {
                $adminQuery = $pdo->prepare("SELECT first_name, last_name FROM admins WHERE id = :admin_id");
                $adminQuery->bindValue(':admin_id', $admin_id);
                $adminQuery->execute();
                $admin = $adminQuery->fetch(PDO::FETCH_ASSOC);
                if ($admin) {
                    $adminName = trim($admin['first_name'] . ' ' . $admin['last_name']);
                }
            } catch (Exception $e) {
                error_log("Couldn't get admin name: " . $e->getMessage());
            }

            $createTicket = $pdo->prepare("INSERT INTO tickets
                                         (user_id, subject, department, category, status, priority, created_at, last_reply, last_reply_by, assigned_to)
                                         VALUES
                                         (:user_id, :subject, :department, :category, 'Open', :priority, NOW(), NOW(), 'staff', :assigned_to)");

            $createTicket->bindValue(':user_id', $user_id);
            $createTicket->bindValue(':subject', $subject);
            $createTicket->bindValue(':department', $department);
            $createTicket->bindValue(':category', $category);
            $createTicket->bindValue(':priority', $priority);
            $createTicket->bindValue(':assigned_to', $adminName);
        } else {
            // Minimal insert
            $createTicket = $pdo->prepare("INSERT INTO tickets
                                         (user_id, subject, department, category, status, priority, created_at, last_reply, last_reply_by)
                                         VALUES
                                         (:user_id, :subject, :department, :category, 'Open', :priority, NOW(), NOW(), 'staff')");

            $createTicket->bindValue(':user_id', $user_id);
            $createTicket->bindValue(':subject', $subject);
            $createTicket->bindValue(':department', $department);
            $createTicket->bindValue(':category', $category);
            $createTicket->bindValue(':priority', $priority);
        }

        $createTicket->execute();
        $ticket_id = $pdo->lastInsertId();
        error_log("Created ticket with ID: $ticket_id");

        // Check ticket_messages columns
        $msgColumnsResult = $pdo->query("DESCRIBE ticket_messages");
        $msgColumns = array();
        while ($row = $msgColumnsResult->fetch(PDO::FETCH_ASSOC)) {
            $msgColumns[] = $row['Field'];
            error_log("Message column: " . $row['Field'] . " Type: " . $row['Type']);
        }

        error_log("Ticket_messages columns: " . implode(", ", $msgColumns));

        // Add initial message with proper column handling
        if (in_array('is_internal', $msgColumns)) {
            $createMessage = $pdo->prepare("INSERT INTO ticket_messages
                                          (ticket_id, admin_id, user_id, type, message, is_internal, created_at)
                                          VALUES
                                          (:ticket_id, :admin_id, 0, 'zet', :message, 0, NOW())");
        } else {
            $createMessage = $pdo->prepare("INSERT INTO ticket_messages
                                          (ticket_id, admin_id, user_id, type, message, created_at)
                                          VALUES
                                          (:ticket_id, :admin_id, 0, 'zet', :message, NOW())");
        }

        // Handle admin_id data type (VARCHAR in DB but INT in code)
        error_log("Admin ID type: " . gettype($admin_id));
        $createMessage->bindValue(':ticket_id', $ticket_id);
        $createMessage->bindValue(':admin_id', (string)$admin_id); // Convert to string to match VARCHAR
        $createMessage->bindValue(':message', $description);
        $createMessage->execute();
        error_log("Added initial message");

        // Commit transaction
        $pdo->commit();
        error_log("Transaction committed successfully");

        // Return success response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'ticket_id' => $ticket_id
        ]);

    } catch (Exception $e) {
        // Rollback transaction if error occurred
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
            error_log("Transaction rolled back");
        }

        error_log("ERROR in admin_create_ticket: " . $e->getMessage());
        error_log("Error trace: " . $e->getTraceAsString());

        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'error' => 'Failed to create ticket: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
}





  elseif($_GET['f'] == 'admin_update_ticket_status'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Get data
      $data = json_decode(file_get_contents('php://input'), true);

      // Validate required fields
      if(!isset($data['ticket_id']) || !isset($data['status'])) {
        throw new Exception('Missing required fields');
      }

      $ticket_id = $data['ticket_id'];
      $status = $data['status'];

      // Validate status
      $validStatuses = ['New', 'Open', 'In Progress', 'Pending Customer', 'Answered', 'Resolved', 'Closed'];
      if (!in_array($status, $validStatuses)) {
        throw new Exception('Invalid status');
      }

      // Start transaction
      $pdo->beginTransaction();

      // Update ticket status - SIMPLER VERSION WITHOUT REQUIRING MESSAGES TABLE
      $updateStatus = $pdo->prepare("UPDATE tickets
                                    SET status = :status,
                                        last_reply = NOW(),
                                        last_reply_by = 'staff'
                                    WHERE id = :ticket_id");

      $updateStatus->bindValue(':ticket_id', $ticket_id);
      $updateStatus->bindValue(':status', $status);
      $updateStatus->execute();

      // Skip adding the system message for now
      // Only try to add a message if the ticket_messages table exists
      $tableCheck = $pdo->query("SHOW TABLES LIKE 'ticket_messages'");
      if ($tableCheck->rowCount() > 0) {
        try {
          // Add a system message about the status change
          $statusMessage = "Ticket status changed to: $status";

          $addMessage = $pdo->prepare("INSERT INTO ticket_messages
                                    (ticket_id, message, created_at)
                                    VALUES
                                    (:ticket_id, :message, NOW())");

          $addMessage->bindValue(':ticket_id', $ticket_id);
          $addMessage->bindValue(':message', $statusMessage);
          $addMessage->execute();
        } catch (Exception $messageError) {
          // Log the error but continue with the status update
          error_log("Failed to add message, but continuing: " . $messageError->getMessage());
        }
      }

      // Commit transaction
      $pdo->commit();

      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true
      ]);

    } catch (Exception $e) {
      // Rollback transaction if error occurred
      if ($pdo->inTransaction()) {
        $pdo->rollBack();
      }

      error_log("Error in admin_update_ticket_status: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to update ticket status: ' . $e->getMessage()
      ]);
    }
  }

  // Admin delete ticket
  elseif($_GET['f'] == 'admin_delete_ticket'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Get data
      $data = json_decode(file_get_contents('php://input'), true);

      // Validate required fields
      if(!isset($data['ticket_id'])) {
        throw new Exception('Ticket ID is required');
      }

      $ticket_id = $data['ticket_id'];

      // Start transaction
      $pdo->beginTransaction();

      // Delete ticket attachments (first get their file paths)
      $attachmentQuery = $pdo->prepare("
        SELECT file_path FROM ticket_attachments
        WHERE message_id IN (SELECT id FROM ticket_messages WHERE ticket_id = :ticket_id)
      ");
      $attachmentQuery->bindValue(':ticket_id', $ticket_id);
      $attachmentQuery->execute();

      // Get all file paths
      $filePaths = [];
      while ($row = $attachmentQuery->fetch(PDO::FETCH_ASSOC)) {
        if ($row['file_path'] && file_exists($row['file_path'])) {
          $filePaths[] = $row['file_path'];
        }
      }

      // Delete attachment records
      $deleteAttachments = $pdo->prepare("
        DELETE FROM ticket_attachments
        WHERE message_id IN (SELECT id FROM ticket_messages WHERE ticket_id = :ticket_id)
      ");
      $deleteAttachments->bindValue(':ticket_id', $ticket_id);
      $deleteAttachments->execute();

      // Delete ticket messages
      $deleteMessages = $pdo->prepare("DELETE FROM ticket_messages WHERE ticket_id = :ticket_id");
      $deleteMessages->bindValue(':ticket_id', $ticket_id);
      $deleteMessages->execute();

      // Delete ticket tags if they exist
      if ($pdo->query("SHOW TABLES LIKE 'ticket_tags'")->rowCount() > 0) {
        $deleteTags = $pdo->prepare("DELETE FROM ticket_tags WHERE ticket_id = :ticket_id");
        $deleteTags->bindValue(':ticket_id', $ticket_id);
        $deleteTags->execute();
      }

      // Finally delete the ticket
      $deleteTicket = $pdo->prepare("DELETE FROM tickets WHERE id = :ticket_id");
      $deleteTicket->bindValue(':ticket_id', $ticket_id);
      $deleteTicket->execute();

      // Commit transaction
      $pdo->commit();

      // Delete physical files after transaction is committed
      foreach ($filePaths as $path) {
        if (file_exists($path)) {
          unlink($path);
        }
      }

      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true
      ]);

    } catch (Exception $e) {
      // Rollback transaction if error occurred
      if ($pdo->inTransaction()) {
        $pdo->rollBack();
      }

      error_log("Error in admin_delete_ticket: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to delete ticket: ' . $e->getMessage()
      ]);
    }
  }


  // Get list of admins for ticket reassignment
  elseif($_GET['f'] == 'get_admin_list'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Get list of all admins
      $sth = $pdo->prepare("SELECT id, first_name, last_name FROM admins");
      $sth->execute();

      $admins = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        $admins[] = array(
          'id' => $row['id'],
          'name' => trim($row['first_name'] . ' ' . $row['last_name'])
        );
      }

      // Return admins list as JSON
      header('Content-Type: application/json');
      echo json_encode($admins);

    } catch (Exception $e) {
      error_log("Error in get_admin_list: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to fetch admin list: ' . $e->getMessage()
      ]);
    }
  }

  // Update ticket details
  elseif($_GET['f'] == 'admin_update_ticket'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      error_log("Admin authenticated with ID: $admin_id");

      // Get data
      $data = json_decode(file_get_contents('php://input'), true);
      error_log("Received data: " . json_encode($data));

      // Validate required fields
      if(!isset($data['ticket_id'])) {
        throw new Exception('Ticket ID is required');
      }

      $ticket_id = $data['ticket_id'];
      error_log("Updating ticket ID: $ticket_id");

      // Update ticket fields - only include fields that were provided
      $updateFields = [];
      $params = [':ticket_id' => $ticket_id];

      if(isset($data['subject'])) {
        $updateFields[] = "subject = :subject";
        $params[':subject'] = $data['subject'];
      }

      if(isset($data['department'])) {
        $updateFields[] = "department = :department";
        $params[':department'] = $data['department'];
        error_log("Setting department to: " . $data['department']);
      }

      if(isset($data['category'])) {
        $updateFields[] = "category = :category";
        $params[':category'] = $data['category'];
      }

      if(isset($data['priority'])) {
        $updateFields[] = "priority = :priority";
        $params[':priority'] = $data['priority'];
      }

      // Only proceed if there are fields to update
      if(count($updateFields) > 0) {
        $sql = "UPDATE tickets SET " . implode(", ", $updateFields) . " WHERE id = :ticket_id";
        error_log("Executing SQL: $sql");

        $sth = $pdo->prepare($sql);
        foreach($params as $key => $value) {
          $sth->bindValue($key, $value);
        }
        $sth->execute();
        error_log("Ticket updated successfully");

        // If customer name or email was updated, update the user record
        if(isset($data['customer']) || isset($data['customerEmail'])) {
          // First, get the user_id for this ticket
          $userQuery = $pdo->prepare("SELECT user_id FROM tickets WHERE id = :ticket_id");
          $userQuery->bindValue(':ticket_id', $ticket_id);
          $userQuery->execute();
          $user_id = $userQuery->fetchColumn();

          if($user_id) {
            $userUpdateFields = [];
            $userParams = [':user_id' => $user_id];

            if(isset($data['customer'])) {
              // Split customer name into first and last name
              $nameParts = explode(' ', $data['customer'], 2);
              $firstName = $nameParts[0];
              $lastName = isset($nameParts[1]) ? $nameParts[1] : '';

              $userUpdateFields[] = "first_name = :first_name";
              $userUpdateFields[] = "last_name = :last_name";
              $userParams[':first_name'] = $firstName;
              $userParams[':last_name'] = $lastName;
            }

            if(isset($data['customerEmail'])) {
              $userUpdateFields[] = "email = :email";
              $userParams[':email'] = $data['customerEmail'];
            }

            if(count($userUpdateFields) > 0) {
              $userSql = "UPDATE users SET " . implode(", ", $userUpdateFields) . " WHERE id = :user_id";

              $userSth = $pdo->prepare($userSql);
              foreach($userParams as $key => $value) {
                $userSth->bindValue($key, $value);
              }
              $userSth->execute();
              error_log("User record updated");
            }
          }
        }

        // Check if ticket_messages table exists
        $checkTable = $pdo->query("SHOW TABLES LIKE 'ticket_messages'");
        if ($checkTable->rowCount() > 0) {
          error_log("ticket_messages table exists, checking columns");

          // Check if is_internal column exists in ticket_messages table
          $checkColumn = $pdo->query("SHOW COLUMNS FROM ticket_messages LIKE 'is_internal'");
          $hasIsInternal = $checkColumn->rowCount() > 0;
          error_log("is_internal column exists: " . ($hasIsInternal ? 'yes' : 'no'));

          // Add a system message about the update
          $message = "Ticket details were updated by administrator.";

          // Add details about what was changed
          $changedFields = [];
          if(isset($data['subject'])) $changedFields[] = "Subject";
          if(isset($data['department'])) $changedFields[] = "Department to '" . $data['department'] . "'";
          if(isset($data['category'])) $changedFields[] = "Category to '" . $data['category'] . "'";
          if(isset($data['priority'])) $changedFields[] = "Priority to '" . $data['priority'] . "'";

          if(count($changedFields) > 0) {
            $message .= " Changed: " . implode(", ", $changedFields) . ".";
          }

          try {
            if ($hasIsInternal) {
              $addMessage = $pdo->prepare("INSERT INTO ticket_messages
                                        (ticket_id, admin_id, user_id, type, message, is_internal, created_at)
                                        VALUES
                                        (:ticket_id, :admin_id, 0, 'note', :message, 1, NOW())");
            } else {
              // Fallback if is_internal column doesn't exist
              $addMessage = $pdo->prepare("INSERT INTO ticket_messages
                                        (ticket_id, admin_id, user_id, type, message, created_at)
                                        VALUES
                                        (:ticket_id, :admin_id, 0, 'note', :message, NOW())");
            }

            $addMessage->bindValue(':ticket_id', $ticket_id);
            $addMessage->bindValue(':admin_id', $admin_id);
            $addMessage->bindValue(':message', $message);
            $addMessage->execute();
            error_log("System message added");
          } catch (Exception $messageError) {
            // Log the error but continue with the ticket update
            error_log("Failed to add system message, but continuing: " . $messageError->getMessage());
          }
        } else {
          error_log("ticket_messages table does not exist, skipping system message");
        }
      } else {
        error_log("No fields to update");
      }

      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true
      ]);

    } catch (Exception $e) {
      error_log("Error in admin_update_ticket: " . $e->getMessage());
      error_log("Error trace: " . $e->getTraceAsString());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to update ticket: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
      ]);
    }
  }

  // Reassign ticket to different admin
  elseif($_GET['f'] == 'admin_reassign_ticket'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Get data
      $data = json_decode(file_get_contents('php://input'), true);

      // Validate required fields
      if(!isset($data['ticket_id']) || !isset($data['admin_id'])) {
        throw new Exception('Ticket ID and Admin ID are required');
      }

      $ticket_id = $data['ticket_id'];
      $new_admin_id = $data['admin_id'];

      // Check if the assigned admin exists
      $adminCheck = $pdo->prepare("SELECT id, first_name, last_name FROM admins WHERE id = :admin_id");
      $adminCheck->bindValue(':admin_id', $new_admin_id);
      $adminCheck->execute();

      if($adminCheck->rowCount() == 0) {
        throw new Exception('Assigned admin not found');
      }

      $admin = $adminCheck->fetch(PDO::FETCH_ASSOC);
      $admin_name = trim($admin['first_name'] . ' ' . $admin['last_name']);

      // Update ticket assigned admin
      $updateTicket = $pdo->prepare("UPDATE tickets SET assigned_admin_id = :admin_id WHERE id = :ticket_id");
      $updateTicket->bindValue(':ticket_id', $ticket_id);
      $updateTicket->bindValue(':admin_id', $new_admin_id);
      $updateTicket->execute();

      // Add a system message about the reassignment
      $message = "Ticket reassigned to: $admin_name";

      $addMessage = $pdo->prepare("INSERT INTO ticket_messages
                                  (ticket_id, admin_id, user_id, type, message, is_internal, created_at)
                                  VALUES
                                  (:ticket_id, :admin_id, 0, 'zet', :message, 1, NOW())");

      $addMessage->bindValue(':ticket_id', $ticket_id);
      $addMessage->bindValue(':admin_id', $admin_id);
      $addMessage->bindValue(':message', $message);
      $addMessage->execute();

      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'assigned_to' => $admin_name
      ]);

    } catch (Exception $e) {
      error_log("Error in admin_reassign_ticket: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to reassign ticket: ' . $e->getMessage()
      ]);
    }
  }


  elseif($_GET['f'] == 'admin_update_message'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Get data
      $data = json_decode(file_get_contents('php://input'), true);

      // Validate required fields
      if(!isset($data['message_id']) || !isset($data['message'])) {
        throw new Exception('Message ID and content are required');
      }

      $message_id = $data['message_id'];
      $message_content = $data['message'];

      // Extra logging to debug
      error_log("Attempting to update message ID: $message_id with content: $message_content");

      // Check if the ticket_messages table has an 'edited_at' column
      $checkColumn = $pdo->query("SHOW COLUMNS FROM ticket_messages LIKE 'edited_at'");
      $hasEditedAt = $checkColumn->rowCount() > 0;

      // Prepare update SQL based on available columns
      if ($hasEditedAt) {
        $sql = "UPDATE ticket_messages SET message = :message, edited_at = NOW() WHERE id = :message_id";
      } else {
        // Simpler fallback if no edited_at column exists
        $sql = "UPDATE ticket_messages SET message = :message WHERE id = :message_id";
      }

      error_log("Executing SQL: $sql");

      // Execute the update
      $updateMessage = $pdo->prepare($sql);
      $updateMessage->bindValue(':message_id', $message_id);
      $updateMessage->bindValue(':message', $message_content);
      $updateMessage->execute();

      // Check if any rows were affected
      $rowCount = $updateMessage->rowCount();
      error_log("Rows updated: $rowCount");

      if ($rowCount === 0) {
        // Try an alternative query with a different column name if no rows were updated
        error_log("No rows updated. Trying alternative query.");
        $altSql = "UPDATE ticket_messages SET content = :message WHERE id = :message_id";
        $altUpdate = $pdo->prepare($altSql);
        $altUpdate->bindValue(':message_id', $message_id);
        $altUpdate->bindValue(':message', $message_content);
        $altUpdate->execute();

        $rowCount = $altUpdate->rowCount();
        error_log("Rows updated with alternative query: $rowCount");
      }

      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'rows_affected' => $rowCount
      ]);

    } catch (Exception $e) {
      error_log("Error in admin_update_message: " . $e->getMessage());
      error_log("Error trace: " . $e->getTraceAsString());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to update message: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
      ]);
    }
  }

  // Simplified API endpoint for deleting messages
  elseif($_GET['f'] == 'admin_delete_message'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Get data
      $data = json_decode(file_get_contents('php://input'), true);

      // Validate required fields
      if(!isset($data['message_id'])) {
        throw new Exception('Message ID is required');
      }

      $message_id = $data['message_id'];

      // Extra logging to debug
      error_log("Attempting to delete message ID: $message_id");

      // Get the ticket_id first (to use for system message later)
      $ticketQuery = $pdo->prepare("SELECT ticket_id FROM ticket_messages WHERE id = :message_id");
      $ticketQuery->bindValue(':message_id', $message_id);
      $ticketQuery->execute();

      if ($ticketQuery->rowCount() === 0) {
        throw new Exception('Message not found');
      }

      $ticket_id = $ticketQuery->fetchColumn();
      error_log("Found ticket_id: $ticket_id for message_id: $message_id");

      // Delete the message directly without any type checks or transactions
      $deleteMessage = $pdo->prepare("DELETE FROM ticket_messages WHERE id = :message_id");
      $deleteMessage->bindValue(':message_id', $message_id);
      $deleteMessage->execute();

      // Check if any rows were affected
      $rowCount = $deleteMessage->rowCount();
      error_log("Rows deleted: $rowCount");

      if ($rowCount > 0 && $ticket_id) {
        // Try to add a system message about the deletion
        try {
          $systemMessage = $pdo->prepare("
            INSERT INTO ticket_messages
            (ticket_id, admin_id, type, message, is_internal, created_at)
            VALUES
            (:ticket_id, :admin_id, 'zet', 'A message was deleted by an administrator', 1, NOW())
          ");
          $systemMessage->bindValue(':ticket_id', $ticket_id);
          $systemMessage->bindValue(':admin_id', $admin_id);
          $systemMessage->execute();

          error_log("Added system message about deletion");
        } catch (Exception $systemErr) {
          // If adding the system message fails, just log it but don't fail the entire operation
          error_log("Failed to add system message: " . $systemErr->getMessage());
        }
      }

      // Return success response
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'rows_affected' => $rowCount
      ]);

    } catch (Exception $e) {
      error_log("Error in admin_delete_message: " . $e->getMessage());
      error_log("Error trace: " . $e->getTraceAsString());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to delete message: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
      ]);
    }
  }

  elseif($_GET['f'] == 'get_departments'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();

      // Get list of all departments
      $sth = $pdo->prepare("SELECT id, department_name FROM departments ORDER BY department_name");
      $sth->execute();

      $departments = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        $departments[] = array(
          'id' => $row['id'],
          'name' => $row['department_name']
        );
      }

      // Return departments list as JSON
      header('Content-Type: application/json');
      echo json_encode($departments);

    } catch (Exception $e) {
      error_log("Error in get_departments: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to fetch departments: ' . $e->getMessage()
      ]);
    }
  }

  // Admin download attachment endpoint
  elseif($_GET['f'] == 'admin_download_attachment'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();
      error_log("Admin authenticated for download_attachment: admin_id = $admin_id");

      // Get attachment ID from GET parameter
      $attachment_id = isset($_GET['id']) ? intval($_GET['id']) : null;

      if (!$attachment_id) {
        error_log("No attachment ID provided");
        http_response_code(400);
        die('Attachment ID is required');
      }

      error_log("Admin attempting to download attachment ID: $attachment_id");

      // Get attachment details from database
      $sth = $pdo->prepare("
        SELECT ta.*, tm.ticket_id
        FROM ticket_attachments ta
        JOIN ticket_messages tm ON ta.message_id = tm.id
        WHERE ta.id = :attachment_id
      ");
      $sth->bindValue(':attachment_id', $attachment_id);
      $sth->execute();

      if ($sth->rowCount() != 1) {
        error_log("Attachment not found: $attachment_id");
        http_response_code(404);
        die('Attachment not found');
      }

      $attachment = $sth->fetch(PDO::FETCH_ASSOC);
      $ticket_id = $attachment['ticket_id'];

      // Verify ticket exists (admins can access all tickets)
      $ticketCheck = $pdo->prepare("SELECT id FROM tickets WHERE id = :ticket_id");
      $ticketCheck->bindValue(':ticket_id', $ticket_id);
      $ticketCheck->execute();

      if ($ticketCheck->rowCount() != 1) {
        error_log("Ticket $ticket_id not found");
        http_response_code(404);
        die('Ticket not found');
      }

      // Get the file path
      $file_path = $attachment['file_path'];

      // Check if file exists
      if (!file_exists($file_path)) {
        error_log("File not found at path: $file_path");
        http_response_code(404);
        die('File not found on server');
      }

      // Set appropriate headers for file download
      $file_name = $attachment['file_name'];
      $file_size = filesize($file_path);

      // Get MIME type
      $finfo = finfo_open(FILEINFO_MIME_TYPE);
      $mime_type = finfo_file($finfo, $file_path);
      finfo_close($finfo);

      // Set headers
      header('Content-Type: ' . $mime_type);
      header('Content-Disposition: attachment; filename="' . $file_name . '"');
      header('Content-Length: ' . $file_size);
      header('Cache-Control: no-cache, must-revalidate');
      header('Expires: 0');

      // Output the file
      readfile($file_path);
      exit;

    } catch (Exception $e) {
      error_log("Error in admin_download_attachment: " . $e->getMessage());
      http_response_code(500);
      die('Server error: ' . $e->getMessage());
    }
  }

  // Debug endpoint to help diagnose department access issues
  elseif($_GET['f'] == 'debug_admin_access'){
    try {
      // Authenticate admin
      $admin_id = auth_admin();

      $debugInfo = [
        'admin_id' => $admin_id,
        'message' => 'Debug information for admin department access'
      ];

      // Check if tables exist
      $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
      $adminDeptTableExists = in_array('admin_departments', $tables);
      $debugInfo['admin_departments_table_exists'] = $adminDeptTableExists;
      $debugInfo['tickets_table_exists'] = in_array('tickets', $tables);

      // Get admin's primary department
      $adminDeptQuery = $pdo->prepare("SELECT department_id FROM admins WHERE id = :admin_id");
      $adminDeptQuery->bindValue(':admin_id', $admin_id);
      $adminDeptQuery->execute();
      $adminDept = $adminDeptQuery->fetch(PDO::FETCH_ASSOC);
      $debugInfo['primary_department_id'] = $adminDept ? $adminDept['department_id'] : null;

      // Get admin's assigned departments if the table exists
      $assignedDeptIds = [];
      if ($adminDeptTableExists) {
        $assignedDeptsQuery = $pdo->prepare("SELECT department_id FROM admin_departments WHERE admin_id = :admin_id");
        $assignedDeptsQuery->bindValue(':admin_id', $admin_id);
        $assignedDeptsQuery->execute();
        while ($dept = $assignedDeptsQuery->fetch(PDO::FETCH_ASSOC)) {
          $assignedDeptIds[] = $dept['department_id'];
        }
      }
      $debugInfo['assigned_department_ids'] = $assignedDeptIds;

      // If primary department exists, add it to assigned departments
      if ($adminDept && $adminDept['department_id'] && !in_array($adminDept['department_id'], $assignedDeptIds)) {
        $assignedDeptIds[] = $adminDept['department_id'];
      }
      $debugInfo['all_accessible_department_ids'] = $assignedDeptIds;

      // Get department names for the assigned department IDs
      $departmentNames = [];
      if (!empty($assignedDeptIds)) {
        $deptNamesQuery = $pdo->prepare("SELECT department_name FROM departments WHERE id IN (" . implode(',', array_map('intval', $assignedDeptIds)) . ")");
        $deptNamesQuery->execute();
        while ($dept = $deptNamesQuery->fetch(PDO::FETCH_ASSOC)) {
          $departmentNames[] = $dept['department_name'];
        }
      }
      $debugInfo['accessible_department_names'] = $departmentNames;

      // Get all departments for reference
      $allDeptsQuery = $pdo->prepare("SELECT id, department_name FROM departments ORDER BY department_name");
      $allDeptsQuery->execute();
      $allDepartments = [];
      while ($dept = $allDeptsQuery->fetch(PDO::FETCH_ASSOC)) {
        $allDepartments[] = $dept;
      }
      $debugInfo['all_departments'] = $allDepartments;

      // Check what tickets exist and their departments
      if (in_array('tickets', $tables)) {
        // Get all tickets count
        $allTicketsQuery = $pdo->prepare("SELECT COUNT(*) as total FROM tickets");
        $allTicketsQuery->execute();
        $allTicketsCount = $allTicketsQuery->fetch(PDO::FETCH_ASSOC);
        $debugInfo['total_tickets_in_database'] = $allTicketsCount['total'];

        // Get unique departments in tickets
        $ticketDeptsQuery = $pdo->prepare("SELECT DISTINCT department FROM tickets WHERE department IS NOT NULL");
        $ticketDeptsQuery->execute();
        $ticketDepartments = [];
        while ($dept = $ticketDeptsQuery->fetch(PDO::FETCH_ASSOC)) {
          $ticketDepartments[] = $dept['department'];
        }
        $debugInfo['departments_in_tickets'] = $ticketDepartments;

        // Check department name matching
        $matchingDepartments = array_intersect($departmentNames, $ticketDepartments);
        $debugInfo['matching_departments'] = $matchingDepartments;

        // Get tickets matching admin's departments
        if (!empty($departmentNames)) {
          $departmentConditions = [];
          foreach ($departmentNames as $deptName) {
            $departmentConditions[] = "department = " . $pdo->quote($deptName);
          }
          $accessibleTicketsQuery = $pdo->prepare("SELECT COUNT(*) as count FROM tickets WHERE " . implode(" OR ", $departmentConditions));
          $accessibleTicketsQuery->execute();
          $accessibleTicketsCount = $accessibleTicketsQuery->fetch(PDO::FETCH_ASSOC);
          $debugInfo['tickets_accessible_to_admin'] = $accessibleTicketsCount['count'];
        } else {
          $debugInfo['tickets_accessible_to_admin'] = 0;
          $debugInfo['note'] = 'Admin has no assigned departments, so no tickets are accessible';
        }

        // Check for mismatches
        if (empty($matchingDepartments) && !empty($departmentNames) && !empty($ticketDepartments)) {
          $debugInfo['department_name_mismatch'] = true;
          $debugInfo['recommendation'] = 'Department names in admin access do not match department names in tickets. Check data consistency.';
        }
      }

      // Return debug info as JSON
      header('Content-Type: application/json');
      echo json_encode($debugInfo);

    } catch (Exception $e) {
      error_log("Error in debug_admin_access: " . $e->getMessage());
      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to get debug info: ' . $e->getMessage()
      ]);
    }
  }

  elseif($_GET['f'] == 'get_user_details'){
    try {
      // Enable detailed error reporting for debugging
      error_log("Starting get_user_details function");

      // Authenticate admin
      $admin_id = auth_admin();
      error_log("Admin authenticated with ID: $admin_id");

      // Get data
      $data = json_decode(file_get_contents('php://input'), true);
      $user_id = isset($data['user_id']) ? $data['user_id'] : null;

      error_log("Requested user_id: $user_id");

      if (!$user_id) {
        throw new Exception('User ID is required');
      }

      // First check if users table exists
      $tablesResult = $pdo->query("SHOW TABLES");
      $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);
      error_log("Database tables found: " . implode(", ", $tables));

      if (!in_array('users', $tables)) {
        error_log("Users table does not exist, returning test data");
        // Return test data if table doesn't exist
        $testUser = [
          'id' => $user_id,
          'first_name' => 'Test',
          'last_name' => 'User',
          'email' => '<EMAIL>'
        ];

        header('Content-Type: application/json');
        echo json_encode($testUser);
        return;
      }

      // Check columns in users table
      $columnsResult = $pdo->query("DESCRIBE users");
      $columns = [];
      while ($row = $columnsResult->fetch(PDO::FETCH_ASSOC)) {
        $columns[] = $row['Field'];
      }
      error_log("Users table columns: " . implode(", ", $columns));

      // Build query based on available columns
      $sql = "SELECT id";

      // Add select fields based on available columns
      if (in_array('first_name', $columns)) $sql .= ", first_name";
      if (in_array('last_name', $columns)) $sql .= ", last_name";
      if (in_array('email', $columns)) $sql .= ", email";
      if (in_array('company_name', $columns)) $sql .= ", company_name";
      if (in_array('phone', $columns)) $sql .= ", phone";

      $sql .= " FROM users WHERE id = :user_id";

      error_log("Executing SQL: $sql");
      $sth = $pdo->prepare($sql);
      $sth->bindValue(':user_id', $user_id);
      $sth->execute();

      if ($sth->rowCount() === 0) {
        error_log("No user found with ID: $user_id");
        header('Content-Type: application/json');
        echo json_encode(['error' => 'User not found']);
        return;
      }

      $user = $sth->fetch(PDO::FETCH_ASSOC);
      error_log("Found user: " . json_encode($user));

      header('Content-Type: application/json');
      echo json_encode($user);

    } catch (Exception $e) {
      error_log("Error in get_user_details: " . $e->getMessage());
      error_log("Error trace: " . $e->getTraceAsString());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to get user details: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
      ]);
    }
  }

  elseif($_GET['f'] == 'search_users'){
    try {
      // Enable detailed error reporting for debugging
      error_log("Starting search_users function");

      // Authenticate admin
      $admin_id = auth_admin();
      error_log("Admin authenticated with ID: $admin_id");

      // Get search query
      $query = isset($_GET['q']) ? $_GET['q'] : '';
      error_log("Search query: $query");

      if (empty($query) || strlen($query) < 2) {
        error_log("Query too short, returning empty array");
        // Return empty array if query is too short
        header('Content-Type: application/json');
        echo json_encode([]);
        return;
      }

      // First check if users table exists
      $tablesResult = $pdo->query("SHOW TABLES");
      $tables = $tablesResult->fetchAll(PDO::FETCH_COLUMN);
      error_log("Database tables found: " . implode(", ", $tables));

      if (!in_array('users', $tables)) {
        error_log("Users table does not exist, returning test data");
        // Return test data if table doesn't exist
        $testUsers = [
          ['id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>'],
          ['id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>'],
          ['id' => 3, 'name' => 'Michael Johnson', 'email' => '<EMAIL>']
        ];

        // Filter test data based on query
        $filteredUsers = array_filter($testUsers, function($user) use ($query) {
          return (stripos($user['name'], $query) !== false ||
                  stripos($user['email'], $query) !== false);
        });

        header('Content-Type: application/json');
        echo json_encode(array_values($filteredUsers));
        return;
      }

      // Check columns in users table
      $columnsResult = $pdo->query("DESCRIBE users");
      $columns = [];
      while ($row = $columnsResult->fetch(PDO::FETCH_ASSOC)) {
        $columns[] = $row['Field'];
      }
      error_log("Users table columns: " . implode(", ", $columns));

      // Build query based on available columns
      $conditions = [];
      $params = [];

      if (in_array('first_name', $columns)) {
        $conditions[] = "first_name LIKE :first_name";
        $params[':first_name'] = '%' . $query . '%';
      }

      if (in_array('last_name', $columns)) {
        $conditions[] = "last_name LIKE :last_name";
        $params[':last_name'] = '%' . $query . '%';
      }

      if (in_array('email', $columns)) {
        $conditions[] = "email LIKE :email";
        $params[':email'] = '%' . $query . '%';
      }

      if (in_array('username', $columns)) {
        $conditions[] = "username LIKE :username";
        $params[':username'] = '%' . $query . '%';
      }

      if (in_array('first_name', $columns) && in_array('last_name', $columns)) {
        $conditions[] = "CONCAT(first_name, ' ', last_name) LIKE :full_name";
        $params[':full_name'] = '%' . $query . '%';
      }

      if (empty($conditions)) {
        error_log("No searchable columns found in users table");
        header('Content-Type: application/json');
        echo json_encode([]);
        return;
      }

      // Build and execute the query
      $sql = "SELECT id";

      // Add select fields based on available columns
      if (in_array('first_name', $columns)) $sql .= ", first_name";
      if (in_array('last_name', $columns)) $sql .= ", last_name";
      if (in_array('email', $columns)) $sql .= ", email";
      if (in_array('username', $columns) && !in_array('email', $columns)) $sql .= ", username as email";

      $sql .= " FROM users WHERE " . implode(" OR ", $conditions) . " LIMIT 10";

      error_log("Executing SQL: $sql");
      $sth = $pdo->prepare($sql);

      // Bind parameters
      foreach ($params as $key => $value) {
        $sth->bindValue($key, $value);
      }

      $sth->execute();

      // Process results
      $users = array();
      while($row = $sth->fetch(PDO::FETCH_ASSOC)){
        $name = '';

        if (isset($row['first_name']) && isset($row['last_name'])) {
          $name = trim($row['first_name'] . ' ' . $row['last_name']);
        } else if (isset($row['first_name'])) {
          $name = $row['first_name'];
        } else if (isset($row['last_name'])) {
          $name = $row['last_name'];
        } else if (isset($row['username'])) {
          $name = $row['username'];
        } else {
          $name = 'User #' . $row['id'];
        }

        $users[] = array(
          'id' => $row['id'],
          'name' => $name,
          'email' => $row['email'] ?? ''
        );
      }

      error_log("Found " . count($users) . " matching users");

      // Return users list as JSON
      header('Content-Type: application/json');
      echo json_encode($users);

    } catch (Exception $e) {
      error_log("Error in search_users: " . $e->getMessage());
      error_log("Error trace: " . $e->getTraceAsString());

      header('Content-Type: application/json');
      http_response_code(500);
      echo json_encode([
        'error' => 'Failed to search users: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
      ]);
    }
  }

?>