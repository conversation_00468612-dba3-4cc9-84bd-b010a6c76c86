<?php
/**
 * Functions for sending ticket-related emails
 */

// Include necessary files
require_once("mysql.php");

// Import PHPMailer classes if not already imported
use <PERSON><PERSON>Mailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception;

/**
 * Send an email notification when a staff member replies to a ticket
 *
 * @param int $ticket_id The ID of the ticket
 * @param int $message_id The ID of the reply message
 * @return bool True if email was sent successfully, false otherwise
 */
function sendTicketReplyEmail($ticket_id, $message_id) {
    global $pdo;

    try {
        // Get ticket details with user information
        $ticketStmt = $pdo->prepare("
            SELECT t.*, u.email, u.first_name, u.last_name, u.company_name
            FROM tickets t
            JOIN users u ON t.user_id = u.id
            WHERE t.id = :ticket_id
        ");
        $ticketStmt->bindValue(':ticket_id', $ticket_id);
        $ticketStmt->execute();

        $ticket = $ticketStmt->fetch(PDO::FETCH_ASSOC);

        if (!$ticket) {
            error_log("Error sending ticket reply email: Ticket #$ticket_id not found");
            return false;
        }

        // Check if user has an email
        if (empty($ticket['email'])) {
            error_log("Error sending ticket reply email: No email address found for user ID " . $ticket['user_id']);
            return false;
        }

        // Get message details
        $messageStmt = $pdo->prepare("
            SELECT m.*, a.first_name as admin_first_name, a.last_name as admin_last_name, a.function as admin_function
            FROM ticket_messages m
            LEFT JOIN admins a ON m.admin_id = a.id
            WHERE m.id = :message_id
        ");
        $messageStmt->bindValue(':message_id', $message_id);
        $messageStmt->execute();

        $message = $messageStmt->fetch(PDO::FETCH_ASSOC);

        if (!$message) {
            error_log("Error sending ticket reply email: Message #$message_id not found");
            return false;
        }

        // Skip sending email for internal notes
        if (isset($message['is_internal']) && $message['is_internal']) {
            error_log("Skipping email for internal note (message #$message_id)");
            return true;
        }

        // Get email template
        $templateStmt = $pdo->prepare("SELECT * FROM email_templates WHERE type = 'ticket_reply'");
        $templateStmt->execute();

        if ($templateStmt->rowCount() == 0) {
            error_log("Error sending ticket reply email: Template 'ticket_reply' not found");
            return false;
        }

        $template = $templateStmt->fetch(PDO::FETCH_ASSOC);

        // Prepare client name
        $clientName = '';
        if (!empty($ticket['company_name'])) {
            $clientName = $ticket['company_name'];
        } else {
            $clientName = trim(($ticket['first_name'] ?? '') . ' ' . ($ticket['last_name'] ?? ''));
        }
        if (empty($clientName)) {
            $clientName = 'Valued Customer';
        }

        // Prepare staff name
        $staffName = '';
        if (!empty($message['admin_first_name']) || !empty($message['admin_last_name'])) {
            $staffName = trim(($message['admin_first_name'] ?? '') . ' ' . ($message['admin_last_name'] ?? ''));
            if (!empty($message['admin_function'])) {
                $staffName .= ' (' . $message['admin_function'] . ')';
            }
        } else {
            $staffName = 'Support Staff';
        }

        // Replace variables in template
        $subject = $template['subject'];
        $content = $template['content'];

        // Replace variables in subject
        $subject = str_replace('{$ticket_id}', $ticket_id, $subject);
        $subject = str_replace('{$ticket_subject}', $ticket['subject'] ?? '', $subject);

        // Replace variables in content
        $content = str_replace('{$client_name}', $clientName, $content);
        $content = str_replace('{$ticket_id}', $ticket_id, $content);
        $content = str_replace('{$ticket_subject}', $ticket['subject'] ?? '', $content);
        $content = str_replace('{$ticket_status}', $ticket['status'] ?? 'Open', $content);
        $content = str_replace('{$staff_name}', $staffName, $content);
        $content = str_replace('{$message_content}', nl2br($message['message'] ?? ''), $content);

        // Send the email - direct implementation for CLI compatibility
        try {
            // Get email settings from database
            $settingsStmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");

            if ($settingsStmt->rowCount() == 0) {
                error_log("Email settings not configured");
                return false;
            }

            $settings = $settingsStmt->fetch(PDO::FETCH_ASSOC);

            // Check if email sending is enabled
            if (!$settings['enabled']) {
                error_log("Email sending is disabled in settings");
                return false;
            }

            // For Google provider, always set the Gmail SMTP settings
            if ($settings['provider'] === 'google') {
                $settings['smtp_host'] = 'smtp.gmail.com';
                $settings['smtp_port'] = '587';
                $settings['smtp_encryption'] = 'tls';
            }

            // Create a new PHPMailer instance
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/PHPMailer.php';
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/SMTP.php';
            require_once '/var/www/html/New/admin/vendor/phpmailer/phpmailer/src/Exception.php';
            $mail = new PHPMailer(true);

            // Server settings
            $mail->SMTPDebug = SMTP::DEBUG_OFF; // Disable debug output

            // Always use SMTP
            $mail->isSMTP();
            $mail->Host = $settings['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $settings['smtp_username'];
            $mail->Password = $settings['smtp_password'];

            // Set encryption type
            if ($settings['smtp_encryption'] === 'ssl') {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // SSL
                $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 465;
            } else {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS; // TLS
                $mail->Port = !empty($settings['smtp_port']) ? $settings['smtp_port'] : 587;
            }

            // Recipients
            $fromName = $settings['from_name'] ? $settings['from_name'] : 'Support System';
            $fromEmail = $settings['from_email'] ? $settings['from_email'] : '<EMAIL>';
            $mail->setFrom($fromEmail, $fromName);
            $mail->addAddress($ticket['email'], $clientName);

            if (!empty($settings['reply_to'])) {
                $mail->addReplyTo($settings['reply_to']);
            }

            // Content
            $mail->isHTML(true);
            $mail->CharSet = 'UTF-8'; // Set UTF-8 character encoding
            $mail->Subject = $subject;
            $mail->Body = $content;
            $mail->AltBody = strip_tags(str_replace('<br>', "\n", $content)); // Plain text alternative

            // Send the email
            $mail->send();
            $result = true;
        } catch (Exception $mailException) {
            error_log("Error sending ticket reply email: " . $mailException->getMessage());
            $result = false;
        }

        // Log activity if email was sent successfully
        if ($result) {
            error_log("Successfully sent ticket reply email to " . $ticket['email'] . " for ticket #$ticket_id");

            // Try to log the activity in the system_activity table if it exists
            try {
                $activityStmt = $pdo->prepare("
                    INSERT INTO system_activity 
                    (user_id, action, description, user_name)
                    VALUES 
                    (:user_id, 'Ticket Reply Email Sent', :description, :user_name)
                ");

                $activityStmt->bindValue(':user_id', $ticket['user_id']);
                $activityStmt->bindValue(':description', "Ticket reply notification email sent to " . $ticket['email']);
                $activityStmt->bindValue(':user_name', $clientName);
                $activityStmt->execute();
            } catch (Exception $e) {
                // Just log the error but don't fail the email sending
                error_log("Failed to log activity: " . $e->getMessage());
            }

            return true;
        } else {
            error_log("Failed to send ticket reply email to " . $ticket['email'] . " for ticket #$ticket_id");
            return false;
        }

    } catch (Exception $e) {
        error_log("Error in sendTicketReplyEmail: " . $e->getMessage());
        return false;
    }
}
