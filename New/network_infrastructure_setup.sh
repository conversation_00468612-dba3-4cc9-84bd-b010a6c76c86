#!/bin/bash
# Network Infrastructure Setup Script for PXE Boot
# This script sets up DHCP, TFTP, and PXE services for server reinstallation

set -e

# Configuration variables
DHCP_CONFIG="/etc/dhcp/dhcpd.conf"
TFTP_ROOT="/var/lib/tftpboot"
PXE_CONFIG_DIR="$TFTP_ROOT/pxelinux.cfg"
KICKSTART_DIR="/var/www/html/kickstart"
WEB_ROOT="/var/www/html"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

echo_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        echo_error "This script must be run as root"
        exit 1
    fi
}

# Install required packages
install_packages() {
    echo_status "Installing required packages..."
    
    if command -v apt-get &> /dev/null; then
        # Ubuntu/Debian
        apt-get update
        apt-get install -y isc-dhcp-server tftpd-hpa apache2 syslinux-common pxelinux
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL
        yum install -y dhcp-server tftp-server httpd syslinux
    elif command -v dnf &> /dev/null; then
        # Fedora
        dnf install -y dhcp-server tftp-server httpd syslinux
    else
        echo_error "Unsupported package manager"
        exit 1
    fi
}

# Setup TFTP server
setup_tftp() {
    echo_status "Setting up TFTP server..."
    
    # Create TFTP root directory
    mkdir -p "$TFTP_ROOT"
    mkdir -p "$PXE_CONFIG_DIR"
    
    # Copy PXE boot files
    if [[ -f /usr/lib/PXELINUX/pxelinux.0 ]]; then
        cp /usr/lib/PXELINUX/pxelinux.0 "$TFTP_ROOT/"
    elif [[ -f /usr/share/syslinux/pxelinux.0 ]]; then
        cp /usr/share/syslinux/pxelinux.0 "$TFTP_ROOT/"
    fi
    
    # Copy menu modules
    if [[ -d /usr/lib/syslinux/modules/bios ]]; then
        cp /usr/lib/syslinux/modules/bios/*.c32 "$TFTP_ROOT/"
    elif [[ -d /usr/share/syslinux ]]; then
        cp /usr/share/syslinux/*.c32 "$TFTP_ROOT/" 2>/dev/null || true
    fi
    
    # Set permissions
    chown -R tftp:tftp "$TFTP_ROOT"
    chmod -R 755 "$TFTP_ROOT"
    
    # Configure TFTP daemon
    cat > /etc/default/tftpd-hpa << EOF
TFTP_USERNAME="tftp"
TFTP_DIRECTORY="$TFTP_ROOT"
TFTP_ADDRESS="0.0.0.0:69"
TFTP_OPTIONS="--secure"
EOF
    
    # Start and enable TFTP service
    systemctl enable tftpd-hpa
    systemctl start tftpd-hpa
}

# Setup web server for kickstart files
setup_webserver() {
    echo_status "Setting up web server for kickstart files..."
    
    # Create kickstart directory
    mkdir -p "$KICKSTART_DIR"
    chown www-data:www-data "$KICKSTART_DIR"
    chmod 755 "$KICKSTART_DIR"
    
    # Enable Apache and start
    systemctl enable apache2
    systemctl start apache2
    
    # Create simple index page for kickstart directory
    cat > "$KICKSTART_DIR/index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>PXE Kickstart Files</title>
</head>
<body>
    <h1>PXE Installation Files</h1>
    <p>This directory contains automated installation files for PXE boot.</p>
</body>
</html>
EOF
}

# Setup DHCP server basic configuration
setup_dhcp() {
    echo_status "Setting up DHCP server configuration..."
    
    # Backup existing configuration
    if [[ -f "$DHCP_CONFIG" ]]; then
        cp "$DHCP_CONFIG" "$DHCP_CONFIG.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # Create basic DHCP configuration
    cat > "$DHCP_CONFIG" << 'EOF'
# DHCP Server Configuration for PXE Boot
# Generated by PXE setup script

# Global options
default-lease-time 600;
max-lease-time 7200;
authoritative;

# PXE boot options
option space PXE;
option PXE.mtftp-ip code 1 = ip-address;
option PXE.mtftp-cport code 2 = integer 16;
option PXE.mtftp-sport code 3 = integer 16;
option PXE.mtftp-tmout code 4 = integer 8;
option PXE.mtftp-delay code 5 = integer 8;

# Subnet configuration (EXAMPLE - MODIFY FOR YOUR NETWORK)
subnet *********** netmask ************* {
    range ************0 *************;
    option routers ***********;
    option domain-name-servers *******, *******;
    option domain-name "local.lan";
    
    # PXE boot configuration
    next-server ************;  # TFTP server IP
    filename "pxelinux.0";
    
    # Option for UEFI clients
    option architecture-type code 93 = unsigned integer 16;
    
    if option architecture-type = 00:07 {
        filename "bootx64.efi";
    } else {
        filename "pxelinux.0";
    }
}

# PXE HOST DECLARATIONS - DO NOT REMOVE THIS LINE

EOF
    
    echo_warning "DHCP configuration created with example subnet ***********/24"
    echo_warning "Please modify the subnet configuration in $DHCP_CONFIG to match your network"
}

# Create default PXE menu
setup_default_pxe_menu() {
    echo_status "Creating default PXE boot menu..."
    
    cat > "$PXE_CONFIG_DIR/default" << 'EOF'
# Default PXE Boot Menu
DEFAULT menu.c32
PROMPT 0
TIMEOUT 300

MENU TITLE PXE Boot Menu
MENU BACKGROUND pxelinux.cfg/background.jpg

LABEL local
    MENU LABEL ^1) Boot from Hard Drive
    MENU DEFAULT
    LOCALBOOT 0

LABEL memtest
    MENU LABEL ^2) Memory Test
    KERNEL memtest86+/memtest.bin

LABEL shell
    MENU LABEL ^3) Rescue Shell
    KERNEL memdisk
    INITRD rescue.img

# Additional OS installations will be added here dynamically

EOF
}

# Create OS image directories
setup_os_directories() {
    echo_status "Creating OS image directories..."
    
    # Create directories for different OS types
    mkdir -p "$TFTP_ROOT/ubuntu"
    mkdir -p "$TFTP_ROOT/centos"
    mkdir -p "$TFTP_ROOT/debian"
    mkdir -p "$TFTP_ROOT/windows"
    
    # Set permissions
    chown -R tftp:tftp "$TFTP_ROOT"
    chmod -R 755 "$TFTP_ROOT"
    
    echo_status "OS directories created. You can now add kernel and initrd files:"
    echo "  - Ubuntu: $TFTP_ROOT/ubuntu/ (linux, initrd.gz)"
    echo "  - CentOS: $TFTP_ROOT/centos/ (vmlinuz, initrd.img)"
    echo "  - Debian: $TFTP_ROOT/debian/ (linux, initrd.gz)"
}

# Create management scripts
create_management_scripts() {
    echo_status "Creating PXE management scripts..."
    
    # Create script to add DHCP reservations
    cat > /usr/local/bin/pxe-add-host << 'EOF'
#!/bin/bash
# Add DHCP reservation for PXE boot

if [[ $# -ne 4 ]]; then
    echo "Usage: $0 <hostname> <mac_address> <ip_address> <server_id>"
    exit 1
fi

HOSTNAME="$1"
MAC="$2"
IP="$3"
SERVER_ID="$4"

# Format MAC address
MAC_FORMATTED=$(echo "$MAC" | sed 's/[:-]//g' | sed 's/../&:/g;s/:$//')

# Add host declaration to DHCP config
cat >> /etc/dhcp/dhcpd.conf << EOL

# Server ID: $SERVER_ID - PXE Boot Configuration
host $HOSTNAME {
    hardware ethernet $MAC_FORMATTED;
    fixed-address $IP;
    option host-name "$HOSTNAME";
    next-server ************;
    filename "pxelinux.0";
}
EOL

# Test and reload DHCP
if dhcpd -t -cf /etc/dhcp/dhcpd.conf; then
    systemctl reload isc-dhcp-server
    echo "DHCP reservation added for $HOSTNAME ($IP)"
else
    echo "DHCP configuration error!"
    exit 1
fi
EOF

    # Create script to remove DHCP reservations
    cat > /usr/local/bin/pxe-remove-host << 'EOF'
#!/bin/bash
# Remove DHCP reservation

if [[ $# -ne 2 ]]; then
    echo "Usage: $0 <hostname> <server_id>"
    exit 1
fi

HOSTNAME="$1"
SERVER_ID="$2"

# Backup DHCP config
cp /etc/dhcp/dhcpd.conf "/etc/dhcp/dhcpd.conf.backup.$(date +%Y%m%d_%H%M%S)"

# Remove host declaration
sed -i "/# Server ID: $SERVER_ID/,/^}/d" /etc/dhcp/dhcpd.conf

# Test and reload DHCP
if dhcpd -t -cf /etc/dhcp/dhcpd.conf; then
    systemctl reload isc-dhcp-server
    echo "DHCP reservation removed for $HOSTNAME"
else
    echo "DHCP configuration error!"
    exit 1
fi
EOF

    # Create script to create PXE boot configuration
    cat > /usr/local/bin/pxe-create-config << 'EOF'
#!/bin/bash
# Create PXE boot configuration for specific MAC

if [[ $# -ne 4 ]]; then
    echo "Usage: $0 <mac_address> <hostname> <os_template> <kickstart_url>"
    exit 1
fi

MAC="$1"
HOSTNAME="$2"
OS_TEMPLATE="$3"
KICKSTART_URL="$4"

# Convert MAC to PXE format
PXE_MAC="01-$(echo "$MAC" | tr ':' '-' | tr '[:upper:]' '[:lower:]')"
CONFIG_FILE="/var/lib/tftpboot/pxelinux.cfg/$PXE_MAC"

# Create PXE configuration
cat > "$CONFIG_FILE" << EOL
# PXE Configuration for $HOSTNAME ($MAC)
DEFAULT install
PROMPT 0
TIMEOUT 30

LABEL install
    MENU LABEL Install $OS_TEMPLATE on $HOSTNAME
    KERNEL $OS_TEMPLATE/vmlinuz
    APPEND initrd=$OS_TEMPLATE/initrd.img ks=$KICKSTART_URL ksdevice=bootif ip=dhcp

LABEL local
    MENU LABEL Boot from Hard Drive
    LOCALBOOT 0
EOL

echo "PXE configuration created: $CONFIG_FILE"
EOF

    # Make scripts executable
    chmod +x /usr/local/bin/pxe-*
}

# Display final status and instructions
show_completion_status() {
    echo_status "PXE infrastructure setup completed!"
    echo ""
    echo "Services status:"
    systemctl is-active --quiet isc-dhcp-server && echo "✓ DHCP Server: Running" || echo "✗ DHCP Server: Stopped"
    systemctl is-active --quiet tftpd-hpa && echo "✓ TFTP Server: Running" || echo "✗ TFTP Server: Stopped"
    systemctl is-active --quiet apache2 && echo "✓ Web Server: Running" || echo "✗ Web Server: Stopped"
    
    echo ""
    echo "Next steps:"
    echo "1. Review and modify DHCP configuration: $DHCP_CONFIG"
    echo "2. Add OS kernel and initrd files to: $TFTP_ROOT/[ubuntu|centos|debian]/"
    echo "3. Configure firewall to allow DHCP (67/68), TFTP (69), and HTTP (80) traffic"
    echo "4. Use management scripts:"
    echo "   - pxe-add-host <hostname> <mac> <ip> <server_id>"
    echo "   - pxe-remove-host <hostname> <server_id>"
    echo "   - pxe-create-config <mac> <hostname> <os> <kickstart_url>"
    echo ""
    echo "Directory structure:"
    echo "├── $TFTP_ROOT/ (TFTP root)"
    echo "│   ├── pxelinux.0 (PXE bootloader)"
    echo "│   ├── pxelinux.cfg/ (Boot configurations)"
    echo "│   └── [ubuntu|centos|debian]/ (OS files)"
    echo "└── $KICKSTART_DIR/ (Installation files)"
}

# Main execution
main() {
    echo_status "Starting PXE infrastructure setup..."
    
    check_root
    install_packages
    setup_tftp
    setup_webserver
    setup_dhcp
    setup_default_pxe_menu
    setup_os_directories
    create_management_scripts
    
    show_completion_status
}

# Run main function
main "$@" 