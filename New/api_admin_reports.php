<?php
require_once("auth_functions.php");
require_once("mysql.php");

// Set headers for CORS and JSON response
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
  http_response_code(200);
  exit();
}

// Authenticate admin user
try {
  $admin_id = auth_admin();
} catch (Exception $e) {
  http_response_code(401);
  echo json_encode(['error' => 'Authentication failed: ' . $e->getMessage()]);
  exit();
}

// Get request data
$requestBody = file_get_contents('php://input');
$data = json_decode($requestBody, true) ?: [];

// Handle different report endpoints
if (isset($_GET['f'])) {

  // Get report stats (summary metrics)
  if ($_GET['f'] == 'get_report_stats') {
    try {
      $stats = getReportStats($pdo);
      echo json_encode($stats);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get report stats: ' . $e->getMessage()]);
    }
  }

  // Get revenue data
  elseif ($_GET['f'] == 'get_revenue_data') {
    try {
      $timeFrame = isset($_GET['timeFrame']) ? $_GET['timeFrame'] : 'monthly';
      $revenueData = getRevenueData($pdo, $timeFrame);
      echo json_encode($revenueData);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get revenue data: ' . $e->getMessage()]);
    }
  }

  // Get order status distribution
  elseif ($_GET['f'] == 'get_order_status') {
    try {
      $period = isset($_GET['period']) ? $_GET['period'] : 'thisMonth';
      $orderStatusData = getOrderStatusData($pdo, $period);
      echo json_encode($orderStatusData);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get order status data: ' . $e->getMessage()]);
    }
  }

  // Get customer growth data
  elseif ($_GET['f'] == 'get_customer_growth') {
    try {
      $period = isset($_GET['period']) ? $_GET['period'] : 'thisYear';
      $customerGrowthData = getCustomerGrowthData($pdo, $period);
      echo json_encode($customerGrowthData);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get customer growth data: ' . $e->getMessage()]);
    }
  }

  // Get top locations data
  elseif ($_GET['f'] == 'get_top_locations') {
    try {
      $metric = isset($_GET['metric']) ? $_GET['metric'] : 'byRevenue';
      $topLocationsData = getTopLocationsData($pdo, $metric);
      echo json_encode($topLocationsData);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get top locations data: ' . $e->getMessage()]);
    }
  }

  // Get performance metrics data
  elseif ($_GET['f'] == 'get_performance_metrics') {
    try {
      $period = isset($_GET['period']) ? $_GET['period'] : 'thisYear';
      $performanceData = getPerformanceMetricsData($pdo, $period);
      echo json_encode($performanceData);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get performance metrics data: ' . $e->getMessage()]);
    }
  }

  // Get server inventory data
  elseif ($_GET['f'] == 'get_server_inventory') {
    try {
      $serverInventoryData = getServerInventoryData($pdo);
      echo json_encode($serverInventoryData);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get server inventory data: ' . $e->getMessage()]);
    }
  }

  // Get available CPU models
  elseif ($_GET['f'] == 'get_cpu_models') {
    try {
      $cpuModels = getCpuModels($pdo);
      echo json_encode($cpuModels);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get CPU models: ' . $e->getMessage()]);
    }
  }

  // Get ticket data
  elseif ($_GET['f'] == 'get_ticket_data') {
    try {
      $period = isset($_GET['period']) ? $_GET['period'] : 'thisYear';
      $ticketData = getTicketData($pdo, $period);
      echo json_encode($ticketData);
    } catch (Exception $e) {
      http_response_code(500);
      echo json_encode(['error' => 'Failed to get ticket data: ' . $e->getMessage()]);
    }
  }

  // Invalid endpoint
  else {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid endpoint']);
  }
} else {
  http_response_code(400);
  echo json_encode(['error' => 'Missing function parameter']);
}

/**
 * Get report stats (summary metrics)
 */
function getReportStats($pdo) {
  // Get total revenue - include all invoices, not just paid ones
  $revenueSql = "SELECT SUM(value) as total_revenue FROM invoices";
  $revenueStmt = $pdo->query($revenueSql);
  $revenueData = $revenueStmt->fetch(PDO::FETCH_ASSOC);
  $totalRevenue = $revenueData['total_revenue'] ?: 0;

  // Get last year's revenue for comparison
  $lastYearSql = "SELECT SUM(value) as last_year_revenue FROM invoices
                  WHERE date BETWEEN DATE_SUB(NOW(), INTERVAL 2 YEAR) AND DATE_SUB(NOW(), INTERVAL 1 YEAR)";
  $lastYearStmt = $pdo->query($lastYearSql);
  $lastYearData = $lastYearStmt->fetch(PDO::FETCH_ASSOC);
  $lastYearRevenue = $lastYearData['last_year_revenue'] ?: 1; // Avoid division by zero

  // Calculate revenue change percentage
  $revenueChange = (($totalRevenue - $lastYearRevenue) / $lastYearRevenue) * 100;

  // Get total orders
  $ordersSql = "SELECT COUNT(*) as total_orders FROM orders";
  $ordersStmt = $pdo->query($ordersSql);
  $ordersData = $ordersStmt->fetch(PDO::FETCH_ASSOC);
  $totalOrders = $ordersData['total_orders'] ?: 0;

  // Get last month's orders for comparison
  $lastMonthOrdersSql = "SELECT COUNT(*) as last_month_orders FROM orders
                         WHERE order_date BETWEEN DATE_SUB(DATE_SUB(NOW(), INTERVAL DAYOFMONTH(NOW()) DAY), INTERVAL 1 MONTH)
                         AND DATE_SUB(NOW(), INTERVAL DAYOFMONTH(NOW()) DAY)";
  $lastMonthOrdersStmt = $pdo->query($lastMonthOrdersSql);
  $lastMonthOrdersData = $lastMonthOrdersStmt->fetch(PDO::FETCH_ASSOC);
  $lastMonthOrders = $lastMonthOrdersData['last_month_orders'] ?: 1; // Avoid division by zero

  // Calculate orders change percentage
  $ordersChange = (($totalOrders - $lastMonthOrders) / $lastMonthOrders) * 100;

  // Get total customers
  $customersSql = "SELECT COUNT(*) as total_customers FROM users WHERE status = 1";
  $customersStmt = $pdo->query($customersSql);
  $customersData = $customersStmt->fetch(PDO::FETCH_ASSOC);
  $totalCustomers = $customersData['total_customers'] ?: 0;

  // Get last month's customers for comparison
  $lastMonthCustomersSql = "SELECT COUNT(*) as last_month_customers FROM users
                           WHERE status = 1 AND created <= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
  $lastMonthCustomersStmt = $pdo->query($lastMonthCustomersSql);
  $lastMonthCustomersData = $lastMonthCustomersStmt->fetch(PDO::FETCH_ASSOC);
  $lastMonthCustomers = $lastMonthCustomersData['last_month_customers'] ?: 1; // Avoid division by zero

  // Calculate customers change percentage
  $customersChange = (($totalCustomers - $lastMonthCustomers) / $lastMonthCustomers) * 100;

  // Calculate average order value
  $avgOrderValue = $totalOrders > 0 ? $totalRevenue / $totalOrders : 0;

  // Get last month's average order value for comparison
  $lastMonthAvgSql = "SELECT AVG(value) as last_month_avg FROM invoices
                      WHERE date BETWEEN DATE_SUB(DATE_SUB(NOW(), INTERVAL DAYOFMONTH(NOW()) DAY), INTERVAL 1 MONTH)
                      AND DATE_SUB(NOW(), INTERVAL DAYOFMONTH(NOW()) DAY)";
  $lastMonthAvgStmt = $pdo->query($lastMonthAvgSql);
  $lastMonthAvgData = $lastMonthAvgStmt->fetch(PDO::FETCH_ASSOC);
  $lastMonthAvg = $lastMonthAvgData['last_month_avg'] ?: 1; // Avoid division by zero

  // Debug output
  error_log("Total Revenue: $totalRevenue");
  error_log("Total Orders: $totalOrders");
  error_log("Avg Order Value: $avgOrderValue");

  // Calculate average order value change percentage
  $avgOrderValueChange = (($avgOrderValue - $lastMonthAvg) / $lastMonthAvg) * 100;

  // Format values
  $formattedTotalRevenue = '€' . number_format($totalRevenue, 0, '.', ',');
  $formattedAvgOrderValue = '€' . number_format($avgOrderValue, 0, '.', ',');

  // Return stats array
  return [
    [
      'title' => 'Total Revenue',
      'value' => $formattedTotalRevenue,
      'change' => ($revenueChange >= 0 ? '+' : '') . number_format($revenueChange, 1) . '%',
      'period' => 'vs last year',
      'iconClass' => 'icon-dropshadow-info',
      'icon' => null // Will be set in the frontend
    ],
    [
      'title' => 'Orders',
      'value' => $totalOrders,
      'change' => ($ordersChange >= 0 ? '+' : '') . number_format($ordersChange, 1) . '%',
      'period' => 'vs last month',
      'iconClass' => 'icon-dropshadow-success',
      'icon' => null // Will be set in the frontend
    ],
    [
      'title' => 'Customers',
      'value' => $totalCustomers,
      'change' => ($customersChange >= 0 ? '+' : '') . number_format($customersChange, 1) . '%',
      'period' => 'vs last month',
      'iconClass' => 'icon-dropshadow-warning',
      'icon' => null // Will be set in the frontend
    ],
    [
      'title' => 'Avg. Order Value',
      'value' => $formattedAvgOrderValue,
      'change' => ($avgOrderValueChange >= 0 ? '+' : '') . number_format($avgOrderValueChange, 1) . '%',
      'period' => 'vs last month',
      'iconClass' => 'icon-dropshadow-danger',
      'icon' => null // Will be set in the frontend
    ]
  ];
}

/**
 * Get revenue data based on time frame - FIXED VERSION
 */
function getRevenueData($pdo, $timeFrame) {
  $result = [];

  // Debug output
  error_log("Getting revenue data for timeframe: $timeFrame");

  switch ($timeFrame) {
    case 'daily':
      // Get revenue data for the last 24 hours in 4-hour intervals
      // Fixed version that doesn't use GROUP BY
      $timeSlots = [
        '12AM' => ['start_hour' => 0, 'end_hour' => 3],
        '4AM' => ['start_hour' => 4, 'end_hour' => 7],
        '8AM' => ['start_hour' => 8, 'end_hour' => 11],
        '12PM' => ['start_hour' => 12, 'end_hour' => 15],
        '4PM' => ['start_hour' => 16, 'end_hour' => 19],
        '8PM' => ['start_hour' => 20, 'end_hour' => 23]
      ];

      foreach ($timeSlots as $name => $hours) {
        $sql = "SELECT SUM(value) as revenue
                FROM invoices
                WHERE date >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                AND HOUR(date) BETWEEN :start_hour AND :end_hour";

        $stmt = $pdo->prepare($sql);
        $stmt->bindValue(':start_hour', $hours['start_hour']);
        $stmt->bindValue(':end_hour', $hours['end_hour']);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $result[] = [
          'name' => $name,
          'revenue' => floatval($row['revenue'] ?: 0)
        ];
      }
      break;

    case 'weekly':
      // Get revenue data for the last 7 days
      // Fixed version that doesn't use GROUP BY
      $days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

      foreach ($days as $dayIndex => $dayName) {
        // MySQL DAYOFWEEK: 1 = Sunday, 2 = Monday, etc.
        // We need to convert our index (0 = Monday) to MySQL's format
        $mysqlDayIndex = $dayIndex + 2;
        if ($mysqlDayIndex > 7) $mysqlDayIndex = 1; // Handle Sunday

        $sql = "SELECT SUM(value) as revenue
                FROM invoices
                WHERE date >= DATE_SUB(NOW(), INTERVAL 1 WEEK)
                AND DAYOFWEEK(date) = :day_index";

        $stmt = $pdo->prepare($sql);
        $stmt->bindValue(':day_index', $mysqlDayIndex);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $result[] = [
          'name' => $dayName,
          'revenue' => floatval($row['revenue'] ?: 0)
        ];
      }
      break;

    case 'yearly':
      // Get revenue data for the last 6 years
      // Fixed version that doesn't use GROUP BY
      $currentYear = date('Y');
      for ($i = 5; $i >= 0; $i--) {
        $year = $currentYear - $i;

        $sql = "SELECT SUM(value) as revenue
                FROM invoices
                WHERE YEAR(date) = :year";

        $stmt = $pdo->prepare($sql);
        $stmt->bindValue(':year', $year);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $result[] = [
          'name' => (string)$year,
          'revenue' => floatval($row['revenue'] ?: 0)
        ];
      }
      break;

    case 'monthly':
    default:
      // Get revenue data for the last 12 months
      // Fixed version that doesn't use GROUP BY
      $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      foreach ($months as $monthIndex => $monthName) {
        // Month index in MySQL is 1-based
        $mysqlMonthIndex = $monthIndex + 1;

        $sql = "SELECT SUM(value) as revenue
                FROM invoices
                WHERE MONTH(date) = :month_index
                AND YEAR(date) = YEAR(NOW())";

        $stmt = $pdo->prepare($sql);
        $stmt->bindValue(':month_index', $mysqlMonthIndex);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $revenue = floatval($row['revenue'] ?: 0);

        // Debug output for April
        if ($monthName === 'Apr') {
          error_log("April revenue: $revenue");
          error_log("SQL: $sql with month_index = $mysqlMonthIndex");
        }

        $result[] = [
          'name' => $monthName,
          'revenue' => $revenue
        ];
      }
      break;
  }

  return $result;
}

/**
 * Get order status distribution data
 */
function getOrderStatusData($pdo, $period) {
  // Define time period condition
  $timeCondition = "1=1"; // Default: all time
  switch ($period) {
    case 'thisMonth':
      $timeCondition = "MONTH(order_date) = MONTH(NOW()) AND YEAR(order_date) = YEAR(NOW())";
      break;
    case 'lastMonth':
      $timeCondition = "MONTH(order_date) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH)) AND YEAR(order_date) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH))";
      break;
    case 'thisQuarter':
      $timeCondition = "QUARTER(order_date) = QUARTER(NOW()) AND YEAR(order_date) = YEAR(NOW())";
      break;
    case 'lastQuarter':
      $timeCondition = "QUARTER(order_date) = QUARTER(DATE_SUB(NOW(), INTERVAL 3 MONTH)) AND YEAR(order_date) = YEAR(DATE_SUB(NOW(), INTERVAL 3 MONTH))";
      break;
    case 'thisYear':
      $timeCondition = "YEAR(order_date) = YEAR(NOW())";
      break;
  }

  // Query order status distribution
  $sql = "SELECT
            CASE
              WHEN status = 'completed' THEN 'Completed'
              WHEN status = 'processing' THEN 'Processing'
              WHEN status = 'pending' THEN 'Pending'
              WHEN status IN ('cancelled', 'canceled') THEN 'Cancelled'
              ELSE status
            END as name,
            COUNT(*) as value
          FROM orders
          WHERE $timeCondition
          GROUP BY name
          ORDER BY value DESC";

  $stmt = $pdo->query($sql);
  $result = [];

  if ($stmt) {
    // Define colors for each status
    $colors = [
      'Completed' => '#4CD471',
      'Processing' => '#6366F1',
      'Pending' => '#F7C948',
      'Cancelled' => '#F87171'
    ];

    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
      $status = $row['name'];
      $result[] = [
        'name' => $status,
        'value' => intval($row['value']),
        'color' => isset($colors[$status]) ? $colors[$status] : '#9CA3AF'
      ];
    }
  }

  // If no data, return default values
  if (empty($result)) {
    $result = [
      ['name' => 'Completed', 'value' => 0, 'color' => '#4CD471'],
      ['name' => 'Processing', 'value' => 0, 'color' => '#6366F1'],
      ['name' => 'Pending', 'value' => 0, 'color' => '#F7C948'],
      ['name' => 'Cancelled', 'value' => 0, 'color' => '#F87171']
    ];
  }

  return $result;
}

/**
 * Get customer growth data
 */
function getCustomerGrowthData($pdo, $period) {
  $result = [];
  $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  // Initialize result array with all months
  foreach ($months as $month) {
    $result[$month] = [
      'name' => $month,
      'newCustomers' => 0,
      'existingCustomers' => 0
    ];
  }

  // Determine the year based on period
  $year = date('Y'); // Default to current year
  if ($period === 'lastYear') {
    $year = date('Y') - 1;
  }

  // For each month, count new customers created in that month
  foreach ($months as $index => $month) {
    $monthNumber = $index + 1;

    // Query for new customers in this month
    $sql = "SELECT COUNT(*) as count
            FROM users
            WHERE YEAR(created) = :year
            AND MONTH(created) = :month";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':year', $year);
    $stmt->bindValue(':month', $monthNumber);
    $stmt->execute();

    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    $result[$month]['newCustomers'] = intval($row['count']);

    // Query for existing customers (created before this month)
    $sql = "SELECT COUNT(*) as count
            FROM users
            WHERE created < :date";

    $date = "$year-$monthNumber-01";
    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':date', $date);
    $stmt->execute();

    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    $result[$month]['existingCustomers'] = intval($row['count']);
  }

  // Convert to indexed array
  return array_values($result);
}

/**
 * Get top locations data
 */
function getTopLocationsData($pdo, $metric) {
  // Define query based on metric
  switch ($metric) {
    case 'byRevenue':
      $sql = "SELECT
                c.city as name,
                SUM(i.value) as value
              FROM invoices i
              JOIN users u ON i.user_id = u.id
              JOIN cities c ON u.city = c.id
              WHERE i.paid = 1
              GROUP BY c.city
              ORDER BY value DESC
              LIMIT 4";
      break;
    case 'byOrders':
      $sql = "SELECT
                c.city as name,
                COUNT(o.id) as value
              FROM orders o
              JOIN users u ON o.owner_id = u.id
              JOIN cities c ON u.city = c.id
              GROUP BY c.city
              ORDER BY value DESC
              LIMIT 4";
      break;
    case 'byCustomers':
      $sql = "SELECT
                c.city as name,
                COUNT(u.id) as value
              FROM users u
              JOIN cities c ON u.city = c.id
              WHERE u.status = 1
              GROUP BY c.city
              ORDER BY value DESC
              LIMIT 4";
      break;
    default:
      $sql = "SELECT
                c.city as name,
                SUM(i.value) as value
              FROM invoices i
              JOIN users u ON i.user_id = u.id
              JOIN cities c ON u.city = c.id
              WHERE i.paid = 1
              GROUP BY c.city
              ORDER BY value DESC
              LIMIT 4";
  }

  $stmt = $pdo->query($sql);
  $result = [];

  if ($stmt) {
    // Define colors for top locations
    $colors = ['#6366F1', '#4CD471', '#F7C948', '#F87171'];
    $index = 0;

    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
      $result[] = [
        'name' => $row['name'],
        'value' => intval($row['value']),
        'color' => $colors[$index % count($colors)]
      ];
      $index++;
    }
  }

  // If no data, return default values
  if (empty($result)) {
    $result = [
      ['name' => 'Frankfurt', 'value' => 0, 'color' => '#6366F1'],
      ['name' => 'Berlin', 'value' => 0, 'color' => '#4CD471'],
      ['name' => 'Munich', 'value' => 0, 'color' => '#F7C948'],
      ['name' => 'Hamburg', 'value' => 0, 'color' => '#F87171']
    ];
  }

  return $result;
}

/**
 * Get performance metrics data
 */
function getPerformanceMetricsData($pdo, $period) {
  // Define time period
  $timeCondition = "";
  switch ($period) {
    case 'thisYear':
      $timeCondition = "YEAR(timestamp) = YEAR(NOW())";
      break;
    case 'lastYear':
      $timeCondition = "YEAR(timestamp) = YEAR(DATE_SUB(NOW(), INTERVAL 1 YEAR))";
      break;
    case 'last3Years':
      $timeCondition = "timestamp >= DATE_SUB(NOW(), INTERVAL 3 YEAR)";
      break;
    default:
      $timeCondition = "YEAR(timestamp) = YEAR(NOW())";
  }

  // Query performance metrics from activity_log or another monitoring table
  $sql = "SELECT
            DATE_FORMAT(timestamp, '%b') as month,
            AVG(CASE WHEN activity_type = 'network' THEN 100 ELSE NULL END) as network,
            AVG(CASE WHEN activity_type = 'server' THEN 100 ELSE NULL END) as server,
            AVG(CASE WHEN activity_type = 'service' THEN 100 ELSE NULL END) as service
          FROM
            activity_log
          WHERE
            $timeCondition
          GROUP BY
            month
          ORDER BY
            MIN(timestamp)";

  $stmt = $pdo->query($sql);
  $result = [];
  $monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  // Initialize with default values
  foreach ($monthNames as $month) {
    $result[$month] = [
      'name' => $month,
      'network' => 99.95,
      'server' => 99.96,
      'service' => 99.94
    ];
  }

  // Fill in actual data where available
  if ($stmt) {
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
      $month = $row['month'];
      if (isset($result[$month])) {
        $result[$month]['network'] = floatval($row['network'] ?: 99.95);
        $result[$month]['server'] = floatval($row['server'] ?: 99.96);
        $result[$month]['service'] = floatval($row['service'] ?: 99.94);
      }
    }
  }

  // Convert to indexed array
  return array_values($result);
}

/**
 * Get available CPU models from the database
 */
function getCpuModels($pdo) {
  try {
    // Query to get all CPU models from the database
    $sql = "SELECT id, cpu FROM dedicated_cpu ORDER BY id ASC";
    $stmt = $pdo->query($sql);
    $cpuModels = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // If no CPU models found, return default values
    if (empty($cpuModels)) {
      return [
        ['id' => 1, 'cpu' => 'Dual E5-2690v4'],
        ['id' => 2, 'cpu' => 'Dual E5-2670v3']
      ];
    }

    return $cpuModels;
  } catch (PDOException $e) {
    throw new Exception('Database error: ' . $e->getMessage());
  }
}

/**
 * Get server inventory data
 */
function getServerInventoryData($pdo) {
  try {
    // First, get all available CPU models
    $cpuModels = getCpuModels($pdo);

    // Build dynamic SQL for CPU columns
    $cpuCaseStatements = [];
    $cpuColumnNames = [];

    foreach ($cpuModels as $cpu) {
      $cpuId = $cpu['id'];
      $cpuName = $cpu['cpu'];
      $columnName = 'cpu' . $cpuId . 'Servers';
      $cpuColumnNames[] = $columnName;

      $cpuCaseStatements[] = "SUM(CASE WHEN cpu_label = '$cpuName' THEN (dedicatedServers + bladeServers) ELSE 0 END) as $columnName";
    }

    // Join the CPU case statements for the SQL query
    $cpuCaseStatementsStr = implode(",\n              ", $cpuCaseStatements);

    // Query to get servers by location and CPU with status 'Available'
    $sql = "SELECT
              location as name,
              $cpuCaseStatementsStr
            FROM (
              -- Dedicated servers by location and CPU
              SELECT
                COALESCE(c.city, 'Unknown Location') as location,
                COALESCE(dc.cpu, 'Unknown CPU') as cpu_label,
                COUNT(ids.id) as dedicatedServers,
                0 as bladeServers
              FROM
                inventory_dedicated_servers ids
              LEFT JOIN
                cities c ON ids.city_id = c.id
              LEFT JOIN
                dedicated_cpu dc ON ids.cpu = dc.id
              WHERE
                ids.status = 'Available'
              GROUP BY
                location, cpu_label

              UNION ALL

              -- Blade servers by location and CPU
              SELECT
                COALESCE(c.city, 'Unknown Location') as location,
                COALESCE(dc.cpu, 'Unknown CPU') as cpu_label,
                0 as dedicatedServers,
                COUNT(bsi.id) as bladeServers
              FROM
                blade_server_inventory bsi
              LEFT JOIN
                inventory_chassis ic ON bsi.chassis_id = ic.id
              LEFT JOIN
                cities c ON ic.city_id = c.id
              LEFT JOIN
                dedicated_cpu dc ON bsi.cpu = dc.id
              WHERE
                bsi.status = 'Available'
              GROUP BY
                location, cpu_label
            ) as combined_data
            GROUP BY
              location
            HAVING
              " . implode(' + ', $cpuColumnNames) . " > 0
            ORDER BY
              location ASC
            LIMIT 20";

    // Debug output
    error_log("Server inventory SQL: $sql");

    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Debug output
    error_log("Server inventory results count: " . count($results));
    error_log("Server inventory results: " . json_encode($results));

    // Add colors to the results
    $colors = ['#6366F1', '#4CD471', '#F7C948', '#F87171', '#A78BFA', '#60A5FA', '#34D399', '#FBBF24', '#FB7185', '#818CF8'];
    foreach ($results as $key => $result) {
      $results[$key]['color'] = $colors[$key % count($colors)];
    }

    // If no data, check if we have any servers and add debug info
    if (empty($results)) {
      error_log("No server inventory results found. Checking for available servers...");

      // Let's check if we have any servers in the database
      $checkDedicatedSql = "SELECT COUNT(*) as count FROM inventory_dedicated_servers WHERE status = 'Available'";
      $checkBladeSql = "SELECT COUNT(*) as count FROM blade_server_inventory WHERE status = 'Available'";

      $dedicatedCount = $pdo->query($checkDedicatedSql)->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
      $bladeCount = $pdo->query($checkBladeSql)->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

      error_log("Available dedicated servers: $dedicatedCount");
      error_log("Available blade servers: $bladeCount");

      // If we have servers but no results, there might be an issue with the joins or grouping
      if ($dedicatedCount > 0 || $bladeCount > 0) {
        // Add a debug entry with real server counts
        $result = ['name' => 'Available Servers', 'color' => '#6366F1'];

        // Add CPU columns
        foreach ($cpuModels as $index => $cpu) {
          $columnName = 'cpu' . $cpu['id'] . 'Servers';
          $result[$columnName] = $index === 0 ? $dedicatedCount : $bladeCount;
        }

        $results = [$result];
      } else {
        // No servers found, return default values with dynamic CPU columns
        $locations = ['Bucharest', 'Frankfurt', 'Amsterdam', 'London'];
        $results = [];

        foreach ($locations as $index => $location) {
          $result = ['name' => $location, 'color' => $colors[$index % count($colors)]];

          // Add CPU columns with some default values
          foreach ($cpuModels as $cpuIndex => $cpu) {
            $columnName = 'cpu' . $cpu['id'] . 'Servers';
            $result[$columnName] = ($index + $cpuIndex) % 4;
          }

          $results[] = $result;
        }
      }
    }

    // Add CPU models info to the response
    $results = [
      'data' => $results,
      'cpuModels' => $cpuModels
    ];

    // Final debug output
    error_log("Final server inventory results: " . json_encode($results));

    return $results;
  } catch (PDOException $e) {
    throw new Exception('Database error: ' . $e->getMessage());
  }
}

/**
 * Get ticket data
 */
function getTicketData($pdo, $period) {
  try {
    // Define time period condition
    $timeCondition = "1=1"; // Default: all time
    $groupBy = "MONTH(created_at)";
    $dateFormat = "%b"; // Month abbreviation

    switch ($period) {
      case 'thisMonth':
        $timeCondition = "MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())";
        $groupBy = "DAY(created_at)";
        $dateFormat = "%d"; // Day of month
        break;
      case 'lastMonth':
        $timeCondition = "MONTH(created_at) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH)) AND YEAR(created_at) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH))";
        $groupBy = "DAY(created_at)";
        $dateFormat = "%d"; // Day of month
        break;
      case 'thisYear':
        $timeCondition = "YEAR(created_at) = YEAR(NOW())";
        $groupBy = "MONTH(created_at)";
        $dateFormat = "%b"; // Month abbreviation
        break;
      case 'lastYear':
        $timeCondition = "YEAR(created_at) = YEAR(NOW()) - 1";
        $groupBy = "MONTH(created_at)";
        $dateFormat = "%b"; // Month abbreviation
        break;
    }

    // Query for tickets by status
    $sql = "SELECT
              DATE_FORMAT(created_at, '$dateFormat') as name,
              COUNT(CASE WHEN status = 'Open' THEN 1 END) as open,
              COUNT(CASE WHEN status = 'In Progress' THEN 1 END) as inProgress,
              COUNT(CASE WHEN status = 'Closed' THEN 1 END) as closed
            FROM tickets
            WHERE $timeCondition
            GROUP BY $groupBy, name
            ORDER BY MIN(created_at)";

    $stmt = $pdo->query($sql);
    $results = [];

    if ($stmt) {
      while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $results[] = [
          'name' => $row['name'],
          'open' => intval($row['open'] ?: 0),
          'inProgress' => intval($row['inProgress'] ?: 0),
          'closed' => intval($row['closed'] ?: 0)
        ];
      }
    }

    // If no data, return empty array
    if (empty($results)) {
      if ($period === 'thisMonth' || $period === 'lastMonth') {
        // Initialize with days of month
        $days = range(1, 30);
        foreach ($days as $day) {
          $results[] = [
            'name' => sprintf("%02d", $day),
            'open' => 0,
            'inProgress' => 0,
            'closed' => 0
          ];
        }
      } else {
        // Initialize with months of year
        $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        foreach ($months as $month) {
          $results[] = [
            'name' => $month,
            'open' => 0,
            'inProgress' => 0,
            'closed' => 0
          ];
        }
      }
    }

    return $results;
  } catch (PDOException $e) {
    throw new Exception('Database error: ' . $e->getMessage());
  }
}
?>
