# Manual ESXi Setup Commands

## Quick Setup Guide

### Step 1: Download ESXi ISO Files

**ESXi 8.0 (Latest)**
```bash
# Create download directory
mkdir -p /tmp/esxi_downloads
cd /tmp/esxi_downloads

# Download ESXi 8.0U3 (you need VMware account)
# Go to: https://customerconnect.vmware.com/downloads/details?downloadGroup=ESXI80U3&productId=974
# Direct download (requires login):
wget --user-agent="Mozilla/5.0" \
  --header="Cookie: your_vmware_session_cookie" \
  "https://download.vmware.com/download/VMware-VMvisor-Installer-8.0.3-********.x86_64.iso" \
  -O "VMware-VMvisor-Installer-8.0.3-********.x86_64.iso"
```

**ESXi 9.0 (Latest)**
```bash
# Download ESXi 9.0
# Go to: https://customerconnect.vmware.com/downloads/details?downloadGroup=ESXI90U1&productId=1345
# Direct download (requires login):
wget --user-agent="Mozilla/5.0" \
  --header="Cookie: your_vmware_session_cookie" \
  "https://download.vmware.com/download/VMware-VMvisor-Installer-9.0.0-********.x86_64.iso" \
  -O "VMware-VMvisor-Installer-9.0.0-********.x86_64.iso"
```

### Step 2: Alternative Download Methods

**Using curl with VMware account:**
```bash
# Login to VMware and get session cookie, then:
curl -L -o "ESXi-8.0.3.iso" \
  -H "User-Agent: Mozilla/5.0" \
  -H "Cookie: your_session_cookie" \
  "https://download.vmware.com/download/VMware-VMvisor-Installer-8.0.3-********.x86_64.iso"

curl -L -o "ESXi-9.0.0.iso" \
  -H "User-Agent: Mozilla/5.0" \
  -H "Cookie: your_session_cookie" \
  "https://download.vmware.com/download/VMware-VMvisor-Installer-9.0.0-********.x86_64.iso"
```

**Manual Browser Download:**
1. Go to https://customerconnect.vmware.com/
2. Sign in with VMware account
3. Navigate to Downloads > vSphere
4. Download:
   - VMware vSphere Hypervisor (ESXi) 8.0 Update 3
   - VMware vSphere Hypervisor (ESXi) 9.0

### Step 3: Extract and Setup Files

**Create directories:**
```bash
sudo mkdir -p /var/www/html/New/os/esxi-8.0
sudo mkdir -p /var/www/html/New/os/esxi-9.0
```

**Extract ESXi 8.0:**
```bash
# Mount and extract
sudo mkdir -p /mnt/esxi8
sudo mount -o loop /tmp/esxi_downloads/VMware-VMvisor-Installer-8.*.iso /mnt/esxi8
sudo cp -r /mnt/esxi8/* /var/www/html/New/os/esxi-8.0/
sudo umount /mnt/esxi8
sudo rmdir /mnt/esxi8

# Set permissions
sudo chown -R www-data:www-data /var/www/html/New/os/esxi-8.0
sudo chmod -R 755 /var/www/html/New/os/esxi-8.0
```

**Extract ESXi 9.0:**
```bash
# Mount and extract
sudo mkdir -p /mnt/esxi9
sudo mount -o loop /tmp/esxi_downloads/VMware-VMvisor-Installer-9.*.iso /mnt/esxi9
sudo cp -r /mnt/esxi9/* /var/www/html/New/os/esxi-9.0/
sudo umount /mnt/esxi9
sudo rmdir /mnt/esxi9

# Set permissions
sudo chown -R www-data:www-data /var/www/html/New/os/esxi-9.0
sudo chmod -R 755 /var/www/html/New/os/esxi-9.0
```

### Step 4: Verify Installation

**Check critical files exist:**
```bash
# ESXi 8.0 verification
ls -la /var/www/html/New/os/esxi-8.0/mboot.c32
ls -la /var/www/html/New/os/esxi-8.0/boot.cfg
ls -la /var/www/html/New/os/esxi-8.0/efi/boot/bootx64.efi

# ESXi 9.0 verification
ls -la /var/www/html/New/os/esxi-9.0/mboot.c32
ls -la /var/www/html/New/os/esxi-9.0/boot.cfg
ls -la /var/www/html/New/os/esxi-9.0/efi/boot/bootx64.efi
```

**Test web accessibility:**
```bash
# Test if files are accessible via web server
curl -I http://localhost/New/os/esxi-8.0/mboot.c32
curl -I http://localhost/New/os/esxi-9.0/mboot.c32

# Or test with your domain
curl -I http://auto.x-zoneit.ro/os/esxi-8.0/mboot.c32
curl -I http://auto.x-zoneit.ro/os/esxi-9.0/mboot.c32
```

### Step 5: Quick Setup Script

**Make the script executable and run:**
```bash
chmod +x download_esxi_files.sh
sudo ./download_esxi_files.sh
```

### Step 6: Alternative - Using 7zip for extraction

**If you prefer 7zip:**
```bash
# Install 7zip
sudo apt-get install p7zip-full

# Extract ESXi 8.0
7z x /tmp/esxi_downloads/VMware-VMvisor-Installer-8.*.iso -o/var/www/html/New/os/esxi-8.0/

# Extract ESXi 9.0  
7z x /tmp/esxi_downloads/VMware-VMvisor-Installer-9.*.iso -o/var/www/html/New/os/esxi-9.0/

# Set permissions
sudo chown -R www-data:www-data /var/www/html/New/os/esxi-*
sudo chmod -R 755 /var/www/html/New/os/esxi-*
```

## File Size Information

- ESXi 8.0U3 ISO: ~370 MB
- ESXi 9.0 ISO: ~380 MB
- Extracted files: ~400-450 MB each

## Directory Structure After Setup

```
/var/www/html/New/os/
├── esxi-8.0/
│   ├── BOOT.CFG
│   ├── boot.cfg
│   ├── mboot.c32
│   ├── EFI/
│   │   └── BOOT/
│   │       ├── BOOTX64.EFI
│   │       └── BOOT.CFG
│   ├── ISOLINUX/
│   ├── UPGRADE/
│   └── [other files...]
└── esxi-9.0/
    ├── BOOT.CFG
    ├── boot.cfg
    ├── mboot.c32
    ├── EFI/
    │   └── BOOT/
    │       ├── BOOTX64.EFI
    │       └── BOOT.CFG
    ├── ISOLINUX/
    ├── UPGRADE/
    └── [other files...]
```

## One-liner Commands

**Complete setup (after downloading ISOs manually):**
```bash
# ESXi 8.0
sudo mkdir -p /mnt/tmp && sudo mount -o loop /path/to/esxi-8.iso /mnt/tmp && sudo cp -r /mnt/tmp/* /var/www/html/New/os/esxi-8.0/ && sudo umount /mnt/tmp && sudo chown -R www-data:www-data /var/www/html/New/os/esxi-8.0 && sudo chmod -R 755 /var/www/html/New/os/esxi-8.0

# ESXi 9.0
sudo mkdir -p /mnt/tmp && sudo mount -o loop /path/to/esxi-9.iso /mnt/tmp && sudo cp -r /mnt/tmp/* /var/www/html/New/os/esxi-9.0/ && sudo umount /mnt/tmp && sudo chown -R www-data:www-data /var/www/html/New/os/esxi-9.0 && sudo chmod -R 755 /var/www/html/New/os/esxi-9.0
```

## Testing the Setup

**Test PXE installation:**
```bash
# Check available templates
curl -X GET "https://your-domain.com/pxe_api_integration.php?f=reinstallable_os"

# Start ESXi installation
curl -X POST https://your-domain.com/pxe_api_integration.php?action=execute_server_reinstall \
  -H "Content-Type: application/json" \
  -d '{
    "server_id": 1000001,
    "server_type": "dedicated", 
    "os_template": "esxi-8.0",
    "ipmi_address": "*************",
    "ipmi_username": "root",
    "ipmi_password": "your_ipmi_password"
  }'
```

## Troubleshooting

**Permission issues:**
```bash
sudo chown -R www-data:www-data /var/www/html/New/os/
sudo chmod -R 755 /var/www/html/New/os/
```

**Web server not serving files:**
```bash
# Check Apache/Nginx configuration
sudo systemctl reload apache2
# or
sudo systemctl reload nginx

# Test direct file access
wget http://localhost/New/os/esxi-8.0/mboot.c32
```

**Missing files:**
```bash
# Re-extract if files are missing
sudo rm -rf /var/www/html/New/os/esxi-*
# Then repeat extraction steps
``` 