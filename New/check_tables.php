<?php
// Check if required tables exist
require_once 'auth_functions.php';

echo "=== CHECKING REQUIRED TABLES ===\n";

$tables_to_check = [
    'orders_items',
    'orders', 
    'dedicated_cpu',
    'dedicated_storages',
    'dedicated_bandwidth',
    'dedicated_cities',
    'cities',
    'users'
];

foreach ($tables_to_check as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✓ Table '$table' exists\n";
            
            // Check if table has data
            $countStmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $countStmt->fetchColumn();
            echo "  - Records: $count\n";
            
            // Show sample data for key tables
            if (in_array($table, ['dedicated_cpu', 'dedicated_cities']) && $count > 0) {
                $sampleStmt = $pdo->query("SELECT * FROM $table LIMIT 3");
                $samples = $sampleStmt->fetchAll(PDO::FETCH_ASSOC);
                foreach ($samples as $sample) {
                    if ($table == 'dedicated_cpu') {
                        echo "    Sample: ID {$sample['id']} - {$sample['cpu']}\n";
                    } elseif ($table == 'dedicated_cities') {
                        echo "    Sample: ID {$sample['id']} - {$sample['name']}\n";
                    }
                }
            }
        } else {
            echo "✗ Table '$table' does NOT exist\n";
        }
    } catch (Exception $e) {
        echo "✗ Error checking table '$table': " . $e->getMessage() . "\n";
    }
    echo "\n";
}

echo "=== END TABLE CHECK ===\n";
?>
