<?php
// Include database connection
require_once("mysql.php");

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Checking general_settings table...\n";

try {
    // Check if the table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'general_settings'");
    $tableExists = $stmt->rowCount() > 0;

    if ($tableExists) {
        echo "The general_settings table exists.\n";

        // Check if there are any records
        $countStmt = $pdo->query("SELECT COUNT(*) FROM general_settings");
        $count = $countStmt->fetchColumn();

        echo "The table contains $count records.\n";

        if ($count > 0) {
            // Show the records
            $settingsStmt = $pdo->query("SELECT * FROM general_settings");
            $settings = $settingsStmt->fetchAll(PDO::FETCH_ASSOC);

            echo "Settings:\n";
            foreach ($settings as $setting) {
                echo "- {$setting['setting_key']}: {$setting['setting_value']}\n";
            }
        } else {
            echo "The table is empty. Creating default settings...\n";

            // Insert default settings
            $settings = [
                [
                    'setting_key' => 'company_name',
                    'setting_value' => 'Your Company Name',
                    'description' => 'The name of your company'
                ],
                [
                    'setting_key' => 'company_website',
                    'setting_value' => 'https://example.com',
                    'description' => 'Your company website URL'
                ],
                [
                    'setting_key' => 'client_portal_url',
                    'setting_value' => 'https://example.com/client',
                    'description' => 'The URL to your client portal'
                ],
                [
                    'setting_key' => 'invoice_page_url',
                    'setting_value' => 'https://example.com/client/invoices',
                    'description' => 'The URL to the invoices page in your client portal'
                ],
                [
                    'setting_key' => 'api_domain',
                    'setting_value' => 'https://test.x-zoneit.ro',
                    'description' => 'The API domain URL'
                ]
            ];

            $insertStmt = $pdo->prepare("
                INSERT INTO general_settings (setting_key, setting_value, description)
                VALUES (:key, :value, :description)
            ");

            foreach ($settings as $setting) {
                $insertStmt->bindValue(':key', $setting['setting_key']);
                $insertStmt->bindValue(':value', $setting['setting_value']);
                $insertStmt->bindValue(':description', $setting['description']);
                $insertStmt->execute();
            }

            echo "Default settings created successfully.\n";
        }
    } else {
        echo "The general_settings table does not exist. Creating it...\n";

        // Create the table
        $pdo->exec("CREATE TABLE `general_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `setting_key` varchar(255) NOT NULL,
            `setting_value` text NOT NULL,
            `description` text,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

        echo "Table created successfully.\n";

        // Insert default settings
        $settings = [
            [
                'setting_key' => 'company_name',
                'setting_value' => 'Your Company Name',
                'description' => 'The name of your company'
            ],
            [
                'setting_key' => 'company_website',
                'setting_value' => 'https://example.com',
                'description' => 'Your company website URL'
            ],
            [
                'setting_key' => 'client_portal_url',
                'setting_value' => 'https://example.com/client',
                'description' => 'The URL to your client portal'
            ],
            [
                'setting_key' => 'invoice_page_url',
                'setting_value' => 'https://example.com/client/invoices',
                'description' => 'The URL to the invoices page in your client portal'
            ],
            [
                'setting_key' => 'api_domain',
                'setting_value' => 'https://test.x-zoneit.ro',
                'description' => 'The API domain URL'
            ]
        ];

        $insertStmt = $pdo->prepare("
            INSERT INTO general_settings (setting_key, setting_value, description)
            VALUES (:key, :value, :description)
        ");

        foreach ($settings as $setting) {
            $insertStmt->bindValue(':key', $setting['setting_key']);
            $insertStmt->bindValue(':value', $setting['setting_value']);
            $insertStmt->bindValue(':description', $setting['description']);
            $insertStmt->execute();
        }

        echo "Default settings created successfully.\n";
    }

    // Test the API endpoint
    echo "\nTesting the API endpoint...\n";

    // Include the API file
    require_once("api_admin_general_settings.php");

    echo "API endpoint included successfully.\n";

} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
