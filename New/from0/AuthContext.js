import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';
import { API_URL } from './config';

// Create the authentication context
const AuthContext = createContext(null);

// Base API URLs
const AUTH_API_URL = `${API_URL}/auth_functions.php`;
const PROFILE_API_URL = `${API_URL}/api_admin_profile.php`;

// Storage keys for multi-admin session management
const STORAGE_KEYS = {
  PRIMARY_TOKEN: 'admin_token',
  REMEMBERED_ADMINS: 'remembered_admins',
  CURRENT_ADMIN_ID: 'current_admin_id',
  ADMIN_SESSION_PREFIX: 'admin_session_'
};

// Utility functions for secure storage management
const StorageManager = {
  // Get remembered admins from localStorage
  getRememberedAdmins: () => {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.REMEMBERED_ADMINS);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error parsing remembered admins:', error);
      return [];
    }
  },

  // Save remembered admins to localStorage
  setRememberedAdmins: (admins) => {
    try {
      localStorage.setItem(STORAGE_KEYS.REMEMBERED_ADMINS, JSON.stringify(admins));
    } catch (error) {
      console.error('Error saving remembered admins:', error);
    }
  },

  // Get current admin ID
  getCurrentAdminId: () => {
    return localStorage.getItem(STORAGE_KEYS.CURRENT_ADMIN_ID);
  },

  // Set current admin ID
  setCurrentAdminId: (adminId) => {
    if (adminId) {
      localStorage.setItem(STORAGE_KEYS.CURRENT_ADMIN_ID, adminId);
    } else {
      localStorage.removeItem(STORAGE_KEYS.CURRENT_ADMIN_ID);
    }
  },

  // Get admin session token
  getAdminSession: (adminId) => {
    return localStorage.getItem(`${STORAGE_KEYS.ADMIN_SESSION_PREFIX}${adminId}`);
  },

  // Set admin session token
  setAdminSession: (adminId, token) => {
    if (token) {
      localStorage.setItem(`${STORAGE_KEYS.ADMIN_SESSION_PREFIX}${adminId}`, token);
    } else {
      localStorage.removeItem(`${STORAGE_KEYS.ADMIN_SESSION_PREFIX}${adminId}`);
    }
  },

  // Remove admin session
  removeAdminSession: (adminId) => {
    localStorage.removeItem(`${STORAGE_KEYS.ADMIN_SESSION_PREFIX}${adminId}`);
  },

  // Clear all admin sessions
  clearAllSessions: () => {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(STORAGE_KEYS.ADMIN_SESSION_PREFIX)) {
        localStorage.removeItem(key);
      }
    });
    localStorage.removeItem(STORAGE_KEYS.REMEMBERED_ADMINS);
    localStorage.removeItem(STORAGE_KEYS.CURRENT_ADMIN_ID);
    localStorage.removeItem(STORAGE_KEYS.PRIMARY_TOKEN);
  }
};

// Provider component that wraps the app and makes auth object available to any child component that calls useAuth()
export function AuthProvider({ children }) {
  const [token, setToken] = useState(localStorage.getItem('admin_token'));
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [adminProfile, setAdminProfile] = useState(null);

  // Multi-admin session state
  const [rememberedAdmins, setRememberedAdmins] = useState(StorageManager.getRememberedAdmins());
  const [currentAdminId, setCurrentAdminId] = useState(StorageManager.getCurrentAdminId());
  const [isAdminSwitching, setIsAdminSwitching] = useState(false);

  // Function to handle API requests
  const apiRequest = async (endpoint, data, baseUrl = AUTH_API_URL) => {
    const response = await fetch(`${baseUrl}?f=${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    // Check if the response is valid
    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }

    // Parse the JSON response
    return await response.json();
  };

  // Function to add or update remembered admin
  const addRememberedAdmin = useCallback((adminData) => {
    console.log('Adding remembered admin:', adminData);
    const currentRemembered = StorageManager.getRememberedAdmins();
    console.log('Current remembered admins:', currentRemembered);
    const existingIndex = currentRemembered.findIndex(admin => admin.id === adminData.id);

    const adminEntry = {
      id: adminData.id,
      email: adminData.email,
      first_name: adminData.first_name,
      last_name: adminData.last_name,
      function: adminData.function,
      department_name: adminData.department_name,
      last_switch: new Date().toISOString(),
      picture_url: adminData.picture_url
    };

    if (existingIndex >= 0) {
      currentRemembered[existingIndex] = adminEntry;
      console.log('Updated existing admin at index:', existingIndex);
    } else {
      currentRemembered.push(adminEntry);
      console.log('Added new admin, total count:', currentRemembered.length);
    }

    StorageManager.setRememberedAdmins(currentRemembered);
    setRememberedAdmins(currentRemembered);
    console.log('Updated remembered admins state:', currentRemembered);
  }, []);

  // Function to remove remembered admin
  const removeRememberedAdmin = useCallback((adminId) => {
    console.log('removeRememberedAdmin called for adminId:', adminId);
    console.log('Current remembered admins before removal:', StorageManager.getRememberedAdmins());

    const currentRemembered = StorageManager.getRememberedAdmins();
    const filteredAdmins = currentRemembered.filter(admin => admin.id !== adminId);

    console.log('Filtered admins after removal:', filteredAdmins);

    StorageManager.setRememberedAdmins(filteredAdmins);
    StorageManager.removeAdminSession(adminId);
    setRememberedAdmins(filteredAdmins);

    // If we're removing the currently active admin, switch back to primary
    if (currentAdminId === adminId) {
      console.log('Removing currently active admin, switching back to primary');
      setCurrentAdminId(null);
      StorageManager.setCurrentAdminId(null);
    }
  }, [currentAdminId]);

  // Function to get current effective token (primary or switched admin)
  const getCurrentToken = useCallback(() => {
    if (currentAdminId) {
      return StorageManager.getAdminSession(currentAdminId) || token;
    }
    return token;
  }, [token, currentAdminId]);

  // Function to verify if admin token is valid
  const verifyToken = useCallback(async () => {
    setIsLoading(true);

    const effectiveToken = getCurrentToken();
    if (!effectiveToken) {
      setIsAuthenticated(false);
      setIsLoading(false);
      return false;
    }

    try {
      const data = await apiRequest('admin_login_check', { token: effectiveToken });

      if (data.success && data.is_admin) {
        setIsAuthenticated(true);
        setError(null);
        // Fetch admin profile after successful authentication
        fetchAdminProfile();
        return true;
      } else {
        // Handle token expiry - clean up the specific session
        console.log('Token verification failed, currentAdminId:', currentAdminId);
        if (currentAdminId) {
          console.log('Removing session for switched admin, but keeping in remembered list');
          StorageManager.removeAdminSession(currentAdminId);
          // Don't remove from remembered admins - just clear the current session
          // removeRememberedAdmin(currentAdminId);
          setCurrentAdminId(null);
          StorageManager.setCurrentAdminId(null);
        } else {
          console.log('Primary admin token expired');
          localStorage.removeItem('admin_token');
          setToken(null);
        }
        setIsAuthenticated(false);
        setError('Session expired. Please log in again.');
        return false;
      }
    } catch (error) {
      setError(`Verification error: ${error.message}`);
      return isAuthenticated;
    } finally {
      setIsLoading(false);
    }
  }, [getCurrentToken, currentAdminId, removeRememberedAdmin, isAuthenticated]);

  // Function to authenticate admin switch (new admin login)
  const authenticateAdminSwitch = async (email, password) => {
    setIsAdminSwitching(true);
    setError(null);

    try {
      const data = await apiRequest('authenticate_admin_switch', {
        email: email,
        password: password,
        current_token: getCurrentToken()
      });

      if (data.success && data.token && data.admin) {
        console.log('Admin switch successful, data received:', data);

        // Store the new admin session
        StorageManager.setAdminSession(data.admin.id, data.token);
        console.log('Stored admin session for ID:', data.admin.id);

        // Add to remembered admins
        addRememberedAdmin(data.admin);

        // Switch to this admin
        setCurrentAdminId(data.admin.id);
        StorageManager.setCurrentAdminId(data.admin.id);
        console.log('Set current admin ID to:', data.admin.id);

        // Update admin profile
        setAdminProfile(data.admin);

        return { success: true, admin: data.admin };
      } else {
        const errorMessage = data.message || 'Invalid email or password';
        setError(errorMessage);
        return { success: false, message: errorMessage };
      }
    } catch (error) {
      setError(`Admin switch failed: ${error.message}`);
      return { success: false, message: `Server error: ${error.message}` };
    } finally {
      setIsAdminSwitching(false);
    }
  };

  // Function to switch to remembered admin
  const switchToRememberedAdmin = async (adminId) => {
    setIsAdminSwitching(true);
    setError(null);

    try {
      const adminToken = StorageManager.getAdminSession(adminId);
      if (!adminToken) {
        throw new Error('No session found for this admin');
      }

      const data = await apiRequest('switch_to_remembered_admin', {
        admin_id: adminId,
        admin_token: adminToken,
        current_token: getCurrentToken()
      });

      if (data.success && data.admin) {
        // Switch to this admin
        setCurrentAdminId(adminId);
        StorageManager.setCurrentAdminId(adminId);

        // Update admin profile
        setAdminProfile(data.admin);

        // Update last switch time
        const currentRemembered = StorageManager.getRememberedAdmins();
        const adminIndex = currentRemembered.findIndex(admin => admin.id === adminId);
        if (adminIndex >= 0) {
          currentRemembered[adminIndex].last_switch = new Date().toISOString();
          StorageManager.setRememberedAdmins(currentRemembered);
          setRememberedAdmins(currentRemembered);
        }

        return { success: true, admin: data.admin };
      } else {
        // Session might be expired, remove it
        removeRememberedAdmin(adminId);
        const errorMessage = data.message || 'Session expired for this admin';
        setError(errorMessage);
        return { success: false, message: errorMessage };
      }
    } catch (error) {
      setError(`Switch failed: ${error.message}`);
      return { success: false, message: `Server error: ${error.message}` };
    } finally {
      setIsAdminSwitching(false);
    }
  };

  // Function to return to primary admin
  const returnToPrimaryAdmin = async () => {
    setIsAdminSwitching(true);
    setError(null);

    try {
      console.log('Returning to primary admin, current remembered admins:', rememberedAdmins);

      // Verify primary token is still valid
      const data = await apiRequest('admin_login_check', { token });

      if (data.success && data.is_admin) {
        // Clear current admin ID but keep remembered admins
        setCurrentAdminId(null);
        StorageManager.setCurrentAdminId(null);
        console.log('Cleared current admin ID, remembered admins should persist');

        // Fetch primary admin profile
        await fetchAdminProfile();

        console.log('Returned to primary, remembered admins after:', StorageManager.getRememberedAdmins());

        return { success: true };
      } else {
        setError('Primary admin session expired. Please log in again.');
        return { success: false, message: 'Primary admin session expired' };
      }
    } catch (error) {
      setError(`Return to primary failed: ${error.message}`);
      return { success: false, message: `Server error: ${error.message}` };
    } finally {
      setIsAdminSwitching(false);
    }
  };

  // Function to handle admin login
  const login = async (email, password) => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await apiRequest('admin_login', {
        username: email,
        password: password
      });

      if (data.success && data.token) {
        updateToken(data.token);
        // Fetch admin profile after successful login
        fetchAdminProfile();
        return { success: true };
      } else {
        const errorMessage = data.message || 'Invalid email or password';
        setError(errorMessage);
        return { success: false, message: errorMessage };
      }
    } catch (error) {
      setError(`Login failed: ${error.message}`);
      return { success: false, message: `Server error: ${error.message}` };
    } finally {
      setIsLoading(false);
    }
  };

  // Function to set token in both state and localStorage
  const updateToken = (newToken) => {
    if (newToken) {
      localStorage.setItem('admin_token', newToken);
      setToken(newToken);
      setIsAuthenticated(true);
    } else {
      localStorage.removeItem('admin_token');
      setToken(null);
      setIsAuthenticated(false);
    }
  };

  // Function to fetch admin profile
  const fetchAdminProfile = async () => {
    const effectiveToken = getCurrentToken();
    if (!effectiveToken) return;

    try {
      const data = await apiRequest('get_current_admin', { token: effectiveToken }, PROFILE_API_URL);

      if (data.success && data.admin) {
        console.log('Fetched admin profile:', data.admin);
        setAdminProfile(data.admin);

        // If this is a switched admin, update remembered admin info
        // But don't do this when returning to primary admin
        if (currentAdminId && data.admin.id === currentAdminId) {
          console.log('Updating remembered admin info for switched admin');
          addRememberedAdmin(data.admin);
        } else if (!currentAdminId) {
          console.log('Fetched primary admin profile, not updating remembered admins');
        }
      }
    } catch (error) {
      console.error('Error fetching admin profile:', error);
      // Don't set error state here to avoid disrupting the main authentication flow
    }
  };

  // Function to logout (enhanced for multi-admin)
  const logout = () => {
    // Clear all admin sessions and data
    StorageManager.clearAllSessions();

    // Reset all state
    setToken(null);
    setIsAuthenticated(false);
    setAdminProfile(null);
    setError(null);
    setRememberedAdmins([]);
    setCurrentAdminId(null);
  };

  // Function to logout only current admin (keep other sessions)
  const logoutCurrentAdmin = () => {
    console.log('logoutCurrentAdmin called, currentAdminId:', currentAdminId);
    if (currentAdminId) {
      // Remove current switched admin session AND remove from remembered list
      console.log('Removing current switched admin from remembered list');
      removeRememberedAdmin(currentAdminId);
      // Return to primary admin
      returnToPrimaryAdmin();
    } else {
      // Full logout if on primary admin
      console.log('Full logout - on primary admin');
      logout();
    }
  };

  // Verify token on component mount and when token changes
  useEffect(() => {
    verifyToken();

    // Set up interval to periodically verify token (every 6 hours instead of 10 minutes)
    // This is more appropriate for a 30-day session
    const intervalId = setInterval(() => {
      const effectiveToken = getCurrentToken();
      if (effectiveToken) {
        verifyToken();
      }
    }, 6 * 60 * 60 * 1000); // 6 hours in milliseconds

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [token, currentAdminId, verifyToken, getCurrentToken]);

  // Update remembered admins when localStorage changes (for cross-tab sync)
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === STORAGE_KEYS.REMEMBERED_ADMINS) {
        setRememberedAdmins(StorageManager.getRememberedAdmins());
      } else if (e.key === STORAGE_KEYS.CURRENT_ADMIN_ID) {
        setCurrentAdminId(StorageManager.getCurrentAdminId());
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // The value that will be available to consumers of this context
  const value = {
    // Original authentication
    token,
    setToken: updateToken,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    verifyToken,
    adminProfile,
    fetchAdminProfile,

    // Multi-admin session management
    rememberedAdmins,
    currentAdminId,
    isAdminSwitching,
    authenticateAdminSwitch,
    switchToRememberedAdmin,
    returnToPrimaryAdmin,
    removeRememberedAdmin,
    logoutCurrentAdmin,
    getCurrentToken,

    // Utility functions
    addRememberedAdmin
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook for child components to get the auth object and re-render when it changes
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === null) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};