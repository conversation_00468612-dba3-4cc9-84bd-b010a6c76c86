import React, { useState, useEffect } from 'react';
import {
  Download,
  TrendingUp,
  BarChart2,
  <PERSON>Chart,
  Map,
  Users,
  DollarSign,
  ShoppingCart,
  RefreshCw,
  HelpCircle,
  ChevronDown,
  MessageSquare,
  Server
} from 'lucide-react';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  Pie<PERSON>hart as RechartsPieChart,
  Pie,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import { API_URL } from '../config';

// Format currency values
const formatCurrency = (value) => {
  return '€' + new Intl.NumberFormat('de-DE').format(value);
};

const ReportsPage = ({ navigateTo, sidebarCollapsed, toggleSidebar }) => {
  const [activeTimeFrame, setActiveTimeFrame] = useState('monthly');
  const [orderStatusPeriod, setOrderStatusPeriod] = useState('thisMonth');
  const [customerGrowthPeriod, setCustomerGrowthPeriod] = useState('thisYear');
  const [performancePeriod, setPerformancePeriod] = useState('thisYear');

  // State management
  const [ticketData, setTicketData] = useState([]);
  const [ticketPeriod, setTicketPeriod] = useState('thisYear');
  const [revenueData, setRevenueData] = useState({ daily: [], weekly: [], monthly: [], yearly: [] });
  const [chartData, setChartData] = useState([]);
  const [reportStats, setReportStats] = useState([]);
  const [orderStatusData, setOrderStatusData] = useState([]);
  const [customerGrowthData, setCustomerGrowthData] = useState([]);
  const [topLocationsData, setTopLocationsData] = useState([]);
  const [cpuModels, setCpuModels] = useState([]);
  const [performanceData, setPerformanceData] = useState([]);

  const [lastUpdated, setLastUpdated] = useState('Not yet loaded');
  const [loading, setLoading] = useState({
    stats: false,
    revenue: false,
    orderStatus: false,
    customerGrowth: false,
    topLocations: false,
    performance: false,
    tickets: false
  });
  const [error, setError] = useState({
    stats: null,
    revenue: null,
    orderStatus: null,
    customerGrowth: null,
    topLocations: null,
    performance: null,
    tickets: null
  });

  // Fetch report stats
  const fetchReportStats = async () => {
    setLoading(prev => ({ ...prev, stats: true }));
    setError(prev => ({ ...prev, stats: null }));

    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_reports.php?f=get_report_stats`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log('Report stats response:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing JSON:', parseError);
        throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);
      }

      if (Array.isArray(data)) {
        // Add icons to the stats data
        const statsWithIcons = data.map((stat) => {
          let icon;
          let iconClass;
          switch (stat.title) {
            case 'Total Revenue':
              icon = <DollarSign className="text-indigo-700" size={40} strokeWidth={2} />;
              iconClass = 'icon-dropshadow-info';
              break;
            case 'Orders':
              icon = <ShoppingCart className="text-emerald-500" size={40} strokeWidth={2} />;
              iconClass = 'icon-dropshadow-success';
              break;
            case 'Customers':
              icon = <Users className="text-amber-500" size={40} strokeWidth={2} />;
              iconClass = 'icon-dropshadow-warning';
              break;
            case 'Avg. Order Value':
              icon = <BarChart2 className="text-rose-500" size={40} strokeWidth={2} />;
              iconClass = 'icon-dropshadow-danger';
              break;
            default:
              icon = <HelpCircle className="text-gray-500" size={40} strokeWidth={2} />;
              iconClass = 'icon-dropshadow-secondary';
          }
          return { ...stat, icon, iconClass };
        });
        setReportStats(statsWithIcons);
      } else if (data && data.error) {
        console.error('Report stats error:', data.error);
        setReportStats([]);
        setError(prev => ({ ...prev, stats: 'No report stats available' }));
      } else {
        console.error('Report stats data is not an array:', data);
        setReportStats([]);
        setError(prev => ({ ...prev, stats: 'Invalid report stats format' }));
      }
    } catch (err) {
      console.error('Error fetching report stats:', err);
      setReportStats([]);
      setError(prev => ({ ...prev, stats: 'Failed to load report stats' }));
    } finally {
      setLoading(prev => ({ ...prev, stats: false }));
    }
  };

  // Fetch revenue data
  const fetchRevenueData = async (timeFrame) => {
    setLoading(prev => ({ ...prev, revenue: true }));
    setError(prev => ({ ...prev, revenue: null }));

    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_reports.php?f=get_revenue_data&timeFrame=${timeFrame}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log(`${timeFrame} revenue data response:`, responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing JSON:', parseError);
        throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);
      }

      if (Array.isArray(data)) {
        setRevenueData(prev => ({
          ...prev,
          [timeFrame]: data
        }));
        if (timeFrame === activeTimeFrame) {
          setChartData(data);
        }
      } else if (data && data.error) {
        console.error(`${timeFrame} revenue data error:`, data.error);
        setRevenueData(prev => ({
          ...prev,
          [timeFrame]: []
        }));
        if (timeFrame === activeTimeFrame) {
          setChartData([]);
        }
        setError(prev => ({ ...prev, revenue: `No revenue data available for ${timeFrame}` }));
      } else {
        console.error(`${timeFrame} revenue data is not an array:`, data);
        setRevenueData(prev => ({
          ...prev,
          [timeFrame]: []
        }));
        if (timeFrame === activeTimeFrame) {
          setChartData([]);
        }
        setError(prev => ({ ...prev, revenue: `Invalid ${timeFrame} revenue data format` }));
      }
    } catch (err) {
      console.error(`Error fetching ${timeFrame} revenue data:`, err);
      setRevenueData(prev => ({
        ...prev,
        [timeFrame]: []
      }));
      if (timeFrame === activeTimeFrame) {
        setChartData([]);
      }
      setError(prev => ({ ...prev, revenue: `Failed to load ${timeFrame} revenue data` }));
    } finally {
      setLoading(prev => ({ ...prev, revenue: false }));
    }
  };

  // Fetch order status data
  const fetchOrderStatusData = async (period) => {
    setLoading(prev => ({ ...prev, orderStatus: true }));
    setError(prev => ({ ...prev, orderStatus: null }));

    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_reports.php?f=get_order_status&period=${period}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log('Order status data response:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing JSON:', parseError);
        throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);
      }

      if (Array.isArray(data)) {
        // Add modern colors to the data
        const coloredData = data.map(item => {
          let color;
          switch (item.name) {
            case 'Completed':
              color = '#10b981'; // emerald-500
              break;
            case 'Processing':
              color = '#6366f1'; // indigo-500
              break;
            case 'Pending':
              color = '#f59e0b'; // amber-500
              break;
            case 'Cancelled':
              color = '#ef4444'; // red-500
              break;
            default:
              color = '#8b5cf6'; // violet-500
          }
          return { ...item, color };
        });
        setOrderStatusData(coloredData);
      } else if (data && data.error) {
        console.error('Order status data error:', data.error);
        setOrderStatusData([]);
        setError(prev => ({ ...prev, orderStatus: 'No order status data available' }));
      } else {
        console.error('Order status data is not an array:', data);
        setOrderStatusData([]);
        setError(prev => ({ ...prev, orderStatus: 'Invalid data format received' }));
      }
    } catch (err) {
      console.error('Error fetching order status data:', err);
      setOrderStatusData([]);
      setError(prev => ({ ...prev, orderStatus: 'Failed to load order status data' }));
    } finally {
      setLoading(prev => ({ ...prev, orderStatus: false }));
    }
  };

  // Fetch customer growth data
  const fetchCustomerGrowthData = async (period) => {
    setLoading(prev => ({ ...prev, customerGrowth: true }));
    setError(prev => ({ ...prev, customerGrowth: null }));

    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_reports.php?f=get_customer_growth&period=${period}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log('Customer growth data response:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing JSON:', parseError);
        throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);
      }

      if (Array.isArray(data)) {
        setCustomerGrowthData(data);
      } else if (data && data.error) {
        console.error('Customer growth data error:', data.error);
        setCustomerGrowthData([]);
        setError(prev => ({ ...prev, customerGrowth: 'No customer growth data available' }));
      } else {
        console.error('Customer growth data is not an array:', data);
        setCustomerGrowthData([]);
        setError(prev => ({ ...prev, customerGrowth: 'Invalid data format received' }));
      }
    } catch (err) {
      console.error('Error fetching customer growth data:', err);
      setCustomerGrowthData([]);
      setError(prev => ({ ...prev, customerGrowth: 'Failed to load customer growth data' }));
    } finally {
      setLoading(prev => ({ ...prev, customerGrowth: false }));
    }
  };

  // Fetch server inventory data by location and CPU model
  const fetchTopLocationsData = async () => {
    setLoading(prev => ({ ...prev, topLocations: true }));
    setError(prev => ({ ...prev, topLocations: null }));

    try {
      const token = localStorage.getItem('admin_token');

      // Fetch server inventory data from database
      const response = await fetch(`${API_URL}/api_admin_reports.php?f=get_server_inventory`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log('Server inventory data response:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing JSON:', parseError);
        throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);
      }

      if (data && data.data && Array.isArray(data.data) && data.cpuModels && Array.isArray(data.cpuModels)) {
        // Set the location data
        setTopLocationsData(data.data);

        // Store CPU models for the legend
        setCpuModels(data.cpuModels);
      } else if (data && data.error) {
        console.error('Server inventory data error:', data.error);
        setTopLocationsData([]);
        setError(prev => ({ ...prev, topLocations: 'No server inventory data available' }));
      } else {
        console.error('Invalid server inventory data format:', data);
        setTopLocationsData([]);
        setError(prev => ({ ...prev, topLocations: 'Invalid data format received' }));
      }
    } catch (err) {
      console.error('Error fetching server inventory data:', err);
      setTopLocationsData([]);
      setError(prev => ({ ...prev, topLocations: 'Failed to load server inventory data' }));
    } finally {
      setLoading(prev => ({ ...prev, topLocations: false }));
    }
  };

  // Fetch performance metrics data
  const fetchPerformanceData = async (period) => {
    setLoading(prev => ({ ...prev, performance: true }));
    setError(prev => ({ ...prev, performance: null }));

    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_reports.php?f=get_performance_metrics&period=${period}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log('Performance metrics data response:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing JSON:', parseError);
        throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);
      }

      if (Array.isArray(data)) {
        setPerformanceData(data);
      } else if (data && data.error) {
        console.error('Performance data error:', data.error);
        setPerformanceData([]);
        setError(prev => ({ ...prev, performance: 'No performance data available' }));
      } else {
        console.error('Performance data is not an array:', data);
        setPerformanceData([]);
        setError(prev => ({ ...prev, performance: 'Invalid data format received' }));
      }
    } catch (err) {
      console.error('Error fetching performance data:', err);
      setPerformanceData([]);
      setError(prev => ({ ...prev, performance: 'Failed to load performance data' }));
    } finally {
      setLoading(prev => ({ ...prev, performance: false }));
    }
  };

  // Fetch ticket data
  const fetchTicketData = async (period) => {
    setLoading(prev => ({ ...prev, tickets: true }));
    setError(prev => ({ ...prev, tickets: null }));

    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_reports.php?f=get_ticket_data&period=${period}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const responseText = await response.text();
      console.log('Ticket data response:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing JSON:', parseError);
        throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);
      }

      if (Array.isArray(data)) {
        setTicketData(data);
      } else if (data && data.error) {
        console.error('Ticket data error:', data.error);
        setTicketData([]);
        setError(prev => ({ ...prev, tickets: 'No ticket data available' }));
      } else {
        console.error('Ticket data is not an array:', data);
        setTicketData([]);
        setError(prev => ({ ...prev, tickets: 'Invalid data format received' }));
      }
    } catch (err) {
      console.error('Error fetching ticket data:', err);
      setTicketData([]);
      setError(prev => ({ ...prev, tickets: 'Failed to load ticket data' }));
    } finally {
      setLoading(prev => ({ ...prev, tickets: false }));
    }
  };

  // Check if admin token exists
  const [isAuthenticated, setIsAuthenticated] = useState(true);

  // Check for admin token on component mount
  useEffect(() => {
    const token = localStorage.getItem('admin_token');
    if (!token) {
      setIsAuthenticated(false);
      setError({
        stats: 'Authentication required',
        revenue: 'Authentication required',
        orderStatus: 'Authentication required',
        customerGrowth: 'Authentication required',
        topLocations: 'Authentication required',
        performance: 'Authentication required',
        tickets: 'Authentication required'
      });
    } else {
      setIsAuthenticated(true);
    }
  }, []);

  // Load all data on initial render
  useEffect(() => {
    if (isAuthenticated) {
      fetchReportStats();
      fetchRevenueData('daily');
      fetchRevenueData('weekly');
      fetchRevenueData('monthly');
      fetchRevenueData('yearly');
      fetchOrderStatusData(orderStatusPeriod);
      fetchCustomerGrowthData(customerGrowthPeriod);
      fetchTopLocationsData();
      fetchPerformanceData(performancePeriod);
      fetchTicketData(ticketPeriod);

      setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
    }
  }, [isAuthenticated]);

  // Update chart data when time frame changes
  useEffect(() => {
    if (revenueData[activeTimeFrame] && revenueData[activeTimeFrame].length > 0) {
      setChartData(revenueData[activeTimeFrame]);
    } else {
      fetchRevenueData(activeTimeFrame);
    }
  }, [activeTimeFrame]);

  // Update order status data when period changes
  useEffect(() => {
    fetchOrderStatusData(orderStatusPeriod);
  }, [orderStatusPeriod]);

  // Update customer growth data when period changes
  useEffect(() => {
    fetchCustomerGrowthData(customerGrowthPeriod);
  }, [customerGrowthPeriod]);

  // Update performance data when period changes
  useEffect(() => {
    fetchPerformanceData(performancePeriod);
  }, [performancePeriod]);

  // Update ticket data when period changes
  useEffect(() => {
    fetchTicketData(ticketPeriod);
  }, [ticketPeriod]);

  const handleRefreshData = () => {
    const token = localStorage.getItem('admin_token');
    if (!token) {
      setIsAuthenticated(false);
      setError({
        stats: 'Authentication required',
        revenue: 'Authentication required',
        orderStatus: 'Authentication required',
        customerGrowth: 'Authentication required',
        topLocations: 'Authentication required',
        performance: 'Authentication required',
        tickets: 'Authentication required'
      });
      return;
    }

    fetchReportStats();
    fetchRevenueData(activeTimeFrame);
    fetchOrderStatusData(orderStatusPeriod);
    fetchCustomerGrowthData(customerGrowthPeriod);
    fetchTopLocationsData();
    fetchPerformanceData(performancePeriod);
    fetchTicketData(ticketPeriod);

    setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
  };

  const handleTimeFrameChange = (timeFrame) => {
    setActiveTimeFrame(timeFrame);
  };

  const handleOrderStatusPeriodChange = (e) => {
    setOrderStatusPeriod(e.target.value);
  };

  const handleCustomerGrowthPeriodChange = (e) => {
    setCustomerGrowthPeriod(e.target.value);
  };

  const handlePerformancePeriodChange = (e) => {
    setPerformancePeriod(e.target.value);
  };

  const handleTicketPeriodChange = (e) => {
    setTicketPeriod(e.target.value);
  };

  // Enhanced tooltips with better styling
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800 mb-2">{`${label}`}</p>
          <p className="text-indigo-700 font-medium">{`Revenue: ${formatCurrency(payload[0].value)}`}</p>
        </div>
      );
    }
    return null;
  };

  const CustomTooltipPerformance = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border rounded-md shadow-lg">
          <p className="font-semibold text-gray-800 mb-2">{`${label}`}</p>
          {payload.map((entry, index) => (
            <p key={`item-${index}`} className="font-medium" style={{ color: entry.color }}>
              {`${entry.name}: ${entry.value.toFixed(2)}%`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  const CustomTooltipServers = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      // Calculate total servers across all CPU types - filter out null/undefined values
      let total = 0;
      const validEntries = [];

      payload.forEach(p => {
        if (p.value && p.value > 0) {
          total += parseInt(p.value) || 0;
          validEntries.push(p);
        }
      });

      return (
        <div className="bg-white p-3 border rounded-md shadow-lg max-w-xs z-50 relative">
          <p className="font-semibold text-gray-800 mb-2 text-sm">{`${label}`}</p>
          <div className="space-y-1">
            {validEntries.map((entry, index) => {
              // Extract CPU model name from the dataKey
              const cpuIdMatch = entry.dataKey.match(/cpu(\d+)Servers/);
              const cpuId = cpuIdMatch ? parseInt(cpuIdMatch[1]) : null;
              const cpuModel = cpuModels.find(cpu => cpu.id === cpuId);
              const cpuName = cpuModel ? cpuModel.cpu : entry.dataKey;

              return (
                <p key={index} className="font-medium text-xs" style={{ color: entry.color }}>
                  {`${cpuName}: ${entry.value} servers`}
                </p>
              );
            })}
          </div>
          {total > 0 && (
            <p className="text-gray-600 text-xs mt-2 pt-2 border-t border-gray-200 font-medium">
              {`Total Servers: ${total}`}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  const CustomTooltipCustomers = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      // Find the new customers and existing customers entries
      const newCustomers = payload.find(p => p.dataKey === 'newCustomers');
      const existingCustomers = payload.find(p => p.dataKey === 'existingCustomers');

      return (
        <div className="bg-white p-4 border rounded-md shadow-lg">
          <p className="font-semibold text-gray-800 mb-2">{`${label}`}</p>
          {newCustomers && (
            <p className="text-indigo-700 font-medium">{`New Customers: ${newCustomers.value}`}</p>
          )}
          {existingCustomers && (
            <p className="text-emerald-600 font-medium">{`Existing Customers: ${existingCustomers.value}`}</p>
          )}
          <p className="text-gray-600 text-sm mt-2 pt-2 border-t border-gray-200">
            {`Total: ${(newCustomers?.value || 0) + (existingCustomers?.value || 0)}`}
          </p>
        </div>
      );
    }
    return null;
  };

  const CustomTooltipTickets = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      // Find the ticket status entries
      const open = payload.find(p => p.dataKey === 'open');
      const inProgress = payload.find(p => p.dataKey === 'inProgress');
      const closed = payload.find(p => p.dataKey === 'closed');

      // Calculate total
      const total = (open?.value || 0) + (inProgress?.value || 0) + (closed?.value || 0);

      return (
        <div className="bg-white p-4 border rounded-md shadow-lg">
          <p className="font-semibold text-gray-800 mb-2">{`${label}`}</p>
          {open && (
            <p className="text-emerald-500 font-medium">{`Open: ${open.value}`}</p>
          )}
          {inProgress && (
            <p className="text-amber-500 font-medium">{`In Progress: ${inProgress.value}`}</p>
          )}
          {closed && (
            <p className="text-rose-500 font-medium">{`Closed: ${closed.value}`}</p>
          )}
          <p className="text-gray-600 text-sm mt-2 pt-2 border-t border-gray-200">
            {`Total Tickets: ${total}`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        activeMenu="Reports"
        navigateTo={navigateTo}
        toggleSidebar={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gray-100">
        {/* Top Menu */}
        <TopMenu toggleSidebar={toggleSidebar} />

        {/* Reports Content */}
        <div className="p-6 space-y-6 overflow-auto">
          {!isAuthenticated && (
            <div className="bg-amber-50 border-l-4 border-amber-400 p-4 mb-4 rounded-r-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-amber-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-amber-700">
                    Authentication required. Please <a href="/login" className="font-medium underline text-amber-700 hover:text-amber-600 transition-colors">log in</a> to view reports.
                  </p>
                </div>
              </div>
            </div>
          )}
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">Reports & Analytics</h1>
            <div className="flex items-center space-x-3">
              <div className="text-sm text-gray-500 flex items-center">
               
                Last updated: {lastUpdated}
              </div>
              <button
                onClick={handleRefreshData}
                className="p-2 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-white hover:shadow-sm transition-all duration-200"
                disabled={!isAuthenticated}
                aria-label="Refresh data"
              >
                <RefreshCw className="w-5 h-5" />
              </button>
              <button
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center shadow-sm hover:shadow"
                disabled={!isAuthenticated}
              >
                <Download className="w-4 h-4 mr-1.5" />
                Export Reports
              </button>
            </div>
          </div>

          {/* Report Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {error.stats ? (
              <div className="col-span-4 bg-rose-50 p-4 rounded-md border border-rose-200 text-rose-700">
                <div className="flex items-center">
                  <span className="mr-2">⚠️</span>
                  <span>{error.stats}</span>
                </div>
              </div>
            ) : loading.stats ? (
              <div className="col-span-4 flex justify-center items-center p-8">
                <RefreshCw className="w-8 h-8 animate-spin text-indigo-600" />
                <span className="ml-3 text-gray-600 font-medium">Loading stats...</span>
              </div>
            ) : (
              reportStats.map((stat, index) => (
                <div
                  key={index}
                  className="bg-white p-6 shadow-sm hover:shadow-md transition-shadow duration-300 rounded-md flex items-center justify-between border border-gray-100 overflow-hidden relative"
                >
                  <div className="z-10">
                    <div className="text-sm text-gray-600 font-medium">{stat.title}</div>
                    <div className="text-2xl font-bold mt-1 text-gray-800">{stat.value}</div>
                    <div className="text-xs text-gray-500 mt-1.5">
                      <span className={`font-medium ${stat.change.startsWith('+') ? 'text-emerald-500' : 'text-rose-500'}`}>
                        {stat.change}
                      </span> {stat.period}
                    </div>
                  </div>
                  <div className={`card-custom-icon p-3 rounded-full ${stat.iconClass}`}>
                    {stat.icon}
                  </div>
                  <div className="absolute right-0 bottom-0 w-24 h-24 bg-indigo-50 rounded-tl-full opacity-30"></div>
                </div>
              ))
            )}
          </div>

          {/* Revenue Chart */}
          <div className="bg-white shadow-sm hover:shadow-md transition-shadow duration-300 rounded-md border border-gray-100 overflow-hidden">
            <div className="p-5 border-b border-gray-100 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-indigo-600" />
                <h2 className="text-lg font-semibold text-gray-800">Revenue Overview</h2>
                <div className="text-xs text-gray-500 flex items-center">
                  <HelpCircle className="w-3 h-3 mr-1 text-gray-400" />
                  Revenue generated across all channels
                </div>
              </div>
              <div className="flex space-x-1 bg-gray-100 p-1 rounded-md">
                <button
                  className={`px-3 py-1.5 rounded-md text-sm transition-all duration-200 ${activeTimeFrame === 'daily' ? 'bg-white text-indigo-600 shadow-sm font-medium' : 'text-gray-600 hover:bg-gray-50'}`}
                  onClick={() => handleTimeFrameChange('daily')}
                >
                  Day
                </button>
                <button
                  className={`px-3 py-1.5 rounded-md text-sm transition-all duration-200 ${activeTimeFrame === 'weekly' ? 'bg-white text-indigo-600 shadow-sm font-medium' : 'text-gray-600 hover:bg-gray-50'}`}
                  onClick={() => handleTimeFrameChange('weekly')}
                >
                  Week
                </button>
                <button
                  className={`px-3 py-1.5 rounded-md text-sm transition-all duration-200 ${activeTimeFrame === 'monthly' ? 'bg-white text-indigo-600 shadow-sm font-medium' : 'text-gray-600 hover:bg-gray-50'}`}
                  onClick={() => handleTimeFrameChange('monthly')}
                >
                  Month
                </button>
                <button
                  className={`px-3 py-1.5 rounded-md text-sm transition-all duration-200 ${activeTimeFrame === 'yearly' ? 'bg-white text-indigo-600 shadow-sm font-medium' : 'text-gray-600 hover:bg-gray-50'}`}
                  onClick={() => handleTimeFrameChange('yearly')}
                >
                  Year
                </button>
              </div>
            </div>
            <div className="p-6 h-80">
              {error.revenue ? (
                <div className="h-full flex items-center justify-center bg-rose-50 rounded-md border border-rose-200 text-rose-700">
                  <div className="flex items-center">
                    <span className="mr-2">⚠️</span>
                    <span>{error.revenue}</span>
                  </div>
                </div>
              ) : loading.revenue ? (
                <div className="h-full flex justify-center items-center">
                  <RefreshCw className="w-8 h-8 animate-spin text-indigo-600" />
                  <span className="ml-3 text-gray-600 font-medium">Loading revenue data...</span>
                </div>
              ) : chartData && chartData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={chartData}
                    margin={{
                      top: 10,
                      right: 30,
                      left: 20,
                      bottom: 10,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f1f5f9" />
                    <XAxis dataKey="name" stroke="#94a3b8" />
                    <YAxis stroke="#94a3b8" tickFormatter={formatCurrency} />
                    <Tooltip content={<CustomTooltip />} />
                    <defs>
                      <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#818cf8" stopOpacity={0.9}/>
                        <stop offset="95%" stopColor="#818cf8" stopOpacity={0.05}/>
                      </linearGradient>
                    </defs>
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stroke="#4f46e5"
                      strokeWidth={2.5}
                      fill="url(#revenueGradient)"
                      animationDuration={1000}
                      dot={{ stroke: "#4f46e5", strokeWidth: 2, r: 4, fill: "white" }}
                      activeDot={{ stroke: "#4f46e5", strokeWidth: 2, r: 6, fill: "white" }}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              ) : (
                <div className="h-full flex items-center justify-center text-gray-500">
                  <span>No revenue data available for this time period</span>
                </div>
              )}
            </div>
          </div>

          {/* Charts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Order Status Distribution */}
            <div className="bg-white shadow-sm hover:shadow-md transition-shadow duration-300 rounded-md border border-gray-100 overflow-hidden">
              <div className="p-5 border-b border-gray-100 flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <PieChart className="w-5 h-5 text-indigo-600" />
                  <h2 className="text-lg font-semibold text-gray-800">Order Status Distribution</h2>
                </div>
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none bg-white"
                    value={orderStatusPeriod}
                    onChange={handleOrderStatusPeriodChange}
                    disabled={loading.orderStatus}
                  >
                    <option value="thisMonth">This Month</option>
                    <option value="lastMonth">Last Month</option>
                    <option value="thisQuarter">This Quarter</option>
                    <option value="lastQuarter">Last Quarter</option>
                    <option value="thisYear">This Year</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                  {loading.orderStatus && <div className="absolute right-8 top-1/2 -translate-y-1/2"><RefreshCw className="w-4 h-4 animate-spin text-indigo-600" /></div>}
                </div>
              </div>
              <div className="p-6 h-80">
                {error.orderStatus ? (
                  <div className="h-full flex items-center justify-center bg-rose-50 rounded-md border border-rose-200 text-rose-700">
                    <div className="flex items-center">
                      <span className="mr-2">⚠️</span>
                      <span>{error.orderStatus}</span>
                    </div>
                  </div>
                ) : loading.orderStatus ? (
                  <div className="h-full flex justify-center items-center">
                    <RefreshCw className="w-8 h-8 animate-spin text-indigo-600" />
                    <span className="ml-3 text-gray-600 font-medium">Loading order status data...</span>
                  </div>
                ) : orderStatusData && orderStatusData.length > 0 ? (
                  <div className="h-full flex flex-col md:flex-row items-center justify-between">
                    <div className="w-full md:w-1/2 h-full">
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsPieChart>
                          <defs>
                            {orderStatusData.map((entry, index) => (
                              <linearGradient key={`gradient-${index}`} id={`colorGradient-${index}`} x1="0" y1="0" x2="0" y2="1">
                                <stop offset="0%" stopColor={entry.color} stopOpacity={1} />
                                <stop offset="100%" stopColor={entry.color} stopOpacity={0.8} />
                              </linearGradient>
                            ))}
                          </defs>
                          <Pie
                            data={orderStatusData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={80}
                            innerRadius={40}
                            fill="#8884d8"
                            dataKey="value"
                            paddingAngle={3}
                            animationDuration={1000}
                            animationBegin={0}
                            label={({ percent }) => {
                              // Only show percentage if it's significant enough to display
                              return percent > 0.08 ? `${(percent * 100).toFixed(0)}%` : '';
                            }}
                          >
                            {orderStatusData.map((entry, index) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={`url(#colorGradient-${index})`}
                                stroke={entry.color}
                                strokeWidth={1}
                              />
                            ))}
                          </Pie>
                          <Tooltip />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="w-full md:w-1/2 flex flex-col space-y-4 mt-4 md:mt-0 md:pl-6">
                      {orderStatusData.map((item, index) => (
                        <div key={index} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md transition-colors">
                          <div className="flex items-center">
                            <div
                              className="w-3 h-3 rounded-full mr-2"
                              style={{ backgroundColor: item.color }}
                            ></div>
                            <span className="text-sm font-medium text-gray-700">{item.name}</span>
                          </div>
                          <div className="flex items-center">
                            <span className="font-medium mr-2 text-gray-800">{item.value}</span>
                            <span className="text-xs text-gray-500">
                              {orderStatusData.reduce((acc, curr) => acc + curr.value, 0) > 0
                                ? `(${((item.value / orderStatusData.reduce((acc, curr) => acc + curr.value, 0)) * 100).toFixed(1)}%)`
                                : '(0%)'
                              }
                            </span>
                          </div>
                        </div>
                      ))}
                      <div className="pt-4 mt-2 border-t border-gray-100">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-700">Total Orders</span>
                          <span className="font-bold text-gray-900">{orderStatusData.reduce((acc, curr) => acc + curr.value, 0)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="h-full flex items-center justify-center text-gray-500">
                    <span>No order status data available for this period</span>
                  </div>
                )}
              </div>
            </div>

            {/* Customer Growth */}
            <div className="bg-white shadow-sm hover:shadow-md transition-shadow duration-300 rounded-md border border-gray-100 overflow-hidden">
              <div className="p-5 border-b border-gray-100 flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-indigo-600" />
                  <h2 className="text-lg font-semibold text-gray-800">Customer Growth</h2>
                </div>
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none bg-white"
                    value={customerGrowthPeriod}
                    onChange={handleCustomerGrowthPeriodChange}
                    disabled={loading.customerGrowth}
                  >
                    <option value="thisYear">This Year</option>
                    <option value="lastYear">Last Year</option>
                    <option value="last3Years">Last 3 Years</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                  {loading.customerGrowth && <div className="absolute right-8 top-1/2 -translate-y-1/2"><RefreshCw className="w-4 h-4 animate-spin text-indigo-600" /></div>}
                </div>
              </div>
              <div className="p-6 h-80">
                {error.customerGrowth ? (
                  <div className="h-full flex items-center justify-center bg-rose-50 rounded-md border border-rose-200 text-rose-700">
                    <div className="flex items-center">
                      <span className="mr-2">⚠️</span>
                      <span>{error.customerGrowth}</span>
                    </div>
                  </div>
                ) : loading.customerGrowth ? (
                  <div className="h-full flex justify-center items-center">
                    <RefreshCw className="w-8 h-8 animate-spin text-indigo-600" />
                    <span className="ml-3 text-gray-600 font-medium">Loading customer growth data...</span>
                  </div>
                ) : customerGrowthData && customerGrowthData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={customerGrowthData}
                      margin={{
                        top: 10,
                        right: 30,
                        left: 20,
                        bottom: 10,
                      }}
                      barGap={8}
                      barCategoryGap={20}
                    >
                      <defs>
                        <linearGradient id="newCustomersGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="0%" stopColor="#818cf8" stopOpacity={1} />
                          <stop offset="100%" stopColor="#4f46e5" stopOpacity={0.9} />
                        </linearGradient>
                        <linearGradient id="existingCustomersGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="0%" stopColor="#34d399" stopOpacity={1} />
                          <stop offset="100%" stopColor="#10b981" stopOpacity={0.9} />
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f1f5f9" />
                      <XAxis dataKey="name" stroke="#94a3b8" />
                      <YAxis
                        stroke="#94a3b8"
                        domain={[0, 'auto']}
                        allowDecimals={false}
                        minTickGap={10}
                      />
                      <Tooltip content={<CustomTooltipCustomers />} />
                      <Legend verticalAlign="top" height={36} />
                      <Bar
                        dataKey="newCustomers"
                        name="New Customers"
                        barSize={15}
                        radius={[4, 4, 0, 0]}
                        fill="url(#newCustomersGradient)"
                        animationDuration={1000}
                      />
                      <Bar
                        dataKey="existingCustomers"
                        name="Existing Customers"
                        barSize={15}
                        radius={[4, 4, 0, 0]}
                        fill="url(#existingCustomersGradient)"
                        animationDuration={1500}
                        animationBegin={300}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-full flex items-center justify-center text-gray-500">
                    <span>No customer growth data available for this period</span>
                  </div>
                )}
              </div>
            </div>

            {/* Server Inventory by CPU Model and Location */}
            <div className="bg-white shadow-sm hover:shadow-md transition-shadow duration-300 rounded-md border border-gray-100 overflow-visible">
              <div className="p-5 border-b border-gray-100 flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <Server className="w-5 h-5 text-indigo-600" />
                  <h2 className="text-lg font-semibold text-gray-800">Server Inventory by Location</h2>
                </div>
                <div className="relative">
                  {loading.topLocations && <div className="absolute right-0 top-1/2 -translate-y-1/2"><RefreshCw className="w-4 h-4 animate-spin text-indigo-600" /></div>}
                </div>
              </div>
              <div className="p-6 h-96 relative overflow-visible">
                {error.topLocations ? (
                  <div className="h-full flex items-center justify-center bg-rose-50 rounded-md border border-rose-200 text-rose-700">
                    <div className="flex items-center">
                      <span className="mr-2">⚠️</span>
                      <span>{error.topLocations}</span>
                    </div>
                  </div>
                ) : loading.topLocations ? (
                  <div className="h-full flex justify-center items-center">
                    <RefreshCw className="w-8 h-8 animate-spin text-indigo-600" />
                    <span className="ml-3 text-gray-600 font-medium">Loading location data...</span>
                  </div>
                ) : topLocationsData && topLocationsData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={topLocationsData}
                      layout="vertical"
                      margin={{
                        top: 50,
                        right: 30,
                        left: 80,
                        bottom: 20,
                      }}
                      barGap={5}
                      barCategoryGap={15}
                    >
                      <defs>
                        {cpuModels.map((cpu, index) => {
                          const colors = [
                            ['#6366F1', '#4338ca'], // indigo
                            ['#10b981', '#047857'], // emerald
                            ['#f59e0b', '#d97706'], // amber
                            ['#ef4444', '#b91c1c'], // red
                            ['#8b5cf6', '#6d28d9'], // violet
                            ['#3b82f6', '#1d4ed8']  // blue
                          ];
                          const colorPair = colors[index % colors.length];
                          return (
                            <linearGradient key={`cpuGradient-${cpu.id}`} id={`cpuGradient-${cpu.id}`} x1="0" y1="0" x2="1" y2="0">
                              <stop offset="0%" stopColor={colorPair[0]} stopOpacity={0.9} />
                              <stop offset="100%" stopColor={colorPair[1]} stopOpacity={0.9} />
                            </linearGradient>
                          );
                        })}
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} stroke="#f1f5f9" />
                      <XAxis
                        type="number"
                        stroke="#94a3b8"
                        axisLine={false}
                        tickLine={false}
                      />
                      <YAxis
                        dataKey="name"
                        type="category"
                        stroke="#94a3b8"
                        axisLine={false}
                        tickLine={false}
                        width={75}
                      />
                      <Tooltip
                        content={<CustomTooltipServers />}
                        wrapperStyle={{ outline: 'none' }}
                        allowEscapeViewBox={{ x: false, y: false }}
                        position={{ x: undefined, y: undefined }}
                      />
                      <Legend
                        verticalAlign="top"
                        height={45}
                        wrapperStyle={{
                          paddingTop: '0px',
                          paddingBottom: '5px',
                          fontSize: '11px',
                          lineHeight: '14px',
                          marginTop: '-45px',
                          transform: 'translateY(-5px)'
                        }}
                        iconType="rect"
                        layout="horizontal"
                      />
                      {cpuModels.map((cpu, index) => {
                        const columnName = `cpu${cpu.id}Servers`;
                        return (
                          <Bar
                            key={cpu.id}
                            dataKey={columnName}
                            name={cpu.cpu}
                            fill={`url(#cpuGradient-${cpu.id})`}
                            radius={[0, 4, 4, 0]}
                            animationDuration={1000 + (index * 200)}
                            animationBegin={index * 150}
                            barSize={20}
                          />
                        );
                      })}
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-full flex items-center justify-center text-gray-500">
                    <span>No server inventory data available</span>
                  </div>
                )}
              </div>
            </div>

            {/* Ticket Status */}
            <div className="bg-white shadow-sm hover:shadow-md transition-shadow duration-300 rounded-md border border-gray-100 overflow-hidden">
              <div className="p-5 border-b border-gray-100 flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <MessageSquare className="w-5 h-5 text-indigo-600" />
                  <h2 className="text-lg font-semibold text-gray-800">Ticket Status</h2>
                </div>
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none bg-white"
                    value={ticketPeriod}
                    onChange={handleTicketPeriodChange}
                    disabled={loading.tickets}
                  >
                    <option value="thisMonth">This Month</option>
                    <option value="lastMonth">Last Month</option>
                    <option value="thisYear">This Year</option>
                    <option value="lastYear">Last Year</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                  {loading.tickets && <div className="absolute right-8 top-1/2 -translate-y-1/2"><RefreshCw className="w-4 h-4 animate-spin text-indigo-600" /></div>}
                </div>
              </div>
              <div className="p-6 h-80">
                {error.tickets ? (
                  <div className="h-full flex items-center justify-center bg-rose-50 rounded-md border border-rose-200 text-rose-700">
                    <div className="flex items-center">
                      <span className="mr-2">⚠️</span>
                      <span>{error.tickets}</span>
                    </div>
                  </div>
                ) : loading.tickets ? (
                  <div className="h-full flex justify-center items-center">
                    <RefreshCw className="w-8 h-8 animate-spin text-indigo-600" />
                    <span className="ml-3 text-gray-600 font-medium">Loading ticket data...</span>
                  </div>
                ) : ticketData && ticketData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={ticketData}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <defs>
                        <linearGradient id="openTicketsGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="0%" stopColor="#34d399" stopOpacity={1} />
                          <stop offset="100%" stopColor="#10b981" stopOpacity={0.9} />
                        </linearGradient>
                        <linearGradient id="inProgressTicketsGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="0%" stopColor="#fbbf24" stopOpacity={1} />
                          <stop offset="100%" stopColor="#f59e0b" stopOpacity={0.9} />
                        </linearGradient>
                        <linearGradient id="closedTicketsGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="0%" stopColor="#f87171" stopOpacity={1} />
                          <stop offset="100%" stopColor="#ef4444" stopOpacity={0.9} />
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f1f5f9" />
                      <XAxis
                        dataKey="name"
                        stroke="#94a3b8"
                        axisLine={false}
                        tickLine={false}
                      />
                      <YAxis
                        stroke="#94a3b8"
                        axisLine={false}
                        tickLine={false}
                      />
                      <Tooltip content={<CustomTooltipTickets />} />
                      <Legend />
                      <Bar
                        dataKey="open"
                        name="Open"
                        fill="url(#openTicketsGradient)"
                        radius={[4, 4, 0, 0]}
                        animationDuration={1000}
                      />
                      <Bar
                        dataKey="inProgress"
                        name="In Progress"
                        fill="url(#inProgressTicketsGradient)"
                        radius={[4, 4, 0, 0]}
                        animationDuration={1200}
                        animationBegin={200}
                      />
                      <Bar
                        dataKey="closed"
                        name="Closed"
                        fill="url(#closedTicketsGradient)"
                        radius={[4, 4, 0, 0]}
                        animationDuration={1400}
                        animationBegin={400}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-full flex items-center justify-center text-gray-500">
                    <span>No ticket data available</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportsPage;