import React, { useState, useEffect } from 'react';
import {
  Save,
  Mail,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Settings,
  Send,
  Lock,
  Key,
  FileSignature,
  FileText,
  Edit,
  Code,
  MessageSquare
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import axios from 'axios';
import { toast } from 'react-toastify';

const EmailSettingsPage = ({ sidebarCollapsed, toggleSidebar }) => {
  // Get the current origin for the redirect URI
  const redirectUri = window.location.origin + '/admin/email-settings/oauth-callback';

  // State for email settings
  const [emailSettings, setEmailSettings] = useState({
    enabled: false,
    provider: 'google',
    // Google OAuth settings
    clientId: '',
    clientSecret: '',
    redirectUri: redirectUri,
    // SMTP settings
    smtpHost: '',
    smtpPort: '587',
    smtpUsername: '',
    smtpPassword: '',
    smtpEncryption: 'tls',
    // Common settings
    fromEmail: '',
    fromName: 'Admin System',
    replyTo: '',
    testEmail: '',
    // Signature settings
    useSignature: false,
    signature: '<p>Best regards,<br/>The Team</p>'
  });

  // State for general settings
  const [generalSettings, setGeneralSettings] = useState([]);

  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [activeTab, setActiveTab] = useState('settings');

  // State for email templates
  const [emailTemplates, setEmailTemplates] = useState([]);
  const [currentTemplate, setCurrentTemplate] = useState({
    type: 'password_reset',
    subject: '',
    content: ''
  });
  const [templateEditMode, setTemplateEditMode] = useState(false);

  // Fetch email settings on component mount and when returning from OAuth
  useEffect(() => {
    fetchEmailSettings();
    fetchEmailTemplates();
    fetchGeneralSettings();

    // Check if we're returning from OAuth authorization
    const checkOAuthReturn = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const oauthReturn = urlParams.get('oauth_return');

      if (oauthReturn === 'success') {
        toast.success('Google authorization successful!');
        // Remove the query parameter to avoid showing the message again on refresh
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    };

    checkOAuthReturn();
  }, []);

  const fetchEmailSettings = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('admin_token');

      const response = await axios.post(`/api_admin_settings.php?f=get_email_settings`, {
        token: token
      });

      if (response.data.success) {
        setEmailSettings(response.data.settings);
        setIsAuthorized(response.data.settings.authorized || false);
      }
    } catch (error) {
      console.error('Error fetching email settings:', error);
      toast.error('Failed to load email settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setEmailSettings({
      ...emailSettings,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle general setting input changes
  const handleGeneralSettingChange = (id, value) => {
    setGeneralSettings(prevSettings =>
      prevSettings.map(setting =>
        setting.id === id ? { ...setting, setting_value: value } : setting
      )
    );
  };

  const saveSettings = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('admin_token');

      const response = await axios.post(`/api_admin_settings.php?f=save_email_settings`, {
        token: token,
        settings: emailSettings
      });

      if (response.data.success) {
        toast.success('Email settings saved successfully');
        setEditMode(false);
        fetchEmailSettings(); // Refresh settings
      } else {
        toast.error(response.data.message || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving email settings:', error);
      toast.error('Failed to save email settings');
    } finally {
      setIsLoading(false);
    }
  };

  const authorizeGoogle = async () => {
    if (!emailSettings.clientId) {
      toast.error('Client ID is required');
      return;
    }

    if (!emailSettings.clientSecret) {
      toast.error('Client Secret is required');
      return;
    }

    // First save the current settings to ensure client ID and secret are stored
    setIsLoading(true);
    try {
      const token = localStorage.getItem('admin_token');

      // Save settings before authorization
      const response = await axios.post(`/api_admin_settings.php?f=save_email_settings`, {
        token: token,
        settings: emailSettings
      });

      if (response.data.success) {
        // Google OAuth 2.0 authorization URL
        const authUrl = `https://accounts.google.com/o/oauth2/auth?client_id=${emailSettings.clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=https://www.googleapis.com/auth/gmail.send&access_type=offline&prompt=consent`;

        // Open the authorization URL in a new window
        window.open(authUrl, '_blank');

        toast.info('Please complete the authorization in the new window');
      } else {
        toast.error(response.data.message || 'Failed to save settings before authorization');
      }
    } catch (error) {
      console.error('Error saving settings before authorization:', error);
      toast.error('Failed to save settings before authorization');
    } finally {
      setIsLoading(false);
    }
  };

  const sendTestEmail = async () => {
    if (!emailSettings.testEmail) {
      toast.error('Test email address is required');
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailSettings.testEmail)) {
      toast.error('Please enter a valid email address');
      return;
    }

    setIsTesting(true);
    try {
      const token = localStorage.getItem('admin_token');

      const response = await axios.post(`/api_admin_settings.php?f=send_test_email`, {
        token: token,
        email: emailSettings.testEmail
      });

      if (response.data.success) {
        toast.success('Test email sent successfully! Please check your inbox.');
        // Refresh settings to get updated authorization status
        fetchEmailSettings();
      } else {
        toast.error(response.data.message || 'Failed to send test email');
      }
    } catch (error) {
      console.error('Error sending test email:', error);

      // Provide more detailed error message if available
      if (error.response && error.response.data && error.response.data.error) {
        toast.error(`Failed to send test email: ${error.response.data.error}`);
      } else {
        toast.error('Failed to send test email. Please check server logs for details.');
      }
    } finally {
      setIsTesting(false);
    }
  };

  // Fetch general settings from the server
  const fetchGeneralSettings = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await axios.post(`/api_admin_general_settings.php?f=get_general_settings`, {
        token: token
      });

      if (response.data.success) {
        setGeneralSettings(response.data.settings);
      }
    } catch (error) {
      console.error('Error fetching general settings:', error);
      toast.error('Failed to load general settings');
    }
  };

  // Save general settings
  const saveGeneralSettings = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('admin_token');

      const response = await axios.post(`/api_admin_general_settings.php?f=save_general_settings`, {
        token: token,
        settings: generalSettings
      });

      if (response.data.success) {
        toast.success('General settings saved successfully');
        setEditMode(false);
        fetchGeneralSettings(); // Refresh settings
      } else {
        toast.error(response.data.message || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving general settings:', error);
      toast.error('Failed to save general settings');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch email templates from the server
  const fetchEmailTemplates = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await axios.post(`/api_admin_settings.php?f=get_email_templates`, {
        token: token
      });

      if (response.data.success) {
        setEmailTemplates(response.data.templates);

        // If we have templates, set the current template to the first one
        if (response.data.templates.length > 0) {
          setCurrentTemplate(response.data.templates[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching email templates:', error);
      toast.error('Failed to load email templates');
    }
  };

  // Handle template selection
  const handleTemplateSelect = (type) => {
    // Find the template with the matching type
    const template = emailTemplates.find(t => t.type === type);

    if (template) {
      setCurrentTemplate(template);
    } else {
      // If template doesn't exist, create a new one with default values
      setCurrentTemplate({
        type: type,
        subject: '',
        content: ''
      });
    }
  };

  // Handle template input changes
  const handleTemplateChange = (e) => {
    const { name, value } = e.target;
    setCurrentTemplate({
      ...currentTemplate,
      [name]: value
    });
  };

  // Save the current template
  const saveTemplate = async () => {
    if (!currentTemplate.subject || !currentTemplate.content) {
      toast.error('Subject and content are required');
      return;
    }

    setIsLoading(true);
    try {
      const token = localStorage.getItem('admin_token');

      const response = await axios.post(`/api_admin_settings.php?f=save_email_template`, {
        token: token,
        template: currentTemplate
      });

      if (response.data.success) {
        toast.success('Email template saved successfully');
        setTemplateEditMode(false);
        fetchEmailTemplates(); // Refresh templates
      } else {
        toast.error(response.data.message || 'Failed to save template');
      }
    } catch (error) {
      console.error('Error saving email template:', error);
      toast.error('Failed to save email template');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        activeMenu="Settings"
        toggleSidebar={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gray-100">
        {/* Top Menu */}
        <TopMenu toggleSidebar={toggleSidebar} />

        {/* Email Settings Content */}
        <div className="p-4 space-y-4 overflow-auto">
          <div className="flex justify-between items-center mb-2">
            <h1 className="text-xl font-bold text-gray-800">Email Settings</h1>
            <div className="flex items-center space-x-2">
              <div className="text-xs text-gray-500">
                {isAuthorized ? (
                  <span className="flex items-center text-green-600">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Authorized
                  </span>
                ) : (
                  <span className="flex items-center text-amber-600">
                    <AlertTriangle className="w-3 h-3 mr-1" />
                    Not Authorized
                  </span>
                )}
              </div>
              <button
                onClick={() => {
                  if (activeTab === 'settings') fetchEmailSettings();
                  else if (activeTab === 'templates') fetchEmailTemplates();
                  else if (activeTab === 'general') fetchGeneralSettings();
                }}
                className="p-1 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
                disabled={isLoading}
              >
                <RefreshCw className={`w-3 h-3 ${isLoading ? 'animate-spin' : ''}`} />
              </button>
              {activeTab === 'settings' && !editMode ? (
                <button
                  onClick={() => setEditMode(true)}
                  className="bg-indigo-700 hover:bg-indigo-800 text-white px-3 py-1 rounded-md text-xs font-medium transition-colors duration-200 flex items-center"
                >
                  <Settings className="w-3 h-3 mr-1" />
                  Edit
                </button>
              ) : activeTab === 'settings' && editMode ? (
                <button
                  onClick={saveSettings}
                  className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-xs font-medium transition-colors duration-200 flex items-center"
                  disabled={isLoading}
                >
                  <Save className="w-3 h-3 mr-1" />
                  Save
                </button>
              ) : activeTab === 'templates' && !templateEditMode ? (
                <button
                  onClick={() => setTemplateEditMode(true)}
                  className="bg-indigo-700 hover:bg-indigo-800 text-white px-3 py-1 rounded-md text-xs font-medium transition-colors duration-200 flex items-center"
                >
                  <Edit className="w-3 h-3 mr-1" />
                  Edit
                </button>
              ) : activeTab === 'templates' && templateEditMode ? (
                <button
                  onClick={saveTemplate}
                  className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-xs font-medium transition-colors duration-200 flex items-center"
                  disabled={isLoading}
                >
                  <Save className="w-3 h-3 mr-1" />
                  Save
                </button>
              ) : activeTab === 'general' && !editMode ? (
                <button
                  onClick={() => setEditMode(true)}
                  className="bg-indigo-700 hover:bg-indigo-800 text-white px-3 py-1 rounded-md text-xs font-medium transition-colors duration-200 flex items-center"
                >
                  <Settings className="w-3 h-3 mr-1" />
                  Edit
                </button>
              ) : activeTab === 'general' && editMode ? (
                <button
                  onClick={saveGeneralSettings}
                  className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-xs font-medium transition-colors duration-200 flex items-center"
                  disabled={isLoading}
                >
                  <Save className="w-3 h-3 mr-1" />
                  Save
                </button>
              ) : null}
            </div>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-gray-200 mb-4">
            <button
              className={`py-2 px-4 text-sm font-medium ${
                activeTab === 'settings'
                  ? 'text-indigo-700 border-b-2 border-indigo-700'
                  : 'text-gray-500 hover:text-indigo-700'
              }`}
              onClick={() => setActiveTab('settings')}
            >
              <div className="flex items-center">
                <Settings className="w-3 h-3 mr-1" />
                Email Settings
              </div>
            </button>
            <button
              className={`py-2 px-4 text-sm font-medium ${
                activeTab === 'templates'
                  ? 'text-indigo-700 border-b-2 border-indigo-700'
                  : 'text-gray-500 hover:text-indigo-700'
              }`}
              onClick={() => setActiveTab('templates')}
            >
              <div className="flex items-center">
                <FileText className="w-3 h-3 mr-1" />
                Email Templates
              </div>
            </button>
            <button
              className={`py-2 px-4 text-sm font-medium ${
                activeTab === 'general'
                  ? 'text-indigo-700 border-b-2 border-indigo-700'
                  : 'text-gray-500 hover:text-indigo-700'
              }`}
              onClick={() => setActiveTab('general')}
            >
              <div className="flex items-center">
                <Settings className="w-3 h-3 mr-1" />
                General Settings
              </div>
            </button>
          </div>

          {activeTab === 'settings' && (
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="space-y-3">
                {/* Enable Email & Provider Selection in one row */}
                <div className="flex flex-wrap items-center justify-between gap-2">
                  <div className="flex items-center space-x-4">
                    <div>
                      <h3 className="text-sm font-medium">Enable Email</h3>
                      <label className="relative inline-flex items-center cursor-pointer mt-1">
                        <input
                          type="checkbox"
                          name="enabled"
                          className="sr-only peer"
                          checked={emailSettings.enabled}
                          onChange={handleInputChange}
                          disabled={!editMode}
                        />
                        <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-indigo-600"></div>
                      </label>
                    </div>
                  </div>
                  <div className="w-full sm:w-auto">
                    <label className="block text-xs font-medium text-gray-700 mb-1">Provider</label>
                    <select
                      name="provider"
                      className="w-full sm:w-40 p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                      value={emailSettings.provider}
                      onChange={handleInputChange}
                      disabled={!editMode}
                    >
                      <option value="google">Google (Gmail)</option>
                      <option value="smtp">SMTP Server</option>
                    </select>
                  </div>
                </div>

                {/* Provider-specific Settings */}
                {emailSettings.provider === 'google' && (
                  <div className="space-y-3 p-3 bg-gray-50 rounded-md">
                    <h3 className="text-sm font-medium flex items-center">
                      <Mail className="w-3 h-3 mr-1 text-indigo-700" />
                      Google OAuth 2.0
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">Client ID</label>
                        <input
                          type="text"
                          name="clientId"
                          className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                          value={emailSettings.clientId}
                          onChange={handleInputChange}
                          disabled={!editMode}
                          placeholder="Your Google OAuth Client ID"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">Client Secret</label>
                        <input
                          type="password"
                          name="clientSecret"
                          className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                          value={emailSettings.clientSecret}
                          onChange={handleInputChange}
                          disabled={!editMode}
                          placeholder="Your Google OAuth Client Secret"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Redirect URI</label>
                      <div className="flex items-center">
                        <input
                          type="text"
                          name="redirectUri"
                          className="w-full p-1 text-xs border border-gray-300 rounded-md focus:outline-none bg-gray-100"
                          value={redirectUri}
                          disabled={true}
                          readOnly
                        />
                        <button
                          onClick={authorizeGoogle}
                          className="ml-2 bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded-md text-xs font-medium transition-colors duration-200 flex items-center whitespace-nowrap"
                          disabled={isLoading || !emailSettings.clientId || !emailSettings.clientSecret}
                        >
                          <Key className="w-3 h-3 mr-1" />
                          {isAuthorized ? 'Reauthorize' : 'Authorize'}
                        </button>
                      </div>
                    </div>

                    <div className="border-t pt-2 mt-2">
                      <h4 className="text-xs font-medium text-red-600 mb-1">Gmail SMTP Credentials</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">Gmail Address</label>
                          <input
                            type="text"
                            name="smtpUsername"
                            className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                            value={emailSettings.smtpUsername}
                            onChange={handleInputChange}
                            disabled={!editMode}
                            placeholder="<EMAIL>"
                          />
                        </div>

                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">Password/App Password</label>
                          <input
                            type="password"
                            name="smtpPassword"
                            className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                            value={emailSettings.smtpPassword}
                            onChange={handleInputChange}
                            disabled={!editMode}
                            placeholder="Your Gmail password or app password"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {emailSettings.provider === 'smtp' && (
                  <div className="space-y-3 p-3 bg-gray-50 rounded-md">
                    <h3 className="text-sm font-medium flex items-center">
                      <Mail className="w-3 h-3 mr-1 text-indigo-700" />
                      SMTP Server
                    </h3>

                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">SMTP Host</label>
                        <input
                          type="text"
                          name="smtpHost"
                          className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                          value={emailSettings.smtpHost}
                          onChange={handleInputChange}
                          disabled={!editMode}
                          placeholder="smtp.example.com"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">SMTP Port</label>
                        <input
                          type="text"
                          name="smtpPort"
                          className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                          value={emailSettings.smtpPort}
                          onChange={handleInputChange}
                          disabled={!editMode}
                          placeholder="587"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">Username</label>
                        <input
                          type="text"
                          name="smtpUsername"
                          className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                          value={emailSettings.smtpUsername}
                          onChange={handleInputChange}
                          disabled={!editMode}
                          placeholder="<EMAIL>"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">Password</label>
                        <input
                          type="password"
                          name="smtpPassword"
                          className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                          value={emailSettings.smtpPassword}
                          onChange={handleInputChange}
                          disabled={!editMode}
                          placeholder="Your SMTP password"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Encryption</label>
                      <select
                        name="smtpEncryption"
                        className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                        value={emailSettings.smtpEncryption}
                        onChange={handleInputChange}
                        disabled={!editMode}
                      >
                        <option value="tls">TLS</option>
                        <option value="ssl">SSL</option>
                        <option value="none">None</option>
                      </select>
                    </div>
                  </div>
                )}

                {/* Email Configuration */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Email Configuration</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">From Email</label>
                      <input
                        type="email"
                        name="fromEmail"
                        className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                        value={emailSettings.fromEmail}
                        onChange={handleInputChange}
                        disabled={!editMode}
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">From Name</label>
                      <input
                        type="text"
                        name="fromName"
                        className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                        value={emailSettings.fromName}
                        onChange={handleInputChange}
                        disabled={!editMode}
                        placeholder="Your Company Name"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">Reply-To Email</label>
                    <input
                      type="email"
                      name="replyTo"
                      className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                      value={emailSettings.replyTo}
                      onChange={handleInputChange}
                      disabled={!editMode}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                {/* Test Email */}
                <div className="space-y-2 p-3 bg-gray-50 rounded-md">
                  <h3 className="text-sm font-medium flex items-center">
                    <Send className="w-3 h-3 mr-1 text-indigo-700" />
                    Test Email
                  </h3>

                  <div className="flex items-end space-x-2">
                    <div className="flex-grow">
                      <label className="block text-xs font-medium text-gray-700 mb-1">Send Test To</label>
                      <input
                        type="email"
                        name="testEmail"
                        className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                        value={emailSettings.testEmail}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <button
                      onClick={sendTestEmail}
                      className="bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-1 rounded-md text-xs font-medium transition-colors duration-200 flex items-center h-8"
                      disabled={isTesting || !isAuthorized}
                    >
                      <Send className="w-3 h-3 mr-1" />
                      {isTesting ? 'Sending...' : 'Send Test'}
                    </button>
                  </div>

                  {!isAuthorized && (
                    <div className="text-amber-600 text-xs flex items-center mt-1">
                      <AlertTriangle className="w-3 h-3 mr-1" />
                      Authorization required
                    </div>
                  )}

                  {!emailSettings.enabled && (
                    <div className="text-amber-600 text-xs flex items-center mt-1">
                      <AlertTriangle className="w-3 h-3 mr-1" />
                      Email sending is disabled
                    </div>
                  )}
                </div>

                {/* Email Signature */}
                <div className="space-y-2 p-3 bg-gray-50 rounded-md mt-3">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium flex items-center">
                      <FileSignature className="w-3 h-3 mr-1 text-indigo-700" />
                      Email Signature
                    </h3>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        name="useSignature"
                        className="sr-only peer"
                        checked={emailSettings.useSignature}
                        onChange={handleInputChange}
                        disabled={!editMode}
                      />
                      <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-indigo-600"></div>
                    </label>
                  </div>

                  {emailSettings.useSignature && (
                    <div className="mt-2">
                      <label className="block text-xs font-medium text-gray-700 mb-1">Signature Content</label>
                      <textarea
                        name="signature"
                        className="w-full p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200 min-h-[100px]"
                        value={emailSettings.signature}
                        onChange={handleInputChange}
                        disabled={!editMode}
                        placeholder="<p>Your signature HTML here</p>"
                      />
                      <div className="mt-2 p-2 border rounded-md bg-white">
                        <p className="text-xs text-gray-500 mb-1">Signature Preview:</p>
                        <div
                          className="text-sm p-2 border-t"
                          dangerouslySetInnerHTML={{ __html: emailSettings.signature }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        You can use HTML tags for formatting. Example: &lt;p&gt;Best regards,&lt;br/&gt;The Team&lt;/p&gt;
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* General Settings Tab */}
          {activeTab === 'general' && (
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="space-y-3">
                <h3 className="text-sm font-medium">General Settings</h3>
                <p className="text-xs text-gray-500 mb-4">Configure system-wide settings like website domain and client portal URLs.</p>

                {isLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin h-8 w-8 border-4 border-indigo-500 rounded-full border-t-transparent"></div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {generalSettings.map(setting => (
                      <div key={setting.id} className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          {setting.description || setting.setting_key}
                        </label>
                        <input
                          type="text"
                          value={setting.setting_value}
                          onChange={(e) => handleGeneralSettingChange(setting.id, e.target.value)}
                          className="w-full p-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                          disabled={!editMode}
                        />
                      </div>
                    ))}

                    {!editMode ? (
                      <button
                        onClick={() => setEditMode(true)}
                        className="mt-4 bg-indigo-700 hover:bg-indigo-800 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
                      >
                        <Settings className="w-4 h-4 mr-2" />
                        Edit Settings
                      </button>
                    ) : (
                      <button
                        onClick={saveGeneralSettings}
                        className="mt-4 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
                        disabled={isLoading}
                      >
                        <Save className="w-4 h-4 mr-2" />
                        {isLoading ? 'Saving...' : 'Save Settings'}
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Email Templates Tab */}
          {activeTab === 'templates' && (
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="space-y-4">
                <div className="flex space-x-4">
                  {/* Template Selection */}
                  <div className="w-1/4 border-r pr-4">
                    <h3 className="text-sm font-medium mb-2">Email Templates</h3>
                    <div className="space-y-2">
                      <button
                        className={`w-full text-left p-2 text-xs rounded ${
                          currentTemplate.type === 'password_reset'
                            ? 'bg-indigo-100 text-indigo-700'
                            : 'hover:bg-gray-100'
                        }`}
                        onClick={() => handleTemplateSelect('password_reset')}
                      >
                        <div className="flex items-center">
                          <Key className="w-3 h-3 mr-1" />
                          Password Reset
                        </div>
                      </button>
                      <button
                        className={`w-full text-left p-2 text-xs rounded ${
                          currentTemplate.type === 'server_details'
                            ? 'bg-indigo-100 text-indigo-700'
                            : 'hover:bg-gray-100'
                        }`}
                        onClick={() => handleTemplateSelect('server_details')}
                      >
                        <div className="flex items-center">
                          <Code className="w-3 h-3 mr-1" />
                          Server Details
                        </div>
                      </button>
                      <button
                        className={`w-full text-left p-2 text-xs rounded ${
                          currentTemplate.type === 'invoice_generated'
                            ? 'bg-indigo-100 text-indigo-700'
                            : 'hover:bg-gray-100'
                        }`}
                        onClick={() => handleTemplateSelect('invoice_generated')}
                      >
                        <div className="flex items-center">
                          <FileText className="w-3 h-3 mr-1" />
                          Invoice Generated
                        </div>
                      </button>
                      <button
                        className={`w-full text-left p-2 text-xs rounded ${
                          currentTemplate.type === 'invoice_paid'
                            ? 'bg-indigo-100 text-indigo-700'
                            : 'hover:bg-gray-100'
                        }`}
                        onClick={() => handleTemplateSelect('invoice_paid')}
                      >
                        <div className="flex items-center">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Invoice Paid
                        </div>
                      </button>
                      <button
                        className={`w-full text-left p-2 text-xs rounded ${
                          currentTemplate.type === 'ticket_reply'
                            ? 'bg-indigo-100 text-indigo-700'
                            : 'hover:bg-gray-100'
                        }`}
                        onClick={() => handleTemplateSelect('ticket_reply')}
                      >
                        <div className="flex items-center">
                          <MessageSquare className="w-3 h-3 mr-1" />
                          Ticket Reply
                        </div>
                      </button>
                    </div>
                  </div>

                  {/* Template Editor */}
                  <div className="w-3/4">
                    <div className="space-y-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">Subject</label>
                        <input
                          type="text"
                          name="subject"
                          className="w-full p-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200"
                          value={currentTemplate.subject}
                          onChange={handleTemplateChange}
                          disabled={!templateEditMode}
                          placeholder="Email subject line"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">Content</label>
                        <textarea
                          name="content"
                          className="w-full p-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-200 min-h-[200px] font-mono"
                          value={currentTemplate.content}
                          onChange={handleTemplateChange}
                          disabled={!templateEditMode}
                          placeholder="<p>Email content in HTML format</p>"
                        />
                      </div>

                      {/* Preview */}
                      <div>
                        <h4 className="text-xs font-medium text-gray-700 mb-1">Preview</h4>
                        <div className="border rounded-md p-3 bg-gray-50">
                          <div className="text-xs font-medium mb-2">Subject: {currentTemplate.subject}</div>
                          <div
                            className="border-t pt-2 text-sm"
                            dangerouslySetInnerHTML={{ __html: currentTemplate.content }}
                          />
                        </div>
                      </div>

                      {/* Available Variables */}
                      <div className="mt-4">
                        <h4 className="text-xs font-medium text-gray-700 mb-1">Available Variables</h4>
                        <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded border">
                          {currentTemplate.type === 'password_reset' && (
                            <div className="space-y-1">
                              <div><code>{'{$client_name}'}</code> - The client's name</div>
                              <div><code>{'{$password}'}</code> - The temporary password</div>
                            </div>
                          )}

                          {currentTemplate.type === 'server_details' && (
                            <div className="space-y-1">
                              <div><code>{'{$client_name}'}</code> - The client's name</div>
                              <div><code>{'{$service_product_name}'}</code> - The service product name</div>
                              <div><code>{'{$service_domain}'}</code> - The server domain/hostname</div>
                              <div><code>{'{$service_dedicated_ip}'}</code> - The main IP address</div>
                              <div><code>{'{$service_password}'}</code> - The root password</div>
                              <div><code>{'{$service_assigned_ips}'}</code> - Additional IP addresses</div>
                            </div>
                          )}

                          {currentTemplate.type === 'invoice_generated' && (
                            <div className="space-y-1">
                              <div><code>{'{$client_name}'}</code> - The client's name</div>
                              <div><code>{'{$invoice_date_created}'}</code> - The date the invoice was created</div>
                              <div><code>{'{$invoice_payment_method}'}</code> - The payment method</div>
                              <div><code>{'{$invoice_num}'}</code> - The invoice number</div>
                              <div><code>{'{$invoice_total}'}</code> - The total amount due</div>
                              <div><code>{'{$invoice_date_due}'}</code> - The payment due date</div>
                              <div><code>{'{$invoice_html_contents}'}</code> - The detailed invoice items</div>
                              <div><code>{'{$invoice_link}'}</code> - Link to view the invoice</div>
                            </div>
                          )}

                          {currentTemplate.type === 'invoice_paid' && (
                            <div className="space-y-1">
                              <div><code>{'{$client_name}'}</code> - The client's name</div>
                              <div><code>{'{$invoice_num}'}</code> - The invoice number</div>
                              <div><code>{'{$invoice_total}'}</code> - The amount paid</div>
                              <div><code>{'{$payment_date}'}</code> - The date of payment</div>
                              <div><code>{'{$invoice_html_contents}'}</code> - The detailed invoice items</div>
                            </div>
                          )}

                          {currentTemplate.type === 'ticket_reply' && (
                            <div className="space-y-1">
                              <div><code>{'{$client_name}'}</code> - The client's name</div>
                              <div><code>{'{$ticket_id}'}</code> - The support ticket ID</div>
                              <div><code>{'{$ticket_subject}'}</code> - The ticket subject</div>
                              <div><code>{'{$ticket_status}'}</code> - The ticket status</div>
                              <div><code>{'{$ticket_message}'}</code> - The content of the reply</div>
                              <div><code>{'{$staff_name}'}</code> - The name of the staff member who replied</div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Instructions - Collapsible */}
          {activeTab === 'settings' && (
            <div className="bg-white rounded-lg shadow-sm p-3">
              <details>
                <summary className="cursor-pointer text-sm font-medium text-indigo-700 mb-2">
                  {emailSettings.provider === 'google' ? 'How to Set Up Google OAuth 2.0' : 'How to Set Up SMTP Email'}
                </summary>

                {emailSettings.provider === 'google' ? (
                  <>
                    <ol className="list-decimal pl-4 space-y-1 text-xs">
                      <li>Go to the <a href="https://console.cloud.google.com/" target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:underline">Google Cloud Console</a></li>
                      <li>Create a new project or select an existing one</li>
                      <li>Navigate to "APIs & Services" {'>>'} "Credentials"</li>
                      <li>Click "Create Credentials" and select "OAuth client ID"</li>
                      <li>Set the application type to "Web application"</li>
                      <li>
                        Add this Redirect URI:
                        <code className="bg-gray-100 p-1 rounded block mt-1 text-xs overflow-x-auto">{redirectUri}</code>
                      </li>
                      <li>Copy the Client ID and Client Secret to the form above</li>
                      <li className="text-red-600">Enter your Gmail address and app password in the SMTP credentials section</li>
                      <li>
                        For 2-Step Verification users, create an App Password:
                        <ul className="list-disc pl-4 mt-1 text-xs">
                          <li>Go to <a href="https://myaccount.google.com/security" target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:underline">Google Account Security</a></li>
                          <li>Under "Signing in to Google," select "App passwords"</li>
                          <li>Select "Mail" as the app and "Other" as the device</li>
                          <li>Use the generated password as your SMTP password</li>
                        </ul>
                      </li>
                      <li>Click "Save" and then "Authorize with Google"</li>
                    </ol>

                    <div className="mt-2 p-2 bg-blue-50 text-blue-800 rounded-md text-xs">
                      <div className="flex items-start">
                        <Lock className="w-3 h-3 mr-1 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="font-medium">Security Note</p>
                          <p className="mt-1">
                            Your OAuth credentials are stored securely and only used for sending emails.
                          </p>
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <ol className="list-decimal pl-4 space-y-1 text-xs">
                      <li>Obtain SMTP server details from your email provider</li>
                      <li>
                        Common SMTP settings:
                        <div className="mt-1 overflow-x-auto">
                          <table className="min-w-full text-xs">
                            <thead>
                              <tr className="bg-gray-100">
                                <th className="px-2 py-1 text-left">Provider</th>
                                <th className="px-2 py-1 text-left">SMTP Host</th>
                                <th className="px-2 py-1 text-left">Port</th>
                                <th className="px-2 py-1 text-left">Encryption</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td className="border px-2 py-1">Gmail</td>
                                <td className="border px-2 py-1">smtp.gmail.com</td>
                                <td className="border px-2 py-1">587</td>
                                <td className="border px-2 py-1">TLS</td>
                              </tr>
                              <tr>
                                <td className="border px-2 py-1">Outlook</td>
                                <td className="border px-2 py-1">smtp.office365.com</td>
                                <td className="border px-2 py-1">587</td>
                                <td className="border px-2 py-1">TLS</td>
                              </tr>
                              <tr>
                                <td className="border px-2 py-1">Yahoo</td>
                                <td className="border px-2 py-1">smtp.mail.yahoo.com</td>
                                <td className="border px-2 py-1">587</td>
                                <td className="border px-2 py-1">TLS</td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </li>
                      <li>Enter your SMTP server details in the form above</li>
                      <li>For Gmail, you may need to create an app password</li>
                      <li>Click "Save" to store your configuration</li>
                    </ol>

                    <div className="mt-2 p-2 bg-blue-50 text-blue-800 rounded-md text-xs">
                      <div className="flex items-start">
                        <Lock className="w-3 h-3 mr-1 flex-shrink-0 mt-0.5" />
                        <p className="font-medium">
                          Your SMTP credentials are stored securely.
                        </p>
                      </div>
                    </div>
                  </>
                )}
              </details>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmailSettingsPage;