import React, { useState, useEffect } from 'react';
import {
  Mail,
  RefreshCw,
  Filter,
  Search,
  X,
  Send,
  Users,
  UserCheck,
  UserX,
  User,
  Eye
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';

// Add some CSS for the email preview
const emailPreviewStyles = `
  .email-content {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
  }
  .email-content h1, .email-content h2, .email-content h3 {
    color: #444;
    margin-top: 20px;
    margin-bottom: 10px;
  }
  .email-content p {
    margin-bottom: 16px;
  }
  .email-content a {
    color: #3366cc;
    text-decoration: underline;
  }
  .email-content ul, .email-content ol {
    margin-bottom: 16px;
    padding-left: 20px;
  }
  .email-content img {
    max-width: 100%;
    height: auto;
  }
  .email-content table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 16px;
  }
  .email-content table, .email-content th, .email-content td {
    border: 1px solid #ddd;
  }
  .email-content th, .email-content td {
    padding: 8px;
    text-align: left;
  }
`;

const MassEmailPage = ({ navigateTo, sidebarCollapsed, toggleSidebar }) => {
  // State for email data
  const [massEmailData, setMassEmailData] = useState({
    subject: '',
    message: '',
    status: 'All',
    role: 'All'
  });

  // State for UI
  const [sendingMassEmail, setSendingMassEmail] = useState(false);
  const [massEmailResult, setMassEmailResult] = useState(null);
  const [uniqueStatuses, setUniqueStatuses] = useState(['All', 'Active', 'Inactive', 'Locked', 'Pending']);
  const [uniqueRoles, setUniqueRoles] = useState(['All', 'Customer', 'Vendor', 'Partner', 'Guest']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState('');
  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [previewContent, setPreviewContent] = useState({
    subject: '',
    message: ''
  });

  // Stats
  const [emailStats, setEmailStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    inactiveUsers: 0
  });

  // Email signature
  const [emailSignature, setEmailSignature] = useState('');

  // Load initial data
  useEffect(() => {
    loadData();
  }, []);

  // Load all required data
  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      await fetchAccountStats();
      await fetchAccountStatuses();
      await fetchEmailSignature();

      setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
      setLoading(false);
    } catch (err) {
      console.error("Error loading data:", err);
      setError("Failed to load account data. Please try again.");
      setLoading(false);
    }
  };

  // Fetch email signature
  const fetchEmailSignature = async () => {
    try {
      const response = await fetch(`/api_email.php?f=get_email_settings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.settings) {
        setEmailSignature(data.settings.signature || '');
      }
    } catch (err) {
      console.error("Error fetching email signature:", err);
      // Don't throw error, just log it - signature is optional
    }
  };

  // Fetch account statistics
  const fetchAccountStats = async () => {
    try {
      const response = await fetch(`/api_accounts.php?f=get_account_stats`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      setEmailStats({
        totalUsers: data.totalAccounts || 0,
        activeUsers: data.activeUsers || 0,
        inactiveUsers: data.inactiveUsers || 0
      });

    } catch (err) {
      console.error("Error fetching account stats:", err);
      throw err;
    }
  };

  // Fetch unique account statuses and roles
  const fetchAccountStatuses = async () => {
    try {
      const response = await fetch(`/api_accounts.php?f=get_accounts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const accounts = await response.json();

      if (Array.isArray(accounts)) {
        // Extract unique statuses and roles
        const statuses = new Set(['All', ...accounts.map(account =>
          account.status === 1 ? 'Active' :
          (account.status === 0 ? 'Inactive' :
          (account.status === 2 ? 'Locked' :
          (account.status === 3 ? 'Pending' : 'Unknown')))
        )]);

        const roles = new Set(['All', ...accounts.map(account => account.role || 'Customer')]);

        setUniqueStatuses(Array.from(statuses));
        setUniqueRoles(Array.from(roles));
      }
    } catch (err) {
      console.error("Error fetching account statuses:", err);
      // Use default values if fetch fails
      setUniqueStatuses(['All', 'Active', 'Inactive', 'Locked', 'Pending']);
      setUniqueRoles(['All', 'Customer', 'Vendor', 'Partner', 'Guest']);
    }
  };

  // Handle changes in mass email form
  const handleMassEmailChange = (e) => {
    const { name, value } = e.target;
    setMassEmailData({
      ...massEmailData,
      [name]: value
    });
  };

  // Send mass email
  const handleSendMassEmail = async () => {
    // Validate required fields
    if (!massEmailData.subject || !massEmailData.message) {
      alert('Please fill in both subject and message fields');
      return;
    }

    try {
      setSendingMassEmail(true);

      const response = await fetch(`/api_accounts.php?f=send_mass_email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          subject: massEmailData.subject,
          message: massEmailData.message,
          status: massEmailData.status,
          role: massEmailData.role
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setMassEmailResult(result.stats);
      } else {
        throw new Error(result.error || 'Failed to send mass email');
      }
    } catch (err) {
      console.error("Error sending mass email:", err);
      alert('Failed to send mass email: ' + err.message);
    } finally {
      setSendingMassEmail(false);
    }
  };

  // Reset the form
  const handleResetForm = () => {
    setMassEmailData({
      subject: '',
      message: '',
      status: 'All',
      role: 'All'
    });
    setMassEmailResult(null);
  };

  // Handle opening the preview modal
  const handleOpenPreview = () => {
    // Create a preview with sample data
    const sampleName = "John Doe";
    const sampleEmail = "<EMAIL>";

    // Replace variables in content
    let previewSubject = massEmailData.subject;
    let previewMessage = massEmailData.message;

    // Replace template variables with sample data
    previewMessage = previewMessage.replace(/\{\$client_name\}/g, sampleName);
    previewMessage = previewMessage.replace(/\{\$email\}/g, sampleEmail);

    // Add signature if available
    if (emailSignature) {
      previewMessage += '<br><br><hr style="border-top: 1px solid #ddd; margin: 20px 0;">';
      previewMessage += emailSignature;
    }

    setPreviewContent({
      subject: previewSubject,
      message: previewMessage
    });

    setPreviewModalOpen(true);
  };

  // Handle closing the preview modal
  const handleClosePreview = () => {
    setPreviewModalOpen(false);
  };

  // Generate stats cards
  const generateStatsCards = () => {
    const stats = [
      {
        title: 'Total Users',
        value: emailStats.totalUsers,
        icon: <Users className="text-indigo-700" size={40} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-info'
      },
      {
        title: 'Active Users',
        value: emailStats.activeUsers,
        icon: <UserCheck className="text-success" size={40} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-success'
      },
      {
        title: 'Inactive Users',
        value: emailStats.inactiveUsers,
        icon: <UserX className="text-warning" size={40} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-warning'
      }
    ];

    return stats.map((stat, index) => (
      <div
        key={index}
        className="bg-white p-6 shadow-sm flex items-center justify-between border rounded metric-card"
      >
        <div>
          <div className="text-sm text-gray-700">{stat.title}</div>
          <div className="text-2xl font-bold mt-1">{stat.value}</div>
        </div>
        <div className={`card-custom-icon ${stat.iconClass}`}>
          {stat.icon}
        </div>
      </div>
    ));
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        activeMenu="MassEmail"
        navigateTo={navigateTo}
        toggleSidebar={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gray-100">
        {/* Top Menu */}
        <TopMenu toggleSidebar={toggleSidebar} />

        {/* Mass Email Content */}
        <div className="p-6 space-y-6 overflow-auto">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">Mass Email</h1>
            <div className="flex items-center space-x-2">
              <div className="text-sm text-gray-500">Last updated: {lastUpdated}</div>
              <button
                onClick={loadData}
                className="p-2 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>

          {/* Error message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error}</span>
            </div>
          )}

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {generateStatsCards()}
          </div>

          {/* Email Form */}
          <div className="bg-white border border-gray-200 shadow-sm rounded-md">
            <div className="p-4 border-b">
              <h2 className="text-lg font-semibold">Send Mass Email</h2>
            </div>

            <div className="p-4">
              {massEmailResult ? (
                <div className="space-y-4">
                  <div className="bg-green-50 border border-green-200 rounded-md p-4">
                    <h3 className="text-lg font-medium text-green-800">Email Sending Results</h3>
                    <div className="mt-2 space-y-2">
                      <p className="text-sm text-gray-700">Total emails: <span className="font-medium">{massEmailResult.total}</span></p>
                      <p className="text-sm text-gray-700">Successfully sent: <span className="font-medium text-green-600">{massEmailResult.success}</span></p>
                      <p className="text-sm text-gray-700">Failed: <span className="font-medium text-red-600">{massEmailResult.failed}</span></p>

                      {massEmailResult.failed > 0 && (
                        <div>
                          <p className="text-sm font-medium text-gray-700 mt-2">Failed email addresses:</p>
                          <ul className="mt-1 text-xs text-red-600 list-disc list-inside">
                            {massEmailResult.failed_emails.map((email, index) => (
                              <li key={index}>{email}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                    <div className="mt-4">
                      <button
                        onClick={handleResetForm}
                        className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                      >
                        Send Another Email
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Filter Recipients</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">Account Status</label>
                        <select
                          name="status"
                          value={massEmailData.status}
                          onChange={handleMassEmailChange}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-200"
                        >
                          {uniqueStatuses.map(status => (
                            <option key={status} value={status}>{status === 'All' ? 'All Statuses' : status}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">User Role</label>
                        <select
                          name="role"
                          value={massEmailData.role}
                          onChange={handleMassEmailChange}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-200"
                        >
                          {uniqueRoles.map(role => (
                            <option key={role} value={role}>{role === 'All' ? 'All Roles' : role}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Email Subject</label>
                    <input
                      type="text"
                      name="subject"
                      value={massEmailData.subject}
                      onChange={handleMassEmailChange}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-200"
                      placeholder="Enter email subject"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Email Content</label>
                    <textarea
                      name="message"
                      value={massEmailData.message}
                      onChange={handleMassEmailChange}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-200 min-h-[300px]"
                      placeholder="Enter email content (HTML supported)"
                    ></textarea>
                    <p className="text-xs text-gray-500">
                      You can use the following variables in your email: {'{$client_name}'}, {'{$email}'}
                    </p>
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <button
                      onClick={handleResetForm}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                    >
                      Reset
                    </button>
                    <button
                      onClick={handleOpenPreview}
                      disabled={!massEmailData.subject || !massEmailData.message}
                      className="px-4 py-2 border border-indigo-300 bg-indigo-50 rounded-md text-sm font-medium text-indigo-700 hover:bg-indigo-100 flex items-center"
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      Preview
                    </button>
                    <button
                      onClick={handleSendMassEmail}
                      disabled={sendingMassEmail}
                      className={`px-4 py-2 rounded-md text-sm font-medium text-white flex items-center ${
                        sendingMassEmail ? 'bg-indigo-400' : 'bg-indigo-600 hover:bg-indigo-700'
                      }`}
                    >
                      {sendingMassEmail ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-1 animate-spin" />
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4 mr-1" />
                          Send Email
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Preview Modal */}
      {previewModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl mx-4 max-h-[90vh] flex flex-col">
            <style>{emailPreviewStyles}</style>
            <div className="px-4 py-3 border-b flex justify-between items-center">
              <h2 className="text-lg font-bold text-gray-800">Email Preview</h2>
              <button
                onClick={handleClosePreview}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="p-4 overflow-y-auto flex-grow">
              <div className="bg-gray-50 border border-gray-200 rounded-md p-6 mb-4">
                <div className="mb-4">
                  <span className="text-sm text-gray-500">From:</span>
                  <span className="ml-2 font-medium">Your Company Name</span>
                </div>
                <div className="mb-4">
                  <span className="text-sm text-gray-500">To:</span>
                  <span className="ml-2 font-medium"><EMAIL></span>
                </div>
                <div className="mb-4">
                  <span className="text-sm text-gray-500">Subject:</span>
                  <span className="ml-2 font-medium">{previewContent.subject || '(No subject)'}</span>
                </div>
                <hr className="my-4" />
                <div className="email-content bg-white p-4 border border-gray-100 rounded-md">
                  {previewContent.message ? (
                    <div dangerouslySetInnerHTML={{ __html: previewContent.message }} />
                  ) : (
                    <p className="text-gray-500 italic">(No content)</p>
                  )}
                </div>
              </div>

              <div className="text-sm text-gray-500 mb-4">
                <p>This is a preview of how your email will appear to recipients. The actual email may look slightly different depending on the recipient's email client.</p>
                <p className="mt-2">Sample variables have been replaced:</p>
                <ul className="list-disc list-inside mt-1">
                  <li>{'{$client_name}'} → John Doe</li>
                  <li>{'{$email}'} → <EMAIL></li>
                </ul>
                {emailSignature && (
                  <p className="mt-2">Your email signature has been automatically added to the bottom of the message.</p>
                )}
              </div>
            </div>

            <div className="px-4 py-3 border-t flex justify-end">
              <button
                onClick={handleClosePreview}
                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md mr-2"
              >
                Close Preview
              </button>
              <button
                onClick={() => {
                  handleClosePreview();
                  handleSendMassEmail();
                }}
                className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md flex items-center"
              >
                <Send className="w-4 h-4 mr-1" />
                Send Email
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MassEmailPage;
