import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';
import { API_URL } from '../config';

// Create the authentication context
const AuthContext = createContext(null);

// Base API URL
const AUTH_API_URL = `${API_URL}/auth_functions.php`;

// Provider component that wraps the app and makes auth object available to any child component that calls useAuth()
export function AuthProvider({ children }) {
  const [token, setToken] = useState(localStorage.getItem('admin_token'));
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Function to handle API requests
  const apiRequest = async (endpoint, data) => {
    const response = await fetch(`${AUTH_API_URL}?f=${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    // Check if the response is valid
    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }

    // Parse the JSON response
    return await response.json();
  };

  // Function to verify if admin token is valid
  const verifyToken = useCallback(async () => {
    setIsLoading(true);

    if (!token) {
      setIsAuthenticated(false);
      setIsLoading(false);
      return false;
    }

    try {
      const data = await apiRequest('admin_login_check', { token });

      if (data.success && data.is_admin) {
        setIsAuthenticated(true);
        setError(null);
        return true;
      } else {
        localStorage.removeItem('admin_token');
        setToken(null);
        setIsAuthenticated(false);
        setError('Session expired. Please log in again.');
        return false;
      }
    } catch (error) {
      setError(`Verification error: ${error.message}`);
      return isAuthenticated;
    } finally {
      setIsLoading(false);
    }
  }, [token, isAuthenticated]);

  // Function to handle admin login
  const login = async (email, password) => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await apiRequest('admin_login', {
        username: email,
        password: password
      });

      if (data.success && data.token) {
        updateToken(data.token);
        return { success: true };
      } else {
        const errorMessage = data.message || 'Invalid email or password';
        setError(errorMessage);
        return { success: false, message: errorMessage };
      }
    } catch (error) {
      setError(`Login failed: ${error.message}`);
      return { success: false, message: `Server error: ${error.message}` };
    } finally {
      setIsLoading(false);
    }
  };

  // Function to set token in both state and localStorage
  const updateToken = (newToken) => {
    if (newToken) {
      localStorage.setItem('admin_token', newToken);
      setToken(newToken);
      setIsAuthenticated(true);
    } else {
      localStorage.removeItem('admin_token');
      setToken(null);
      setIsAuthenticated(false);
    }
  };

  // Function to logout
  const logout = () => {
    localStorage.removeItem('admin_token');
    setToken(null);
    setIsAuthenticated(false);
    setError(null);
  };

  // Verify token on component mount and when token changes
  useEffect(() => {
    verifyToken();

    // Set up interval to periodically verify token (every 6 hours instead of 10 minutes)
    // This is more appropriate for a 30-day session
    const intervalId = setInterval(() => {
      if (token) {
        verifyToken();
      }
    }, 6 * 60 * 60 * 1000); // 6 hours in milliseconds

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [token, verifyToken]);

  // The value that will be available to consumers of this context
  const value = {
    token,
    setToken: updateToken,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    verifyToken
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook for child components to get the auth object and re-render when it changes
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === null) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};