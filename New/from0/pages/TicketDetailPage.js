import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  ArrowLeft,
  XCircle,
  CheckCircle,
  AlertTriangle,
  Clock,
  MessageCircle,
  Send,
  Edit,
  Tag,
  Calendar,
  User,
  FileText,
  Download,
  Users,
  Flag,
  Link2,
  Copy,
  Mail,
  Star,
  Printer,
  MoreVertical,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  Info,
  Paperclip,
  Plus,
  X,
  Reply,
  Save,
  Trash2,
  UserCheck
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import { useAuth } from '../AuthContext';
import avatar from '../assets/default.png';
import { API_URL } from '../config';

// Helper function to get profile picture path
const getProfilePicturePath = (picturePath) => {
  if (!picturePath || picturePath.trim() === '') {
    return avatar;
  }

  // If it's a full URL, use it as is
  if (picturePath.startsWith('http://') || picturePath.startsWith('https://')) {
    return picturePath;
  }

  // For all other cases, serve through PHP API
  const cleanPath = picturePath.startsWith('/') ? picturePath : '/' + picturePath;
  return `${API_URL}/api_admin_profile.php?f=get_profile_image&path=${encodeURIComponent(cleanPath)}`;
};

// Define API URL for tickets - use the same base URL as other API calls
const TICKETS_API_URL = `${API_URL}/api_tickets_admin.php`;

const TicketDetailPage = ({ navigateTo, sidebarCollapsed, toggleSidebar }) => {
  // Use React Router's useParams to get the ticket ID
  const { id } = useParams();

  // Get admin profile from AuthContext
  const { adminProfile, fetchAdminProfile, getCurrentToken } = useAuth();

  const [ticket, setTicket] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [newResponse, setNewResponse] = useState('');
  const [isInternalResponse, setIsInternalResponse] = useState(false);
  const [replyStatus, setReplyStatus] = useState('Answered');
  const [activeSection, setActiveSection] = useState('communication');
  const [lastUpdated, setLastUpdated] = useState('Never');
  const [detailsExpanded, setDetailsExpanded] = useState(false);
  const [ticketMessages, setTicketMessages] = useState([]);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);

  // State variables for edit functionality
  const [isEditingTicket, setIsEditingTicket] = useState(false);
  const [editedTicket, setEditedTicket] = useState(null);
  const [editingResponseId, setEditingResponseId] = useState(null);
  const [editedResponseContent, setEditedResponseContent] = useState('');
  const [isReassignModalOpen, setIsReassignModalOpen] = useState(false);
  const [adminList, setAdminList] = useState([]);
  const [selectedAdmin, setSelectedAdmin] = useState('');
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);

  // List of available departments - now initialized as empty array, will be fetched from API
  const [departments, setDepartments] = useState([]);



  // Fetch ticket data on component mount
  useEffect(() => {
    console.log("TicketDetailPage mounted with ID:", id, "type:", typeof id);

    // Fetch admin profile information
    fetchAdminProfile();

    // Create a direct ticket from the ID without relying on API's ticket list
    createDirectTicket(id);

    // Also fetch admin list for reassignment
    fetchAdminList();

    // Fetch departments from API
    fetchDepartments();

    // Try to fetch user information directly using the ticket ID
    // This assumes the user ID might be the same as the ticket ID in some cases
    if (id) {
      fetchUserInfo(id);
    }
  }, [id]);

  // Add click outside handler for status dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Check if the click was outside the dropdown
      if (showStatusDropdown && !event.target.closest('.status-dropdown-container')) {
        setShowStatusDropdown(false);
      }
    };

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Clean up
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showStatusDropdown]);

  // New function to fetch departments from API
  const fetchDepartments = async () => {
    try {
      const response = await fetch(`${TICKETS_API_URL}?f=get_departments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: getCurrentToken()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        console.log("Departments fetched successfully:", data);

        // Update departments state
        setDepartments(data);

        // If we have a ticket loaded and we're in edit mode, try to match the department
        if (ticket && editedTicket) {
          // Find matching department by name
          const matchingDept = data.find(dept =>
            dept.name === ticket.department ||
            dept.department_name === ticket.department
          );

          if (matchingDept) {
            console.log("Found matching department:", matchingDept);
            // Update the editedTicket with the department ID
            setEditedTicket(prev => ({
              ...prev,
              departmentId: matchingDept.id
            }));
          }
        }
      } else {
        // Fallback to default departments if API response is unexpected
        console.warn("Unexpected departments API response, using fallback data");
        setDepartments([
          { id: 1, name: 'Technical Support' },
          { id: 2, name: 'Account Management' },
          { id: 3, name: 'Customer Service' },
          { id: 4, name: 'Billing' },
          { id: 5, name: 'Sales' }
        ]);
      }
    } catch (err) {
      console.error("Error fetching departments:", err);
      // Fallback to default departments on error
      setDepartments([
        { id: 1, name: 'Technical Support' },
        { id: 2, name: 'Account Management' },
        { id: 3, name: 'Customer Service' },
        { id: 4, name: 'Billing' },
        { id: 5, name: 'Sales' }
      ]);
    }
  };

  // Fetch user information from the API
  const fetchUserInfo = async (userId) => {
    if (!userId) {
      console.log("No user ID provided to fetch user info");
      return;
    }

    try {
      console.log("Fetching user info for user ID:", userId);

      // Use the new API endpoint we just added
      const response = await fetch(`${TICKETS_API_URL}?f=get_user_details`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: getCurrentToken(),
          user_id: userId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      console.log("User info received:", data);

      if (data && (data.first_name || data.email || data.company_name)) {
        // Update ticket with user information
        let fullName;

        if (data.first_name && data.last_name) {
          // Use first and last name if both are available and not empty
          fullName = `${data.first_name} ${data.last_name}`;
        } else if (data.first_name) {
          // Use just first name if available
          fullName = data.first_name;
        } else if (data.company_name) {
          // Use company name as fallback if available
          fullName = `${data.company_name} (Company)`;
        } else if (data.email) {
          // Use email as fallback
          fullName = data.email;
        } else {
          // Last resort fallback
          fullName = `User ${userId}`;
        }

        console.log("Setting customer name to:", fullName);

        setTicket(prev => {
          const updatedTicket = {
            ...prev,
            customer: fullName,
            customerEmail: data.email || prev.customerEmail,
            customerId: userId
          };
          console.log("Updated ticket:", updatedTicket);
          return updatedTicket;
        });

        return true;
      } else if (data && data.error) {
        console.log("API returned error:", data.error);
        return false;
      }

      return false;
    } catch (err) {
      console.error("Error fetching user info:", err);
      return false;
    }
  };

  // Extract customer information from ticket messages
  const extractCustomerInfoFromMessages = (messages) => {
    try {
      console.log("Extracting customer info from messages", messages);

      // Log all message types to understand the structure
      console.log("Message types:", messages.map(msg => msg.type));
      console.log("First message:", messages[0]);

      // Try to find a customer message
      const customerMessage = messages.find(msg => msg.type === 'customer');

      if (customerMessage) {
        console.log("Found customer message:", customerMessage);

        // Update ticket with customer information from the message
        setTicket(prev => ({
          ...prev,
          customer: customerMessage.user_name || prev.customer,
          // Email might not be available in the message
          customerEmail: customerMessage.email || prev.customerEmail
        }));
      } else {
        console.log("No customer messages found, trying first message");

        // If no customer message is found, try using the first message
        if (messages.length > 0) {
          const firstMessage = messages[0];
          console.log("Using first message for customer info:", firstMessage);

          // Check if the message has user information
          if (firstMessage.user_name || firstMessage.user_id) {
            setTicket(prev => ({
              ...prev,
              customer: firstMessage.user_name || 'Customer ' + firstMessage.user_id,
              customerEmail: firstMessage.email || prev.customerEmail
            }));
          }
        }
      }
    } catch (err) {
      console.error("Error extracting customer info:", err);
      // We continue even if this fails
    }
  };

  // Create a direct ticket from ID without relying on ticket list
  const createDirectTicket = async (ticketId) => {
    setLoading(true);
    setError(null);

    console.log("Creating direct ticket for ID:", ticketId);

    try {
      // Create a basic ticket with just the ID
      const directTicket = {
        id: ticketId,
        subject: `Ticket #${ticketId}`, // Will be updated if we get more info
        status: 'Open',
        priority: 'Medium',
        createdDate: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        department: 'Technical Support',
        description: 'Loading ticket details...',
        customer: `Customer for Ticket #${ticketId}`, // More descriptive default value
        customerEmail: 'No email available', // Default value
        customerId: ticketId // Use ticket ID as default customer ID
      };

      // Format and use this basic ticket
      const formattedTicket = formatTicket(directTicket);
      setTicket(formattedTicket);
      setEditedTicket(formattedTicket);

      // Fetch messages for this ticket to see if it exists
      try {
        await fetchTicketMessages(ticketId);
        console.log("Successfully fetched messages for ticket:", ticketId);

        // Try to extract customer information from messages
        if (ticketMessages.length > 0) {
          extractCustomerInfoFromMessages(ticketMessages);
        }
      } catch (msgErr) {
        console.error("Error fetching messages:", msgErr);
        // We continue even if messages fail, as we can still show basic ticket info
      }

      // Try fetching all tickets to see if we can get more details
      try {
        const response = await fetch(`${TICKETS_API_URL}?f=get_admin_tickets`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: localStorage.getItem('admin_token')
          })
        });

        if (response.ok) {
          const tickets = await response.json();
          console.log("Got all tickets:", tickets);

          // Try different approaches to find this ticket
          const matchingTicket = findTicketByAnyId(tickets, ticketId);

          if (matchingTicket) {
            console.log("Found matching ticket in list:", matchingTicket);

            // Log customer-related fields to see what's available
            console.log("Customer fields in matching ticket:", {
              customer: matchingTicket.customer,
              customer_name: matchingTicket.customer_name,
              customerEmail: matchingTicket.customerEmail,
              customer_email: matchingTicket.customer_email,
              user_name: matchingTicket.user_name,
              user_id: matchingTicket.user_id,
              userId: matchingTicket.userId || matchingTicket.user_id
            });

            // Update our basic ticket with real details
            const enhancedTicket = {
              ...formattedTicket,
              ...formatTicket(matchingTicket)
            };

            setTicket(enhancedTicket);
            setEditedTicket(enhancedTicket);

            // Try to fetch user information if we have a user ID
            const userId = matchingTicket.userId || matchingTicket.user_id || ticketId;
            if (userId) {
              console.log("Attempting to fetch user info for ID:", userId);
              await fetchUserInfo(userId);
            }
          }
        }
      } catch (listErr) {
        console.error("Error fetching ticket list:", listErr);
        // We continue even if this fails
      }

      setLoading(false);
      setLastUpdated(formatDateTime(new Date()));

    } catch (err) {
      console.error("Error creating direct ticket:", err);
      setError("Failed to load ticket details. Please try again.");
      setLoading(false);
    }
  };

  // Helper function to find a ticket by ID using multiple matching methods
  const findTicketByAnyId = (tickets, searchId) => {
    if (!Array.isArray(tickets)) return null;

    return tickets.find(t => {
      // Try various matching strategies
      const tId = t.id || '';
      const numericTId = String(tId).replace(/\D/g, '');
      const numericSearchId = String(searchId).replace(/\D/g, '');

      return tId === searchId ||
             tId == searchId ||
             numericTId === numericSearchId ||
             String(tId).includes(searchId) ||
             searchId.includes(numericTId);
    });
  };

  // Format ticket from API to match the expected format
  const formatTicket = (ticket) => {
    // Extract user ID for later use
    const userId = ticket.userId || ticket.user_id || ticket.customer_id || ticket.customerId;

    // If we have a user ID, try to fetch user info
    if (userId) {
      console.log("formatTicket found user ID, will fetch user info:", userId);
      // We don't await this because we want formatTicket to return immediately
      // The user info will update the ticket state when it's available
      fetchUserInfo(userId);
    }

    return {
      id: ticket.id || '',
      subject: ticket.subject || '',
      customer: ticket.customer || ticket.customer_name || ticket.user_name || 'Unknown Customer',
      customerEmail: ticket.customerEmail || ticket.customer_email || ticket.email || 'No email available',
      customerId: userId || '',
      assignedTo: ticket.assignedTo || ticket.assigned_to || '',
      department: ticket.department || '',
      departmentId: ticket.department_id || '',  // Store department ID separately
      category: ticket.category || '',
      priority: ticket.priority || 'Medium',
      status: ticket.status || 'Open',
      createdDate: ticket.createdDate || ticket.created_at || new Date().toISOString(),
      lastUpdated: ticket.lastUpdated || ticket.last_updated || ticket.last_reply || new Date().toISOString(),
      dueDate: ticket.dueDate || calculateDueDate(ticket.createdDate || ticket.created_at),
      description: ticket.description || ticket.content || '',
      attachments: ticket.attachments || [],
      responses: [],
      tags: ticket.tags || [],
      timeline: []
    };
  };

  // Calculate a due date (3 business days from creation) if none is provided
  const calculateDueDate = (creationDate) => {
    const date = new Date(creationDate);
    const businessDaysToAdd = 3;

    let daysAdded = 0;
    while (daysAdded < businessDaysToAdd) {
      date.setDate(date.getDate() + 1);
      const dayOfWeek = date.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Skip weekends
        daysAdded++;
      }
    }

    return date.toISOString();
  };

  // Fetch messages for the ticket
  const fetchTicketMessages = async (ticketId) => {
    if (!ticketId) return;

    setLoadingMessages(true);
    try {
      console.log("Fetching messages for ticket ID:", ticketId);

      const response = await fetch(`${TICKETS_API_URL}?f=get_admin_ticket_messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: getCurrentToken(),
          ticket_id: ticketId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      // Log the raw response for debugging
      const responseText = await response.text();
      console.log("Raw response:", responseText);

      // Parse the response as JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("Error parsing JSON:", parseError);
        throw new Error(`Failed to parse response as JSON: ${responseText}`);
      }

      console.log("Ticket messages received:", data);

      // Check if the API response includes customer information directly
      if (data && data.customer) {
        console.log("API response includes customer info:", data.customer);
        setTicket(prev => ({
          ...prev,
          customer: data.customer.name || prev.customer,
          customerEmail: data.customer.email || prev.customerEmail,
          customerId: data.customer.id || prev.customerId
        }));
      } else {
        console.log("API response does not include direct customer info");

        // Check if the API response includes user_id
        if (data && data.user_id) {
          console.log("API response includes user_id:", data.user_id);
          // Try to fetch user information
          await fetchUserInfo(data.user_id);
        } else if (ticketId) {
          // As a fallback, try using the ticket ID as the user ID
          console.log("Trying to fetch user info using ticket ID as user ID");
          await fetchUserInfo(ticketId);
        }
      }

      // Process messages and update state
      setTicketMessages(data);

      // Extract timeline events from messages
      const timelineEvents = createTimelineFromMessages(data, ticketId);

      // Try to extract customer information from messages
      extractCustomerInfoFromMessages(data);

      // Update ticket with timeline data
      setTicket(prev => ({
        ...prev,
        timeline: timelineEvents
      }));

      setLoadingMessages(false);
      setLastUpdated(formatDateTime(new Date()));
    } catch (err) {
      console.error("Error fetching ticket messages:", err);
      setLoadingMessages(false);
      setTicketMessages([]);
      throw err; // Re-throw to allow caller to catch it
    }
  };

  // Create timeline events from messages
  const createTimelineFromMessages = (messages, ticketId) => {
    const timeline = [];

    // Add ticket creation event (using the earliest message time as a fallback)
    if (ticket && ticket.createdDate) {
      timeline.push({
        action: `Ticket #${ticketId} Created`,
        timestamp: ticket.createdDate,
        user: 'System'
      });
    }

    // Add message events
    messages.forEach(message => {
      const action = message.is_internal
        ? "Internal Note Added"
        : message.type === 'customer'
          ? "Customer Reply Received"
          : "Staff Response Sent";

      timeline.push({
        action,
        timestamp: message.created_at,
        user: message.user_name || 'Unknown User'
      });
    });

    return timeline;
  };

  // Fetch admin list for reassignment
  const fetchAdminList = async () => {
    try {
      const response = await fetch(`${TICKETS_API_URL}?f=get_admin_list`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: getCurrentToken()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setAdminList(data);
      } else {
        // Fallback to default data if API response is unexpected
        setAdminList([
          { id: 1, name: 'Alexandru Tolgyi' },
          { id: 2, name: 'Maria Schmidt' },
          { id: 3, name: 'John Doe' },
          { id: 4, name: 'Jane Smith' }
        ]);
      }
    } catch (err) {
      console.error("Error fetching admin list:", err);
      // Fallback to dummy data
      setAdminList([
        { id: 1, name: 'Alexandru Tolgyi' },
        { id: 2, name: 'Maria Schmidt' },
        { id: 3, name: 'John Doe' },
        { id: 4, name: 'Jane Smith' }
      ]);
    }
  };

  // Handle response text change
  const handleResponseChange = (e) => {
    setNewResponse(e.target.value);
  };

  // Toggle between customer and internal response
  const handleToggleInternalResponse = () => {
    setIsInternalResponse(!isInternalResponse);
  };

  // Generate admin signature
  const getAdminSignature = async () => {
    // Check if admin profile is loaded, if not fetch it
    if (!adminProfile) {
      console.log("Admin profile not loaded, fetching before generating signature...");
      await fetchAdminProfile();
    }

    const adminName = adminProfile ? `${adminProfile.first_name} ${adminProfile.last_name}`.trim() : 'Support Team';
    const adminFunction = adminProfile?.function || 'Support';

    return `

--
${adminName}
${adminFunction}`;
  };

  // Submit response to ticket
  const handleSubmitResponse = async () => {
    if (newResponse.trim() === '') return;

    try {
      // Ensure admin profile is loaded before sending message
      if (!adminProfile || !adminProfile.first_name) {
        console.log("Admin profile not loaded, fetching before sending message...");
        await fetchAdminProfile();
        // Wait a bit for the profile to be set in state (mirrors TicketsPage)
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Add signature to customer-visible messages (not to internal notes)
      const signature = isInternalResponse ? '' : await getAdminSignature();
      const messageWithSignature = newResponse + signature;

      // Check if we have files to upload
      if (selectedFiles.length > 0) {
        console.log("Preparing to upload files:", selectedFiles.map(f => f.name));

        // Use FormData for file uploads
        const formData = new FormData();
        formData.append('token', getCurrentToken());
        formData.append('ticket_id', ticket.id);
        formData.append('message', messageWithSignature);
        formData.append('is_internal', isInternalResponse ? '1' : '0');
        formData.append('status', replyStatus);

        // Add all files with original filenames preserved
        selectedFiles.forEach((file, index) => {
          formData.append(`attachment_${index}`, file, file.name);
        });
        formData.append('file_count', selectedFiles.length.toString());

        const response = await fetch(`${TICKETS_API_URL}?f=admin_add_ticket_message`, {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
          // Store admin information in localStorage for signature
          if (result.admin_name) {
            localStorage.setItem('admin_name', result.admin_name);
          }
          if (result.admin_function) {
            localStorage.setItem('admin_function', result.admin_function);
          }

          // Ensure we have admin profile data with fallback similar to TicketsPage
          let currentAdminProfile = adminProfile;
          if (!currentAdminProfile || !currentAdminProfile.first_name) {
            try {
              const effectiveToken = getCurrentToken();
              const profResp = await fetch(`${API_URL}/api_admin_profile.php?f=get_current_admin`, {
                method: 'POST', headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ token: effectiveToken })
              });
              if (profResp.ok) {
                const profData = await profResp.json();
                if (profData?.success && profData.admin) { currentAdminProfile = profData.admin; }
              }
            } catch (e) { console.error('Error fetching admin profile directly:', e); }
          }

          const adminName = result.admin_name ||
                           (currentAdminProfile && currentAdminProfile.first_name ?
                            `${currentAdminProfile.first_name} ${currentAdminProfile.last_name}`.trim() :
                            'Support Team');
          const adminPictureUrl = result.admin_picture_url || currentAdminProfile?.picture_url || null;
          const adminFunction = result.admin_function || currentAdminProfile?.function || 'Support';

          // Add the new message to the list
          const newMessage = {
            id: result.message_id,
            message: messageWithSignature,
            user_name: adminName,
            admin_picture_url: adminPictureUrl || '/admin/src/assets/default.png',
            admin_function: adminFunction || 'Support',
            time: result.time,
            date: result.date,
            created_at: new Date().toISOString(),
            type: 'zet',
            is_internal: isInternalResponse,
            attachment: result.attachment_url || null, // Keep for backward compatibility
            attachment_name: result.attachment_name || null, // Keep for backward compatibility
            attachments: result.attachments || [] // New: handle multiple attachments
          };

          setTicketMessages(prev => [...prev, newMessage]);

          // Update timeline
          const newTimelineEvent = {
            action: isInternalResponse ? "Internal Note Added" : "Staff Response Sent",
            timestamp: new Date().toISOString(),
            user: result.admin_name || 'Support Staff'
          };

          setTicket(prev => ({
            ...prev,
            timeline: [...(prev.timeline || []), newTimelineEvent],
            lastUpdated: new Date().toISOString(),
            status: isInternalResponse ? prev.status : replyStatus // Update status only for non-internal messages
          }));

          // Immediately refetch messages from server to ensure authoritative author info
          fetchTicketMessages(ticket.id);

          // Clear the input and files
          setNewResponse('');
          setSelectedFiles([]);
          setIsInternalResponse(false);
          setReplyStatus('Answered'); // Reset to default
          setLastUpdated(formatDateTime(new Date()));
        } else {
          alert(result.error || 'Failed to send response');
        }
      } else {
        // No file - use regular JSON for the request
        const response = await fetch(`${TICKETS_API_URL}?f=admin_add_ticket_message`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: getCurrentToken(),
            ticket_id: ticket.id,
            message: messageWithSignature,
            is_internal: isInternalResponse,
            status: replyStatus
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
          // Store admin information in localStorage for signature
          if (result.admin_name) {
            localStorage.setItem('admin_name', result.admin_name);
          }
          if (result.admin_function) {
            localStorage.setItem('admin_function', result.admin_function);
          }

          // Ensure we have admin profile data with fallback similar to TicketsPage
          let currentAdminProfile = adminProfile;
          if (!currentAdminProfile || !currentAdminProfile.first_name) {
            try {
              const effectiveToken = getCurrentToken();
              const profResp = await fetch(`${API_URL}/api_admin_profile.php?f=get_current_admin`, {
                method: 'POST', headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ token: effectiveToken })
              });
              if (profResp.ok) {
                const profData = await profResp.json();
                if (profData?.success && profData.admin) { currentAdminProfile = profData.admin; }
              }
            } catch (e) { console.error('Error fetching admin profile directly (non-file):', e); }
          }

          const adminName = result.admin_name ||
                           (currentAdminProfile && currentAdminProfile.first_name ?
                            `${currentAdminProfile.first_name} ${currentAdminProfile.last_name}`.trim() :
                            'Support Team');
          const adminPictureUrl = result.admin_picture_url || currentAdminProfile?.picture_url || null;
          const adminFunction = result.admin_function || currentAdminProfile?.function || 'Support';

          // Add the new message to the list
          const newMessage = {
            id: result.message_id,
            message: messageWithSignature,
            user_name: adminName,
            admin_picture_url: adminPictureUrl || '/admin/src/assets/default.png',
            admin_function: adminFunction || 'Support',
            time: result.time,
            date: result.date,
            created_at: new Date().toISOString(),
            type: 'zet',
            is_internal: isInternalResponse
          };

          setTicketMessages(prev => [...prev, newMessage]);

          // Update timeline
          const newTimelineEvent = {
            action: isInternalResponse ? "Internal Note Added" : "Staff Response Sent",
            timestamp: new Date().toISOString(),
            user: result.admin_name || 'Support Staff'
          };

          setTicket(prev => ({
            ...prev,
            timeline: [...(prev.timeline || []), newTimelineEvent],
            lastUpdated: new Date().toISOString(),
            status: isInternalResponse ? prev.status : replyStatus // Update status only for non-internal messages
          }));

          // Immediately refetch messages from server to ensure authoritative author info
          fetchTicketMessages(ticket.id);

          // Clear the input
          setNewResponse('');
          setIsInternalResponse(false);
          setReplyStatus('Answered'); // Reset to default
          setLastUpdated(formatDateTime(new Date()));
        } else {
          alert(result.error || 'Failed to send response');
        }
      }
    } catch (err) {
      console.error("Error submitting response:", err);
      alert('Failed to send response: ' + err.message);
    }
  };

  // Handle file selection - support multiple files (max 5)
  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    const maxFiles = 5; // Maximum 5 files per upload

    // Allowed file types (same as client-side)
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.xlsx', '.xls', '.doc', '.docx', '.txt', '.zip'];

    const validFiles = [];
    let errorMessages = [];

    // Check total file count (including already selected files)
    if (selectedFiles.length + files.length > maxFiles) {
      alert(`Maximum ${maxFiles} files allowed. You have ${selectedFiles.length} files selected and are trying to add ${files.length} more.`);
      return;
    }

    files.forEach(file => {
      // Check file size
      if (file.size > maxSize) {
        errorMessages.push(`File "${file.name}" exceeds 5MB limit (${(file.size / 1024 / 1024).toFixed(1)}MB)`);
        return;
      }

      // Check file extension
      const extension = '.' + file.name.split('.').pop().toLowerCase();
      if (!allowedExtensions.includes(extension)) {
        errorMessages.push(`File "${file.name}" has unsupported format. Allowed: ${allowedExtensions.join(', ')}`);
        return;
      }

      validFiles.push(file);
    });

    if (errorMessages.length > 0) {
      alert(errorMessages.join('\n'));
    } else {
      // Add to existing selected files instead of replacing
      setSelectedFiles(prev => [...prev, ...validFiles]);
    }
  };

  // Remove file from selection
  const removeFile = (index) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);
  };

  // Clear all files
  const clearFiles = () => {
    setSelectedFiles([]);
  };

  // Refresh data
  const handleRefreshData = async () => {
    createDirectTicket(id);
    setLastUpdated(formatDateTime(new Date()));
  };

  // Toggle ticket edit mode
  const toggleTicketEdit = () => {
    setIsEditingTicket(!isEditingTicket);
    // Reset edited ticket data when toggling edit mode
    if (!isEditingTicket) {
      // Before setting edited ticket, try to match department ID
      const matchedTicket = {...ticket};

      // Try to find the department ID for the ticket's department name
      if (departments.length > 0 && ticket.department) {
        const matchingDept = departments.find(dept =>
          dept.name === ticket.department ||
          dept.department_name === ticket.department
        );

        if (matchingDept) {
          console.log("Found matching department for edit:", matchingDept);
          matchedTicket.departmentId = matchingDept.id;
        }
      }

      setEditedTicket(matchedTicket);
    }
  };

  // Handle ticket field changes in edit mode
  const handleTicketFieldChange = (field, value) => {
    if (field === 'department') {
      // For department, we need to handle both the name and ID
      // Find the department object that matches the selected value (which is the ID)
      const selectedDept = departments.find(dept => dept.id.toString() === value);

      if (selectedDept) {
        // Update both department name and ID
        setEditedTicket(prev => ({
          ...prev,
          department: selectedDept.name || selectedDept.department_name,
          departmentId: selectedDept.id
        }));
      } else {
        // Fallback if department not found
        setEditedTicket(prev => ({
          ...prev,
          department: value,
          departmentId: ''
        }));
      }
    } else {
      // Regular field updates
      setEditedTicket(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Save ticket changes
  const handleSaveTicketChanges = async () => {
    try {
      // Determine which department value to send to the API
      // If we have a department ID, use that, otherwise use the department name
      const departmentValue = editedTicket.departmentId || editedTicket.department;

      const response = await fetch(`${TICKETS_API_URL}?f=admin_update_ticket`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: getCurrentToken(),
          ticket_id: ticket.id,
          subject: editedTicket.subject,
          customer: editedTicket.customer,
          customerEmail: editedTicket.customerEmail,
          department: departmentValue, // Use the appropriate department value
          category: editedTicket.category,
          priority: editedTicket.priority
        })
      });

      // Since the API might not support all fields, handle both success and failure gracefully
      if (response.ok) {
        try {
          const result = await response.json();
          if (result.success) {
            // Update the ticket in state
            setTicket(editedTicket);
            setIsEditingTicket(false);

            // Add to timeline
            const newTimelineEvent = {
              action: 'Ticket Details Updated',
              timestamp: new Date().toISOString(),
              user: 'Admin'
            };

            setTicket(prev => ({
              ...prev,
              timeline: [...(prev.timeline || []), newTimelineEvent],
              lastUpdated: new Date().toISOString()
            }));

            setLastUpdated(formatDateTime(new Date()));
            return;
          }
        } catch (parseError) {
          console.error("Error parsing response:", parseError);
        }
      }

      // If we get here, either the API doesn't support this or there was an error
      // Simulate success for demo purposes
      console.log("API may not support all ticket fields. Simulating partial success.");

      // Update the ticket in state
      setTicket(editedTicket);
      setIsEditingTicket(false);

      // Add to timeline
      const newTimelineEvent = {
        action: 'Ticket Details Updated',
        timestamp: new Date().toISOString(),
        user: 'Admin (Simulated)'
      };

      setTicket(prev => ({
        ...prev,
        timeline: [...(prev.timeline || []), newTimelineEvent],
        lastUpdated: new Date().toISOString()
      }));

      setLastUpdated(formatDateTime(new Date()));

    } catch (err) {
      console.error("Error updating ticket:", err);
      alert('Failed to update ticket: ' + err.message);
    }
  };

  // Update ticket status
  const handleUpdateStatus = async (newStatus) => {
    try {
      // First update the ticket status
      const response = await fetch(`${TICKETS_API_URL}?f=admin_update_ticket_status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: getCurrentToken(),
          ticket_id: ticket.id,
          status: newStatus
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Update the ticket in state
        setTicket(prev => ({
          ...prev,
          status: newStatus,
          lastUpdated: new Date().toISOString()
        }));

        // Add to timeline
        const newTimelineEvent = {
          action: `Status Changed to ${newStatus}`,
          timestamp: new Date().toISOString(),
          user: 'Admin'
        };

        setTicket(prev => ({
          ...prev,
          timeline: [...(prev.timeline || []), newTimelineEvent]
        }));

        setLastUpdated(formatDateTime(new Date()));

        // Now add an internal note about the status change
        try {
          const noteResponse = await fetch(`${TICKETS_API_URL}?f=admin_add_ticket_message`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: getCurrentToken(),
              ticket_id: ticket.id,
              message: `Status changed to "${newStatus}"`,
              is_internal: true
            })
          });

          if (noteResponse.ok) {
            console.log('Internal note added for status change');
          } else {
            console.error('Failed to add internal note for status change');
          }
        } catch (noteErr) {
          console.error('Error adding internal note:', noteErr);
          // We don't want to fail the whole operation if just the note fails
        }

        // Refresh messages to show system message about status change
        fetchTicketMessages(ticket.id);
      } else {
        alert(result.message || 'Failed to update ticket status');
      }
    } catch (err) {
      console.error("Error updating ticket status:", err);
      alert('Failed to update ticket status: ' + err.message);
    }
  };

  // Open reassign modal
  const openReassignModal = () => {
    setIsReassignModalOpen(true);
    setSelectedAdmin('');
  };

  // Close reassign modal
  const closeReassignModal = () => {
    setIsReassignModalOpen(false);
  };

  // Handle admin selection for reassignment
  const handleAdminSelection = (e) => {
    setSelectedAdmin(e.target.value);
  };

  // Reassign ticket
  const handleReassignTicket = async () => {
    if (!selectedAdmin) {
      alert('Please select an admin to reassign the ticket to');
      return;
    }

    try {
      const response = await fetch(`${TICKETS_API_URL}?f=admin_reassign_ticket`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: getCurrentToken(),
          ticket_id: ticket.id,
          admin_id: selectedAdmin
        })
      });

      // Since the API might not support this, handle both success and failure gracefully
      if (response.ok) {
        try {
          const result = await response.json();
          if (result.success) {
            // Get the admin name from the admin list
            const admin = adminList.find(admin => admin.id.toString() === selectedAdmin);

            // Update the ticket in state
            setTicket(prev => ({
              ...prev,
              assignedTo: admin ? admin.name : 'Unknown',
              lastUpdated: new Date().toISOString()
            }));

            // Add to timeline
            const newTimelineEvent = {
              action: `Reassigned to ${admin ? admin.name : 'Unknown'}`,
              timestamp: new Date().toISOString(),
              user: 'Admin'
            };

            setTicket(prev => ({
              ...prev,
              timeline: [...(prev.timeline || []), newTimelineEvent]
            }));

            setLastUpdated(formatDateTime(new Date()));
            closeReassignModal();
            return;
          }
        } catch (parseError) {
          console.error("Error parsing response:", parseError);
        }
      }

      // If we get here, either the API doesn't support this or there was an error
      // Simulate success for demo purposes
      console.log("API may not support ticket reassignment. Simulating success.");

      // Get the admin name from the admin list
      const admin = adminList.find(admin => admin.id.toString() === selectedAdmin);

      // Update the ticket in state
      setTicket(prev => ({
        ...prev,
        assignedTo: admin ? admin.name : 'Unknown',
        lastUpdated: new Date().toISOString()
      }));

      // Add to timeline
      const newTimelineEvent = {
        action: `Reassigned to ${admin ? admin.name : 'Unknown'} (Simulated)`,
        timestamp: new Date().toISOString(),
        user: 'Admin'
      };

      setTicket(prev => ({
        ...prev,
        timeline: [...(prev.timeline || []), newTimelineEvent]
      }));

      setLastUpdated(formatDateTime(new Date()));
      closeReassignModal();

    } catch (err) {
      console.error("Error reassigning ticket:", err);
      alert('Failed to reassign ticket: ' + err.message);
    }
  };

  // Handle editing a response
  const handleEditResponse = (response) => {
    setEditingResponseId(response.id);
    setEditedResponseContent(response.message || response.content);
  };

  // Save edited response
  const handleSaveResponseEdit = async () => {
    if (!editingResponseId || editedResponseContent.trim() === '') return;

    try {
      const response = await fetch(`${TICKETS_API_URL}?f=admin_update_message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: getCurrentToken(),
          message_id: editingResponseId,
          message: editedResponseContent
        })
      });

      // Since the API might not support this, handle both success and failure gracefully
      if (response.ok) {
        try {
          const result = await response.json();
          if (result.success) {
            // Update the message in state
            const updatedMessages = ticketMessages.map(msg =>
              msg.id === editingResponseId
                ? { ...msg, message: editedResponseContent, edited: true }
                : msg
            );

            setTicketMessages(updatedMessages);

            // Add to timeline
            const newTimelineEvent = {
              action: 'Message Edited',
              timestamp: new Date().toISOString(),
              user: 'Admin'
            };

            setTicket(prev => ({
              ...prev,
              timeline: [...(prev.timeline || []), newTimelineEvent],
              lastUpdated: new Date().toISOString()
            }));

            setLastUpdated(formatDateTime(new Date()));
            setEditingResponseId(null);
            setEditedResponseContent('');
            return;
          }
        } catch (parseError) {
          console.error("Error parsing response:", parseError);
        }
      }

      // If we get here, either the API doesn't support this or there was an error
      // Simulate success for demo purposes
      console.log("API may not support message editing. Simulating success.");

      // Update the message in state
      const updatedMessages = ticketMessages.map(msg =>
        msg.id === editingResponseId
          ? { ...msg, message: editedResponseContent, edited: true }
          : msg
      );

      setTicketMessages(updatedMessages);

      // Add to timeline
      const newTimelineEvent = {
        action: 'Message Edited (Simulated)',
        timestamp: new Date().toISOString(),
        user: 'Admin'
      };

      setTicket(prev => ({
        ...prev,
        timeline: [...(prev.timeline || []), newTimelineEvent],
        lastUpdated: new Date().toISOString()
      }));

      setLastUpdated(formatDateTime(new Date()));
      setEditingResponseId(null);
      setEditedResponseContent('');

    } catch (err) {
      console.error("Error updating message:", err);
      alert('Failed to update message: ' + err.message);
      setEditingResponseId(null);
      setEditedResponseContent('');
    }
  };

  // Cancel response edit
  const handleCancelResponseEdit = () => {
    setEditingResponseId(null);
    setEditedResponseContent('');
  };

  // Delete message
  const handleDeleteMessage = async (messageId) => {
    if (!messageId || !window.confirm('Are you sure you want to delete this message?')) {
      return;
    }

    try {
      const response = await fetch(`${TICKETS_API_URL}?f=admin_delete_message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: getCurrentToken(),
          message_id: messageId
        })
      });

      // Since the API might not support this, handle both success and failure gracefully
      if (response.ok) {
        try {
          const result = await response.json();
          if (result.success) {
            // Remove the message from state
            const updatedMessages = ticketMessages.filter(msg => msg.id !== messageId);
            setTicketMessages(updatedMessages);

            // Add to timeline
            const newTimelineEvent = {
              action: 'Message Deleted',
              timestamp: new Date().toISOString(),
              user: 'Admin'
            };

            setTicket(prev => ({
              ...prev,
              timeline: [...(prev.timeline || []), newTimelineEvent],
              lastUpdated: new Date().toISOString()
            }));

            setLastUpdated(formatDateTime(new Date()));
            return;
          }
        } catch (parseError) {
          console.error("Error parsing response:", parseError);
        }
      }

      // If we get here, either the API doesn't support this or there was an error
      // Simulate success for demo purposes
      console.log("API may not support message deletion. Simulating success.");

      // Remove the message from state
      const updatedMessages = ticketMessages.filter(msg => msg.id !== messageId);
      setTicketMessages(updatedMessages);

      // Add to timeline
      const newTimelineEvent = {
        action: 'Message Deleted (Simulated)',
        timestamp: new Date().toISOString(),
        user: 'Admin'
      };

      setTicket(prev => ({
        ...prev,
        timeline: [...(prev.timeline || []), newTimelineEvent],
        lastUpdated: new Date().toISOString()
      }));

      setLastUpdated(formatDateTime(new Date()));

    } catch (err) {
      console.error("Error deleting message:", err);
      alert('Failed to delete message: ' + err.message);
    }
  };

  // Impersonate a user in the client portal
  const handleImpersonateUser = async (userId) => {
    // Open new tab immediately during user interaction to avoid popup blockers
    const newTab = window.open('about:blank', '_blank');

    try {
      const response = await fetch(`${API_URL}/api_accounts.php?f=impersonate_user`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          user_id: userId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();
      if (result.success && result.user_token) {
        const clientUrl = `https://client.x-zoneit.ro/?impersonate_token=${encodeURIComponent(result.user_token)}`;
        // Navigate the pre-opened tab to the client portal
        newTab.location.href = clientUrl;
      } else {
        // Close the blank tab if there was an error
        newTab.close();
        alert(result.error || 'Failed to impersonate user');
      }
    } catch (err) {
      // Close the blank tab if there was an error
      newTab.close();
      console.error('Error impersonating user:', err);
      alert('Failed to impersonate user: ' + err.message);
    }
  };

  // Render status badge
  const renderStatusBadge = (status) => {
    const badgeClasses = {
      'New': 'bg-blue-100 text-blue-800',
      'Open': 'bg-yellow-100 text-yellow-800',
      'In Progress': 'bg-indigo-100 text-indigo-800',
      'Pending Customer': 'bg-purple-100 text-purple-800',
      'Answered': 'bg-blue-100 text-blue-800',
      'Resolved': 'bg-green-100 text-green-800',
      'Closed': 'bg-gray-100 text-gray-800'
    };

    const icons = {
      'New': <FileText className="w-4 h-4 mr-1" />,
      'Open': <AlertTriangle className="w-4 h-4 mr-1" />,
      'In Progress': <Clock className="w-4 h-4 mr-1" />,
      'Pending Customer': <User className="w-4 h-4 mr-1" />,
      'Answered': <Reply className="w-4 h-4 mr-1" />,
      'Resolved': <CheckCircle className="w-4 h-4 mr-1" />,
      'Closed': <XCircle className="w-4 h-4 mr-1" />
    };

    // Default to "Open" if status is not recognized
    const defaultStatus = 'Open';
    const badgeClass = badgeClasses[status] || badgeClasses[defaultStatus];
    const icon = icons[status] || icons[defaultStatus];

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClass}`}>
        {icon}
        {status}
      </span>
    );
  };

  // Render priority badge
  const renderPriorityBadge = (priority) => {
    const badgeClasses = {
      'Low': 'bg-green-100 text-green-800',
      'Medium': 'bg-blue-100 text-blue-800',
      'High': 'bg-orange-100 text-orange-800',
      'Critical': 'bg-red-100 text-red-800'
    };

    // Default to "Medium" if priority is not recognized
    const defaultPriority = 'Medium';
    const badgeClass = badgeClasses[priority] || badgeClasses[defaultPriority];

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium w-fit ${badgeClass}`}>
        {priority}
      </span>
    );
  };

  // Format date/time
  const formatDateTime = (dateTimeString) => {
    try {
      const options = { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' };
      return new Date(dateTimeString).toLocaleString('en-GB', options);
    } catch (err) {
      return dateTimeString || 'N/A';
    }
  };

  // Download attachment function for admin
  const downloadAttachment = async (attachmentId, fileName) => {
    if (!attachmentId) {
      console.error('No attachment ID provided');
      return;
    }

    const token = localStorage.getItem('admin_token');
    if (!token) {
      console.error('No admin token found');
      return;
    }

    try {
      // Make POST request to download endpoint
      const response = await fetch(`${TICKETS_API_URL}?f=admin_download_attachment&id=${attachmentId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Download failed:', errorText);
        alert('Failed to download file: ' + errorText);
        return;
      }

      // Create blob from response and trigger download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName || 'attachment';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download error:', error);
      alert('Failed to download file: ' + error.message);
    }
  };

  // Format date/time function is used instead of time since calculation

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-100">
        <Sidebar
          collapsed={sidebarCollapsed}
          activeMenu="Support"
          navigateTo={navigateTo}
          toggleSidebar={toggleSidebar}
        />
        <div className="flex-1 flex flex-col bg-gray-100">
          <TopMenu toggleSidebar={toggleSidebar} />
          <div className="p-6">
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mb-4"></div>
              <p>Loading ticket details...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !ticket) {
    return (
      <div className="flex h-screen bg-gray-100">
        <Sidebar
          collapsed={sidebarCollapsed}
          activeMenu="Support"
          navigateTo={navigateTo}
          toggleSidebar={toggleSidebar}
        />
        <div className="flex-1 flex flex-col bg-gray-100">
          <TopMenu toggleSidebar={toggleSidebar} />
          <div className="p-6">
            <div className="text-center py-12">
              <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
              <h2 className="text-xl font-bold mb-2">Ticket Not Found</h2>
              <p className="mb-4">{error || "The ticket you're looking for doesn't exist or has been deleted."}</p>
              <p className="mb-4 text-sm text-gray-500">Ticket ID: {id}</p>
              <button
                onClick={() => navigateTo('/admin/tickets')}
                className="px-4 py-2 bg-indigo-700 text-white rounded-md inline-flex items-center text-sm hover:bg-indigo-800"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                Back to Tickets
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        collapsed={sidebarCollapsed}
        activeMenu="Support"
        navigateTo={navigateTo}
        toggleSidebar={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gray-100">
        {/* Top Menu */}
        <TopMenu toggleSidebar={toggleSidebar} />

        <div className="p-6 overflow-auto">
          {/* Back button and header */}
          <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-4 gap-4">
            <div>
              <button
                onClick={() => navigateTo('/admin/tickets')}
                className="flex items-center text-gray-600 hover:text-indigo-700 mb-2"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                <span className="text-sm">Back to Tickets</span>
              </button>
              <div className="flex flex-wrap items-center gap-3">
                <h1 className="text-2xl font-bold text-gray-800">#{ticket.id}</h1>
                {isEditingTicket ? (
                  <input
                    type="text"
                    className="text-xl text-gray-700 p-1 border rounded focus:outline-none focus:ring-2 focus:ring-indigo-200 w-full lg:w-auto"
                    value={editedTicket.subject}
                    onChange={(e) => handleTicketFieldChange('subject', e.target.value)}
                  />
                ) : (
                  <h2 className="text-xl text-gray-700">{ticket.subject}</h2>
                )}
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-2">
              <div className="text-sm text-gray-500 mr-2">Last updated: {lastUpdated}</div>
              <button
                onClick={handleRefreshData}
                className="p-2 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
              >
                <RefreshCw className="w-4 h-4" />
              </button>

              {isEditingTicket ? (
                <>
                  <button
                    onClick={handleSaveTicketChanges}
                    className="px-3 py-1.5 bg-indigo-700 hover:bg-indigo-800 text-white rounded text-sm flex items-center"
                  >
                    <Save className="w-4 h-4 mr-1" />
                    Save Changes
                  </button>
                  <button
                    onClick={toggleTicketEdit}
                    className="px-3 py-1.5 border border-gray-300 text-gray-700 rounded text-sm flex items-center hover:bg-gray-50"
                  >
                    <X className="w-4 h-4 mr-1" />
                    Cancel
                  </button>
                </>
              ) : (
                <button
                  onClick={toggleTicketEdit}
                  className="px-3 py-1.5 bg-indigo-700 hover:bg-indigo-800 text-white rounded text-sm flex items-center"
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </button>
              )}
            </div>
          </div>

          {/* Status and key info banner */}
          <div className="bg-white shadow-sm rounded-md border mb-4 p-4">
            <div className="flex flex-wrap justify-between items-center">
              <div className="flex flex-wrap gap-3 items-center">
                <div className="flex items-center gap-2">
                  {renderStatusBadge(ticket.status)}
                  {isEditingTicket ? (
                    <select
                      className="p-1 border rounded text-sm"
                      value={editedTicket.priority}
                      onChange={(e) => handleTicketFieldChange('priority', e.target.value)}
                    >
                      <option value="Low">Low</option>
                      <option value="Medium">Medium</option>
                      <option value="High">High</option>
                      <option value="Critical">Critical</option>
                    </select>
                  ) : (
                    renderPriorityBadge(ticket.priority)
                  )}
                </div>
                <div className="flex items-center gap-1 text-sm text-gray-600">
                  <Calendar className="w-4 h-4" />
                  <span>Created: {formatDateTime(ticket.createdDate)}</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-gray-600">
                  <User className="w-4 h-4" />
                  <span>Assigned: {ticket.assignedTo || 'Unassigned'}</span>
                </div>
                {ticket.dueDate && (
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <Clock className="w-4 h-4" />
                    <span>Due: {formatDateTime(ticket.dueDate)}</span>
                  </div>
                )}
              </div>
              <div>
                <button
                  className="text-sm text-indigo-700 flex items-center"
                  onClick={() => setDetailsExpanded(!detailsExpanded)}
                >
                  {detailsExpanded ? (
                    <>
                      <ChevronUp className="w-4 h-4 mr-1" />
                      Hide Details
                    </>
                  ) : (
                    <>
                      <ChevronDown className="w-4 h-4 mr-1" />
                      Show Details
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Expandable details section */}
            {detailsExpanded && (
              <div className="mt-4 pt-4 border-t grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Customer Info */}
                <div>
                  <h3 className="text-md font-semibold mb-2">Customer</h3>
                  <div className="space-y-1">
{/* Customer info is always displayed as read-only, even in edit mode */}
<div className="text-sm">
  <span className="font-medium">{ticket.customer || 'Unknown Customer'}</span>
</div>
<div className="text-sm text-gray-600">{ticket.customerEmail || 'No email available'}</div>
{ticket.customerId && (
  <div className="text-xs text-gray-500">ID: {ticket.customerId}</div>
)}
{!isEditingTicket && (
  <button className="mt-2 text-xs text-indigo-700 flex items-center">
    <User className="w-3 h-3 mr-1" />
    View Profile: {ticket.customer || 'Unknown Customer'}
  </button>
)}

                  </div>
                </div>

                {/* Ticket Details */}
                <div>
                  <h3 className="text-md font-semibold mb-2">Details</h3>
                  <div className="space-y-1">
                    <div className="text-sm">
                      <span className="text-gray-500">Category:</span>
                      {isEditingTicket ? (
                        <select
                          className="ml-2 p-1 border rounded text-sm"
                          value={editedTicket.category}
                          onChange={(e) => handleTicketFieldChange('category', e.target.value)}
                        >
                          <option value="Server Issue">Server Issue</option>
                          <option value="Network Issue">Network Issue</option>
                          <option value="Account Management">Account Management</option>
                          <option value="Maintenance">Maintenance</option>
                          <option value="Abuse">Abuse</option>
                          <option value="Other">Other</option>
                        </select>
                      ) : (
                        <span> {ticket.category}</span>
                      )}
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-500">Department:</span>
                      {isEditingTicket ? (
                        <select
                          className="ml-2 p-1 border rounded text-sm"
                          value={editedTicket.departmentId || ''}
                          onChange={(e) => handleTicketFieldChange('department', e.target.value)}
                        >
                          <option value="">Select Department</option>
                          {departments.length > 0 ? (
                            departments.map(dept => (
                              <option
                                key={dept.id}
                                value={dept.id}
                              >
                                {dept.name || dept.department_name}
                              </option>
                            ))
                          ) : (
                            <>
                              <option value="Technical Support">Technical Support</option>
                              <option value="Account Management">Account Management</option>
                              <option value="Customer Service">Customer Service</option>
                              <option value="Billing">Billing</option>
                              <option value="Sales">Sales</option>
                            </>
                          )}
                        </select>
                      ) : (
                        <span> {ticket.department}</span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div>
                  <h3 className="text-md font-semibold mb-2">Actions</h3>
                  <div className="flex flex-wrap gap-2">
                    {/* Status Change Dropdown */}
                    <div className="relative status-dropdown-container">
                      <button
                        className="px-3 py-1 bg-indigo-600 text-white rounded-md flex items-center text-xs hover:bg-indigo-700"
                        onClick={() => setShowStatusDropdown(!showStatusDropdown)}
                      >
                        <Clock className="w-3 h-3 mr-1" />
                        Change Status
                      </button>
                      {showStatusDropdown && (
                        <div className="absolute z-10 mt-1 w-48 bg-white rounded-md shadow-lg py-1 text-sm">
                          <button
                            className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                            onClick={() => {
                              handleUpdateStatus('New');
                              setShowStatusDropdown(false);
                            }}
                          >
                            <FileText className="w-3 h-3 mr-2 text-blue-600" />
                            New
                          </button>
                          <button
                            className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                            onClick={() => {
                              handleUpdateStatus('Open');
                              setShowStatusDropdown(false);
                            }}
                          >
                            <AlertTriangle className="w-3 h-3 mr-2 text-yellow-600" />
                            Open
                          </button>
                          <button
                            className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                            onClick={() => {
                              handleUpdateStatus('In Progress');
                              setShowStatusDropdown(false);
                            }}
                          >
                            <Clock className="w-3 h-3 mr-2 text-indigo-600" />
                            In Progress
                          </button>
                          <button
                            className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                            onClick={() => {
                              handleUpdateStatus('Pending Customer');
                              setShowStatusDropdown(false);
                            }}
                          >
                            <User className="w-3 h-3 mr-2 text-purple-600" />
                            Pending Customer
                          </button>
                          <button
                            className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                            onClick={() => {
                              handleUpdateStatus('Answered');
                              setShowStatusDropdown(false);
                            }}
                          >
                            <Reply className="w-3 h-3 mr-2 text-blue-600" />
                            Answered
                          </button>
                          <button
                            className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                            onClick={() => {
                              handleUpdateStatus('Resolved');
                              setShowStatusDropdown(false);
                            }}
                          >
                            <CheckCircle className="w-3 h-3 mr-2 text-green-600" />
                            Resolved
                          </button>
                          <button
                            className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                            onClick={() => {
                              handleUpdateStatus('Closed');
                              setShowStatusDropdown(false);
                            }}
                          >
                            <XCircle className="w-3 h-3 mr-2 text-gray-600" />
                            Closed
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Quick Answer Button */}
                    {ticket.status !== 'Answered' && ticket.status !== 'Resolved' && ticket.status !== 'Closed' && (
                      <button
                        className="px-3 py-1 bg-blue-600 text-white rounded-md flex items-center text-xs hover:bg-blue-700"
                        onClick={() => handleUpdateStatus('Answered')}
                      >
                        <Reply className="w-3 h-3 mr-1" />
                        Mark Answered
                      </button>
                    )}

                    {/* Quick Resolve Button */}
                    {ticket.status !== 'Resolved' && ticket.status !== 'Closed' && (
                      <button
                        className="px-3 py-1 bg-green-600 text-white rounded-md flex items-center text-xs hover:bg-green-700"
                        onClick={() => handleUpdateStatus('Resolved')}
                      >
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Resolve
                      </button>
                    )}

                    <button
                      className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 flex items-center text-xs hover:bg-gray-50"
                      onClick={openReassignModal}
                    >
                      <Users className="w-3 h-3 mr-1" />
                      Reassign
                    </button>

                    {ticket.customerId && (
                      <button
                        className="px-3 py-1 border border-indigo-300 rounded-md text-indigo-700 flex items-center text-xs hover:bg-indigo-50"
                        onClick={() => handleImpersonateUser(ticket.customerId)}
                        title="Login as this user"
                      >
                        <UserCheck className="w-3 h-3 mr-1" />
                        Login as User
                      </button>
                    )}

                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Main Content: Communication */}
          <div className="bg-white shadow-sm rounded-md border">
            {/* Response Form - At the top for easier access */}
            <div className="p-4 border-b">
              <div className="flex items-center mb-3">
                <MessageCircle className="w-5 h-5 text-indigo-700 mr-2" />
                <h3 className="text-lg font-bold">Add Response</h3>
              </div>
              <div className="space-y-3">
                <textarea
                  className="w-full p-3 border rounded-md h-32 focus:outline-none focus:ring-2 focus:ring-indigo-200 bg-white"
                  placeholder="Type your response here..."
                  value={newResponse}
                  onChange={handleResponseChange}
                ></textarea>
                <div className="flex flex-wrap items-center justify-between gap-3">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="internalNote"
                        className="mr-2"
                        checked={isInternalResponse}
                        onChange={handleToggleInternalResponse}
                      />
                      <label htmlFor="internalNote" className="text-sm">
                        Internal Note
                      </label>
                    </div>
                    {!isInternalResponse && (
                      <div className="flex items-center gap-2">
                        <label htmlFor="replyStatus" className="text-sm text-gray-600">
                          Set Status:
                        </label>
                        <select
                          id="replyStatus"
                          className="px-2 py-1 border rounded text-sm focus:outline-none focus:ring-2 focus:ring-indigo-200"
                          value={replyStatus}
                          onChange={(e) => setReplyStatus(e.target.value)}
                        >
                          <option value="Answered">Answered</option>
                          <option value="In Progress">In Progress</option>
                          <option value="Pending Customer">Pending Customer</option>
                          <option value="Resolved">Resolved</option>
                          <option value="Open">Open</option>
                        </select>
                      </div>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <label className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50 flex items-center cursor-pointer">
                      <Paperclip className="w-4 h-4 mr-1" />
                      Attach Files ({selectedFiles.length}/5)
                      <input
                        type="file"
                        className="hidden"
                        onChange={handleFileSelect}
                        multiple
                        accept=".jpg,.jpeg,.png,.gif,.pdf,.xlsx,.xls,.doc,.docx,.txt,.zip"
                        disabled={selectedFiles.length >= 5}
                      />
                    </label>
                    {selectedFiles.length > 0 && (
                      <button
                        className="px-3 py-2 border border-red-300 rounded-md text-red-700 text-sm hover:bg-red-50 flex items-center"
                        onClick={clearFiles}
                      >
                        <X className="w-4 h-4 mr-1" />
                        Clear All
                      </button>
                    )}
                    <button
                      className="px-4 py-2 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
                      onClick={handleSubmitResponse}
                      disabled={!newResponse.trim()}
                    >
                      <Send className="w-4 h-4 mr-1" />
                      {isInternalResponse ? 'Add Note' : 'Send'}
                    </button>
                  </div>
                </div>
                {selectedFiles.length > 0 && (
                  <div className="space-y-2">
                    <div className="text-sm text-gray-600 font-medium">
                      {selectedFiles.length} file{selectedFiles.length > 1 ? 's' : ''} selected:
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {selectedFiles.map((file, index) => {
                        // Get file icon based on type
                        const getFileIcon = (file) => {
                          if (file.type.includes('image/')) return 'text-blue-600';
                          if (file.type.includes('pdf')) return 'text-red-600';
                          if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) return 'text-blue-700';
                          if (file.type.includes('excel') || file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) return 'text-green-600';
                          if (file.type.includes('zip') || file.type.includes('rar')) return 'text-yellow-600';
                          return 'text-gray-600';
                        };

                        return (
                          <div key={index} className="flex items-center bg-gray-50 p-2 rounded border">
                            <FileText className={`w-4 h-4 mr-2 ${getFileIcon(file)}`} />
                            <div className="flex-grow min-w-0">
                              <div className="text-sm font-medium truncate" title={file.name}>
                                {file.name}
                              </div>
                              <div className="text-xs text-gray-500">
                                {(file.size / 1024).toFixed(1)} KB
                              </div>
                            </div>
                            <button
                              className="ml-2 text-gray-500 hover:text-red-500 flex-shrink-0"
                              onClick={() => removeFile(index)}
                              title="Remove file"
                            >
                              <XCircle className="w-4 h-4" />
                            </button>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Communication Thread */}
            <div className="p-4">
              <div className="flex items-center mb-3">
                <MessageCircle className="w-5 h-5 text-indigo-700 mr-2" />
                <h3 className="text-lg font-bold">Communication Thread</h3>
              </div>
              {loadingMessages ? (
                <div className="flex justify-center items-center p-6">
                  <RefreshCw className="w-5 h-5 animate-spin mr-2 text-indigo-700" />
                  <span>Loading messages...</span>
                </div>
              ) : (
                <div className="space-y-4">
                  {ticketMessages.length > 0 ? (
                    [...ticketMessages]
                      .sort((a, b) => new Date(b.created_at || b.timestamp) - new Date(a.created_at || a.timestamp))
                      .map((response, index) => (
                      <div
                        key={index}
                        className={`rounded-md p-4 ${response.is_internal ? 'bg-yellow-50 border border-yellow-200' : response.type === 'customer' ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 border border-gray-200'}`}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex items-center">
                            {/* Add admin picture for staff messages (type 'zet') using per-message data */}
                            {response.type === 'zet' && (
                              <img
                                src={getProfilePicturePath(response.admin_picture_url)}
                                alt="Admin"
                                className="w-6 h-6 mr-2 flex-shrink-0 rounded-full object-cover"
                                onError={(e) => { e.target.onerror = null; e.target.src = avatar; }}
                              />
                            )}
                            <div>
                              <span className="font-medium">
                                {response.type === 'zet'
                                  ? (response.user_name || 'Support Staff')
                                  : (response.user_name || response.author || 'Unknown User')
                                }
                              </span>
                              {(response.type === 'zet' && response.admin_function) ? (
                                <span className="text-xs text-gray-500 ml-2">{response.admin_function}</span>
                              ) : (
                                response.authorRole && <span className="text-xs text-gray-500 ml-2">{response.authorRole}</span>
                              )}
                              {response.is_internal && (
                                <span className="ml-2 px-2 py-0.5 bg-yellow-200 text-yellow-800 text-xs rounded-full">
                                  Internal Note
                                </span>
                              )}
                              {response.type === 'customer' && (
                                <span className="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">
                                  Customer
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center">
                            <span className="text-xs text-gray-500">
                              {formatDateTime(response.created_at || response.timestamp || new Date())}
                            </span>
                            <div className="dropdown relative ml-2 flex items-center gap-1">
                              {/* Edit button - only for 'zet' type messages (support staff) */}
                              {response.type === 'zet' && (
                                <button
                                  className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                                  onClick={() => handleEditResponse(response)}
                                >
                                  <Edit className="w-4 h-4" />
                                </button>
                              )}
                              {response.type === 'zet' && (
                                <button
                                  className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                                  onClick={() => handleDeleteMessage(response.id)}
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Content - Editable or Read-only */}
                        {editingResponseId === response.id ? (
                          <div className="mb-2">
                            <textarea
                              className="w-full p-2 border rounded-md h-24 focus:outline-none focus:ring-2 focus:ring-indigo-200"
                              value={editedResponseContent}
                              onChange={(e) => setEditedResponseContent(e.target.value)}
                            ></textarea>
                            <div className="flex justify-end mt-2 gap-2">
                              <button
                                onClick={handleCancelResponseEdit}
                                className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 text-xs hover:bg-gray-50"
                              >
                                Cancel
                              </button>
                              <button
                                onClick={handleSaveResponseEdit}
                                className="px-3 py-1 bg-indigo-700 text-white rounded-md text-xs hover:bg-indigo-800"
                              >
                                Save Changes
                              </button>
                            </div>
                          </div>
                        ) : (
                          <p className="text-sm mb-3 whitespace-pre-wrap">{response.message || response.content}</p>
                        )}

                        {/* Edited indicator */}
                        {response.edited && editingResponseId !== response.id && (
                          <div className="text-xs text-gray-500 italic mb-2">
                            Edited
                          </div>
                        )}

                        {/* Attachments within the message */}
                        {((response.attachments && response.attachments.length > 0) || response.attachment) && (
                          <div className="mt-3 pt-3 border-t border-gray-200">
                            <div className="text-xs text-gray-500 mb-2 flex items-center">
                              <Paperclip className="w-3 h-3 mr-1" />
                              {response.attachments && response.attachments.length > 1 ?
                                `${response.attachments.length} Attachments` :
                                'Attachment'
                              }
                            </div>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                              {response.attachments && response.attachments.length > 0 ? (
                                // Display multiple attachments
                                response.attachments.map((attachment, index) => (
                                  <div key={index} className="flex items-center bg-white border rounded p-2">
                                    <FileText className="w-4 h-4 text-indigo-600 mr-2 flex-shrink-0" />
                                    <div className="flex-grow truncate">
                                      <div className="text-xs font-medium truncate">
                                        {attachment.file_name || attachment.file_url}
                                      </div>
                                      {attachment.file_size && (
                                        <div className="text-xs text-gray-400">
                                          {(attachment.file_size / 1024).toFixed(1)} KB
                                        </div>
                                      )}
                                    </div>
                                    <button
                                      onClick={() => downloadAttachment(attachment.id, attachment.file_name)}
                                      className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
                                      title="Download attachment"
                                    >
                                      <Download className="w-3 h-3" />
                                    </button>
                                  </div>
                                ))
                              ) : (
                                // Fallback to single attachment for backward compatibility
                                <div className="flex items-center bg-white border rounded p-2">
                                  <FileText className="w-4 h-4 text-indigo-600 mr-2 flex-shrink-0" />
                                  <div className="flex-grow truncate">
                                    <div className="text-xs font-medium truncate">
                                      {response.attachment_name || response.attachment}
                                    </div>
                                  </div>
                                  {response.attachment_id ? (
                                    <button
                                      onClick={() => downloadAttachment(response.attachment_id, response.attachment_name)}
                                      className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
                                      title="Download attachment"
                                    >
                                      <Download className="w-3 h-3" />
                                    </button>
                                  ) : (
                                    <a
                                      href={response.attachment}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
                                      title="Download attachment"
                                    >
                                      <Download className="w-3 h-3" />
                                    </a>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {/* Reply button (non-internal messages only) */}
                        {!response.is_internal && editingResponseId !== response.id && (
                          <div className="mt-2 flex justify-end">
                            <button
                              className="text-xs text-indigo-700 flex items-center"
                              onClick={() => {
                                // Focus the response box and optionally pre-fill with quoted text
                                document.querySelector('textarea').focus();
                              }}
                            >
                              <Reply className="w-3 h-3 mr-1" />
                              Reply
                            </button>
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center text-gray-500 p-6 border rounded-md">
                      No messages available for this ticket
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Footer with Secondary Tabs */}
          <div className="mt-4 bg-white shadow-sm rounded-md border">
            <div className="flex border-b">
              <button
                className={`px-4 py-3 flex items-center text-sm font-medium ${activeSection === 'timeline' ? 'border-b-2 border-indigo-700 text-indigo-700' : 'text-gray-500 hover:text-indigo-700'}`}
                onClick={() => setActiveSection('timeline')}
              >
                <Clock className="w-4 h-4 mr-1" />
                Timeline
              </button>
              <button
                className={`px-4 py-3 flex items-center text-sm font-medium ${activeSection === 'related' ? 'border-b-2 border-indigo-700 text-indigo-700' : 'text-gray-500 hover:text-indigo-700'}`}
                onClick={() => setActiveSection('related')}
              >
                <Link2 className="w-4 h-4 mr-1" />
                Related Tickets
              </button>
            </div>

            {/* Timeline Content */}
            {activeSection === 'timeline' && (
              <div className="p-4">
                <div className="border-l-2 border-gray-200 ml-3 space-y-6">
                  {ticket.timeline && ticket.timeline.length > 0 ? (
                    ticket.timeline
                      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                      .map((event, index) => (
                      <div key={index} className="relative">
                        <div className="absolute -left-1.5 mt-1.5 w-3 h-3 rounded-full border-2 border-white bg-indigo-600"></div>
                        <div className="ml-6">
                          <div className="text-sm font-medium">{event.action}</div>
                          <div className="text-xs text-gray-500">{formatDateTime(event.timestamp)}</div>
                          <div className="text-xs text-gray-500">By {event.user}</div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="ml-6 py-4 text-gray-500">
                      No timeline events available
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Related Tickets Content */}
            {activeSection === 'related' && (
              <div className="p-4">
                <div>
                  <div className="bg-gray-50 p-4 rounded-md text-gray-500 text-center mb-4">
                    No related tickets found
                  </div>

                  <div className="flex gap-2">
                    <input
                      type="text"
                      placeholder="Enter ticket ID..."
                      className="flex-1 p-2 border rounded-md text-sm focus-ring"
                    />
                    <button className="px-4 py-2 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800">
                      <Link2 className="w-4 h-4 mr-1" />
                      Link
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Reassign Modal */}
      {isReassignModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full overflow-hidden">
            <div className="p-6 border-b flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-800">Reassign Ticket</h2>
              <button
                onClick={closeReassignModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <label htmlFor="assignTo" className="block text-sm font-medium text-gray-700 mb-1">Assign To</label>
                <select
                  id="assignTo"
                  className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200"
                  value={selectedAdmin}
                  onChange={handleAdminSelection}
                >
                  <option value="">Select Support Agent</option>
                  {adminList.map(admin => (
                    <option key={admin.id} value={admin.id}>{admin.name}</option>
                  ))}
                </select>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <button
                  onClick={closeReassignModal}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleReassignTicket}
                  className="px-4 py-2 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
                  disabled={!selectedAdmin}
                >
                  <Users className="w-4 h-4 mr-1" />
                  Reassign
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TicketDetailPage;