import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  Search,
  Plus,
  Filter,
  ArrowUp,
  ArrowDown,
  MoreVertical,
  Eye,
  Server,
  Network,
  Cpu,
  EyeOff,
  Settings,
  Wifi,
  Layers,
  Calendar,
  XCircle,
  RefreshCw,
  Edit,
  Trash2,
  Save,
  X,
  Database,
  Globe,
  MapPin,
  Wrench,
  AlertTriangle,
  CheckCircle,
  Lock,
  Building,
  User,
  ChevronLeft,
  Power,
  PowerOff,
  HardDrive,
  Activity,
  Check,
  Terminal
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import SwitchPortsManager from '../components/SwitchPortsManager';
import PortTrafficGraph from '../components/PortTrafficGraph';
import CountryIpSelector from '../components/CountryIpSelector';

import { API_URL } from '../config';
const SwitchView = ({
  navigateTo,
  sidebarCollapsed,
  toggleSidebar
}) => {
  // Get switch ID from URL params
  const params = useParams();
  const switchId = params.id || '';

  // State for switch data
  const [switchData, setSwitchData] = useState(null);

  // State for UI interactions
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [lastUpdated, setLastUpdated] = useState('');
  const [visiblePasswords, setVisiblePasswords] = useState({});

  // State for related data
  const [racks, setRacks] = useState([]);
  const [cities, setCities] = useState([]);
  const [countries, setCountries] = useState([]);
  const [selectedPortForTraffic, setSelectedPortForTraffic] = useState(null);
  const [showTrafficModal, setShowTrafficModal] = useState(false);

  // State for switch models - Added this to fix the model fetching issue
  const [switchModels, setSwitchModels] = useState([]);
  const [showAddSwitchModelModal, setShowAddSwitchModelModal] = useState(false);
  const [newSwitchModel, setNewSwitchModel] = useState({ name: '', size: 1 });

  // Add port manually states
  const [showAddPortModal, setShowAddPortModal] = useState(false);
  const [newPortData, setNewPortData] = useState({
    port_number: '',
    port_name: '',
    max_speed: '10000', // Default to 10Gbps
  });
  const [addingPort, setAddingPort] = useState(false);

  // New state for SSH terminal modal
  const [showTerminalModal, setShowTerminalModal] = useState(false);

  // Function to fetch switch data
  const fetchSwitch = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_switches`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response text:', errorText);
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        // Find the specific switch by ID
        const foundSwitch = data.find(item => String(item.id) === String(switchId));

        if (foundSwitch) {
          setSwitchData(foundSwitch);
          console.log("Switch data loaded:", foundSwitch);
          setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
        } else {
          throw new Error(`Switch with ID ${switchId} not found`);
        }
      } else {
        throw new Error('Invalid data format returned from API');
      }

      setLoading(false);
    } catch (err) {
      console.error("Error fetching switch:", err);
      setError(`Failed to load switch: ${err.message}`);
      setLoading(false);
    }
  };

  // Get datacenter name for a city
  const getDatacenterName = (cityId) => {
    if (!cityId || cities.length === 0) return '';

    const cityInfo = cities.find(city => city.id === parseInt(cityId));
    return cityInfo?.datacenter || '';
  };

  // Add function to fetch switch models - similar to the one in InventoryPage.js
  const fetchSwitchModels = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_switch_models`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setSwitchModels(data);
        console.log(`Loaded ${data.length} switch models`);
      } else {
        setSwitchModels([]);
      }
    } catch (err) {
      console.error("Error fetching switch models:", err);
      setSwitchModels([]);
    }
  };

  // Add function to add a new switch model
  const handleAddSwitchModel = async () => {
    try {
      if (!newSwitchModel.name.trim()) {
        alert('Switch model name is required');
        return;
      }

      setLoading(true);
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_switch_model`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          name: newSwitchModel.name,
          size: newSwitchModel.size
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setShowAddSwitchModelModal(false);
        setNewSwitchModel({ name: '', size: 1 });

        // Refresh switch models
        await fetchSwitchModels();

        alert('Switch model added successfully');
      } else {
        alert(result.error || 'Failed to add switch model');
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error("Error adding switch model:", err);
      alert('Failed to add switch model: ' + err.message);
    }
  };

  // Fetch cities
  const fetchCities = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_cities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        console.log("Cities loaded:", data);
        setCities(data);
      }
    } catch (err) {
      console.error("Error fetching cities:", err);
    }
  };

  // Load all required data
  const loadAllData = async () => {
    setLoading(true);
    setError(null);

    try {
      // First fetch cities, countries, and racks for reference data
      await Promise.all([
        fetchCities(),
        fetchCountries(),
        fetchRacks(),
        fetchSwitchModels()
      ]);

      // Then fetch the switch data
      await fetchSwitch();

      setLoading(false);
    } catch (err) {
      console.error("Error loading data:", err);
      setError("Failed to load switch data. Please try again.");
      setLoading(false);
    }
  };


  // Effect to load data when component mounts
  useEffect(() => {
    loadAllData();
  }, [switchId]);

  useEffect(() => {
    // Function to handle inventory data change events
    const handleDataChange = (event) => {
      // If the event relates to ports on this switch
      if (event.detail.switchId === switchId ||
          event.detail.action === 'ports-discovered') {
        // Refresh the SwitchPortsManager component
        console.log("Switch detected port changes, refreshing data");
        loadAllData();
      }
    };

    // Add event listener
    window.addEventListener('inventory-data-change', handleDataChange);

    // Clean up event listener when component unmounts
    return () => {
      window.removeEventListener('inventory-data-change', handleDataChange);
    };
  }, [switchId]); // Only re-run if switchId changes

  // Fetch racks
  const fetchRacks = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_racks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setRacks(data);
      }
    } catch (err) {
      console.error("Error fetching racks:", err);
      setRacks([]);
    }
  };

  // Fetch countries
  const fetchCountries = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_countries`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setCountries(data);
      }
    } catch (err) {
      console.error("Error fetching countries:", err);
    }
  };

  // Update switch handler
  const handleUpdateSwitch = async () => {
    try {
      if (!switchData || !switchData.id) {
        alert('Invalid switch data');
        return;
      }

      setLoading(true);
      const token = localStorage.getItem('admin_token');

      const apiData = {
        token: token,
        ...switchData
      };

      console.log(`Sending update:`, JSON.stringify(apiData, null, 2));

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=update_switch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(apiData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Handle IP allocation after successful switch update
        const ipAllocationWarnings = [];

        // Handle switch IP allocation (only if not empty)
        if (switchData.switch_ip && switchData.switch_ip.trim()) {
          console.log(`Allocating switch IP ${switchData.switch_ip} for switch ${switchData.id} (${switchData.label})`);
          const ipAllocated = await allocateIp(switchData.switch_ip, 'switch', switchData.id, switchData.label || 'Unknown');
          if (!ipAllocated) {
            console.warn(`Failed to allocate switch IP ${switchData.switch_ip}, but switch update was successful`);
            ipAllocationWarnings.push(`Switch IP ${switchData.switch_ip}`);
          } else {
            console.log(`Successfully allocated switch IP ${switchData.switch_ip} for switch ${switchData.id}`);
          }
        }

        // Turn off edit mode
        setIsEditMode(false);

        // Refresh data from server to ensure we have the latest
        fetchSwitch();

        setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
        
        // Show appropriate success message
        if (ipAllocationWarnings.length > 0) {
          alert(`Switch updated successfully, but failed to allocate the following IPs: ${ipAllocationWarnings.join(', ')}`);
        } else {
          alert('Switch updated successfully');
        }
      } else {
        alert(result.error || 'Failed to update switch');
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error('Error updating switch:', err);
      alert(`Failed to update switch: ${err.message}`);
    }
  };

  // IP allocation and deallocation functions
  const allocateIp = async (ipAddress, deviceType, deviceId, deviceLabel) => {
    if (!ipAddress || !deviceId) {
      console.error('Missing IP address or device ID for allocation');
      return false;
    }

    try {
      const token = localStorage.getItem('admin_token');
      
      // assigned_to field contains only the device ID for clean data management
      const manual_alocation = deviceId ? deviceId.toString() : 'unknown';
      
      // notes field can contain descriptive information
      const notes = deviceType === 'switch' ? `Switch ${deviceLabel} (ID: ${deviceId})` : `Server ${deviceLabel} (ID: ${deviceId})`;

      const requestData = {
        token: token,
        ip_address: ipAddress,
        manual_alocation: manual_alocation,
        notes: notes,
        for_server_ipmi: deviceType === 'server',
        for_switch: deviceType === 'switch',
        server_id: deviceType === 'server' ? deviceId : undefined,
        switch_id: deviceType === 'switch' ? deviceId : undefined
      };

      console.log('Allocating IP:', requestData);

      const response = await fetch(`${API_URL}/api_admin_subnets.php?f=allocate_ip`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        console.log(`Successfully allocated IP ${ipAddress} for ${deviceType} ${deviceId}`);
        console.log(`✅ IP marked as used (is_used = 1) in ip_addresses table: ${ipAddress}`);
        return true;
      } else {
        console.warn(`Regular allocation failed for IP ${ipAddress}: ${result.error || 'Unknown error'}`);
        console.log(`Attempting force allocation as fallback...`);

        // Try force allocation as fallback (can create IP if it doesn't exist)
        try {
          const forceAllocateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=force_allocate_ip`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: token,
              ip_address: ipAddress,
              manual_alocation: manual_alocation,
              notes: notes,
              for_server_ipmi: deviceType === 'server',
              for_switch: deviceType === 'switch'
            })
          });

          const forceAllocateResult = await forceAllocateResponse.json();

          if (forceAllocateResult.success) {
            console.log(`✅ Successfully force-allocated IP ${ipAddress} for ${deviceType} ${deviceId}`);
            console.log(`✅ IP marked as used (is_used = 1) in ip_addresses table: ${ipAddress}`);
            return true;
          } else {
            console.error(`❌ Force allocation also failed for IP ${ipAddress}:`, forceAllocateResult.error || 'Unknown error');
            return false;
          }
        } catch (forceErr) {
          console.error(`❌ Error during force allocation for IP ${ipAddress}:`, forceErr);
          return false;
        }
      }
    } catch (error) {
      console.error(`Error allocating IP ${ipAddress}:`, error);
      return false;
    }
  };

  const deallocateIp = async (ipAddress, deviceType, deviceId) => {
    if (!ipAddress) {
      console.error('Missing IP address for deallocation');
      return false;
    }

    try {
      const token = localStorage.getItem('admin_token');

      const requestData = {
        token: token,
        ip_address: ipAddress,
        for_server_ipmi: deviceType === 'server',
        for_switch: deviceType === 'switch'
      };

      console.log('Deallocating IP:', requestData);

      const response = await fetch(`${API_URL}/api_admin_subnets.php?f=force_deallocate_ip`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        console.log(`Successfully deallocated IP ${ipAddress}`);
        return true;
      } else {
        console.error(`Failed to deallocate IP ${ipAddress}:`, result.error);
        return false;
      }
    } catch (error) {
      console.error(`Error deallocating IP ${ipAddress}:`, error);
      return false;
    }
  };

  // Handler for editing field values
  const handleEditSwitchChange = async (e) => {
    const { name, value } = e.target;
    const oldValue = switchData[name];

    // Handle IP address changes - deallocate old IP if changing or clearing
    if (name === 'switch_ip' && oldValue && oldValue !== value && switchData.id) {
      console.log(`Switch IP changed from ${oldValue} to ${value || 'empty'}, deallocating old IP`);
      await deallocateIp(oldValue, 'switch', switchData.id);
    }

    // Create a copy of the switch
    const updatedSwitch = { ...switchData };

    // For numeric fields that should be numbers, convert string to number
    if (['rack_id', 'city_id', 'country_id', 'model_id'].includes(name) && value !== '') {
      updatedSwitch[name] = Number(value);
    } else {
      updatedSwitch[name] = value;
    }

    // Update the switch state
    setSwitchData(updatedSwitch);
  };

  // Toggle password visibility
  const togglePasswordVisibility = (field) => {
    setVisiblePasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  // Go back to inventory list
  const handleBackToInventory = () => {
    navigateTo('/admin/inventory');
  };

  // Switch Model Field Component - Added for model selection
  const SwitchModelField = ({
    isEditMode,
    value,
    onChange,
    switchModels,
    onAddClick,
    label = "Switch Model"
  }) => {
    return (
      <div>
        <div className="text-xs text-gray-500">{label}</div>
        {isEditMode ? (
          <div className="flex">
            <select
              name="model_id"
              value={value || ''}
              onChange={onChange}
              className="font-medium w-full px-2 py-0.5 border border-gray-300 rounded-md text-sm"
            >
              <option value="">Select Switch Model</option>
              {switchModels.map(model => (
                <option key={model.id} value={model.id}>{model.name} ({model.size}U)</option>
              ))}
            </select>
            <button
              type="button"
              onClick={onAddClick}
              className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
              title="Add New Switch Model"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
        ) : (
          <div className="font-medium flex items-center">
            <Network className="w-4 h-4 mr-1.5 text-indigo-700" />
            {switchModels.find(m => m.id === parseInt(value))?.name || 'Not specified'}
            {switchData?.size_ru && ` (${switchData.size_ru}U)`}
          </div>
        )}
      </div>
    );
  };

  // Password Field Component
  const PasswordField = ({
    isEditMode,
    fieldName,
    value,
    onChange,
    placeholder = "••••••••",
    label
  }) => {
    const isVisible = visiblePasswords[fieldName] || false;

    return (
      <div>
        <div className="text-xs text-gray-500">{label}</div>
        {isEditMode ? (
          <div className="relative">
            <input
              type={isVisible ? "text" : "password"}
              name={fieldName}
              value={value || ''}
              onChange={(e) => {
                onChange(e);
              }}
              className="font-medium w-full px-2 py-0.5 pr-8 border border-gray-300 rounded-md text-sm"
              placeholder={placeholder}
            />
            <button
              type="button"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-indigo-700"
              onClick={(e) => {
                e.preventDefault();
                togglePasswordVisibility(fieldName);
              }}
            >
              {isVisible ? (
                <Eye className="w-4 h-4" />
              ) : (
                <EyeOff className="w-4 h-4" />
              )}
            </button>
          </div>
        ) : (
          <div className="font-medium flex items-center">
            <Lock className="w-4 h-4 mr-1.5 text-indigo-700" />
            {value ? (
              <div className="flex items-center">
                <span>{isVisible ? value : placeholder}</span>
                <button
                  type="button"
                  className="ml-2 text-gray-500 hover:text-indigo-700"
                  onClick={() => togglePasswordVisibility(fieldName)}
                >
                  {isVisible ? (
                    <Eye className="w-4 h-4" />
                  ) : (
                    <EyeOff className="w-4 h-4" />
                  )}
                </button>
              </div>
            ) : (
              'Not set'
            )}
          </div>
        )}
      </div>
    );
  };

  // Render status badge
  const renderStatusBadge = (status) => {
    const badgeClasses = {
      'Available': 'bg-green-100 text-green-800',
      'Active': 'bg-green-100 text-green-800', // Keep for backward compatibility
      'In use': 'bg-yellow-100 text-yellow-800',
      'Offline': 'bg-red-100 text-red-800'
    };

    const icons = {
      'Available': <CheckCircle className="w-4 h-4 mr-1" />,
      'Active': <CheckCircle className="w-4 h-4 mr-1" />, // Keep for backward compatibility
      'In use': <Wrench className="w-4 h-4 mr-1" />,
      'Offline': <AlertTriangle className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`px-2 py-0.5 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertTriangle className="w-4 h-4 mr-1" />}
        {status}
      </span>
    );
  };

  // Get country flag emoji
  const getCountryFlag = (countryName) => {
    if (!countryName) return '';

    // Map of country names to ISO country codes
    const countryCodeMap = {
      'Albania': 'AL',
      'Andorra': 'AD',
      'Austria': 'AT',
      'Belarus': 'BY',
      'Belgium': 'BE',
      'Bosnia and Herzegovina': 'BA',
      'Bulgaria': 'BG',
      'Croatia': 'HR',
      'Cyprus': 'CY',
      'Czech Republic': 'CZ',
      'Denmark': 'DK',
      'Estonia': 'EE',
      'Finland': 'FI',
      'France': 'FR',
      'Germany': 'DE',
      'Greece': 'GR',
      'Hungary': 'HU',
      'Iceland': 'IS',
      'Ireland': 'IE',
      'Italy': 'IT',
      'Latvia': 'LV',
      'Liechtenstein': 'LI',
      'Lithuania': 'LT',
      'Luxembourg': 'LU',
      'Malta': 'MT',
      'Moldova': 'MD',
      'Monaco': 'MC',
      'Montenegro': 'ME',
      'Netherlands': 'NL',
      'North Macedonia': 'MK',
      'Norway': 'NO',
      'Poland': 'PL',
      'Portugal': 'PT',
      'Romania': 'RO',
      'Russia': 'RU',
      'San Marino': 'SM',
      'Serbia': 'RS',
      'Slovakia': 'SK',
      'Slovenia': 'SI',
      'Spain': 'ES',
      'Sweden': 'SE',
      'Switzerland': 'CH',
      'Ukraine': 'UA',
      'United Kingdom': 'GB',
      'UK': 'GB',
      'United States': 'US',
      'USA': 'US',
      'Canada': 'CA',
      'Australia': 'AU',
      'Japan': 'JP',
      'China': 'CN',
      'Singapore': 'SG',
      'South Korea': 'KR',
      'India': 'IN',
      'Brazil': 'BR',
      'Mexico': 'MX',
      'South Africa': 'ZA'
    };

    // Get the country code
    const countryCode = countryCodeMap[countryName] || '';

    if (!countryCode) return '';

    // Convert country code to regional indicator symbols (flag emoji)
    const flagEmoji = countryCode
      .toUpperCase()
      .split('')
      .map(char => String.fromCodePoint(char.charCodeAt(0) + 127397))
      .join('');

    return flagEmoji;
  };

  // Get standard speed options for dropdowns
  const getSpeedOptions = () => {
    return [
      { value: 100, label: '100 Mbps (Fast Ethernet)' },
      { value: 1000, label: '1000 Mbps (Gigabit)' },
      { value: 2500, label: '2.5 Gbps' },
      { value: 5000, label: '5 Gbps' },
      { value: 10000, label: '10 Gbps' },
      { value: 25000, label: '25 Gbps' },
      { value: 100000, label: '100 Gbps' },
      { value: 400000, label: '400 Gbps' }
    ];
  };

  // Add a new port manually
  const handleAddPortManually = async () => {
    if (!newPortData.port_number) {
      alert('Port number is required.');
      return;
    }
    
    setAddingPort(true);
    
    try {
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_manual_port`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token,
          switch_id: switchId,
          port_number: newPortData.port_number,
          port_name: newPortData.port_name,
          max_speed: newPortData.max_speed,
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        alert('Port added successfully!');
        setShowAddPortModal(false);
        setNewPortData({ port_number: '', port_name: '', max_speed: '1000' });
        loadAllData(); // Refresh all data, which will re-render SwitchPortsManager
      } else {
        throw new Error(result.error || 'Failed to add port.');
      }
    } catch (error) {
      console.error("Error adding port manually:", error);
      alert(`Failed to add port: ${error.message}`);
    } finally {
      setAddingPort(false);
    }
  };

  // Loading state
  if (loading && !switchData) {
    return (
      <div className="flex h-screen bg-gray-100">
        <Sidebar
          sidebarCollapsed={sidebarCollapsed}
          activeMenu="Inventory"
          navigateTo={navigateTo}
          toggleSidebar={toggleSidebar}
        />
        <div className="flex-1 flex flex-col">
          <TopMenu toggleSidebar={toggleSidebar} />
          <div className="p-4 flex items-center justify-center h-full">
            <div className="text-center">
              <RefreshCw className="w-12 h-12 animate-spin mx-auto text-indigo-700 mb-2" />
              <p className="text-lg text-gray-600">Loading switch details...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex h-screen bg-gray-100">
        <Sidebar
          sidebarCollapsed={sidebarCollapsed}
          activeMenu="Inventory"
          navigateTo={navigateTo}
          toggleSidebar={toggleSidebar}
        />
        <div className="flex-1 flex flex-col">
          <TopMenu toggleSidebar={toggleSidebar} />
          <div className="p-4 flex items-center justify-center h-full">
            <div className="text-center max-w-md">
              <div className="bg-red-100 p-3 rounded-lg mb-3">
                <AlertTriangle className="w-12 h-12 text-red-600 mx-auto mb-2" />
                <p className="text-lg text-red-700 font-medium">{error}</p>
              </div>
              <button
                onClick={handleBackToInventory}
                className="mt-3 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-1.5 rounded-md text-sm font-medium transition-colors"
              >
                Back to Inventory
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        activeMenu="Inventory"
        navigateTo={navigateTo}
        toggleSidebar={toggleSidebar}
      />
      <div className="flex-1 flex flex-col bg-gray-100">
        <TopMenu toggleSidebar={toggleSidebar} />

        <div className="p-3 space-y-3 overflow-auto">
          {/* Header with back button */}
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <button
                onClick={handleBackToInventory}
                className="mr-3 bg-white p-2 rounded-full shadow-sm hover:bg-gray-50"
              >
                <ChevronLeft className="w-5 h-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-xl font-bold text-gray-800">
                  {switchData?.label || 'Switch Details'}
                </h1>
                <div className="flex items-center text-sm text-gray-500">
                  <span className="mr-2">ID: {switchData?.id}</span>
                  <span className="mx-2">•</span>
                  <span className="flex items-center">
                    <Network className="w-4 h-4 mr-1" />
                    Network Switch
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <div className="text-sm text-gray-500">Last updated: {lastUpdated}</div>
              <button
                onClick={() => loadAllData()}
                className="p-1.5 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </button>
              {/* SSH Terminal Button */}
              <button
                className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md flex items-center text-sm transition-colors"
                onClick={() => setShowTerminalModal(true)}
              >
                <Terminal className="w-4 h-4 mr-1" />
                SSH
              </button>
              {!isEditMode ? (
                <button
                  className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md flex items-center text-sm transition-colors"
                  onClick={() => setIsEditMode(true)}
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </button>
              ) : (
                <>
                  <button
                    className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                    onClick={() => {
                      setIsEditMode(false);
                      // Reload switch to reset any changes
                      fetchSwitch();
                    }}
                  >
                    <X className="w-4 h-4 mr-1" />
                    Cancel
                  </button>
                  <button
                    className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md flex items-center text-sm transition-colors"
                    onClick={handleUpdateSwitch}
                    disabled={loading}
                  >
                    <Save className="w-4 h-4 mr-1" />
                    Save
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Status */}
          <div className="bg-white rounded-lg shadow-sm p-3 flex justify-between items-center">
            <div className="flex items-center">
              {isEditMode ? (
                <select
                  name="status"
                  value={switchData?.status || 'Available'}
                  onChange={handleEditSwitchChange}
                  className="px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                >
                  <option value="Available">Available</option>
                  <option value="In use">In use</option>
                  <option value="Offline">Offline</option>
                </select>
              ) : (
                renderStatusBadge(switchData?.status || 'Available')
              )}
            </div>

            <div className="flex items-center">
              <span className="text-sm font-medium text-gray-600 mr-2">IP Address:</span>
              <span className="text-sm text-indigo-700">{switchData?.switch_ip || 'Not set'}</span>
            </div>
          </div>

          {/* First row: Switch Information and Location Information side by side */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 mb-3">
            {/* Switch Information */}
            <div className="bg-white rounded-lg shadow-sm p-3">
              <h4 className="text-sm font-semibold text-gray-800 mb-2 border-b pb-1 flex items-center">
                <Network className="w-4 h-4 inline mr-1 text-indigo-700" />
                Switch Information
              </h4>
              <div className="space-y-2">
                <div>
                  <div className="text-xs text-gray-500">Label</div>
                  {isEditMode ? (
                    <input
                      type="text"
                      name="label"
                      value={switchData?.label || ''}
                      onChange={handleEditSwitchChange}
                      className="font-medium w-full px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                    />
                  ) : (
                    <div className="font-medium">{switchData?.label}</div>
                  )}
                </div>

                {/* Switch Model Field - Enhanced to use model selection instead of free text */}
                <SwitchModelField
                  isEditMode={isEditMode}
                  value={switchData?.model_id || ''}
                  onChange={handleEditSwitchChange}
                  switchModels={switchModels}
                  onAddClick={() => setShowAddSwitchModelModal(true)}
                  label="Switch Model"
                />

                <div>
                  <div className="text-xs text-gray-500">IP Address</div>
                  {isEditMode ? (
                    <CountryIpSelector
                      selectedItem={switchData}
                      onChange={handleEditSwitchChange}
                      name="switch_ip"
                      value={switchData?.switch_ip || ''}
                      label=""
                      placeholder="Select a Switch IP address"
                    />
                  ) : (
                    <div className="font-medium flex items-center">
                      <Network className="w-4 h-4 mr-1.5 text-indigo-700" />
                      {switchData?.switch_ip || 'Not assigned'}
                    </div>
                  )}
                </div>
                <PasswordField
                  isEditMode={isEditMode}
                  fieldName="root_password"
                  value={switchData?.root_password || ''}
                  onChange={handleEditSwitchChange}
                  placeholder="••••••••"
                  label="Root Password"
                />

              </div>
            </div>

            {/* Location Information */}
            <div className="bg-white rounded-lg shadow-sm p-3">
              <h4 className="text-sm font-semibold text-gray-800 mb-2 border-b pb-1 flex items-center">
                <Building className="w-4 h-4 inline mr-1 text-indigo-700" />
                Location Information
              </h4>
              <div className="space-y-2">
                <div>
                  <div className="text-xs text-gray-500">Datacenter</div>
                  {isEditMode ? (
                    <select
                      name="city_id"
                      value={switchData?.city_id || ''}
                      onChange={handleEditSwitchChange}
                      className="font-medium w-full px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="">Select City/Datacenter</option>
                      {cities.map(city => (
                        <option key={city.id} value={city.id}>
                          {city.city} {city.datacenter ? `(${city.datacenter})` : ''}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <div className="font-medium flex items-center">
                      <Building className="w-4 h-4 mr-1.5 text-indigo-700" />
                      {switchData?.city_name} {(() => {
                        // Get datacenter name from cities array
                        const datacenter = getDatacenterName(switchData?.city_id);
                        return datacenter ? `(${datacenter})` : '';
                      })()}
                    </div>
                  )}
                </div>

                <div>
                  <div className="text-xs text-gray-500">Country</div>
                  {isEditMode ? (
                    <select
                      name="country_id"
                      value={switchData?.country_id || ''}
                      onChange={handleEditSwitchChange}
                      className="font-medium w-full px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="">Select Country</option>
                      {countries.map(country => (
                        <option key={country.id} value={country.id}>{country.country}</option>
                      ))}
                    </select>
                  ) : (
                    <div className="font-medium flex items-center">
                      <Globe className="w-4 h-4 mr-1.5 text-indigo-700" />
                      {switchData?.country_name} {getCountryFlag(switchData?.country_name)}
                    </div>
                  )}
                </div>

                <div>
                  <div className="text-xs text-gray-500">Rack</div>
                  {isEditMode ? (
                    <div className="flex gap-2">
                      <select
                        name="rack_id"
                        value={switchData?.rack_id || ''}
                        onChange={handleEditSwitchChange}
                        className="font-medium w-2/3 px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                      >
                        <option value="">Select Rack</option>
                        {racks.map(rack => (
                          <option key={rack.id} value={rack.id}>{rack.rack_name}</option>
                        ))}
                      </select>
                      <input
                        type="text"
                        name="position_in_rack"
                        value={switchData?.position_in_rack || ''}
                        onChange={handleEditSwitchChange}
                        className="font-medium w-1/3 px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                        placeholder="Position"
                      />
                    </div>
                  ) : (
                    <div className="font-medium flex items-center">
                      <Server className="w-4 h-4 mr-1.5 text-indigo-700" />
                      {switchData?.rack_name}
                      {switchData?.position_in_rack ? `(${switchData.position_in_rack})` : ''}
                    </div>
                  )}
                </div>

                <div>
                <PasswordField
                  isEditMode={isEditMode}
                  fieldName="snmp_community"
                  value={switchData?.snmp_community || ''}
                  onChange={handleEditSwitchChange}
                  placeholder="••••••••"
                  label="SNMP Community String"
                />
                </div>

              </div>
            </div>
          </div>

          {/* Second row: Port Management */}
          <div className="bg-white rounded-lg shadow-sm p-3 mb-3">
            <h4 className="text-sm font-semibold text-gray-800 mb-2 border-b pb-1 flex items-center">
              <Activity className="w-4 h-4 inline mr-1 text-indigo-700" />
              Port Management
            </h4>
            {switchData && (
              <SwitchPortsManager
                switchId={switchData.id}
                onPortSelected={(port) => {
                  setSelectedPortForTraffic(port);
                  setShowTrafficModal(true);
                }}
                onAddPort={() => setShowAddPortModal(true)}
              />
            )}
            

          </div>

          {/* Third row: Notes */}
          <div className="bg-white rounded-lg shadow-sm p-3">
            <h4 className="text-sm font-semibold text-gray-800 mb-2 border-b pb-1 flex items-center">
              <Edit className="w-4 h-4 inline mr-1 text-indigo-700" />
              Notes
            </h4>
            {isEditMode ? (
              <textarea
                name="notes"
                value={switchData?.notes || ''}
                onChange={handleEditSwitchChange}
                rows="3"
                className="w-full px-2 py-0.5 border border-gray-300 rounded-md text-sm"
              ></textarea>
            ) : (
              <div className="text-sm text-gray-700 whitespace-pre-line bg-gray-50 p-2 rounded min-h-[80px]">
                {switchData?.notes || 'No notes available'}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Traffic Modal */}
      {showTrafficModal && selectedPortForTraffic && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-11/12 max-w-6xl mx-4 overflow-hidden flex flex-col max-h-[90vh]">
            <div className="p-4 border-b bg-gray-50 flex justify-between items-center sticky top-0 z-10">
              <h2 className="text-lg font-bold flex items-center">
                <Activity className="w-5 h-5 mr-2 text-indigo-600" />
                Port {selectedPortForTraffic.port_number} Traffic
              </h2>
              <button
                onClick={() => setShowTrafficModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>

            <div className="p-5 overflow-y-auto flex-grow">
              <div className="bg-gray-50 p-3 rounded-md mb-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <div className="text-xs text-gray-500">Port Number</div>
                    <div className="font-medium">{selectedPortForTraffic.port_number}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Port Name</div>
                    <div className="font-medium">{selectedPortForTraffic.port_name || '-'}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Status</div>
                    <div className="font-medium">
                      <span className={`px-2 py-0.5 rounded-full text-xs font-medium inline-flex items-center
                        ${selectedPortForTraffic.status === 'Available' ? 'bg-green-100 text-green-800' :
                          selectedPortForTraffic.status === 'Used' ? 'bg-blue-100 text-blue-800' :
                          'bg-red-100 text-red-800'}`}>
                        <span className={`w-2 h-2 rounded-full mr-1.5
                          ${selectedPortForTraffic.status === 'Available' ? 'bg-green-500' :
                            selectedPortForTraffic.status === 'Used' ? 'bg-blue-500' :
                            'bg-red-500'}`} />
                        {selectedPortForTraffic.status}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <PortTrafficGraph port_id={selectedPortForTraffic.id} days={7} />
            </div>
          </div>
        </div>
      )}

      {/* Add Switch Model Modal */}
      {showAddSwitchModelModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold">Add New Switch Model</h3>
              <button
                onClick={() => {
                  setShowAddSwitchModelModal(false);
                  setNewSwitchModel({ name: '', size: 1 });
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Switch Model Name</label>
              <input
                type="text"
                value={newSwitchModel.name}
                onChange={(e) => setNewSwitchModel({...newSwitchModel, name: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="e.g. Cisco Nexus 9396TX"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Size (U)</label>
              <input
                type="number"
                min="1"
                max="10"
                value={newSwitchModel.size}
                onChange={(e) => setNewSwitchModel({...newSwitchModel, size: parseInt(e.target.value)})}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="e.g. 1"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={() => {
                  setShowAddSwitchModelModal(false);
                  setNewSwitchModel({ name: '', size: 1 });
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleAddSwitchModel}
                className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Switch Model
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Port Modal */}
      {showAddPortModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-4">
            <h3 className="text-lg font-bold mb-4">Add Port Manually</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Port Number</label>
                <input
                  type="text"
                  placeholder="e.g., Gi1/0/1 or 1"
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  value={newPortData.port_number}
                  onChange={(e) => setNewPortData({ ...newPortData, port_number: e.target.value })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Port Name / Description</label>
                <input
                  type="text"
                  placeholder="e.g., Uplink to Core"
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  value={newPortData.port_name}
                  onChange={(e) => setNewPortData({ ...newPortData, port_name: e.target.value })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Max Speed (Mbps)</label>
                <select
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  value={newPortData.max_speed}
                  onChange={(e) => setNewPortData({ ...newPortData, max_speed: e.target.value })}
                >
                  {getSpeedOptions().map(opt => (
                    <option key={opt.value} value={opt.value}>{opt.label}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <button
                className="px-3 py-1.5 border border-gray-300 bg-white rounded-md text-gray-700 text-sm hover:bg-gray-50"
                onClick={() => setShowAddPortModal(false)}
                disabled={addingPort}
              >
                Cancel
              </button>
              <button
                className={`px-3 py-1.5 rounded-md text-white text-sm flex items-center
                  ${addingPort ? 'bg-indigo-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'}`}
                onClick={handleAddPortManually}
                disabled={addingPort}
              >
                {addingPort ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-1.5 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <Check className="w-4 h-4 mr-1.5" />
                    Add Port
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

export default SwitchView;