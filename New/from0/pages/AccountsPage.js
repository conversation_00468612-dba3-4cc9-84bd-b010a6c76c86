import React, { useState, useEffect, useRef } from 'react';
import {
  Search,
  Plus,
  Filter,
  ArrowUp,
  ArrowDown,
  MoreVertical,
  Eye,
  Shield,
  UserPlus,
  Users,
  User,
  Lock,
  CheckCircle,
  AlertTriangle,
  AlertCircle,
  Globe,
  Building,
  Calendar,
  XCircle,
  RefreshCw,
  Edit,
  Trash2,
  UserCheck,
  Save,
  X,
  FileText,
  Activity,
  Phone,
  MapPin,
  Wallet,
  Server,
  Mail
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import InvoicesTab from '../components/InvoicesTab';
import CreditsTab from '../components/CreditsTab';
import ServicesTab from '../components/ServicesTab';
import Pagination from '../components/Pagination';
import { API_URL } from '../config';
const AccountsPage = ({ navigateTo, sidebarCollapsed, toggleSidebar }) => {
  // State for accounts data
  const [accounts, setAccounts] = useState([]);
  const [admins, setAdmins] = useState([]);
  const [selectedTab, setSelectedTab] = useState('users');

  // State for UI interactions
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedRole, setSelectedRole] = useState('All');
  const [sortField, setSortField] = useState('lastLogin');
  const [sortDirection, setSortDirection] = useState('desc');
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [selectedAdmin, setSelectedAdmin] = useState(null);
  const [lastUpdated, setLastUpdated] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeModalTab, setActiveModalTab] = useState('info');
  // State for admin management modals
  const [addAdminModalOpen, setAddAdminModalOpen] = useState(false);
  const [editAdminModalOpen, setEditAdminModalOpen] = useState(false);

  // State for new admin
  const [newAdmin, setNewAdmin] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    function: '',
    departmentId: '',
    assignedDepartments: []
  });

  // State for edit admin
  const [editAdmin, setEditAdmin] = useState({
    id: '',
    email: '',
    firstName: '',
    lastName: '',
    function: '',
    departmentId: '',
    assignedDepartments: []
  });

  // State for user profile editing
  const [editUser, setEditUser] = useState(null);
  const [editUserModalOpen, setEditUserModalOpen] = useState(false);

  // State for departments (used in admin forms)
  const [departments, setDepartments] = useState([]);

  // Get unique statuses and roles
  const [uniqueStatuses, setUniqueStatuses] = useState(['All']);
  const [uniqueRoles, setUniqueRoles] = useState(['All']);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);
  const [totalPages, setTotalPages] = useState(1);

  // Stats
  const [accountStats, setAccountStats] = useState({
    totalAccounts: 0,
    activeUsers: 0,
    inactiveUsers: 0,
    lockedAccounts: 0
  });
  const [addUserModalOpen, setAddUserModalOpen] = useState(false);
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    companyName: '',
    vatId: '',
    status: 'Active',
    country: '',
    city: '',
    phone: '',
    address: '',
    role: 'Customer',
    notes: ''
  });
  // Load initial data
  useEffect(() => {
    loadAllData();

    // Check if we need to open a specific account (from search)
    const openAccountId = localStorage.getItem('open_account_id');
    if (openAccountId) {
      // Clear the localStorage item to prevent reopening on page refresh
      localStorage.removeItem('open_account_id');

      // Find the account in the data
      const findAndOpenAccount = async () => {
        try {
          // Wait for the initial data load to complete
          await loadAllData();

          // Find the account in the loaded data first
          // Convert both to strings for comparison to handle numeric IDs
          const foundAccount = accounts.find(acc => String(acc.id) === String(openAccountId));

          if (foundAccount) {
            console.log("Found account in loaded data:", foundAccount);
            setSelectedAccount(foundAccount);
            setActiveModalTab('info'); // Make sure the info tab is active
          } else {
            // If not found in loaded data, fetch accounts again
            console.log("Account not found in loaded data, fetching accounts again");

            // Wait for accounts to be loaded
            await fetchAccounts();

            // Try to find the account in the loaded data again
            const foundAccountInAll = accounts.find(acc => String(acc.id) === String(openAccountId));

            if (foundAccountInAll) {
              console.log("Found account in all accounts:", foundAccountInAll);
              setTimeout(() => {
                setSelectedAccount(foundAccountInAll);
                setActiveModalTab('info'); // Make sure the info tab is active
              }, 500);
              return;
            }

            // If still not found, make a direct API call to get the account
            console.log("Account not found in any data, making direct API call");

            // Create a complete minimal account object with the ID as a placeholder
            const minimalAccount = {
              id: openAccountId,
              name: "Loading...",
              email: "<EMAIL>",
              status: "Active",
              role: "Customer",
              created: new Date().toISOString(),
              last_login: null,
              company_name: "Not specified",
              first_name: "Loading",
              last_name: "...",
              address: "Not specified",
              city: "Not specified",
              country: "Not specified",
              phone: "Not specified",
              vat_id: "Not specified",
              notes: "No notes available",
              last_ip: ""
            };

            console.log("Created minimal account object:", minimalAccount);

            // Set the minimal account first to show something to the user
            setSelectedAccount(minimalAccount);
            setActiveModalTab('info');

            // Then make a direct API call to get the actual account data
            try {
              console.log(`Making direct API call to get account with ID: ${openAccountId}`);

              // Try a simpler, more direct approach
              console.log("Making direct API call with XMLHttpRequest");

              // Use XMLHttpRequest for more control
              const xhr = new XMLHttpRequest();
              xhr.open('GET', `${API_URL}/api_accounts.php?f=get_account_by_id&id=${openAccountId}&token=${localStorage.getItem('admin_token')}`, true);

              xhr.onreadystatechange = function() {
                console.log(`XHR state changed: readyState=${xhr.readyState}, status=${xhr.status}`);

                if (xhr.readyState === 4) {
                  console.log("XHR complete, status:", xhr.status);
                  console.log("Response headers:", xhr.getAllResponseHeaders());
                  console.log("Response text:", xhr.responseText);

                  if (xhr.status === 200) {
                    try {
                      const accountData = JSON.parse(xhr.responseText);
                      console.log("Successfully parsed account data:", accountData);

                      if (accountData && accountData.id) {
                        // Create a complete account object with all required fields
                        const processedAccount = {
                          // Start with default values for all fields
                          id: openAccountId,
                          name: "Account #" + openAccountId,
                          email: accountData.email || `account${openAccountId}@example.com`,
                          status: "Active",
                          role: "Customer",
                          created: new Date().toISOString(),
                          last_login: null,
                          company_name: "Not specified",
                          first_name: "",
                          last_name: "",
                          address: "Not specified",
                          city: "Not specified",
                          country: "Not specified",
                          phone: "Not specified",
                          vat_id: "Not specified",
                          notes: "No notes available",
                          last_ip: "",

                          // Then override with any actual data we received
                          ...accountData,

                          // Ensure name is properly formatted
                          name: accountData.name || (
                            (accountData.first_name && accountData.last_name)
                              ? `${accountData.first_name} ${accountData.last_name}`
                              : (accountData.first_name || accountData.last_name || accountData.email || 'Account #' + openAccountId)
                          )
                        };

                        console.log("Final processed account:", processedAccount);
                        setSelectedAccount(processedAccount);
                      } else {
                        console.error("Account data missing ID:", accountData);
                      }
                    } catch (parseError) {
                      console.error("Error parsing account data:", parseError);
                    }
                  } else {
                    console.error("XHR request failed with status:", xhr.status);
                  }
                }
              };

              xhr.onerror = function() {
                console.error("XHR request failed with network error");
              };

              // Send the request
              console.log("Sending XHR request");
              xhr.send();
            } catch (error) {
              console.error("Error in direct API call:", error);
            }
          }
        } catch (error) {
          console.error("Error fetching account:", error);
        }
      };

      findAndOpenAccount();
    }
  }, []);

  // Load all required data
  const loadAllData = async () => {
    setLoading(true);
    setError(null);

    try {
      if (selectedTab === 'users') {
        await fetchAccounts();
        await fetchAccountStats();
      } else {
        await fetchAdmins();
        await fetchDepartments();
      }

      setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
      setLoading(false);
    } catch (err) {
      console.error("Error loading data:", err);
      setError("Failed to load account data. Please try again.");
      setLoading(false);
    }
  };

  // Effect to reload data when tab changes
  useEffect(() => {
    loadAllData();
    setCurrentPage(1); // Reset pagination when switching tabs
  }, [selectedTab]);

  // Separate effect to handle opening an account from search
  // This prevents the infinite loop by not depending on the accounts array
  useEffect(() => {
    // Check if we need to open a specific account (fallback mechanism)
    if (selectedTab === 'users') {
      const openAccountId = localStorage.getItem('open_account_id');
      if (openAccountId) {
        // Only process this once by removing it immediately
        localStorage.removeItem('open_account_id');

        console.log("Checking for account ID:", openAccountId);

        // Use a ref to track if we've already tried to open this account
        const accountIdRef = useRef(openAccountId);

        // Only proceed if we haven't already tried to open this account
        if (accountIdRef.current === openAccountId) {
          accountIdRef.current = null; // Mark as processed

          // Find the account in the loaded data
          // Convert both to strings for comparison to handle numeric IDs
          const foundAccount = accounts.find(acc => String(acc.id) === String(openAccountId));

          if (foundAccount) {
            console.log("Found account in accounts data:", foundAccount);
            setTimeout(() => {
              setSelectedAccount(foundAccount);
              setActiveModalTab('info');
            }, 300);
          } else if (accounts.length > 0) {
            // Only try to fetch again if we already have some accounts loaded
            // but couldn't find the specific one
            console.log("Account not found in accounts data, fetching once more");

            // Use a one-time fetch that doesn't trigger the useEffect again
            const fetchAccountOnce = async () => {
              try {
                console.log(`Fetching account with ID: ${openAccountId} directly from API`);

                const response = await fetch(`${API_URL}/api_accounts.php?f=get_account_by_id&id=${openAccountId}`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                    token: localStorage.getItem('admin_token'),
                    id: openAccountId // Also include ID in the body
                  })
                });

                if (response.ok) {
                  const responseText = await response.text();
                  console.log("API response text:", responseText);

                  try {
                    const accountData = JSON.parse(responseText);
                    console.log("Parsed account data:", accountData);

                    if (accountData && accountData.id) {
                      console.log("Fetched account directly:", accountData);

                      // The API now returns properly formatted status, so we don't need to convert it
                      const processedAccount = {
                        ...accountData,
                        // The API now adds the name field, but we'll keep this as a fallback
                        name: accountData.name || (
                          (accountData.first_name && accountData.last_name)
                            ? `${accountData.first_name} ${accountData.last_name}`
                            : (accountData.first_name || accountData.last_name || accountData.email || 'Unknown')
                        ),
                        role: accountData.role || 'Customer',
                        // Ensure all required fields exist
                        id: accountData.id,
                        email: accountData.email || '<EMAIL>',
                        first_name: accountData.first_name || '',
                        last_name: accountData.last_name || '',
                        company_name: accountData.company_name || '',
                        vat_id: accountData.vat_id || '',
                        phone: accountData.phone || '',
                        address: accountData.address || '',
                        city: accountData.city || '',
                        country: accountData.country || '',
                        notes: accountData.notes || '',
                        created: accountData.created || new Date().toISOString(),
                        last_login: accountData.last_login || null,
                        last_ip: accountData.last_ip || '',
                        status: accountData.status || 'Active'
                      };

                      console.log("Processed account data:", processedAccount);

                      setSelectedAccount(processedAccount);
                      setActiveModalTab('info');
                    }
                  } catch (parseError) {
                    console.error("Error parsing account data:", parseError);
                  }
                }
              } catch (error) {
                console.error("Error fetching account directly:", error);
              }
            };

            fetchAccountOnce();
          }
        }
      }
    }
  }, [selectedTab, accounts.length]);

  // Fetch user accounts
// Enhanced function to fetch user accounts with all relevant fields
// Enhanced function to fetch user accounts with all relevant fields
const fetchAccounts = async () => {
  try {
    setLoading(true);
    const token = localStorage.getItem('admin_token');

    // Format the request exactly like the working admin functions
    const response = await fetch(`${API_URL}/api_accounts.php?f=get_accounts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const data = await response.json();

    // Check if data is an error response
    if (data.error && data.error === 5) {
      throw new Error('Authentication failed');
    }

    // Check if data contains accounts array or is itself the accounts array
    const accountsData = data.accounts || data;

    if (Array.isArray(accountsData)) {
      // Process the returned accounts to ensure consistent structure
      const processedAccounts = accountsData.map(account => {
        // Create a standardized account object with all possible fields
        const fullName = account.first_name && account.last_name
          ? `${account.first_name} ${account.last_name}`
          : (account.first_name || account.last_name || account.email || 'Unknown');

        return {
          // Preserve original data
          ...account,
          // Add normalized fields
          name: fullName,
          vat_id: account.vat_id || account.vatId || null,
          phone: account.phone || null,
          status: account.status === 1 ? 'Active' :
                 (account.status === 0 ? 'Inactive' :
                 (account.status === 2 ? 'Locked' :
                 (account.status === 3 ? 'Pending' : 'Unknown'))),
          role: account.role || 'Customer',
          // Format dates if available
          created: account.created_at || account.created || new Date().toISOString(),
          last_login: account.last_login || null
        };
      });

      setAccounts(processedAccounts);

      // Extract unique statuses and roles
      const statuses = new Set(['All', ...processedAccounts.map(account => account.status)]);
      const roles = new Set(['All', ...processedAccounts.map(account => account.role)]);
      setUniqueStatuses(Array.from(statuses));
      setUniqueRoles(Array.from(roles));

      // Store pagination info if available
      if (data.pagination) {
        console.log('Pagination info:', data.pagination);
        // You can use this for pagination controls
      }

      console.log('Accounts loaded successfully:', processedAccounts.length);
    } else {
      // If we get an object instead of an array, it might be an error
      throw new Error(data.error || 'Unexpected response format');
    }

  } catch (err) {
    console.error("Error fetching accounts:", err);
    setError(`Failed to load account data: ${err.message}`);

    // Set fallback data
    setAccounts([]);
    setUniqueStatuses(['All', 'Active', 'Inactive', 'Locked', 'Pending']);
    setUniqueRoles(['All', 'Customer', 'Vendor', 'Partner', 'Guest']);
  } finally {
    setLoading(false);
  }
};

const handleOpenAddUserModal = () => {
  setAddUserModalOpen(true);
};

// Handle closing the add user modal
const handleCloseAddUserModal = () => {
  setAddUserModalOpen(false);
  setNewUser({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    companyName: '',
    vatId: '',
    status: 'Active',
    country: '',
    city: '',
    phone: '',
    address: '',
    role: 'Customer',
    notes: ''
  });
};

// Handle changes in new user form
const handleNewUserChange = (e) => {
  const { name, value } = e.target;
  setNewUser({
    ...newUser,
    [name]: value
  });
};



const handleAddUser = async () => {
  // Validate all required fields based on database schema
  if (!newUser.email || !newUser.password || !newUser.firstName ||
      !newUser.lastName || !newUser.address || !newUser.city ||
      !newUser.country) {
    alert('Please fill in all required fields: Email, Password, First Name, Last Name, Address, City, and Country');
    return;
  }

  // Validate country code (must be 2 characters)
  if (newUser.country.length !== 2) {
    alert('Country must be a 2-letter country code (ISO 3166-1 alpha-2)');
    return;
  }

  if (newUser.password !== newUser.confirmPassword) {
    alert('Passwords do not match');
    return;
  }

  try {
    // Format the user data according to expected API format
    const userData = {
      token: localStorage.getItem('admin_token'),
      email: newUser.email,
      password: newUser.password,
      first_name: newUser.firstName,
      last_name: newUser.lastName,
      address: newUser.address,
      city: newUser.city,
      country: newUser.country.toUpperCase(), // Ensure country code is uppercase
      company_name: newUser.companyName || '', // Optional but included
      vat_id: newUser.vatId || '', // Optional but included
      status: newUser.status === 'Active' ? 1 :
              newUser.status === 'Inactive' ? 0 :
              newUser.status === 'Locked' ? 2 :
              newUser.status === 'Pending' ? 3 : 1, // Convert to numeric status
    };

    // Add any other optional fields if they have values
    if (newUser.phone) userData.phone = newUser.phone;
    if (newUser.role) userData.role = newUser.role;
    if (newUser.notes) userData.notes = newUser.notes;

    console.log('Sending user data:', userData);

    // Use the new endpoint
    const response = await fetch(`${API_URL}/api_accounts.php?f=add_user_admin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const result = await response.json();
    console.log('API response:', result);

    if (result.success) {
      // Close modal and refresh data
      handleCloseAddUserModal();
      fetchAccounts();

    } else {
      console.error('API error:', result);
      alert(result.error || 'Failed to add user');
    }

  } catch (err) {
    console.error("Error adding user:", err);
    alert('Failed to add user: ' + err.message);
  }
};


  // Fetch admin accounts
  const fetchAdmins = async () => {
    try {
      const response = await fetch(`${API_URL}/api_accounts.php?f=get_admins`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      setAdmins(data);

    } catch (err) {
      console.error("Error fetching admins:", err);
      throw err;
    }
  };

  // Fetch departments for admin forms
  const fetchDepartments = async () => {
    try {
      const response = await fetch(`${API_URL}/api_accounts.php?f=get_departments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      setDepartments(data);

    } catch (err) {
      console.error("Error fetching departments:", err);
      throw err;
    }
  };

  // Fetch account statistics
  const fetchAccountStats = async () => {
    try {
      const response = await fetch(`${API_URL}/api_accounts.php?f=get_account_stats`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      setAccountStats(data);

    } catch (err) {
      console.error("Error fetching account stats:", err);
      throw err;
    }
  };

  // Impersonate a user in the client portal
  const handleImpersonateUser = async (userId) => {
    // Open new tab immediately during user interaction to avoid popup blockers
    const newTab = window.open('about:blank', '_blank');

    try {
      const response = await fetch(`${API_URL}/api_accounts.php?f=impersonate_user`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          user_id: userId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();
      if (result.success && result.user_token) {
        const clientUrl = `https://client.x-zoneit.ro/?impersonate_token=${encodeURIComponent(result.user_token)}`;
        // Navigate the pre-opened tab to the client portal
        newTab.location.href = clientUrl;
      } else {
        // Close the blank tab if there was an error
        newTab.close();
        alert(result.error || 'Failed to impersonate user');
      }
    } catch (err) {
      // Close the blank tab if there was an error
      newTab.close();
      console.error('Error impersonating user:', err);
      alert('Failed to impersonate user: ' + err.message);
    }
  };


  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleStatusFilter = (status) => {
    setSelectedStatus(status);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleRoleFilter = (role) => {
    setSelectedRole(role);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    // Reset to first page when sorting changes
    setCurrentPage(1);
  };

  // Pagination handlers
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    const maxPages = selectedTab === 'admins' ? totalAdminPages : totalAccountPages;
    if (currentPage < maxPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleRefreshData = () => {
    loadAllData();
  };

  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <ArrowUp className="w-4 h-4 inline ml-1" /> :
      <ArrowDown className="w-4 h-4 inline ml-1" />;
  };

  const handleAccountClick = (account) => {
    setSelectedAccount(account);
  };

  const handleAdminClick = (admin) => {
    setSelectedAdmin(admin);
  };

  const closeAccountDetails = () => {
    setSelectedAccount(null);
  };

  const closeAdminDetails = () => {
    setSelectedAdmin(null);
  };

  // Handle opening the add admin modal
  const handleOpenAddAdminModal = () => {
    setAddAdminModalOpen(true);
  };

  // Handle closing the add admin modal
  const handleCloseAddAdminModal = () => {
    setAddAdminModalOpen(false);
    setNewAdmin({
      email: '',
      password: '',
      confirmPassword: '',
      firstName: '',
      lastName: '',
      function: '',
      departmentId: '',
      assignedDepartments: []
    });
  };

  // Handle opening the edit admin modal
  const handleOpenEditAdminModal = async (admin) => {
    console.log('Opening edit modal for admin:', admin);

    // Ensure all required fields are present and have default values if missing
    setEditAdmin({
      id: admin.id,
      email: admin.email || '',
      firstName: admin.first_name || '',
      lastName: admin.last_name || '',
      function: admin.function || '',
      departmentId: admin.department_id || '',
      assignedDepartments: []
    });

    // Fetch assigned departments for this admin
    try {
      const response = await fetch(`${API_URL}/api_accounts.php?f=get_admin_assigned_departments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          admin_id: admin.id
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      console.log('Assigned departments:', data);

      // Update the editAdmin state with assigned departments
      setEditAdmin(prev => ({
        ...prev,
        assignedDepartments: data.assigned_departments?.map(dept => dept.id) || []
      }));
    } catch (err) {
      console.error('Error fetching assigned departments:', err);
      // Continue with empty assigned departments
    }

    setEditAdminModalOpen(true);
  };

  // Handle closing the edit admin modal
  const handleCloseEditAdminModal = () => {
    setEditAdminModalOpen(false);
  };

  // Open the edit user modal
// 1. Improved function to open the edit user modal
const handleOpenEditUserModal = (user) => {
  // Map the user data more comprehensively to the form structure
  // Properly extract fields based on the actual API data structure
  setEditUser({
    id: user.id,
    email: user.email || '',
    firstName: user.first_name || '',
    lastName: user.last_name || '',
    companyName: user.company_name || '',
    vatId: user.vat_id || '',
    status: user.status || 'Active',
    country: user.country || '',
    city: user.city || '',
    // Add any additional fields that should be editable
    phone: user.phone || '',
    address: user.address || '',
    role: user.role || 'Customer',
    notes: user.notes || ''
  });

  setEditUserModalOpen(true);
};

// 2. Improved function to update a user

const handleUpdateUser = async () => {
  try {
    // Use snake_case field names to match PHP backend expectations
    const userData = {
      token: localStorage.getItem('admin_token'),
      user_id: editUser.id,
      email: editUser.email,
      first_name: editUser.firstName,
      last_name: editUser.lastName,
      company_name: editUser.companyName,
      vat_id: editUser.vatId,
      country: editUser.country,
      city: editUser.city,
      status: editUser.status,
      phone: editUser.phone,
      address: editUser.address,
      role: editUser.role,
      notes: editUser.notes
    };

    console.log("Sending user update:", userData);

    // Make the API request
    const response = await fetch(`${API_URL}/api_accounts.php?f=update_user_admin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData)
    });

    // Get the response text first for debugging
    const responseText = await response.text();
    console.log('Raw API response:', responseText);

    // Try to parse the response as JSON
    let result;
    try {
      result = JSON.parse(responseText);
    } catch (parseError) {
      console.error('Failed to parse response as JSON:', parseError);
      throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);
    }

    if (result.success) {
      // Update the user in the accounts array directly
      const updatedUser = {
        id: editUser.id,
        email: editUser.email,
        first_name: editUser.firstName,
        last_name: editUser.lastName,
        company_name: editUser.companyName,
        vat_id: editUser.vatId,
        status: editUser.status,
        country: editUser.country,
        city: editUser.city,
        phone: editUser.phone,
        address: editUser.address,
        role: editUser.role,
        notes: editUser.notes,
        // Add name field for display in the table
        name: `${editUser.firstName} ${editUser.lastName}`
      };

      // Update the accounts state with the modified user
      setAccounts(prevAccounts => {
        return prevAccounts.map(account => {
          if (account.id === editUser.id) {
            return { ...account, ...updatedUser };
          }
          return account;
        });
      });

      // If the selected account is the one being edited, update it
      if (selectedAccount && selectedAccount.id === editUser.id) {
        setSelectedAccount({
          ...selectedAccount,
          ...updatedUser
        });
      }

      // Close modal
      handleCloseEditUserModal();

      // Also fetch accounts to ensure server-side changes are reflected
      fetchAccounts();

      // Show success message
      alert('User updated successfully');
    } else {
      console.error('API returned error:', result);
      alert(result.error || 'Failed to update user profile');
    }
  } catch (err) {
    console.error("Error updating user:", err);
    alert('Failed to update user: ' + err.message);
  }
};

// 3. Enhanced function to handle changes in the edit user form
const handleEditUserChange = (e) => {
  const { name, value } = e.target;
  setEditUser(prev => ({
    ...prev,
    [name]: value
  }));
};

  // Close the edit user modal
  const handleCloseEditUserModal = () => {
    setEditUserModalOpen(false);
    setEditUser(null);
  };





  // Update user profile


  // Handle changes in new admin form
  const handleNewAdminChange = (e) => {
    const { name, value } = e.target;
    setNewAdmin({
      ...newAdmin,
      [name]: value
    });
  };

  // Handle changes in edit admin form
  const handleEditAdminChange = (e) => {
    const { name, value } = e.target;
    setEditAdmin({
      ...editAdmin,
      [name]: value
    });
  };

  // Add a new admin
  const handleAddAdmin = async () => {
    // Validate form
    if (!newAdmin.email || !newAdmin.password || !newAdmin.firstName || !newAdmin.lastName) {
      alert('Please fill in all required fields');
      return;
    }

    if (newAdmin.password !== newAdmin.confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    try {
      const response = await fetch(`${API_URL}/api_accounts.php?f=add_admin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          email: newAdmin.email,
          password: newAdmin.password,
          first_name: newAdmin.firstName,
          last_name: newAdmin.lastName,
          function: newAdmin.function,
          department_id: newAdmin.departmentId || null
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // If we have assigned departments, save them
        if (newAdmin.assignedDepartments && newAdmin.assignedDepartments.length > 0) {
          const deptRequestData = {
            token: localStorage.getItem('admin_token'),
            admin_id: result.admin_id,
            department_ids: newAdmin.assignedDepartments
          };

          try {
            const deptResponse = await fetch(`${API_URL}/api_accounts.php?f=update_admin_assigned_departments`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(deptRequestData)
            });

            if (!deptResponse.ok) {
              console.error('Failed to update assigned departments:', await deptResponse.text());
              // Continue anyway since the main admin creation was successful
            }
          } catch (deptErr) {
            console.error('Error assigning departments:', deptErr);
            // Continue anyway since the main admin creation was successful
          }
        }

        // Close modal and refresh data
        handleCloseAddAdminModal();
        fetchAdmins();
      } else {
        alert(result.error || 'Failed to add admin');
      }

    } catch (err) {
      console.error("Error adding admin:", err);
      alert('Failed to add admin: ' + err.message);
    }
  };

  // Update an existing admin
  const handleUpdateAdmin = async () => {
    // Validate form
    if (!editAdmin.email || !editAdmin.firstName || !editAdmin.lastName) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      console.log('Updating admin with data:', {
        id: editAdmin.id,
        email: editAdmin.email,
        first_name: editAdmin.firstName,
        last_name: editAdmin.lastName,
        function: editAdmin.function,
        department_id: editAdmin.departmentId,
        assigned_departments: editAdmin.assignedDepartments
      });

      // First update the admin's basic info
      const requestData = {
        token: localStorage.getItem('admin_token'),
        id: editAdmin.id,
        email: editAdmin.email,
        first_name: editAdmin.firstName,
        last_name: editAdmin.lastName,
        function: editAdmin.function,
        department_id: editAdmin.departmentId || null
      };

      const response = await fetch(`${API_URL}/api_accounts.php?f=update_admin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      // Get the response text first for debugging
      const responseText = await response.text();
      console.log('Raw API response:', responseText);

      // Try to parse the response as JSON
      let result;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Failed to parse response as JSON:', parseError);
        throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`);
      }

      if (result.success) {
        // Now update the assigned departments
        const deptRequestData = {
          token: localStorage.getItem('admin_token'),
          admin_id: editAdmin.id,
          department_ids: editAdmin.assignedDepartments || []
        };

        const deptResponse = await fetch(`${API_URL}/api_accounts.php?f=update_admin_assigned_departments`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(deptRequestData)
        });

        if (!deptResponse.ok) {
          console.error('Failed to update assigned departments:', await deptResponse.text());
          // Continue anyway since the main admin update was successful
        }

        // Close modal and refresh data
        handleCloseEditAdminModal();
        fetchAdmins();
        alert('Admin updated successfully');
      } else {
        console.error('API returned error:', result);
        alert(result.error || 'Failed to update admin');
      }

    } catch (err) {
      console.error("Error updating admin:", err);
      alert('Failed to update admin: ' + err.message);
    }
  };

  // Delete an admin
  const handleDeleteAdmin = async (adminId) => {
    if (!window.confirm('Are you sure you want to delete this admin?')) {
      return;
    }

    try {
      const response = await fetch(`${API_URL}/api_accounts.php?f=delete_admin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          id: adminId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Refresh admins list
        fetchAdmins();
        // Close details if open
        if (selectedAdmin && selectedAdmin.id === adminId) {
          closeAdminDetails();
        }
      } else {
        alert(result.error || 'Failed to delete admin');
      }

    } catch (err) {
      console.error("Error deleting admin:", err);
      alert('Failed to delete admin: ' + err.message);
    }
  };

  // Generate a random password
  const generateRandomPassword = (length = 12) => {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * chars.length);
      password += chars[randomIndex];
    }

    return password;
  };

  // Reset a user's password
  const handleResetPassword = async (userId) => {
    // Show confirmation dialog
    if (!window.confirm('Are you sure you want to reset this user\'s password? A new random password will be generated and sent to the user via email.')) {
      return;
    }

    // Generate a random password
    const newPassword = generateRandomPassword();

    try {
      const response = await fetch(`${API_URL}/api_accounts.php?f=reset_user_password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          user_id: userId,
          password: newPassword,
          send_email: true // Add parameter to indicate email should be sent
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        alert('Password has been reset successfully and sent to the user via email.');
      } else {
        alert(result.error || 'Failed to reset password');
      }

    } catch (err) {
      console.error("Error resetting password:", err);
      alert('Failed to reset password: ' + err.message);
    }
  };

  // Update account status (active/inactive/locked)
  const handleUpdateAccountStatus = async (userId, newStatus) => {
    try {
      // Format the request exactly like the working admin functions
      const response = await fetch(`${API_URL}/api_accounts.php?f=update_account_status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          user_id: userId,
          status: newStatus
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Refresh accounts list
        fetchAccounts();

        // Update selected account if open
        if (selectedAccount && selectedAccount.id === userId) {
          setSelectedAccount({
            ...selectedAccount,
            status: newStatus
          });
        }
      } else {
        console.error("Failed to update account status:", result.error);
        alert(result.error || 'Failed to update account status');
      }

    } catch (err) {
      console.error("Error updating account status:", err);
      alert('Failed to update account status: ' + err.message);
    }
  };

  const renderStatusBadge = (status) => {
    const badgeClasses = {
      'Active': 'bg-green-100 text-green-800',
      'Inactive': 'bg-gray-100 text-gray-800',
      'Locked': 'bg-red-100 text-red-800',
      'Pending': 'bg-yellow-100 text-yellow-800'
    };

    const icons = {
      'Active': <CheckCircle className="w-4 h-4 mr-1" />,
      'Inactive': <AlertTriangle className="w-4 h-4 mr-1" />,
      'Locked': <Lock className="w-4 h-4 mr-1" />,
      'Pending': <Calendar className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertTriangle className="w-4 h-4 mr-1" />}
        {status}
      </span>
    );
  };

  // Filter accounts based on search query and filters
  const filteredAccounts = accounts.filter(account => {
    const matchesSearch =
      (account.name && account.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (account.email && account.email && account.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (account.role && account.role.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = selectedStatus === 'All' || account.status === selectedStatus;
    const matchesRole = selectedRole === 'All' || account.role === selectedRole;

    return matchesSearch && matchesStatus && matchesRole;
  });

  // Filter admins based on search query
  const filteredAdmins = admins.filter(admin => {
    return (
      (admin.first_name && admin.first_name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (admin.last_name && admin.last_name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (admin.email && admin.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (admin.function && admin.function.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  });

  // Sort filtered accounts
  const sortedAccounts = [...filteredAccounts].sort((a, b) => {
    let comparison = 0;

    if (sortField === 'name') {
      // Handle potentially missing values
      const nameA = a.name || '';
      const nameB = b.name || '';
      comparison = nameA.localeCompare(nameB);
    } else if (sortField === 'email') {
      // Handle potentially missing values
      const emailA = a.email || '';
      const emailB = b.email || '';
      comparison = emailA.localeCompare(emailB);
    } else if (sortField === 'role') {
      // Handle potentially missing values
      const roleA = a.role || '';
      const roleB = b.role || '';
      comparison = roleA.localeCompare(roleB);
    } else if (sortField === 'status') {
      // Handle potentially missing values
      const statusA = a.status || '';
      const statusB = b.status || '';
      comparison = statusA.localeCompare(statusB);
    } else if (sortField === 'created') {
      // Handle potentially missing values
      const dateA = a.created ? new Date(a.created) : new Date(0);
      const dateB = b.created ? new Date(b.created) : new Date(0);
      comparison = dateA - dateB;
    } else if (sortField === 'lastLogin') {
      // Handle potentially missing values
      const dateA = a.last_login ? new Date(a.last_login) : new Date(0);
      const dateB = b.last_login ? new Date(b.last_login) : new Date(0);
      comparison = dateA - dateB;
    } else {
      // Default to ID sort
      comparison = (a.id || 0) - (b.id || 0);
    }

    return sortDirection === 'asc' ? comparison : -comparison;
  });

  // Calculate pagination for accounts
  const totalAccountPages = Math.ceil(sortedAccounts.length / itemsPerPage);
  const accountStartIndex = (currentPage - 1) * itemsPerPage;
  const paginatedAccounts = sortedAccounts.slice(accountStartIndex, accountStartIndex + itemsPerPage);

  // Sort filtered admins
  const sortedAdmins = [...filteredAdmins].sort((a, b) => {
    if (sortField === 'name') {
      const nameA = `${a.first_name} ${a.last_name}`;
      const nameB = `${b.first_name} ${b.last_name}`;
      return sortDirection === 'asc'
        ? nameA.localeCompare(nameB)
        : nameB.localeCompare(nameA);
    } else if (sortField === 'email') {
      return sortDirection === 'asc'
        ? a.email.localeCompare(b.email)
        : b.email.localeCompare(a.email);
    } else if (sortField === 'function') {
      return sortDirection === 'asc'
        ? a.function.localeCompare(b.function)
        : b.function.localeCompare(a.function);
    } else if (sortField === 'department') {
      return sortDirection === 'asc'
        ? (a.department_name || '').localeCompare(b.department_name || '')
        : (b.department_name || '').localeCompare(a.department_name || '');
    } else {
      // Default sort by ID
      return sortDirection === 'asc'
        ? a.id - b.id
        : b.id - a.id;
    }
  });

  // Calculate pagination for admins
  const totalAdminPages = Math.ceil(sortedAdmins.length / itemsPerPage);
  const adminStartIndex = (currentPage - 1) * itemsPerPage;
  const paginatedAdmins = sortedAdmins.slice(adminStartIndex, adminStartIndex + itemsPerPage);

  // Generate account stats cards
  const generateAccountStatsCards = () => {
    const stats = [
      {
        title: 'Total Accounts',
        value: accountStats.totalAccounts || '0',
        change: '+4.5%',
        period: 'vs last month',
        icon: <Users className="text-indigo-700" size={40} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-info'
      },
      {
        title: 'Active Users',
        value: accountStats.activeUsers || '0',
        change: '+2.1%',
        period: 'vs last month',
        icon: <CheckCircle className="text-success" size={40} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-success'
      },
      {
        title: 'Inactive Users',
        value: accountStats.inactiveUsers || '0',
        change: '-1.0%',
        period: 'vs last month',
        icon: <AlertTriangle className="text-warning" size={40} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-warning'
      },
      {
        title: 'Locked Accounts',
        value: accountStats.lockedAccounts || '0',
        change: '+0.5%',
        period: 'vs last month',
        icon: <Lock className="text-danger" size={40} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-danger'
      }
    ];

    return stats.map((stat, index) => (
      <div
        key={index}
        className="bg-white p-6 shadow-sm flex items-center justify-between border rounded metric-card"
      >
        <div>
          <div className="text-sm text-gray-700">{stat.title}</div>
          <div className="text-2xl font-bold mt-1">{stat.value}</div>
          <div className="text-xs text-gray-500 mt-1">
            <span className={stat.change.startsWith('+') ? 'text-green-500' : 'text-red-500'}>
              {stat.change}
            </span> {stat.period}
          </div>
        </div>
        <div className={`card-custom-icon ${stat.iconClass}`}>
          {stat.icon}
        </div>
      </div>
    ));
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        activeMenu="Accounts"
        navigateTo={navigateTo}
        toggleSidebar={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gray-100">
        {/* Top Menu */}
        <TopMenu toggleSidebar={toggleSidebar} />

        {/* Accounts Content */}
        <div className="p-6 space-y-6 overflow-auto">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">Accounts Management</h1>
            <div className="flex items-center space-x-2">
  <div className="text-sm text-gray-500">Last updated: {lastUpdated}</div>
  <button
    onClick={handleRefreshData}
    className="p-2 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
  >
    <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
  </button>
  {selectedTab === 'admins' && (
    <button
      onClick={handleOpenAddAdminModal}
      className="bg-indigo-700 hover:bg-indigo-800 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
    >
      <UserPlus className="w-4 h-4 mr-1" />
      New Admin
    </button>
  )}
  {selectedTab === 'users' && (
    <button
      onClick={handleOpenAddUserModal}
      className="bg-indigo-700 hover:bg-indigo-800 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
    >
      <UserPlus className="w-4 h-4 mr-1" />
      New User
    </button>
  )}
</div>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-gray-200">
            <button
              className={`py-2 px-4 font-medium text-sm ${selectedTab === 'users'
                ? 'text-indigo-700 border-b-2 border-indigo-700'
                : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setSelectedTab('users')}
            >
              <User className="w-4 h-4 inline mr-1" />
              User Accounts
            </button>
            <button
              className={`py-2 px-4 font-medium text-sm ${selectedTab === 'admins'
                ? 'text-indigo-700 border-b-2 border-indigo-700'
                : 'text-gray-500 hover:text-gray-700'}`}
              onClick={() => setSelectedTab('admins')}
            >
              <Shield className="w-4 h-4 inline mr-1" />
              Admin Accounts
            </button>
          </div>

          {/* User Accounts Tab Content */}
          {selectedTab === 'users' && (
            <>

              {/* Account Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {generateAccountStatsCards()}
              </div>

              {/* Accounts Filter and Search */}
              <div className="bg-white border border-gray-200 shadow-sm rounded-md">
                <div className="p-4 border-b flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <div className="flex flex-wrap gap-2">
                    <div className="relative">
                      <select
                        className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                        value={selectedStatus}
                        onChange={(e) => handleStatusFilter(e.target.value)}
                      >
                        {uniqueStatuses.map(status => (
                          <option key={status} value={status}>{status === 'All' ? 'All Status' : status}</option>
                        ))}
                      </select>
                      <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                    </div>
                    <div className="relative">
                      <select
                        className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                        value={selectedRole}
                        onChange={(e) => handleRoleFilter(e.target.value)}
                      >
                        {uniqueRoles.map(role => (
                          <option key={role} value={role}>{role === 'All' ? 'All Roles' : role}</option>
                        ))}
                      </select>
                      <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                    </div>
                  </div>
                  <div className="relative w-full sm:w-64">
                    <input
                      type="text"
                      placeholder="Search accounts..."
                      className="pl-8 pr-3 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 search-input"
                      value={searchQuery}
                      onChange={handleSearch}
                    />
                    <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                  </div>
                </div>

                {/* Accounts Table */}
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="text-gray-500 text-xs border-b">
                        <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('name')}>
                          NAME {getSortIcon('name')}
                        </th>
                        <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('email')}>
                          EMAIL {getSortIcon('email')}
                        </th>
                        <th className="p-4 text-left font-medium cursor-pointer hide-sm" onClick={() => handleSort('role')}>
                          ROLE {getSortIcon('role')}
                        </th>
                        <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('status')}>
                          STATUS {getSortIcon('status')}
                        </th>
                        <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('lastLogin')}>
                          LAST LOGIN {getSortIcon('lastLogin')}
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {loading ? (
                        <tr>
                          <td colSpan="6" className="p-4 text-center">
                            <RefreshCw className="w-5 h-5 animate-spin inline mr-2 text-indigo-700" />
                            Loading accounts...
                          </td>
                        </tr>
                      ) : error ? (
                        <tr>
                          <td colSpan="6" className="p-4 text-center text-red-600">
                            {error}
                          </td>
                        </tr>
                      ) : paginatedAccounts.length > 0 ? (
                        paginatedAccounts.map((account, index) => (
                          <tr
                            key={account.id}
                            className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} task-row`}
                            onClick={() => handleAccountClick(account)}
                          >
                            <td className="p-4 font-medium text-indigo-700">{account.name}</td>
                            <td className="p-4 text-gray-700">{account.email}</td>
                            <td className="p-4 text-gray-700 hide-sm">{account.role}</td>
                            <td className="p-4">{renderStatusBadge(account.status)}</td>
                            <td className="p-4 text-gray-700 hide-xs">
                              {account.last_login ? new Date(account.last_login).toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' }) + ' ' + new Date(account.last_login).toLocaleTimeString('en-GB') : 'Never'}
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan="6" className="p-4 text-center text-gray-500">
                            No accounts found matching your criteria
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalAccountPages}
                  totalItems={sortedAccounts.length}
                  itemsPerPage={itemsPerPage}
                  onPageChange={handlePageChange}
                  onPreviousPage={handlePreviousPage}
                  onNextPage={handleNextPage}
                />
              </div>
            </>
          )}

          {/* Admin Accounts Tab Content */}
          {selectedTab === 'admins' && (
            <div className="bg-white border border-gray-200 shadow-sm rounded-md">
              <div className="p-4 border-b flex justify-between items-center">
                <h2 className="text-lg font-semibold">Admin Accounts</h2>
                <div className="relative w-64">
                  <input
                    type="text"
                    placeholder="Search admins..."
                    className="pl-8 pr-3 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 search-input"
                    value={searchQuery}
                    onChange={handleSearch}
                  />
                  <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                </div>
              </div>

              {/* Admins Table */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-gray-500 text-xs border-b">
                      <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('name')}>
                        NAME {getSortIcon('name')}
                      </th>
                      <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('email')}>
                        EMAIL {getSortIcon('email')}
                      </th>
                      <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('function')}>
                        FUNCTION {getSortIcon('function')}
                      </th>
                      <th className="p-4 text-left font-medium cursor-pointer hide-sm" onClick={() => handleSort('department')}>
                        DEPARTMENT {getSortIcon('department')}
                      </th>
                      <th className="p-4 text-left font-medium">ACTIONS</th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan="5" className="p-4 text-center">
                          <RefreshCw className="w-5 h-5 animate-spin inline mr-2 text-indigo-700" />
                          Loading admins...
                        </td>
                      </tr>
                    ) : error ? (
                      <tr>
                        <td colSpan="5" className="p-4 text-center text-red-600">
                          {error}
                        </td>
                      </tr>
                    ) : paginatedAdmins.length > 0 ? (
                      paginatedAdmins.map((admin, index) => (
                        <tr
                          key={admin.id}
                          className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} cursor-pointer hover:bg-gray-50`}
                          onClick={() => handleAdminClick(admin)}
                        >
                          <td className="p-4 font-medium text-indigo-700">
                            {admin.first_name} {admin.last_name}
                          </td>
                          <td className="p-4 text-gray-700">{admin.email}</td>
                          <td className="p-4 text-gray-700 hide-xs">{admin.function}</td>
                          <td className="p-4 text-gray-700 hide-sm">{admin.department_name || 'N/A'}</td>
                          <td className="p-4">
                            <div className="flex space-x-2">
                              <button
                                className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleOpenEditAdminModal(admin);
                                }}
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                className="p-1 text-gray-500 hover:text-red-700 transition-colors"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteAdmin(admin.id);
                                }}
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="5" className="p-4 text-center text-gray-500">
                          No admin accounts found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <Pagination
                currentPage={currentPage}
                totalPages={totalAdminPages}
                totalItems={sortedAdmins.length}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onPreviousPage={handlePreviousPage}
                onNextPage={handleNextPage}
              />
            </div>
          )}
        </div>
      </div>


      {/* Account Details Modal */}
      {selectedAccount && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-md shadow-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto mx-4">
            <div className="p-4 border-b flex justify-between items-center bg-gray-50 sticky top-0 z-10">
              <h2 className="text-xl font-bold text-gray-800">Account Details</h2>
              <button
                onClick={closeAccountDetails}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>

            {/* Account Header */}
            <div className="p-5 pb-0">
              <div className="flex items-center mb-5">
                <div className="h-14 w-14 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 mr-4">
                  <User className="w-7 h-7" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">{selectedAccount.name}</h3>
                  <div className="text-gray-500 flex items-center">
                    <Mail className="w-4 h-4 mr-1.5" />
                    {selectedAccount.email}
                  </div>
                </div>
                <div className="ml-auto">
                  {renderStatusBadge(selectedAccount.status)}
                </div>
              </div>
            </div>


            {/* Tabs Navigation */}
            <div className="px-5">
              <div className="flex border-b border-gray-200 overflow-x-auto">
                <button
                  className={`py-2 px-4 font-medium text-sm whitespace-nowrap ${
                    activeModalTab === 'info'
                      ? 'text-indigo-700 border-b-2 border-indigo-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  onClick={() => setActiveModalTab('info')}
                >
                  <User className="w-4 h-4 inline mr-1" />
                  Account Info
                </button>
                <button
                  className={`py-2 px-4 font-medium text-sm whitespace-nowrap ${
                    activeModalTab === 'credits'
                      ? 'text-indigo-700 border-b-2 border-indigo-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  onClick={() => setActiveModalTab('credits')}
                >
                  <Wallet className="w-4 h-4 inline mr-1" />
                  Credits
                </button>
                <button
                  className={`py-2 px-4 font-medium text-sm whitespace-nowrap ${
                    activeModalTab === 'invoices'
                      ? 'text-indigo-700 border-b-2 border-indigo-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  onClick={() => setActiveModalTab('invoices')}
                >
                  <FileText className="w-4 h-4 inline mr-1" />
                  Invoices
                </button>
                <button
                  className={`py-2 px-4 font-medium text-sm whitespace-nowrap ${
                    activeModalTab === 'services'
                      ? 'text-indigo-700 border-b-2 border-indigo-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  onClick={() => setActiveModalTab('services')}
                >
                  <Server className="w-4 h-4 inline mr-1" />
                  Services
                </button>
                <button
                  className={`py-2 px-4 font-medium text-sm whitespace-nowrap ${
                    activeModalTab === 'activity'
                      ? 'text-indigo-700 border-b-2 border-indigo-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  onClick={() => setActiveModalTab('activity')}
                >
                  <Activity className="w-4 h-4 inline mr-1" />
                  Activity
                </button>
              </div>
            </div>

            {/* Tab Content */}
            <div className="p-5">
              {/* Account Info Tab */}
              {activeModalTab === 'info' && (
                <>
                  {console.log("Rendering Account Info tab with data:", selectedAccount)}
                  {/* Account Info in Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-5">
                    {/* Basic Info Card */}
                    <div className="bg-gray-50 rounded-md p-4">
                      <h4 className="text-sm font-semibold text-gray-800 mb-3 border-b pb-2">Account Information</h4>
                      <div className="space-y-3">
                        <div>
                          <div className="text-xs text-gray-500">Role</div>
                          <div className="font-medium flex items-center">
                            <Shield className="w-4 h-4 mr-1.5 text-indigo-700" />
                            {selectedAccount?.role || 'Customer'}
                          </div>
                        </div>

                        <div>
                          <div className="text-xs text-gray-500">Account ID</div>
                          <div className="font-medium flex items-center">
                            <User className="w-4 h-4 mr-1.5 text-indigo-700" />
                            #{selectedAccount?.id || '0000'}
                          </div>
                        </div>

                        <div>
                          <div className="text-xs text-gray-500">Account Created</div>
                          <div className="font-medium flex items-center">
                            <Calendar className="w-4 h-4 mr-1.5 text-indigo-700" />
                            {selectedAccount?.created
                              ? new Date(selectedAccount.created).toLocaleDateString('en-GB', {
                                  day: '2-digit',
                                  month: '2-digit',
                                  year: 'numeric'
                                })
                              : 'Not available'}
                          </div>
                        </div>

                        <div>
                          <div className="text-xs text-gray-500">Last Login</div>
                          <div className="font-medium flex items-center">
                            <Calendar className="w-4 h-4 mr-1.5 text-indigo-700" />
                            {selectedAccount?.last_login
                              ? new Date(selectedAccount.last_login).toLocaleString()
                              : 'Never'}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Business Card */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="text-sm font-semibold text-gray-800 mb-3 border-b pb-2">Business Information</h4>
                      <div className="space-y-3">
                        <div>
                          <div className="text-xs text-gray-500">Company</div>
                          <div className="font-medium flex items-center">
                            <Building className="w-4 h-4 mr-1.5 text-indigo-700" />
                            {selectedAccount?.company_name || 'Not specified'}
                          </div>
                        </div>

                        <div>
                          <div className="text-xs text-gray-500">VAT ID</div>
                          <div className="font-medium flex items-center">
                            <Globe className="w-4 h-4 mr-1.5 text-indigo-700" />
                            {selectedAccount?.vat_id || 'Not specified'}
                          </div>
                        </div>

                        <div>
                          <div className="text-xs text-gray-500">Phone</div>
                          <div className="font-medium flex items-center">
                            <Phone className="w-4 h-4 mr-1.5 text-indigo-700" />
                            {selectedAccount?.phone || 'Not specified'}
                          </div>
                        </div>

                        <div>
                          <div className="text-xs text-gray-500">Notes</div>
                          <div className="font-medium text-sm text-gray-700 italic">
                            {selectedAccount?.notes || 'No notes available'}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Address Card */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="text-sm font-semibold text-gray-800 mb-3 border-b pb-2">Address Information</h4>
                      <div className="space-y-3">
                        <div>
                          <div className="text-xs text-gray-500">Street Address</div>
                          <div className="font-medium flex items-center">
                            <Building className="w-4 h-4 mr-1.5 text-indigo-700" />
                            {selectedAccount?.address || 'Not specified'}
                          </div>
                        </div>

                        <div>
                          <div className="text-xs text-gray-500">City</div>
                          <div className="font-medium flex items-center">
                            <MapPin className="w-4 h-4 mr-1.5 text-indigo-700" />
                            {selectedAccount?.city || 'Not specified'}
                          </div>
                        </div>

                        <div>
                          <div className="text-xs text-gray-500">Country</div>
                          <div className="font-medium flex items-center">
                            <Globe className="w-4 h-4 mr-1.5 text-indigo-700" />
                            {selectedAccount?.country || 'Not specified'}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}

                {/* Credits Tab */}
                {activeModalTab === 'credits' && (
                  <CreditsTab
                    userId={selectedAccount.id}
                    token={localStorage.getItem('admin_token')}
                  />
                )}
              {/* Invoices Tab */}
              {activeModalTab === 'invoices' && (
                <InvoicesTab
                userId={selectedAccount.id}
                token={localStorage.getItem('admin_token')}
                onUpdateInvoiceStatus={(invoiceId, status) => {
                  // You can add additional logic here if needed
                  console.log(`Updating invoice ${invoiceId} to status ${status}`);
                }}
              />
              )}

              {/* Services Tab */}
              {activeModalTab === 'services' && (
                <ServicesTab
                  userId={selectedAccount.id}
                  token={localStorage.getItem('admin_token')}
                />
              )}

              {/* Activity Tab */}
              {activeModalTab === 'activity' && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-sm font-semibold text-gray-800 mb-3 border-b pb-2">Recent Activities</h4>
                  {selectedAccount.last_login ? (
                    <div className="text-sm text-gray-600">
                      Last logged in on {new Date(selectedAccount.last_login).toLocaleString()} from IP {selectedAccount.last_ip || 'unknown'}
                    </div>
                  ) : (
                    <div className="text-sm text-gray-600">No recent activity</div>
                  )}
                </div>
              )}

              {/* Account Actions */}
              <div className="flex flex-wrap gap-3 justify-end pt-4 mt-4 border-t sticky bottom-0 z-10 bg-white">


                <button
                  className="px-3 py-1.5 border border-gray-300 rounded-md text-blue-700 flex items-center text-sm hover:bg-blue-50"
                  onClick={() => {
                    // Make a direct API call to fetch the account data
                    console.log("Making direct API call to fetch account data");

                    fetch(`${API_URL}/api_accounts.php?f=get_account_by_id&id=${selectedAccount.id}&token=${localStorage.getItem('admin_token')}`)
                      .then(response => {
                        console.log("API response status:", response.status);
                        return response.text();
                      })
                      .then(text => {
                        console.log("API response text:", text);
                        try {
                          const accountData = JSON.parse(text);
                          console.log("Parsed account data:", accountData);

                          // Create a complete account object with all required fields
                          const processedAccount = {
                            // Start with the current selected account
                            ...selectedAccount,

                            // Override with the new data
                            ...accountData,

                            // Ensure name is properly formatted
                            name: accountData.name || (
                              (accountData.first_name && accountData.last_name)
                                ? `${accountData.first_name} ${accountData.last_name}`
                                : (accountData.first_name || accountData.last_name || accountData.email || 'Account #' + selectedAccount.id)
                            )
                          };

                          console.log("Final processed account:", processedAccount);
                          setSelectedAccount(processedAccount);
                          alert("Account data refreshed. Check console for details.");
                        } catch (error) {
                          console.error("Error parsing account data:", error);
                          alert("Error parsing account data. Check console for details.");
                        }
                      })
                      .catch(error => {
                        console.error("Error fetching account data:", error);
                        alert("Error fetching account data. Check console for details.");
                      });
                  }}
                >
                  <RefreshCw className="w-4 h-4 mr-1" />
                  Refresh Data
                </button>

                <button
                  className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                  onClick={() => handleResetPassword(selectedAccount.id)}
                >
                  <Lock className="w-4 h-4 mr-1" />
                  Reset Password
                </button>

                {selectedAccount.status === 'Active' ? (
                  <button
                    className="px-3 py-1.5 border border-gray-300 rounded-md text-yellow-700 flex items-center text-sm hover:bg-yellow-50"
                    onClick={() => handleUpdateAccountStatus(selectedAccount.id, 'Locked')}
                  >
                    <AlertTriangle className="w-4 h-4 mr-1" />
                    Lock Account
                  </button>
                ) : selectedAccount.status === 'Locked' ? (
                  <button
                    className="px-3 py-1.5 border border-gray-300 rounded-md text-green-700 flex items-center text-sm hover:bg-green-50"
                    onClick={() => handleUpdateAccountStatus(selectedAccount.id, 'Active')}
                  >
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Unlock Account
                  </button>
                ) : selectedAccount.status === 'Inactive' ? (
                  <button
                    className="px-3 py-1.5 border border-gray-300 rounded-md text-green-700 flex items-center text-sm hover:bg-green-50"
                    onClick={() => handleUpdateAccountStatus(selectedAccount.id, 'Active')}
                  >
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Activate Account
                  </button>
                ) : null}

                <button
                  className="px-3 py-1.5 border border-gray-300 rounded-md text-indigo-700 flex items-center text-sm hover:bg-indigo-50"
                  onClick={() => handleImpersonateUser(selectedAccount.id)}
                >
                  <UserCheck className="w-4 h-4 mr-1" />
                  Login as User
                </button>

                <button
                  className="px-3 py-1.5 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
                  onClick={() => handleOpenEditUserModal(selectedAccount)}
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit Profile
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Admin Details Modal */}
      {selectedAdmin && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto mx-4">
            <div className="p-6 border-b flex justify-between items-center sticky top-0 z-10 bg-white">
              <h2 className="text-xl font-bold text-gray-800">Admin Details</h2>
              <button
                onClick={closeAdminDetails}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-6 space-y-6">
              {/* Admin Header */}
              <div className="flex items-center">
                <div className="h-12 w-12 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 mr-4">
                  <UserCheck className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">{selectedAdmin.first_name} {selectedAdmin.last_name}</h3>
                  <div className="text-gray-500">{selectedAdmin.email}</div>
                </div>
              </div>

              {/* Admin Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 border-t border-b py-6">
                <div className="space-y-4">
                  <div>
                    <div className="text-sm text-gray-500">Function</div>
                    <div className="font-medium">
                      {selectedAdmin.function}
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-500">Department</div>
                    <div className="font-medium">
                      {selectedAdmin.department_name || 'Not assigned'}
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <div className="text-sm text-gray-500">Last Login</div>
                    <div className="font-medium">
                      {new Date(selectedAdmin.last_login).toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Last IP</div>
                    <div className="font-medium">
                      {selectedAdmin.last_ip || 'N/A'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Admin Actions */}
              <div className="flex flex-wrap gap-3 justify-end sticky bottom-0 z-10 bg-white pt-4 border-t">
                <button
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                  onClick={() => handleResetPassword(selectedAdmin.id)}
                >
                  <Lock className="w-4 h-4 mr-1" />
                  Reset Password
                </button>
                <button
                  className="px-4 py-2 border border-red-300 rounded-md text-red-700 flex items-center text-sm hover:bg-red-50"
                  onClick={() => handleDeleteAdmin(selectedAdmin.id)}
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  Delete Admin
                </button>
                <button
                  className="px-4 py-2 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
                  onClick={() => handleOpenEditAdminModal(selectedAdmin)}
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit Details
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Admin Modal */}
      {addAdminModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
            <div className="p-4 border-b flex justify-between items-center">
              <h2 className="text-lg font-bold text-gray-800">Add New Admin</h2>
              <button
                onClick={handleCloseAddAdminModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4">
              <form className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                    <input
                      type="text"
                      name="firstName"
                      value={newAdmin.firstName}
                      onChange={handleNewAdminChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                    <input
                      type="text"
                      name="lastName"
                      value={newAdmin.lastName}
                      onChange={handleNewAdminChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input
                    type="email"
                    name="email"
                    value={newAdmin.email}
                    onChange={handleNewAdminChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                    <input
                      type="password"
                      name="password"
                      value={newAdmin.password}
                      onChange={handleNewAdminChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
                    <input
                      type="password"
                      name="confirmPassword"
                      value={newAdmin.confirmPassword}
                      onChange={handleNewAdminChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Function</label>
                  <input
                    type="text"
                    name="function"
                    value={newAdmin.function}
                    onChange={handleNewAdminChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="e.g. System Administrator"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Primary Department</label>
                  <select
                    name="departmentId"
                    value={newAdmin.departmentId}
                    onChange={handleNewAdminChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="">Select Primary Department</option>
                    {departments.map(dept => (
                      <option key={dept.id} value={dept.id}>{dept.department_name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Assigned Departments</label>
                  <div className="border border-gray-300 rounded-md p-2 max-h-40 overflow-y-auto">
                    {departments.map(dept => (
                      <div key={dept.id} className="flex items-center mb-1">
                        <input
                          type="checkbox"
                          id={`new-dept-${dept.id}`}
                          checked={newAdmin.assignedDepartments.includes(dept.id)}
                          onChange={(e) => {
                            const isChecked = e.target.checked;
                            setNewAdmin(prev => ({
                              ...prev,
                              assignedDepartments: isChecked
                                ? [...prev.assignedDepartments, dept.id]
                                : prev.assignedDepartments.filter(id => id !== dept.id)
                            }));
                          }}
                          className="mr-2"
                        />
                        <label htmlFor={`new-dept-${dept.id}`} className="text-sm">{dept.department_name}</label>
                      </div>
                    ))}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Select all departments this admin can view tickets from</p>
                </div>

                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <button
                    type="button"
                    onClick={handleCloseAddAdminModal}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleAddAdmin}
                    className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800"
                  >
                    Add Admin
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit Admin Modal */}
      {editAdminModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
            <div className="p-4 border-b flex justify-between items-center">
              <h2 className="text-lg font-bold text-gray-800">Edit Admin</h2>
              <button
                onClick={handleCloseEditAdminModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4">
              <form className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                    <input
                      type="text"
                      name="firstName"
                      value={editAdmin.firstName}
                      onChange={handleEditAdminChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                    <input
                      type="text"
                      name="lastName"
                      value={editAdmin.lastName}
                      onChange={handleEditAdminChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input
                    type="email"
                    name="email"
                    value={editAdmin.email}
                    onChange={handleEditAdminChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Function</label>
                  <input
                    type="text"
                    name="function"
                    value={editAdmin.function}
                    onChange={handleEditAdminChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Primary Department</label>
                  <select
                    name="departmentId"
                    value={editAdmin.departmentId}
                    onChange={handleEditAdminChange}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="">Select Primary Department</option>
                    {departments.map(dept => (
                      <option key={dept.id} value={dept.id}>{dept.department_name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Assigned Departments</label>
                  <div className="border border-gray-300 rounded-md p-2 max-h-40 overflow-y-auto">
                    {departments.map(dept => (
                      <div key={dept.id} className="flex items-center mb-1">
                        <input
                          type="checkbox"
                          id={`dept-${dept.id}`}
                          checked={editAdmin.assignedDepartments.includes(dept.id)}
                          onChange={(e) => {
                            const isChecked = e.target.checked;
                            setEditAdmin(prev => ({
                              ...prev,
                              assignedDepartments: isChecked
                                ? [...prev.assignedDepartments, dept.id]
                                : prev.assignedDepartments.filter(id => id !== dept.id)
                            }));
                          }}
                          className="mr-2"
                        />
                        <label htmlFor={`dept-${dept.id}`} className="text-sm">{dept.department_name}</label>
                      </div>
                    ))}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Select all departments this admin can view tickets from</p>
                </div>

                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <button
                    type="button"
                    onClick={handleCloseEditAdminModal}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleUpdateAdmin}
                    className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800"
                  >
                    Save Changes
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}




{/* Add User Modal - Compact Version */}
{addUserModalOpen && (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg shadow-lg w-full max-w-5xl mx-4">
      <div className="px-4 py-3 border-b flex justify-between items-center">
        <h2 className="text-lg font-bold text-gray-800">Add New User</h2>
        <button
          onClick={handleCloseAddUserModal}
          className="text-gray-500 hover:text-gray-700"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      <div className="p-4 overflow-y-auto max-h-[80vh]">
        <form>
          {/* Main grid - 3 columns to maximize horizontal space */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-x-4 gap-y-3">
            {/* Login Information */}
            <div>
              <label className="block text-xs font-medium text-gray-700">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                name="email"
                value={newUser.email}
                onChange={handleNewUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                Password <span className="text-red-500">*</span>
              </label>
              <input
                type="password"
                name="password"
                value={newUser.password}
                onChange={handleNewUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                Confirm <span className="text-red-500">*</span>
              </label>
              <input
                type="password"
                name="confirmPassword"
                value={newUser.confirmPassword}
                onChange={handleNewUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
                required
              />
            </div>

            {/* Personal Details */}
            <div>
              <label className="block text-xs font-medium text-gray-700">
                First Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="firstName"
                value={newUser.firstName}
                onChange={handleNewUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                Last Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="lastName"
                value={newUser.lastName}
                onChange={handleNewUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
            <label className="block text-xs font-medium text-gray-700">
              Phone
            </label>
            <input
              type="text"
              name="phone"
              value={newUser.phone}
              onChange={handleNewUserChange}
              className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
            />
          </div>

            {/* Company Information */}
            <div>
              <label className="block text-xs font-medium text-gray-700">
                Company Name
              </label>
              <input
                type="text"
                name="companyName"
                value={newUser.companyName}
                onChange={handleNewUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              />
            </div>

            <div>
            <label className="block text-xs font-medium text-gray-700">
              VAT ID
            </label>
            <input
              type="text"
              name="vatId"
              value={newUser.vatId}
              onChange={handleNewUserChange}
              className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
            />
          </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                Role
              </label>
              <select
                name="role"
                value={newUser.role}
                onChange={handleNewUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              >
                <option value="Customer">Customer</option>
                <option value="Vendor">Vendor</option>
                <option value="Partner">Partner</option>
                <option value="Guest">Guest</option>
              </select>
            </div>

            {/* Location */}
            <div>
              <label className="block text-xs font-medium text-gray-700">
                Address <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="address"
                value={newUser.address}
                onChange={handleNewUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                City <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="city"
                value={newUser.city}
                onChange={handleNewUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                Country Code <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="country"
                value={newUser.country}
                onChange={handleNewUserChange}
                maxLength="2"
                placeholder="US, GB, DE..."
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
                required
              />
            </div>

            {/* Status and Notes */}
            <div>
              <label className="block text-xs font-medium text-gray-700">
                Status
              </label>
              <select
                name="status"
                value={newUser.status}
                onChange={handleNewUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              >
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
                <option value="Locked">Locked</option>
                <option value="Pending">Pending</option>
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-xs font-medium text-gray-700">
                Notes
              </label>
              <input
                type="text"
                name="notes"
                value={newUser.notes}
                onChange={handleNewUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
                placeholder="Optional notes about this user"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-4 pt-2 border-t">
            <div className="text-xs text-gray-500 self-center mr-auto">
              <span className="text-red-500">*</span> Required fields
            </div>
            <button
              type="button"
              onClick={handleCloseAddUserModal}
              className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 text-sm"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleAddUser}
              className="px-3 py-1.5 bg-indigo-700 text-white rounded-md text-sm flex items-center"
            >
              <UserPlus className="w-4 h-4 mr-1" />
              Add User
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
)}

{/* Edit User Modal - Compact Version */}
{editUserModalOpen && editUser && (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg shadow-lg w-full max-w-5xl mx-4">
      <div className="px-4 py-3 border-b flex justify-between items-center">
        <h2 className="text-lg font-bold text-gray-800">Edit User Profile</h2>
        <button
          onClick={handleCloseEditUserModal}
          className="text-gray-500 hover:text-gray-700"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      <div className="p-4 overflow-y-auto max-h-[80vh]">
        <form>
          {/* Main grid - 3 columns to maximize horizontal space */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-x-4 gap-y-3">
            {/* Login and Basic Information */}
            <div>
              <label className="block text-xs font-medium text-gray-700">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                name="email"
                value={editUser.email}
                onChange={handleEditUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                First Name
              </label>
              <input
                type="text"
                name="firstName"
                value={editUser.firstName}
                onChange={handleEditUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                Last Name
              </label>
              <input
                type="text"
                name="lastName"
                value={editUser.lastName}
                onChange={handleEditUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              />
            </div>

            {/* Company Information */}
            <div>
              <label className="block text-xs font-medium text-gray-700">
                Company Name
              </label>
              <input
                type="text"
                name="companyName"
                value={editUser.companyName}
                onChange={handleEditUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                VAT ID
              </label>
              <input
                type="text"
                name="vatId"
                value={editUser.vatId}
                onChange={handleEditUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                Phone
              </label>
              <input
                type="text"
                name="phone"
                value={editUser.phone}
                onChange={handleEditUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              />
            </div>

            {/* Location */}
            <div>
              <label className="block text-xs font-medium text-gray-700">
                Address
              </label>
              <input
                type="text"
                name="address"
                value={editUser.address}
                onChange={handleEditUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                City
              </label>
              <input
                type="text"
                name="city"
                value={editUser.city}
                onChange={handleEditUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                Country
              </label>
              <input
                type="text"
                name="country"
                value={editUser.country}
                onChange={handleEditUserChange}
                maxLength="2"
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              />
            </div>

            {/* Settings and Notes */}
            <div>
              <label className="block text-xs font-medium text-gray-700">
                Role
              </label>
              <select
                name="role"
                value={editUser.role}
                onChange={handleEditUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              >
                <option value="Customer">Customer</option>
                <option value="Vendor">Vendor</option>
                <option value="Partner">Partner</option>
                <option value="Guest">Guest</option>
              </select>
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                Status
              </label>
              <select
                name="status"
                value={editUser.status}
                onChange={handleEditUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              >
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
                <option value="Locked">Locked</option>
                <option value="Pending">Pending</option>
              </select>
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700">
                Notes
              </label>
              <input
                type="text"
                name="notes"
                value={editUser.notes}
                onChange={handleEditUserChange}
                className="w-full p-1.5 text-sm border border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-4 pt-2 border-t">
            <button
              type="button"
              onClick={() => handleResetPassword(editUser.id)}
              className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 text-sm flex items-center mr-auto"
            >
              <Lock className="w-3 h-3 mr-1" />
              Reset Password
            </button>

            <button
              type="button"
              onClick={handleCloseEditUserModal}
              className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 text-sm"
            >
              Cancel
            </button>

            <button
              type="button"
              onClick={handleUpdateUser}
              className="px-3 py-1.5 bg-indigo-700 text-white rounded-md text-sm flex items-center"
            >
              <Save className="w-4 h-4 mr-1" />
              Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
)}




    </div>
  );
};

export default AccountsPage;