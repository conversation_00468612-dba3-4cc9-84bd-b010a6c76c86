/*
 * LocationsPage Component
 * 
 * MULTIPLE DATACENTERS PER CITY IMPLEMENTATION STATUS:
 * 
 * The frontend has been updated to support multiple datacenters per city by:
 * - Adding a separate datacenters state array
 * - Creating helper functions for datacenter operations
 * - Updating the location tree to show datacenters as children of cities
 * - Preparing the UI for full datacenter management
 * 
 * HOWEVER, the backend currently stores datacenter as a single field in the cities table,
 * which limits each city to one datacenter. To fully support multiple datacenters per city,
 * the following backend changes are needed:
 * 
 * 1. Create a separate `datacenters` table with columns:
 *    - id (primary key)
 *    - name (datacenter name)
 *    - city_id (foreign key to cities table)
 *    - created_at, updated_at
 * 
 * 2. Update the racks table to reference datacenter_id instead of/in addition to city_id
 * 
 * 3. Create new API endpoints:
 *    - get_datacenters: Fetch all datacenters
 *    - add_datacenter: Add a new datacenter to a city
 *    - update_datacenter: Update datacenter details
 *    - delete_datacenter: Delete a datacenter
 * 
 * 4. Update existing endpoints to work with the new structure
 * 
 * Until these backend changes are made, the frontend works with the current backend
 * structure where each city can have one datacenter stored in the city.datacenter field.
 * The UI is prepared to support multiple datacenters per city when the backend is updated.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  Plus,
  Filter,
  MoreVertical,
  RefreshCw,
  Edit,
  Trash2,
  ChevronRight,
  ChevronDown,
  MapPin,
  Globe,
  Building,
  Server,
  XCircle,
  ChevronLeft,
  Save,
  X,
  Network,
  HardDrive,
  AlertTriangle,
  Move,
  ChevronUp,
  Info,
  Cpu,
  Memory,
  Settings,
  Activity,
  ExternalLink
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import { API_URL } from '../config';
const LocationsPage = ({ sidebarCollapsed, toggleSidebar }) => {
  // State for location data
  const [countries, setCountries] = useState([]);
  const [cities, setCities] = useState([]);
  const [datacenters, setDatacenters] = useState([]); // New state for datacenters
  const [racks, setRacks] = useState([]);
  const [rackDevices, setRackDevices] = useState([]);
  const [unassignedDevices, setUnassignedDevices] = useState([]);
  const navigate = useNavigate();

  // State for UI interactions
  const [loading, setLoading] = useState(true);
  const [loadingRacks, setLoadingRacks] = useState(false);
  const [loadingUnassigned, setLoadingUnassigned] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedCountries, setExpandedCountries] = useState({});
  const [expandedCities, setExpandedCities] = useState({});
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [selectedRack, setSelectedRack] = useState(null);
  const [lastUpdated, setLastUpdated] = useState('');

  // State for modals
  const [showAddLocationModal, setShowAddLocationModal] = useState(false);
  const [showAddRackModal, setShowAddRackModal] = useState(false);
  const [newLocation, setNewLocation] = useState({
    type: 'country', // 'country', 'city', or 'datacenter'
    name: '',
    parentId: null,
    flagCode: ''
  });
  const [newRack, setNewRack] = useState({
    rack_name: '',
    size: 42,
    city_id: '',
    country_id: ''
  });

  // State for delete confirmation modals
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);

  // State for edit modals
  const [showEditLocationModal, setShowEditLocationModal] = useState(false);
  const [showEditRackModal, setShowEditRackModal] = useState(false);
  const [locationToEdit, setLocationToEdit] = useState(null);
  const [rackToEdit, setRackToEdit] = useState(null);

  // State for device details modals
  const [selectedChassis, setSelectedChassis] = useState(null);
  const [selectedServer, setSelectedServer] = useState(null);
  const [selectedSwitch, setSelectedSwitch] = useState(null);

  // Fetch all location data
  const fetchLocationData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch countries
      const countriesResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_countries`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!countriesResponse.ok) {
        throw new Error(`HTTP error ${countriesResponse.status}`);
      }

      const countriesData = await countriesResponse.json();
      setCountries(countriesData);

      // Fetch cities
      const citiesResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_cities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!citiesResponse.ok) {
        throw new Error(`HTTP error ${citiesResponse.status}`);
      }

      const citiesData = await citiesResponse.json();
      setCities(citiesData);

      // Process datacenters from cities (temporary until backend supports separate datacenters)
      const datacentersFromCities = [];
      citiesData.forEach(city => {
        if (city.datacenter && city.datacenter.trim() !== '') {
          datacentersFromCities.push({
            id: city.id, // Use the city ID directly since datacenter is tied to city
            name: city.datacenter,
            city_id: city.id,
            country_id: city.country_id,
            type: 'datacenter' // Add type to distinguish from cities
          });
        }
      });
      setDatacenters(datacentersFromCities);

      // Fetch racks
      const racksResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_racks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!racksResponse.ok) {
        throw new Error(`HTTP error ${racksResponse.status}`);
      }

      const racksData = await racksResponse.json();
      setRacks(racksData);

      setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
      setLoading(false);
    } catch (err) {
      console.error("Error fetching location data:", err);
      setError("Failed to load location data. Please try again.");
      setLoading(false);
    }
  };


  useEffect(() => {
    // Create a navigation handler that ensures clean navigation
    const handleNavigation = (e) => {
      // Check if the click is on a sidebar menu item
      const sidebarItem = e.target.closest('.sidebar-item');
      if (sidebarItem) {
        // Close any open modals or selections
        setSelectedRack(null);
        setSelectedChassis(null);
        setSelectedServer(null);
        setSelectedSwitch(null);
        setShowAddLocationModal(false);
        setShowAddRackModal(false);
        setShowEditLocationModal(false);
        setShowEditRackModal(false);
        setShowDeleteConfirmation(false);
        
        // Allow the navigation to proceed
        return;
      }
    };
  
    // Add event listener to document
    document.addEventListener('click', handleNavigation, true);
  
    // Cleanup
    return () => {
      document.removeEventListener('click', handleNavigation, true);
    };
  }, []);

  
  // Load data on component mount
  useEffect(() => {
    fetchLocationData();
  }, []);

  // Function to fetch unassigned devices
  const fetchUnassignedDevices = async () => {
    setLoadingUnassigned(true);
    try {
      const response = await fetch(`${API_URL}/api_admin_locations.php?f=get_unassigned_devices`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      setUnassignedDevices(data || []);
    } catch (err) {
      console.error("Error fetching unassigned devices:", err);
      setUnassignedDevices([]);
    } finally {
      setLoadingUnassigned(false);
    }
  };

  // Delete a country
  const deleteCountry = async (countryId) => {
    try {
      // Check if country has cities
      const countryCities = cities.filter(city => city.country_id === countryId);
      if (countryCities.length > 0) {
        alert('Cannot delete this country because it has cities. Delete all cities first.');
        return false;
      }

      const response = await fetch(`${API_URL}/api_admin_locations.php?f=delete_country`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          id: countryId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Refresh location data
        await fetchLocationData();
        return true;
      } else {
        throw new Error(data.error || 'Failed to delete country');
      }
    } catch (err) {
      console.error('Error deleting country:', err);
      alert(`Failed to delete country: ${err.message}`);
      return false;
    }
  };

  // Delete a city
  const deleteCity = async (cityId) => {
    try {
      // First check if city has a datacenter
      const city = cities.find(c => c.id == cityId);
      if (city && city.datacenter && city.datacenter.trim() !== '') {
        alert('Cannot delete this city because it has a datacenter. Delete the datacenter first.');
        return false;
      }

      // Check if city has racks
      const cityRacks = racks.filter(rack => rack.city == cityId);
      if (cityRacks.length > 0) {
        alert('Cannot delete this city because it has racks. Delete all racks first.');
        return false;
      }

      const response = await fetch(`${API_URL}/api_admin_locations.php?f=delete_city`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          id: cityId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Refresh location data
        await fetchLocationData();
        return true;
      } else {
        throw new Error(data.error || 'Failed to delete city');
      }
    } catch (err) {
      console.error('Error deleting city:', err);
      alert(`Failed to delete city: ${err.message}`);
      return false;
    }
  };

  // Delete a rack
  const deleteRack = async (rackId) => {
    try {
      // First check if rack has devices
      const response = await fetch(`${API_URL}/api_admin_locations.php?f=get_rack_devices`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          rack_id: rackId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.devices && data.devices.length > 0) {
        // Count different types of devices for a more informative message
        const deviceTypes = {
          dedicated: data.devices.filter(d => d.type === 'dedicated').length,
          chassis: data.devices.filter(d => d.type === 'chassis').length,
          switch: data.devices.filter(d => d.type === 'switch').length
        };
        
        const deviceSummary = [];
        if (deviceTypes.dedicated > 0) deviceSummary.push(`${deviceTypes.dedicated} server${deviceTypes.dedicated > 1 ? 's' : ''}`);
        if (deviceTypes.chassis > 0) deviceSummary.push(`${deviceTypes.chassis} chassis`);
        if (deviceTypes.switch > 0) deviceSummary.push(`${deviceTypes.switch} switch${deviceTypes.switch > 1 ? 'es' : ''}`);
        
        alert(`Cannot delete this rack because it has ${deviceSummary.join(', ')} assigned to it. Remove all devices first.`);
        return false;
      }

      // Now delete the rack
      const deleteResponse = await fetch(`${API_URL}/api_admin_locations.php?f=delete_rack`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          id: rackId
        })
      });

      if (!deleteResponse.ok) {
        throw new Error(`HTTP error ${deleteResponse.status}`);
      }

      const deleteData = await deleteResponse.json();

      if (deleteData.success) {
        // Refresh location data
        await fetchLocationData();
        // If the deleted rack was selected, reset selection
        if (selectedRack && selectedRack.id === rackId) {
          setSelectedRack(null);
          setRackDevices([]);
        }
        return true;
      } else {
        throw new Error(deleteData.error || 'Failed to delete rack');
      }
    } catch (err) {
      console.error('Error deleting rack:', err);
      alert(`Failed to delete rack: ${err.message}`);
      return false;
    }
  };

  // Update a country
  const updateCountry = async (countryId, countryName) => {
    try {
      const response = await fetch(`${API_URL}/api_admin_locations.php?f=update_country`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          id: countryId,
          country: countryName
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Refresh location data
        await fetchLocationData();
        return true;
      } else {
        throw new Error(data.error || 'Failed to update country');
      }
    } catch (err) {
      console.error('Error updating country:', err);
      alert(`Failed to update country: ${err.message}`);
      return false;
    }
  };

  // Update a city
  const updateCity = async (cityId, updatedCityData) => {
    try {
      const response = await fetch(`${API_URL}/api_admin_locations.php?f=update_city`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          id: cityId,
          ...updatedCityData
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Refresh location data
        await fetchLocationData();
        return true;
      } else {
        throw new Error(data.error || 'Failed to update city');
      }
    } catch (err) {
      console.error('Error updating city:', err);
      alert(`Failed to update city: ${err.message}`);
      return false;
    }
  };

  // Handle delete button click for any item
  const handleDeleteClick = (type, id, name) => {
    setItemToDelete({
      type,
      id,
      name
    });
    setShowDeleteConfirmation(true);
  };

  // Handle confirm deletion
  const handleConfirmDelete = async () => {
    if (!itemToDelete) return;

    let success = false;

    switch (itemToDelete.type) {
      case 'country':
        success = await deleteCountry(itemToDelete.id);
        break;
      case 'city':
        success = await deleteCity(itemToDelete.id);
        break;
      case 'datacenter':
        success = await deleteDatacenter(itemToDelete.id);
        break;
      case 'rack':
        success = await deleteRack(itemToDelete.id);
        break;
      default:
        alert('Unknown item type');
        break;
    }

    if (success) {
      // If we deleted the selected item, reset selection
      if (selectedLocation &&
          selectedLocation.type === itemToDelete.type &&
          selectedLocation.id === itemToDelete.id) {
        setSelectedLocation(null);
      }
    }

    // Close the confirmation dialog
    setShowDeleteConfirmation(false);
    setItemToDelete(null);
  };

  const deleteDatacenter = async (cityId) => {
    try {
      // Check if datacenter has racks
      const cityRacks = racks.filter(rack => rack.city == cityId);
      if (cityRacks.length > 0) {
        alert(`Cannot delete this datacenter because it has ${cityRacks.length} rack${cityRacks.length > 1 ? 's' : ''}. Delete all racks first.`);
        return false;
      }

      const response = await fetch(`${API_URL}/api_admin_locations.php?f=delete_datacenter`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          city_id: cityId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Refresh location data
        await fetchLocationData();
        return true;
      } else {
        throw new Error(data.error || 'Failed to delete datacenter');
      }
    } catch (err) {
      console.error('Error deleting datacenter:', err);
      alert(`Failed to delete datacenter: ${err.message}`);
      return false;
    }
  };
  // Handle edit button click for a country
  const handleEditCountry = (country) => {
    setLocationToEdit({
      type: 'country',
      id: country.id,
      name: country.country,
      parentId: null
    });
    setShowEditLocationModal(true);
  };

  // Handle edit button click for a city
  const handleEditCity = (city) => {
    setLocationToEdit({
      type: 'city',
      id: city.id,
      name: city.city,
      parentId: city.country_id,
      datacenter: city.datacenter || ''
    });
    setShowEditLocationModal(true);
  };

  // Handle edit button click for a rack
  const handleEditRack = (rack) => {
    setRackToEdit({
      id: rack.id,
      rack_name: rack.rack_name,
      size: rack.size || 42,
      city_id: rack.city,
      country_id: rack.country,
      notes: rack.notes || ''
    });
    setShowEditRackModal(true);
  };

  // Handle save location edit
  const handleSaveLocationEdit = async () => {
    if (!locationToEdit) return;

    try {
      let success = false;

      switch (locationToEdit.type) {
        case 'country':
          success = await updateCountry(locationToEdit.id, locationToEdit.name);
          break;
        case 'city':
          // Regular city editing
          success = await updateCity(locationToEdit.id, {
            city: locationToEdit.name,
            datacenter: locationToEdit.datacenter || null
          });
          break;
        case 'datacenter':
          // Update the city's datacenter field via dedicated endpoint
          {
            const response = await fetch(`${API_URL}/api_admin_locations.php?f=update_city_datacenter`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                token: localStorage.getItem('admin_token'),
                city_id: locationToEdit.id,
                datacenter: locationToEdit.name
              })
            });

            if (!response.ok) {
              throw new Error(`HTTP error ${response.status}`);
            }

            const data = await response.json();
            success = data.success;
            if (success) {
              await fetchLocationData();
            } else {
              throw new Error(data.error || 'Failed to update datacenter');
            }
          }
          break;
        default:
          alert('Unknown location type');
          break;
      }

      if (success) {
        setShowEditLocationModal(false);
        setLocationToEdit(null);
      }
    } catch (err) {
      console.error('Error saving location edit:', err);
      alert(`Failed to save changes: ${err.message}`);
    }
  };

  // Handle save rack edit
  const handleSaveRackEdit = async () => {
    if (!rackToEdit) return;

    try {
      // Call the update_rack API
      const response = await fetch(`${API_URL}/api_admin_locations.php?f=update_rack`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          id: rackToEdit.id,
          rack_name: rackToEdit.rack_name,
          size: rackToEdit.size,
          country_id: rackToEdit.country_id,
          city_id: rackToEdit.city_id,
          notes: rackToEdit.notes
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Refresh location data
        await fetchLocationData();

        // If the edited rack was selected, update the selection
        if (selectedRack && selectedRack.id === rackToEdit.id) {
          // Find the updated rack
          const updatedRack = racks.find(rack => rack.id === rackToEdit.id);
          if (updatedRack) {
            setSelectedRack(updatedRack);
          }
        }

        setShowEditRackModal(false);
        setRackToEdit(null);
      } else {
        throw new Error(data.error || 'Failed to update rack');
      }
    } catch (err) {
      console.error('Error updating rack:', err);
      alert(`Failed to update rack: ${err.message}`);
    }
  };

  // Function to handle chassis click
  const handleChassisClick = (chassis) => {
    // First set the state to show the modal
    setSelectedChassis(chassis);

    // For direct navigation to the chassis detail page, use window.location.href
    // This ensures navigation works even when modals are open
    // window.location.href = `/admin/inventory/chassis/${chassis.id}`;
  };

  // Function to handle server click
  const handleServerClick = (server) => {
    // First set the state to show the modal
    setSelectedServer(server);

    // For direct navigation to the server detail page, use window.location.href
    // This ensures navigation works even when modals are open
    // window.location.href = `/admin/inventory/${server.type === 'blade' ? 'blade' : 'dedicated'}/${server.id}`;
  };

  // Function to handle switch click
  const handleSwitchClick = (switchDevice) => {
    // First set the state to show the modal
    setSelectedSwitch(switchDevice);

    // For direct navigation to the switch detail page, use window.location.href
    // This ensures navigation works even when modals are open
    // window.location.href = `/admin/inventory/switch/${switchDevice.id}`;
  };

  // Function to update device position after drag and drop
  const updateDevicePosition = async (deviceId, deviceType, newPosition) => {
    try {
      // Update the device's position in database
      const updateUrl = '${API_URL}/api_admin_locations.php?f=update_device_position';
      const response = await fetch(updateUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          device_id: deviceId,
          device_type: deviceType,
          position: newPosition
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Refresh the rack devices to show updated positions
        await fetchRackDevices(selectedRack.id);
        // Also refresh unassigned devices
        await fetchUnassignedDevices();
      } else {
        throw new Error(data.error || 'Failed to update device position');
      }
    } catch (err) {
      console.error("Error updating device position:", err);
    }
  };

  // Function to assign a device to a rack
  const assignDeviceToRack = async (deviceId, deviceType, rackId, position) => {
    try {
      const assignUrl = '${API_URL}/api_admin_locations.php?f=assign_device_to_rack';
      const response = await fetch(assignUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          device_id: deviceId,
          device_type: deviceType,
          rack_id: rackId,
          position: position
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Refresh both the rack devices and unassigned devices
        await fetchRackDevices(rackId);
        await fetchUnassignedDevices();
      } else {
        throw new Error(data.error || 'Failed to assign device to rack');
      }
    } catch (err) {
      console.error("Error assigning device to rack:", err);
    }
  };

  // Function to remove a device from rack and move to unassigned
  const removeDeviceFromRack = async (deviceId, deviceType) => {
    try {
      const unassignUrl = '${API_URL}/api_admin_locations.php?f=remove_device_from_rack';
      const response = await fetch(unassignUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          device_id: deviceId,
          device_type: deviceType
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Refresh both the rack devices and unassigned devices
        await fetchRackDevices(selectedRack.id);
        await fetchUnassignedDevices();
        return true;
      } else {
        throw new Error(data.error || 'Failed to unassign device from rack');
      }
    } catch (err) {
      console.error("Error unassigning device from rack:", err);
      return false;
    }
  };

  // Fetch devices in a rack
  const fetchRackDevices = async (rackId) => {
    setLoadingRacks(true);
    try {
      const response = await fetch(`${API_URL}/api_admin_locations.php?f=get_rack_devices`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          rack_id: rackId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      setRackDevices(data.devices || []);

      // Also fetch unassigned devices when rack is selected
      await fetchUnassignedDevices();
    } catch (err) {
      console.error("Error fetching rack devices:", err);
      setRackDevices([]);
    } finally {
      setLoadingRacks(false);
    }
  };

  // Handle search
  const handleSearch = (e) => {
    try {
      const query = e.target.value;
      setSearchQuery(query);

      // If search query is empty, don't auto-expand or collapse anything
      if (!query || query.trim() === '') {
        return;
      }

      // Create new expanded objects
      const newExpandedCountries = {};
      const newExpandedCities = {};

      // For each country
      countries.forEach(country => {
        try {
          // Check if country name matches search query
          const countryMatches = country.country &&
            country.country.toLowerCase().includes(query.toLowerCase());

          // Get cities for this country
          const countryCities = getCitiesForCountry(country.id);

          // Check if any city in this country matches search query
          const cityMatches = countryCities.some(city =>
            (city.city && city.city.toLowerCase().includes(query.toLowerCase())) ||
            (city.datacenter && city.datacenter.toLowerCase().includes(query.toLowerCase()))
          );

          // Get racks for this country
          const countryRacks = getRacksForCountry(country.id);

          // Check if any rack in this country matches search query
          const rackMatches = countryRacks.some(rack =>
            rack.rack_name && rack.rack_name.toLowerCase().includes(query.toLowerCase())
          );

          // Check if any datacenter in this country matches search query
          const countryDatacenters = datacenters.filter(dc => dc.country_id === country.id);
          const datacenterMatches = countryDatacenters.some(dc =>
            dc.name && dc.name.toLowerCase().includes(query.toLowerCase())
          );

          // If country matches or any of its children match, expand it
          if (countryMatches || cityMatches || rackMatches || datacenterMatches) {
            newExpandedCountries[country.id] = true;

            // Process cities within this country
            countryCities.forEach(city => {
              try {
                // Check if city name matches
                const cityNameMatches = city.city &&
                  city.city.toLowerCase().includes(query.toLowerCase());

                // Check if datacenter matches
                const datacenterMatches = city.datacenter &&
                  city.datacenter.toLowerCase().includes(query.toLowerCase());

                // Get racks for this city
                const cityRacks = getRacksForCity(city.id);

                // Check if any rack in this city matches
                const cityRackMatches = cityRacks.some(rack =>
                  rack.rack_name && rack.rack_name.toLowerCase().includes(query.toLowerCase())
                );

                // Check if any datacenter in this city matches
                const cityDatacenters = getDatacentersForCity(city.id);
                const cityDatacenterMatches = cityDatacenters.some(dc =>
                  dc.name && dc.name.toLowerCase().includes(query.toLowerCase())
                );

                // If city matches or any of its children match, expand it
                if (cityNameMatches || datacenterMatches || cityRackMatches || cityDatacenterMatches) {
                  newExpandedCities[city.id] = true;
                }
              } catch (cityErr) {
                console.error("Error processing city in search:", cityErr);
              }
            });
          }
        } catch (countryErr) {
          console.error("Error processing country in search:", countryErr);
        }
      });

      // Update expanded states
      setExpandedCountries(newExpandedCountries);
      setExpandedCities(newExpandedCities);
    } catch (err) {
      console.error("Error in search handler:", err);
    }
  };

  // Toggle country expansion
  const toggleCountryExpansion = (countryId, e) => {
    if (e) e.stopPropagation();
    setExpandedCountries(prev => ({
      ...prev,
      [countryId]: !prev[countryId]
    }));
  };

  // Toggle city expansion
  const toggleCityExpansion = (cityId, e) => {
    if (e) e.stopPropagation();
    setExpandedCities(prev => ({
      ...prev,
      [cityId]: !prev[cityId]
    }));
  };

  // Toggle datacenter expansion
  const toggleDatacenterExpansion = (datacenterId, e) => {
    if (e) e.stopPropagation();
    setExpandedDatacenters(prev => ({
      ...prev,
      [datacenterId]: !prev[datacenterId]
    }));
  };

  // Select a location (country, city, or datacenter) to view its contents
  const handleLocationSelect = (type, id, name) => {
    // Add a small delay to ensure any other click handlers have finished
    setTimeout(() => {
      setSelectedLocation({
        type,
        id,
        name
      });
      setSelectedRack(null);
      setRackDevices([]);
      setSelectedChassis(null);
      setSelectedServer(null);
      setSelectedSwitch(null);
    }, 10);
  };


  // Select a rack to view its details
  const handleRackSelect = async (rack) => {
    // Add a small delay to ensure any other click handlers have finished
    setTimeout(async () => {
      setSelectedRack(rack);
      setSelectedChassis(null);
      setSelectedServer(null);
      setSelectedSwitch(null);
      await fetchRackDevices(rack.id);
    }, 10);
  };
  // Open add location modal
  const openAddLocationModal = (type, parentId = null) => {
    // Log what we're doing for debugging
    console.log(`Opening ${type} modal with parent ID:`, parentId);

    setNewLocation({
      type,
      name: '',
      parentId,
      flagCode: ''
    });
    setShowAddLocationModal(true);
  };

  // Close add location modal
  const closeAddLocationModal = () => {
    setShowAddLocationModal(false);
  };

  // Open add rack modal
  const openAddRackModal = () => {
    // If a location is selected, pre-populate the rack data
    let initialRackData = {
      rack_name: '',
      size: 42,
      country_id: '',  // Changed from null to empty string
      city_id: ''      // Changed from null to empty string
    };

    if (selectedLocation) {
      console.log("Pre-populating rack data from selected location:", selectedLocation);

      if (selectedLocation.type === 'country') {
        initialRackData.country_id = selectedLocation.id;
      }
      else if (selectedLocation.type === 'city' || selectedLocation.type === 'datacenter') {
        // For city or datacenter, get both city and country
        const city = cities.find(c => c.id === selectedLocation.id);
        if (city) {
          initialRackData.city_id = city.id;
          initialRackData.country_id = city.country_id;
        }
      }
    }

    // Set the initial data
    setNewRack(initialRackData);
    setShowAddRackModal(true);
  };

  // Close add rack modal
  const closeAddRackModal = () => {
    setShowAddRackModal(false);
  };

  // Close edit location modal
  const closeEditLocationModal = () => {
    setShowEditLocationModal(false);
    setLocationToEdit(null);
  };

  // Close edit rack modal
  const closeEditRackModal = () => {
    setShowEditRackModal(false);
    setRackToEdit(null);
  };

  // Add a new location (country, city, or datacenter)
  const handleAddLocation = async () => {
    try {
      // Show loading state if needed
      // setIsSubmitting(true);

      console.log("Adding new location:", newLocation);

      // Create POST data based on location type
      let endpoint, postData;

      if (newLocation.type === 'country') {
        endpoint = 'add_country';
        postData = {
          country: newLocation.name,
          flag_code: newLocation.flagCode
        };
      }
      else if (newLocation.type === 'city') {
        endpoint = 'add_city';
        postData = {
          city: newLocation.name,
          country_id: newLocation.parentId
        };
      }
      else if (newLocation.type === 'datacenter') {
        // Check if the city already has a datacenter
        const city = cities.find(c => c.id === newLocation.parentId);
        if (city && city.datacenter && city.datacenter.trim() !== '') {
          throw new Error('This city already has a datacenter. Each city can only have one datacenter in the current system.');
        }
        
        endpoint = 'update_city_datacenter';
        postData = {
          city_id: newLocation.parentId,
          datacenter: newLocation.name
        };
      }

      // Make API call
      const response = await fetch(`${API_URL}/api_admin_locations.php?f=${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          ...postData
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Refresh location data
        await fetchLocationData();

        // If we added a country, expand it
        if (newLocation.type === 'country' && data.id) {
          toggleCountryExpansion(data.id);
        }

        // If we added a city, expand its parent country
        if (newLocation.type === 'city') {
          setExpandedCountries(prev => ({
            ...prev,
            [newLocation.parentId]: true
          }));
        }

        // Close the modal
        closeAddLocationModal();
      } else {
        throw new Error(data.error || 'Unknown error occurred');
      }
    } catch (error) {
      console.error(`Error adding ${newLocation.type}:`, error);
      alert(`Failed to add ${newLocation.type}: ${error.message}`);
    } finally {
      // Clear loading state if needed
      // setIsSubmitting(false);
    }
  };

  // Add a new rack
  const handleAddRack = async () => {
    try {
      // Validate required fields
      if (!newRack.rack_name) {
        throw new Error('Rack name is required');
      }
      if (!newRack.country_id) {
        throw new Error('Country is required');
      }
      if (!newRack.city_id) {
        throw new Error('City is required');
      }

      console.log("Adding new rack:", newRack);

      // Make API call
      const response = await fetch(`${API_URL}/api_admin_locations.php?f=add_rack`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          rack_name: newRack.rack_name,
          size: newRack.size || 42,
          country: newRack.country_id,
          city: newRack.city_id,
          notes: newRack.notes || ''
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Refresh location data
        await fetchLocationData();
        closeAddRackModal();
      } else {
        throw new Error(data.error || 'Unknown error occurred');
      }
    } catch (error) {
      console.error('Error adding rack:', error);
      alert(`Failed to add rack: ${error.message}`);
    }
  };

  // Get all countries for rendering
  // We'll do the actual filtering in the render function
  const filteredCountries = countries;

  // Get cities for a specific country
  const getCitiesForCountry = (countryId) => {
    if (!countryId) return []; // Handle empty values
    // Use loose equality (==) instead of strict equality (===) to handle type differences
    return cities.filter(city => city.country_id == countryId);
  };

  // Get racks for a specific city
  const getRacksForCity = (cityId) => {
    return racks.filter(rack => rack.city == cityId);
  };

  // Get racks for a specific country
  const getRacksForCountry = (countryId) => {
    const countryCities = cities.filter(city => city.country_id === countryId);
    const cityIds = countryCities.map(city => city.id);
    return racks.filter(rack => cityIds.includes(rack.city));
  };

  // Get datacenters for a specific city
  const getDatacentersForCity = (cityId) => {
    return datacenters.filter(datacenter => datacenter.city_id == cityId);
  };

  // Get racks for a specific datacenter
  const getRacksForDatacenter = (datacenterId) => {
    // Since datacenter ID is the same as city ID in current backend structure
    return racks.filter(rack => rack.city == datacenterId);
  };

  // Note: We use city.datacenter directly in the search functionality

  // Get the country name from a country ID
  const getCountryName = (countryId) => {
    const country = countries.find(country => country.id === countryId);
    return country ? country.country : '';
  };

  // Get the city name from a city ID
  const getCityName = (cityId) => {
    const city = cities.find(city => city.id === cityId);
    return city ? city.city : '';
  };

  // Get device color based on device type
  const getDeviceColor = (type) => {
    switch(type) {
      case 'switch':
        return 'orange'; // Orange for switches
      case 'dedicated':
        return 'emerald'; // Emerald green for servers
      case 'chassis':
      default:
        return 'indigo'; // Keep indigo for chassis
    }
  };

  // Get device icon based on device type
  const getDeviceTypeIcon = (type, className = "w-4 h-4") => {
    const color = getDeviceColor(type);

    switch(type) {
      case 'dedicated':
        return <Server className={className} style={{ color: `var(--color-${color}-700)` }} />;
      case 'switch':
        return <Network className={className} style={{ color: `var(--color-${color}-700)` }} />;
      case 'chassis':
        return <HardDrive className={className} style={{ color: `var(--color-${color}-700)` }} />;
      default:
        return null;
    }
  };

  // Format device name to be more readable
  const formatDeviceName = (device) => {
    if (!device.label) return "Unnamed Device";

    return device.label;
  };

  // Render rack cards based on selected location
  const renderRackCards = () => {
    let locationRacks = [];

    if (selectedLocation) {
      if (selectedLocation.type === 'country') {
        locationRacks = getRacksForCountry(selectedLocation.id);
      } else if (selectedLocation.type === 'city') {
        locationRacks = getRacksForCity(selectedLocation.id);
      } else if (selectedLocation.type === 'datacenter') {
        locationRacks = getRacksForDatacenter(selectedLocation.id);
      }
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
        {locationRacks.map(rack => (
          <div
            key={rack.id}
            className={`bg-white border rounded-md shadow-sm p-4 cursor-pointer transition-all ${
              selectedRack && selectedRack.id === rack.id ? 'border-indigo-500 ring-2 ring-indigo-200' : 'border-gray-200 hover:border-indigo-300'
            }`}
            onClick={(e) => {
              // Make sure to stop event propagation to prevent bubbling
              e.stopPropagation();
              handleRackSelect(rack);
            }}
          >
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-medium text-indigo-700">{rack.rack_name}</h3>
              <div className="flex space-x-1">
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{rack.size || 42}U</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEditRack(rack);
                  }}
                  className="p-1 text-gray-500 hover:text-indigo-700 hover:bg-gray-100 rounded-full"
                  title="Edit Rack"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteClick('rack', rack.id, rack.rack_name);
                  }}
                  className={`p-1 text-gray-500 hover:text-red-700 hover:bg-gray-100 rounded-full ${rackDevices.length > 0 ? 'text-gray-300 cursor-not-allowed' : ''}`}
                  title="Delete Rack"
                  disabled={rackDevices.length > 0}
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-2 text-sm text-gray-600 mb-3">
              <div>
                <div className="text-xs text-gray-500">City</div>
                <div className="font-medium flex items-center">
                  <MapPin className="w-3 h-3 mr-1 text-indigo-600" />
                  {getCityName(rack.city)}
                </div>
              </div>

              <div>
                <div className="text-xs text-gray-500">Country</div>
                <div className="font-medium flex items-center">
                  <Globe className="w-3 h-3 mr-1 text-indigo-600" />
                  {getCountryName(rack.country)}
                </div>
              </div>
            </div>

            {rack.datacenter && (
              <div className="text-xs flex items-center text-gray-500 mt-2">
                <Building className="w-3 h-3 mr-1" />
                {rack.datacenter}
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };
  // Updated RackVisualization component with unassigned devices and bidirectional drag & drop
  const RackVisualization = ({ rack, devices }) => {
    // State variables
    const [draggingDevice, setDraggingDevice] = useState(null);
    const [hoveredPosition, setHoveredPosition] = useState(null);
    const [validDropPositions, setValidDropPositions] = useState([]);
    const [unassignedFilter, setUnassignedFilter] = useState('');
    const [dragOverUnassigned, setDragOverUnassigned] = useState(false);

    // Get rack size (default to 42U)
    const rackSize = rack.size || 42;

    // Process devices to track which rack units are occupied
    const rackMap = Array(rackSize + 1).fill(null); // +1 because rack positions are 1-indexed

    // Map devices to their positions in the rack
    devices.forEach(device => {
      const position = parseInt(device.position);
      if (isNaN(position)) return;

      // Get device size - ensure we're using the correct size for different device types
      let size = parseInt(device.size) || 1;

      // Special handling for chassis - if a chassis has a set size in the data, use that
      // Otherwise, force chassis to be 2U (can be adjusted as needed)
      if (device.type === 'chassis' && (!device.size || device.size === 0)) {
        size = 2; // Default chassis size if not specified
      }

      // Mark all rack units occupied by this device
      for (let i = 0; i < size; i++) {
        if (position - i <= rackSize && position - i > 0) {
          rackMap[position - i] = {
            device,
            isStart: i === 0, // This is the starting position for the device
            isEnd: i === size - 1, // This is the ending position for the device
            relativePosition: i, // Position relative to the device start (0 = start)
            deviceActualSize: size // Store the actual device size for reference
          };
        }
      }
    });

    // Generate rack units from top to bottom (standard rack numbering)
    const rackUnits = Array.from({ length: rackSize }, (_, i) => rackSize - i);

    // Calculate valid drop positions for the dragging device
    useEffect(() => {
      if (!draggingDevice) {
        setValidDropPositions([]);
        return;
      }

      // Calculate device size
      const deviceSize = parseInt(draggingDevice.size) || 1;

      // Find all valid positions where this device could be placed
      const validPositions = [];

      // Check each rack position
      for (let position = rackSize; position >= 1; position--) {
        let isValid = true;

        // Check if all required units for this device are free
        for (let i = 0; i < deviceSize; i++) {
          // If position is out of bounds, not valid
          if (position - i <= 0) {
            isValid = false;
            break;
          }

          // If this position is occupied by another device, not valid
          // Exception: if it's the same device we're dragging, then it's valid
          const currentUnit = rackMap[position - i];
          if (currentUnit && currentUnit.device) {
            const currentDevice = currentUnit.device;
            if (currentDevice.id !== draggingDevice.id ||
                currentDevice.type !== draggingDevice.type) {
              isValid = false;
              break;
            }
          }
        }

        if (isValid) {
          validPositions.push(position);
        }
      }

      setValidDropPositions(validPositions);
    }, [draggingDevice, rackMap, rackSize]);

    // Handle drag start for a device
    const handleDragStart = (device, e) => {
      // Set data for drag operation
      const dragData = JSON.stringify({
        id: device.id,
        type: device.type,
        position: device.position || null, // Might be null for unassigned devices
        size: device.size || 1,
        rack_id: device.rack_id || rack.id // For rack devices
      });

      e.dataTransfer.setData('application/json', dragData);
      e.dataTransfer.effectAllowed = 'move';

      setDraggingDevice(device);
    };

    // Handle drag end
    const handleDragEnd = () => {
      setDraggingDevice(null);
      setHoveredPosition(null);
      setDragOverUnassigned(false);
    };

    // Handle drag over a position
    const handleDragOver = (position, e) => {
      e.preventDefault();

      // Only allow dropping if this is a valid position
      if (validDropPositions.includes(position)) {
        e.dataTransfer.dropEffect = 'move';
        setHoveredPosition(position);
      } else {
        e.dataTransfer.dropEffect = 'none';
      }
    };

    // Handle drag over unassigned panel
    const handleUnassignedPanelDragOver = (e) => {
      // Only allow dropping if we're dragging a device from the rack (has position)
      if (draggingDevice && draggingDevice.position) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        setDragOverUnassigned(true);
      }
    };

    // Handle drop on unassigned panel
    const handleUnassignedPanelDrop = async (e) => {
      e.preventDefault();
      setDragOverUnassigned(false);

      try {
        const data = JSON.parse(e.dataTransfer.getData('application/json'));

        // Only process if the device has a position (it's coming from a rack)
        if (data.position) {
          await removeDeviceFromRack(data.id, data.type);
        }
      } catch (err) {
        console.error("Error processing unassign drop:", err);
      }

      setDraggingDevice(null);
    };

    // Handle drop - This will update the device position in the UI
    const handleDrop = async (position, e) => {
      e.preventDefault();

      // Get the dragged device data
      try {
        const data = JSON.parse(e.dataTransfer.getData('application/json'));

        // Check if valid position
        if (!validDropPositions.includes(position)) {
          console.error("Invalid drop position");
          return;
        }

        // If the device is already in a rack (has position and rack_id = current rack)
        if (data.position && data.rack_id === rack.id) {
          // Moving within the same rack
          await updateDevicePosition(data.id, data.type, position);
        }
        // If device is unassigned or from another rack
        else {
          // Assign to this rack
          await assignDeviceToRack(data.id, data.type, rack.id, position);
        }
      } catch (err) {
        console.error("Error processing drop", err);
      }

      setDraggingDevice(null);
      setHoveredPosition(null);
    };

    // Filter unassigned devices based on the filter
    const filteredUnassignedDevices = unassignedFilter
      ? unassignedDevices.filter(device => device.type === unassignedFilter)
      : unassignedDevices;

    return (
      <div className="bg-white rounded-md shadow-sm border p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-800">{rack.rack_name}</h3>
          <div className="text-sm text-gray-500">{rackSize}U</div>
        </div>

        {/* Using a 10-column grid with 60/40 split */}
        <div className="grid grid-cols-10 gap-4">
          {/* LEFT SIDE: Unassigned Devices - now 60% (6 columns) */}
          <div className="col-span-6">
            <div className="text-sm text-gray-600 mb-3">
              <div className="font-medium flex items-center">
                Unassigned Devices
                <div className="relative ml-2 group">
                  <Info className="w-4 h-4 text-gray-400 cursor-help" />
                  <div className="absolute left-0 bottom-full mb-2 hidden group-hover:block bg-white p-2 rounded shadow-lg border border-gray-200 text-xs w-48 z-10">
                    Drag devices to assign them to the rack, or drag devices from the rack here to unassign them.
                  </div>
                </div>
              </div>
              <p>Drag devices to assign or unassign</p>
            </div>

            {/* Filter buttons for unassigned devices */}
            <div className="flex items-center space-x-2 mb-3">
              <button
                className={`px-3 py-1 text-xs rounded-full ${unassignedFilter === '' ? 'bg-gray-700 text-white' : 'bg-gray-200 text-gray-700'}`}
                onClick={() => setUnassignedFilter('')}
              >
                All
              </button>
              <button
                className={`px-3 py-1 text-xs rounded-full ${unassignedFilter === 'dedicated' ? 'bg-emerald-700 text-white' : 'bg-emerald-100 text-emerald-700'}`}
                onClick={() => setUnassignedFilter('dedicated')}
              >
                Servers
              </button>
              <button
                className={`px-3 py-1 text-xs rounded-full ${unassignedFilter === 'switch' ? 'bg-orange-700 text-white' : 'bg-orange-100 text-orange-700'}`}
                onClick={() => setUnassignedFilter('switch')}
              >
                Switches
              </button>
              <button
                className={`px-3 py-1 text-xs rounded-full ${unassignedFilter === 'chassis' ? 'bg-indigo-700 text-white' : 'bg-indigo-100 text-indigo-700'}`}
                onClick={() => setUnassignedFilter('chassis')}
              >
                Chassis
              </button>
            </div>

            <div
              className={`border border-gray-200 rounded-md ${dragOverUnassigned ? 'ring-2 ring-blue-500 bg-blue-50' : ''}`}
              onDragOver={handleUnassignedPanelDragOver}
              onDragLeave={() => setDragOverUnassigned(false)}
              onDrop={handleUnassignedPanelDrop}
            >
              <div className="bg-gray-50 px-3 py-2 border-b border-gray-200 text-sm font-medium">
                Unassigned Devices ({filteredUnassignedDevices.length})
              </div>
              <div className="max-h-[600px] overflow-y-auto">
                {loadingUnassigned ? (
                  <div className="flex justify-center items-center p-4">
                    <RefreshCw className="w-5 h-5 animate-spin text-gray-500 mr-2" />
                    <span className="text-gray-500 text-sm">Loading unassigned devices...</span>
                  </div>
                ) : filteredUnassignedDevices.length > 0 ? (
                  <div className="divide-y divide-gray-100">
                    {filteredUnassignedDevices.map((device) => {
                      const deviceColor = getDeviceColor(device.type);
                      return (
                        <div
                          key={`${device.type}-${device.id}`}
                          className="flex items-center p-3 hover:bg-gray-50 cursor-grab active:cursor-grabbing transition-colors hover:shadow-md"
                          draggable
                          onDragStart={(e) => handleDragStart(device, e)}
                          onDragEnd={handleDragEnd}
                          onClick={() => {
                            if (device.type === 'chassis') handleChassisClick(device);
                            else if (device.type === 'dedicated') handleServerClick(device);
                            else if (device.type === 'switch') handleSwitchClick(device);
                          }}
                        >
                          <div className="mr-3">
                            {getDeviceTypeIcon(device.type, "w-5 h-5")}
                          </div>
                          <div className="flex justify-between items-center w-full">
                            <div>
                              <div className="text-sm font-medium" style={{ color: `var(--color-${deviceColor}-700)` }}>
                                {formatDeviceName(device)}
                              </div>
                              <div className="text-xs text-gray-500">
                                Size: {device.size || 1}U | {device.status || 'Unknown'}
                              </div>
                            </div>
                            <div className="text-xs px-2 py-1 rounded-full" style={{
                              backgroundColor: `var(--color-${deviceColor}-100)`,
                              color: `var(--color-${deviceColor}-800)`
                            }}>
                              {device.type === 'dedicated' ? 'Server' :
                               device.type === 'switch' ? 'Switch' :
                               device.type === 'chassis' ? 'Chassis' :
                               device.type}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="p-4 text-center text-sm text-gray-500">
                    No unassigned devices available
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* RIGHT SIDE: Rack Visualization - now 40% (4 columns) */}
          <div className="col-span-4 bg-gray-50 border border-gray-300 rounded overflow-hidden">
            {/* Rack header */}
            <div className="bg-gray-100 p-2 border-b border-gray-300 text-center text-sm font-medium">
              Front of Rack
            </div>

            {/* Rack units */}
            <div className="max-h-[600px] overflow-y-auto">
              <table className="w-full border-collapse">
                <tbody>
                  {rackUnits.map(unit => {
                    const rackMapEntry = rackMap[unit];
                    const hasDevice = rackMapEntry !== null;
                    const isDeviceStart = hasDevice && rackMapEntry.isStart;
                    const isDeviceEnd = hasDevice && rackMapEntry.isEnd;
                    const device = hasDevice ? rackMapEntry.device : null;
                    const deviceSize = hasDevice ? rackMapEntry.deviceActualSize : 0;
                    const relativePosition = hasDevice ? rackMapEntry.relativePosition : 0;

                    // Skip rendering device cells for continuation units
                    if (hasDevice && !isDeviceStart && relativePosition > 0) {
                      // Only render the unit number cell for these rows
                      return (
                        <tr
                          key={unit}
                          className={`border-t border-gray-200 ${
                            draggingDevice && device &&
                            draggingDevice.id === device.id &&
                            draggingDevice.type === device.type ? "opacity-50" : ""
                          }`}
                          onDragOver={(e) => handleDragOver(unit, e)}
                          onDrop={(e) => handleDrop(unit, e)}
                        >
                          <td className="w-7 bg-gray-100 text-center text-xs font-medium border-r border-gray-200 py-1">
                            {unit}
                          </td>
                          {/* No second cell rendered for continuation rows */}
                        </tr>
                      );
                    }

                    // Check if this is a valid drop position when dragging
                    const isValidDropPosition = validDropPositions.includes(unit);
                    const isHighlighted = hoveredPosition === unit && isValidDropPosition;

                    // Get the device color
                    let deviceColor = hasDevice ? getDeviceColor(device.type) : null;

                    // Special classes for start and end of multi-unit devices
                    let unitClasses = "border-t border-gray-200";

                    if (isHighlighted) {
                      unitClasses += " bg-blue-100";
                    } else if (hasDevice) {
                      // Special styling for device cells based on type
                      unitClasses += ` bg-${deviceColor}-50`;

                      // Only add top border on the first unit of a device
                      if (isDeviceStart) unitClasses += ` border-t-2 border-t-${deviceColor}-200`;

                      // Only add bottom border on the last unit of a device
                      if (isDeviceEnd) unitClasses += ` border-b-2 border-b-${deviceColor}-200`;
                    }

                    // Special class when dragging this device
                    if (draggingDevice && device &&
                        draggingDevice.id === device.id &&
                        draggingDevice.type === device.type) {
                      unitClasses += " opacity-50";
                    }

                    // Add highlight for valid drop positions
                    if (isValidDropPosition && draggingDevice) {
                      unitClasses += isHighlighted ? " bg-blue-200 border-blue-400" : " bg-blue-50";
                    }

                    return (
                      <tr
                        key={unit}
                        className={unitClasses}
                        onDragOver={(e) => handleDragOver(unit, e)}
                        onDrop={(e) => handleDrop(unit, e)}
                      >
                        <td className="w-7 bg-gray-100 text-center text-xs font-medium border-r border-gray-200 py-1">
                          {unit}
                        </td>
                        {isDeviceStart && deviceSize > 0 ? (
                          <td className="p-0" rowSpan={deviceSize}>
                            <div
                              className="flex items-center justify-between px-2 py-1 h-full relative cursor-pointer"
                              draggable={true}
                              onDragStart={(e) => {
                                // Make sure to include position in the drag data
                                const deviceWithPosition = {
                                  ...device,
                                  position: unit,
                                  rack_id: rack.id
                                };
                                handleDragStart(deviceWithPosition, e);
                              }}
                              onDragEnd={handleDragEnd}
                              onClick={() => {
                                if (device.type === 'chassis') handleChassisClick(device);
                                else if (device.type === 'dedicated') handleServerClick(device);
                                else if (device.type === 'switch') handleSwitchClick(device);
                              }}
                            >
                              <div className="flex items-center">
                                {getDeviceTypeIcon(device.type)}
                                <span className="ml-1.5 font-medium text-xs truncate max-w-[80px]" style={{ color: `var(--color-${deviceColor}-800)` }}>
                                  {device.label}
                                </span>
                              </div>
                              <div className="flex items-center">
                                <div className="text-xs px-1.5 py-0.5 rounded" style={{
                                  backgroundColor: `var(--color-${deviceColor}-100)`,
                                  color: `var(--color-${deviceColor}-800)`
                                }}>
                                  {deviceSize}U
                                </div>
                                <Move className="w-3 h-3 ml-1 text-gray-400" />
                              </div>
                            </div>
                          </td>
                        ) : !hasDevice ? (
                          <td className="p-0">
                            <div className="h-7 px-2 py-1 text-xs text-gray-400"></div> {/* Empty rack unit */}
                          </td>
                        ) : null}
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* Rack footer */}
            <div className="bg-gray-100 p-2 border-t border-gray-300 text-center text-sm font-medium">
              Bottom of Rack
            </div>
          </div>
        </div>
      </div>
    );
  };

  // ServerDetailView Component
  const ServerDetailView = ({ server, onClose }) => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [serverDetails, setServerDetails] = useState(null);
    const [cpuInfo, setCpuInfo] = useState(null);
    const [ramInfo, setRamInfo] = useState(null);

    useEffect(() => {
      const fetchServerDetails = async () => {
        try {
          setLoading(true);

          // Fetch server details - using the get_blade_server_by_id or get_dedicated_server_by_id based on type
          const endpoint = server.type === 'dedicated'
            ? 'get_dedicated_server_by_id'
            : 'get_blade_server_by_id';

          const response = await fetch(`${API_URL}/api_admin_inventory.php?f=${endpoint}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: localStorage.getItem('admin_token'),
              id: server.id
            })
          });

          if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
          }

          const data = await response.json();
          if (data.success && data.server) {
            setServerDetails(data.server);
          } else {
            throw new Error(data.error || 'Failed to fetch server details');
          }

          // Fetch CPU information
          const cpuResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_dedicated_cpus`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: localStorage.getItem('admin_token')
            })
          });

          if (cpuResponse.ok) {
            const cpuData = await cpuResponse.json();
            setCpuInfo(cpuData);
          }

          // Fetch RAM configurations
          const ramResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_ram_configurations`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: localStorage.getItem('admin_token')
            })
          });

          if (ramResponse.ok) {
            const ramData = await ramResponse.json();
            setRamInfo(ramData);
          }

          setLoading(false);
        } catch (err) {
          console.error("Error fetching server details:", err);
          setError("Failed to load server details. Please try again.");
          setLoading(false);
        }
      };

      fetchServerDetails();
    }, [server.id, server.type]);

    const getCpuName = (cpuId) => {
      if (!cpuId || !cpuInfo) return 'Not specified';
      const cpu = cpuInfo.find(c => c.id == cpuId);
      return cpu ? cpu.cpu : `ID: ${cpuId}`;
    };

    const getRamDescription = (ramId) => {
      if (!ramId || !ramInfo) return 'Not specified';
      const ram = ramInfo.find(r => r.id == ramId);
      return ram ? `${ram.size}GB ${ram.description}` : `ID: ${ramId}`;
    };

    const handleBackgroundClick = (e) => {
      if (e.target === e.currentTarget) {
        onClose();
      }
    };

    return (
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={handleBackgroundClick}
      >
        <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-auto" onClick={e => e.stopPropagation()}>
          <div className="p-4 border-b flex justify-between items-center bg-emerald-50">
            <h3 className="text-lg font-bold text-emerald-800 flex items-center">
              <Server className="w-5 h-5 mr-2 text-emerald-700" />
              Server: {server.label}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-200"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="p-6">
            {loading ? (
              <div className="text-center py-8">
                <RefreshCw className="w-8 h-8 animate-spin mr-2 text-emerald-700 mx-auto mb-3" />
                <span className="text-gray-600">Loading server information...</span>
              </div>
            ) : error ? (
              <div className="p-4 bg-red-50 text-red-700 rounded-md flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2 text-red-500" />
                {error}
              </div>
            ) : serverDetails ? (
              <div className="space-y-6">
                {/* Server Overview */}
                <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                  <h4 className="text-base font-semibold text-gray-700 mb-3 flex items-center">
                    <Info className="w-4 h-4 mr-2 text-emerald-600" />
                    Server Overview
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <div className="text-xs text-gray-500">Status</div>
                      <div className="font-medium">
                        {serverDetails.status || 'Unknown'}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Type</div>
                      <div className="font-medium capitalize">
                        {server.type || 'Unknown'}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Position</div>
                      <div className="font-medium">
                        {serverDetails.position ? `U${serverDetails.position}` : 'Unassigned'}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Size</div>
                      <div className="font-medium">{serverDetails.size || 1}U</div>
                    </div>
                  </div>
                </div>

                {/* Hardware Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* CPU & RAM */}
                  <div className="bg-emerald-50 p-4 rounded-md border border-emerald-200">
                    <h4 className="text-base font-semibold text-emerald-800 mb-3 flex items-center">
                      <Cpu className="w-4 h-4 mr-2 text-emerald-600" />
                      CPU & Memory
                    </h4>
                    <div className="space-y-4">
                      <div>
                        <div className="text-xs text-gray-600">CPU</div>
                        <div className="font-medium">{getCpuName(serverDetails.cpu)}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">Memory</div>
                        <div className="font-medium">{getRamDescription(serverDetails.ram)}</div>
                      </div>
                    </div>
                  </div>

                  {/* Network Information */}
                  <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
                    <h4 className="text-base font-semibold text-blue-800 mb-3 flex items-center">
                      <Network className="w-4 h-4 mr-2 text-blue-600" />
                      Network
                    </h4>
                    <div className="space-y-4">
                      <div>
                        <div className="text-xs text-gray-600">MAC Address</div>
                        <div className="font-medium break-all">{serverDetails.mac || 'Not available'}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-600">IP Address</div>
                        <div className="font-medium">{serverDetails.main_ip || 'Not available'}</div>
                      </div>
                      {serverDetails.ipmi && (
                        <div>
                          <div className="text-xs text-gray-600">IPMI</div>
                          <div className="font-medium">{serverDetails.ipmi}</div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Storage Information - Only for servers with storage bays */}
                {server.type === 'dedicated' && (
                  <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                    <h4 className="text-base font-semibold text-gray-700 mb-3 flex items-center">
                      <HardDrive className="w-4 h-4 mr-2 text-gray-600" />
                      Storage
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {Array.from({ length: 10 }).map((_, index) => {
                        const bayField = `bay${index + 1}`;
                        return serverDetails[bayField] ? (
                          <div key={bayField}>
                            <div className="text-xs text-gray-500">Bay {index + 1}</div>
                            <div className="font-medium">{serverDetails[bayField]}</div>
                          </div>
                        ) : null;
                      })}
                      {!Object.keys(serverDetails).some(key => key.startsWith('bay') && serverDetails[key]) && (
                        <div className="col-span-4 text-gray-500 text-sm">
                          No storage information available
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Notes Section */}
                {serverDetails.notes && (
                  <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                    <h4 className="text-base font-semibold text-gray-700 mb-2">Notes</h4>
                    <div className="text-sm whitespace-pre-wrap">{serverDetails.notes}</div>
                  </div>
                )}
              </div>
            ) : (
              <div className="p-4 bg-yellow-50 text-yellow-700 rounded-md flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2 text-yellow-500" />
                No server details available
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // SwitchDetailView Component
  const SwitchDetailView = ({ switchDevice, onClose }) => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [switchDetails, setSwitchDetails] = useState(null);
    const [switchPorts, setSwitchPorts] = useState([]);

    useEffect(() => {
      const fetchSwitchDetails = async () => {
        try {
          setLoading(true);

          // Retrieve full switch information from backend
          const detailsResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_switch_by_id`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: localStorage.getItem('admin_token'),
              id: switchDevice.id
            })
          });

          if (detailsResponse.ok) {
            const detailsData = await detailsResponse.json();

            if (detailsData.success && detailsData.switch) {
              setSwitchDetails(detailsData.switch);
            } else {
              // Fallback to the data we already have if API did not return expected payload
              setSwitchDetails(switchDevice);
            }
          } else {
            // If request fails, fallback to the passed device data
            setSwitchDetails(switchDevice);
          }

          // Fetch switch ports
          const portsResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_switch_ports`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: localStorage.getItem('admin_token'),
              switch_id: switchDevice.id
            })
          });

          if (portsResponse.ok) {
            const portsData = await portsResponse.json();
            setSwitchPorts(portsData || []);
          }

          setLoading(false);
        } catch (err) {
          console.error("Error fetching switch details:", err);
          setError("Failed to load switch details. Please try again.");
          setLoading(false);
        }
      };

      fetchSwitchDetails();
    }, [switchDevice.id, switchDevice]);

    const handleBackgroundClick = (e) => {
      if (e.target === e.currentTarget) {
        onClose();
      }
    };

    return (
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={handleBackgroundClick}
      >
        <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-auto" onClick={e => e.stopPropagation()}>
          <div className="p-4 border-b flex justify-between items-center bg-orange-50">
            <h3 className="text-lg font-bold text-orange-800 flex items-center">
              <Network className="w-5 h-5 mr-2 text-orange-700" />
              Switch: {switchDevice.label}
            </h3><div>
            <button
              onClick={() => {
                window.location.href = `/admin/inventory/switch/${switchDevice.id}`;
              }}
              className="text-gray-500 hover:text-indigo-700 p-1 rounded-full hover:bg-gray-200"
              title="Open Full Switch View"
            >
              <ExternalLink className="w-5 h-5" />
            </button>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-200"
            >
              <X className="w-5 h-5" />
            </button>
            </div>
          </div>

          <div className="p-6">
            {loading ? (
              <div className="text-center py-8">
                <RefreshCw className="w-8 h-8 animate-spin mr-2 text-orange-700 mx-auto mb-3" />
                <span className="text-gray-600">Loading switch information...</span>
              </div>
            ) : error ? (
              <div className="p-4 bg-red-50 text-red-700 rounded-md flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2 text-red-500" />
                {error}
              </div>
            ) : switchDetails ? (
              <div className="space-y-6">
                {/* Switch Overview */}
                <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                  <h4 className="text-base font-semibold text-gray-700 mb-3 flex items-center">
                    <Info className="w-4 h-4 mr-2 text-orange-600" />
                    Switch Overview
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <div className="text-xs text-gray-500">Status</div>
                      <div className="font-medium">
                        {switchDetails.status || 'Unknown'}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Model</div>
                      <div className="font-medium">
                        {switchDetails.model || 'Unknown'}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Position</div>
                      <div className="font-medium">
                        {switchDetails.rack_position ? `U${switchDetails.rack_position}` : 'Unassigned'}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Size</div>
                      <div className="font-medium">{switchDetails.size_ru || 1}U</div>
                    </div>
                  </div>
                </div>

                {/* Network Information */}
                <div className="bg-orange-50 p-4 rounded-md border border-orange-200">
                  <h4 className="text-base font-semibold text-orange-800 mb-3 flex items-center">
                    <Network className="w-4 h-4 mr-2 text-orange-600" />
                    Network Settings
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-xs text-gray-600">IP Address</div>
                      <div className="font-medium">{switchDetails.switch_ip || 'Not configured'}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-600">SNMP Community</div>
                      <div className="font-medium">
                        {switchDetails.snmp_community ?
                          '••••••••' :
                          'Not configured'}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-600">SNMP Version</div>
                      <div className="font-medium">{switchDetails.snmp_version || 'Not configured'}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-600">SNMP Port</div>
                      <div className="font-medium">{switchDetails.snmp_port || '161 (default)'}</div>
                    </div>
                  </div>
                </div>

                {/* Switch Ports */}
                <div className="border border-gray-200 rounded-md">
                  <div className="bg-gray-50 px-3 py-2 border-b border-gray-200 flex justify-between items-center">
                    <h4 className="text-base font-semibold text-gray-700 flex items-center">
                      <Settings className="w-4 h-4 mr-2 text-gray-600" />
                      Switch Ports
                    </h4>
                    <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">
                      {switchPorts.length} ports
                    </span>
                  </div>

                  <div className="max-h-[400px] overflow-y-auto">
                    {switchPorts.length > 0 ? (
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Port</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Speed</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Connected Device</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {switchPorts.map(port => (
                            <tr key={port.id} className="hover:bg-gray-50">
                              <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                {port.port_number}
                              </td>
                              <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                                {port.port_type || 'Standard'}
                              </td>
                              <td className="px-4 py-2 whitespace-nowrap text-sm">
                                <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                  ${port.status === 'Available' ? 'bg-green-100 text-green-800' :
                                    port.status === 'Used' ? 'bg-blue-100 text-blue-800' :
                                    port.status === 'Disabled' ? 'bg-red-100 text-red-800' :
                                    'bg-gray-100 text-gray-800'}`}>
                                  {port.status}
                                </span>
                              </td>
                              <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                                {port.max_speed ? `${port.max_speed} Mbps` : 'Unknown'}
                              </td>
                              <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                                {port.connected_device_id ?
                                  `${port.connected_device_type} (ID: ${port.connected_device_id})` :
                                  'None'}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    ) : (
                      <div className="p-4 text-center text-sm text-gray-500">
                        No ports available for this switch
                      </div>
                    )}
                  </div>
                </div>

                {/* Notes Section */}
                {switchDetails.notes && (
                  <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                    <h4 className="text-base font-semibold text-gray-700 mb-2">Notes</h4>
                    <div className="text-sm whitespace-pre-wrap">{switchDetails.notes}</div>
                  </div>
                )}
              </div>
            ) : (
              <div className="p-4 bg-yellow-50 text-yellow-700 rounded-md flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2 text-yellow-500" />
                No switch details available
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const resetAllStates = () => {
    setSelectedLocation(null);
    setSelectedRack(null);
    setRackDevices([]);
    setSelectedChassis(null);
    setSelectedServer(null);
    setSelectedSwitch(null);
    setShowAddLocationModal(false);
    setShowAddRackModal(false);
    setShowEditLocationModal(false);
    setShowEditRackModal(false);
    setShowDeleteConfirmation(false);
    setUnassignedDevices([]);
    setExpandedDatacenters({});
  };

  // ChassisDetailView Component
  const ChassisDetailView = ({ chassis, onClose }) => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [bladeServers, setBladeServers] = useState([]);
    const [cpuData, setCpuData] = useState([]);
    const [ramData, setRamData] = useState([]);

    // Fetch all the necessary data
    useEffect(() => {
      const fetchAllData = async () => {
        try {
          setLoading(true);

          // Fetch blade servers for this chassis
          const bladeResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_blade_servers_by_chassis`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: localStorage.getItem('admin_token'),
              chassis_id: chassis.id
            })
          });

          if (!bladeResponse.ok) {
            throw new Error(`HTTP error ${bladeResponse.status}`);
          }

          const bladeData = await bladeResponse.json();

          // Sort blade servers by ID or label if possible
          const sortedBladeServers = [...bladeData].sort((a, b) => {
            // Try to sort by numeric part in the label (e.g., BLxSy where y is the slot number)
            const aMatch = a.label && a.label.match(/S(\d+)/i);
            const bMatch = b.label && b.label.match(/S(\d+)/i);

            if (aMatch && bMatch) {
              return parseInt(aMatch[1]) - parseInt(bMatch[1]);
            }

            // Fallback to sorting by ID
            return parseInt(a.id) - parseInt(b.id);
          });

          setBladeServers(sortedBladeServers);

          // Fetch CPU data
          const cpuResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_dedicated_cpus`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: localStorage.getItem('admin_token')
            })
          });

          if (cpuResponse.ok) {
            const cpuList = await cpuResponse.json();
            setCpuData(cpuList);
          }

          // Fetch RAM data
          const ramResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_ram_configurations`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: localStorage.getItem('admin_token')
            })
          });

          if (ramResponse.ok) {
            const ramList = await ramResponse.json();
            setRamData(ramList);
          }

          setLoading(false);
        } catch (err) {
          console.error("Error fetching data:", err);
          setError("Failed to load blade server data. Please try again.");
          setLoading(false);
        }
      };

      fetchAllData();
    }, [chassis.id]);

    // Get CPU name from ID
    const getCpuName = (cpuId) => {
      if (!cpuId) return 'Not specified';
      const cpu = cpuData.find(c => c.id == cpuId);
      return cpu ? cpu.cpu : `ID: ${cpuId}`;
    };

    // Get RAM description from ID
    const getRamDescription = (ramId) => {
      if (!ramId) return 'Not specified';
      const ram = ramData.find(r => r.id == ramId);
      return ram ? `${ram.description}` : `ID: ${ramId}`;
    };

    // Handle background click to close the modal
    const handleBackgroundClick = (e) => {
      if (e.target === e.currentTarget) {
        onClose();
      }
    };

    // Get a blade server for a specific bay
    const getBladeServerForBay = (bayNumber) => {
      // If we have fewer blade servers than the bay number, there's no server for this bay
      if (bladeServers.length < bayNumber) {
        return null;
      }

      // Try to find a server with a slot number in its label
      const serverWithSlotNumber = bladeServers.find(server => {
        const match = server.label && server.label.match(/S(\d+)/i);
        return match && parseInt(match[1]) === bayNumber;
      });

      if (serverWithSlotNumber) {
        return serverWithSlotNumber;
      }

      // If we can't find by slot number, just use the index
      // The array is already sorted, so we can just use index
      return bladeServers[bayNumber - 1];
    };

    return (
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={handleBackgroundClick}
      >
        <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-auto" onClick={e => e.stopPropagation()}>
          <div className="p-4 border-b flex justify-between items-center bg-indigo-50">
            <h3 className="text-lg font-bold text-indigo-800 flex items-center">
              <HardDrive className="w-5 h-5 mr-2 text-indigo-700" />
              Chassis: {chassis.label} (Back View)
            </h3>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-200"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="p-6">
            {loading ? (
              <div className="text-center py-8">
                <RefreshCw className="w-8 h-8 animate-spin mr-2 text-indigo-700 mx-auto mb-3" />
                <span className="text-gray-600">Loading blade servers...</span>
              </div>
            ) : error ? (
              <div className="p-4 bg-red-50 text-red-700 rounded-md flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2 text-red-500" />
                {error}
              </div>
            ) : (
              <div>
                <div className="grid grid-cols-2 gap-6 mb-6">
                  {/* Display chassis information */}
                  <div className="col-span-2 bg-gray-50 p-4 rounded-md border border-gray-200">
                    <h4 className="text-base font-semibold text-gray-700 mb-3">Chassis Information</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div>
                        <div className="text-xs text-gray-500">Model</div>
                        <div className="font-medium">
                          {/* Check all possible model name fields */}
                          {chassis.name ||
                           (chassis.model_id && `ID: ${chassis.model_id}`) ||
                           (chassis.model && chassis.model) ||
                           'N/A'}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500">Status</div>
                        <div className="font-medium">{chassis.status || 'Unknown'}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500">Position</div>
                        <div className="font-medium">U{chassis.position || 'N/A'}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500">Size</div>
                        <div className="font-medium">{chassis.size || 4}U</div>
                      </div>
                    </div>

                    {chassis.notes && (
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <div className="text-xs text-gray-500">Notes</div>

                        <div className="text-sm">{chassis.notes}</div>
                        </div>

                                            )}
                              </div>
                                     </div>
                {/* Display blade servers grid */}
                <h4 className="text-base font-semibold text-gray-700 mb-3">
                  Blade Servers <span className="text-indigo-500 text-sm font-normal">({bladeServers.length} servers found)</span>
                </h4>

                {bladeServers.length === 0 ? (
                  <div className="text-center py-6 bg-gray-50 rounded-md border border-gray-200">
                    <HardDrive className="w-12 h-12 mx-auto text-gray-300 mb-2" />
                    <p className="text-gray-500">No blade servers found in this chassis</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[3, 1, 4, 2].map((bayNumber) => {
                      // Get blade server for this bay
                      const bayServer = getBladeServerForBay(bayNumber);

                      return (
                        <div
                          key={bayNumber}
                          className={`border rounded-md ${
                            bayServer ? 'cursor-pointer hover:shadow-md bg-indigo-50 border-indigo-200' : 'bg-gray-50 border-gray-200'
                          }`}
                          onClick={() => {
                            if (bayServer) {
                              // Close the chassis detail modal before navigating
                              if (onClose) onClose();
                              // Navigate to the ServerView page for this blade server using full page load
                              window.location.href = `/admin/inventory/blade/${bayServer.id}`;
                            }
                          }}
                        >
                          <div className="p-3 border-b bg-gray-100">
                            <div className="flex justify-between items-center">
                              <span className="font-medium">Bay {bayNumber}</span>
                              {bayServer ? (
                                <span className="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">
                                  Occupied
                                </span>
                              ) : (
                                <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                                  Empty
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="p-4">
                            {bayServer ? (
                              <div>
                                <div className="flex items-center mb-2">
                                  <Server className="w-4 h-4 text-indigo-700 mr-2" />
                                  <div className="font-medium text-indigo-700">{bayServer.label}</div>
                                </div>
                                <div className="grid grid-cols-2 gap-3 text-xs text-gray-600 mt-3">
                                  {bayServer.id && (
                                    <div>
                                      <div className="text-gray-500">ID</div>
                                      <div>{bayServer.id}</div>
                                    </div>
                                  )}
                                  {bayServer.cpu && (
                                    <div>
                                      <div className="text-gray-500">CPU</div>
                                      <div>{getCpuName(bayServer.cpu)}</div>
                                    </div>
                                  )}
                                  {bayServer.ram && (
                                    <div>
                                      <div className="text-gray-500">RAM</div>
                                      <div>{getRamDescription(bayServer.ram)}</div>
                                    </div>
                                  )}
                                  {bayServer.status && (
                                    <div>
                                      <div className="text-gray-500">Status</div>
                                      <div>{bayServer.status}</div>
                                    </div>
                                  )}
                                  {bayServer.mac && (
                                    <div>
                                      <div className="text-gray-500">MAC</div>
                                      <div className="break-all">{bayServer.mac}</div>
                                    </div>
                                  )}
                                  {bayServer.ipmi && (
                                    <div>
                                      <div className="text-gray-500">IPMI</div>
                                      <div>{bayServer.ipmi}</div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            ) : (
                              <div className="text-center py-6">
                                <div className="text-sm text-gray-400">Empty slot</div>
                                <div className="text-xs text-gray-400 mt-1">No blade server installed</div>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render the main content area based on selection
  const renderContent = () => {
    if (!selectedLocation) {
      return (
        <div className="flex flex-col items-center justify-center bg-white rounded-lg shadow-sm p-8 h-full border">
          <Globe className="w-16 h-16 text-indigo-200 mb-4" />
          <h3 className="text-lg font-medium text-gray-800 mb-2">Select a Location</h3>
          <p className="text-gray-500 text-center max-w-md mb-4">
            Choose a country, city, or datacenter from the left panel to view available racks
          </p>
          <button
            onClick={() => openAddLocationModal('country')}
            className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md flex items-center text-sm transition-colors"
          >
            <Plus className="w-4 h-4 mr-1" />
            Add New Country
          </button>
        </div>
      );
    }

    if (selectedRack) {
      return (
        <div>
          <div className="flex items-center mb-4">
            <button
              onClick={() => {
                setSelectedRack(null);
                setRackDevices([]);
              }}
              className="mr-3 bg-white p-2 rounded-full shadow-sm hover:bg-gray-50 text-gray-600"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <h2 className="text-lg font-bold text-gray-800">
              Rack Visualization
            </h2>
          </div>
    
          {loadingRacks ? (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center border">
              <RefreshCw className="w-8 h-8 text-indigo-600 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Loading rack data...</p>
            </div>
          ) : (
            <RackVisualization
              rack={selectedRack}
              devices={rackDevices}
            />
          )}
        </div>
      );
    }

    return (
      <div>
        {/* Display rack actions */}
        <div className="flex justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-800">
            {selectedLocation.type === 'country' ? (
              <div className="flex items-center">
                <Globe className="w-5 h-5 mr-2 text-indigo-600" />
                Racks in {selectedLocation.name}
              </div>
            ) : selectedLocation.type === 'city' ? (
              <div className="flex items-center">
                <MapPin className="w-5 h-5 mr-2 text-indigo-600" />
                Racks in {selectedLocation.name}
              </div>
            ) : (
              <div className="flex items-center">
                <Building className="w-5 h-5 mr-2 text-indigo-600" />
                Racks in {selectedLocation.name}
              </div>
            )}
          </h2>

        </div>

        {renderRackCards()}
      </div>
    );
  };

  // Delete Confirmation Modal
  const DeleteConfirmationModal = () => {
    if (!showDeleteConfirmation || !itemToDelete) return null;

    // Get type label for display
    const typeLabel =
      itemToDelete.type === 'country' ? 'Country' :
      itemToDelete.type === 'city' ? 'City' :
      itemToDelete.type === 'datacenter' ? 'Datacenter' :
      itemToDelete.type === 'rack' ? 'Rack' : 'Item';

    // Get warning message based on type and check current dependencies
    let warningMessage = '';
    let canDelete = true;
    
    if (itemToDelete.type === 'country') {
      const countryCities = cities.filter(city => city.country_id === itemToDelete.id);
      if (countryCities.length > 0) {
        warningMessage = `This country has ${countryCities.length} city/cities. All cities must be deleted first.`;
        canDelete = false;
      }
    } else if (itemToDelete.type === 'city') {
      const city = cities.find(c => c.id === itemToDelete.id);
      const cityRacks = racks.filter(rack => rack.city == itemToDelete.id);
      
      if (city && city.datacenter && city.datacenter.trim() !== '') {
        warningMessage = 'This city has a datacenter. The datacenter must be deleted first.';
        canDelete = false;
      } else if (cityRacks.length > 0) {
        warningMessage = `This city has ${cityRacks.length} rack${cityRacks.length > 1 ? 's' : ''}. All racks must be deleted first.`;
        canDelete = false;
      }
    } else if (itemToDelete.type === 'datacenter') {
      const datacenterRacks = racks.filter(rack => rack.city == itemToDelete.id);
      if (datacenterRacks.length > 0) {
        warningMessage = `This datacenter has ${datacenterRacks.length} rack${datacenterRacks.length > 1 ? 's' : ''}. All racks must be deleted first.`;
        canDelete = false;
      }
    } else if (itemToDelete.type === 'rack') {
      warningMessage = 'All devices must be removed from the rack before it can be deleted.';
    }

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
          <div className="mb-4">
            <h3 className="text-lg font-bold text-gray-800 mb-2">Delete {typeLabel}</h3>
            <p className="text-sm text-gray-600">
              Are you sure you want to delete the {typeLabel.toLowerCase()} "{itemToDelete.name}"?
              This action cannot be undone.
            </p>
            {warningMessage && (
              <div className={`mt-3 p-2 border rounded-md text-sm flex items-start ${
                canDelete 
                  ? 'bg-yellow-50 border-yellow-200 text-yellow-800' 
                  : 'bg-red-50 border-red-200 text-red-800'
              }`}>
                <AlertTriangle className={`w-4 h-4 mr-2 mt-0.5 flex-shrink-0 ${
                  canDelete ? 'text-yellow-500' : 'text-red-500'
                }`} />
                <span>{warningMessage}</span>
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowDeleteConfirmation(false)}
              className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirmDelete}
              disabled={!canDelete}
              className={`px-3 py-1.5 rounded-md text-sm transition-colors ${
                canDelete 
                  ? 'bg-red-600 hover:bg-red-700 text-white' 
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Edit Location Modal (Country/City/Datacenter)
  const EditLocationModal = () => {
    if (!showEditLocationModal || !locationToEdit) return null;

    // Get title based on location type
    const title =
      locationToEdit.type === 'country' ? 'Edit Country' :
      locationToEdit.type === 'datacenter' ? 'Edit Datacenter' :
      'Edit City';

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
          <div className="p-4 border-b flex justify-between items-center">
            <h3 className="text-lg font-bold text-gray-800">{title}</h3>
            <button
              onClick={closeEditLocationModal}
              className="text-gray-500 hover:text-gray-700"
            >
              <XCircle className="w-5 h-5" />
            </button>
          </div>

          <div className="p-4 space-y-4">
            {/* Country Edit Form */}
            {locationToEdit.type === 'country' && (
              <div>
                <label className="block text-sm text-gray-700 mb-1">Country Name</label>
                <input
                  type="text"
                  value={locationToEdit.name}
                  onChange={(e) => setLocationToEdit({...locationToEdit, name: e.target.value})}
                  className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                  placeholder="Enter country name"
                  autoFocus
                />
              </div>
            )}

            {/* City Edit Form */}
            {locationToEdit.type === 'city' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm text-gray-700 mb-1">City Name</label>
                  <input
                    type="text"
                    value={locationToEdit.name}
                    onChange={(e) => setLocationToEdit({...locationToEdit, name: e.target.value})}
                    className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                    placeholder="Enter city name"
                  />
                </div>
              </div>
            )}

            {/* Datacenter Edit Form */}
            {locationToEdit.type === 'datacenter' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm text-gray-700 mb-1">City</label>
                  <input
                    type="text"
                    value={cities.find(c => c.id === locationToEdit.id)?.city || ''}
                    className="w-full p-2 border rounded-md bg-gray-100 text-sm"
                    disabled
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Editing datacenter for {cities.find(c => c.id === locationToEdit.id)?.city || 'selected city'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm text-gray-700 mb-1">Datacenter Name</label>
                  <input
                    type="text"
                    value={locationToEdit.name}
                    onChange={(e) => setLocationToEdit({...locationToEdit, name: e.target.value})}
                    className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                    placeholder="Enter datacenter name"
                    autoFocus
                  />
                </div>
              </div>
            )}
          </div>

          <div className="p-4 border-t flex justify-end space-x-3">
            <button
              onClick={closeEditLocationModal}
              className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSaveLocationEdit}
              className="px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md text-sm transition-colors"
              disabled={!locationToEdit.name}
            >
              Save Changes
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Edit Rack Modal
  const EditRackModal = () => {
    if (!showEditRackModal || !rackToEdit) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
          <div className="p-4 border-b flex justify-between items-center">
            <h3 className="text-lg font-bold text-gray-800">Edit Rack</h3>
            <button
              onClick={closeEditRackModal}
              className="text-gray-500 hover:text-gray-700"
            >
              <XCircle className="w-5 h-5" />
            </button>
          </div>

          <div className="p-4 space-y-4">
            <div>
              <label className="block text-sm text-gray-700 mb-1">Rack Name*</label>
              <input
                type="text"
                value={rackToEdit.rack_name}
                onChange={(e) => setRackToEdit({...rackToEdit, rack_name: e.target.value})}
                className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                placeholder="e.g. Rack A01"
                autoFocus
              />
            </div>

            <div>
              <label className="block text-sm text-gray-700 mb-1">Rack Size (U)</label>
              <input
                type="number"
                value={rackToEdit.size}
                onChange={(e) => setRackToEdit({...rackToEdit, size: parseInt(e.target.value) || 42})}
                className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                placeholder="42"
                min="1"
                max="100"
              />
            </div>

            <div>
              <label className="block text-sm text-gray-700 mb-1">Country*</label>
              <select
                className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                value={rackToEdit.country_id || ''}
                onChange={(e) => {
                  const newCountryId = e.target.value;
                  setRackToEdit({
                    ...rackToEdit,
                    country_id: newCountryId,
                    city_id: '' // Clear city when country changes
                  });
                }}
              >
                <option value="">Select Country</option>
                {countries.map(country => (
                  <option key={country.id} value={country.id}>{country.country}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm text-gray-700 mb-1">City*</label>
              <select
                className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                value={rackToEdit.city_id || ''}
                onChange={(e) => setRackToEdit({...rackToEdit, city_id: e.target.value})}
                disabled={!rackToEdit.country_id}
              >
                <option value="">Select City</option>
                {getCitiesForCountry(rackToEdit.country_id).map(city => (
                  <option key={city.id} value={city.id}>{city.city}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm text-gray-700 mb-1">Notes (Optional)</label>
              <textarea
                value={rackToEdit.notes || ''}
                onChange={(e) => setRackToEdit({...rackToEdit, notes: e.target.value})}
                className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                placeholder="Additional information about this rack"
                rows="3"
              ></textarea>
            </div>
          </div>

          <div className="p-4 border-t flex justify-end space-x-3">
            <button
              onClick={closeEditRackModal}
              className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSaveRackEdit}
              className="px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md text-sm transition-colors"
              disabled={!rackToEdit.rack_name || !rackToEdit.city_id || !rackToEdit.country_id}
            >
              Save Changes
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Handle edit button click for a datacenter
  const handleEditDatacenter = (city) => {
    // A datacenter is tied to a city record in the current backend structure
    setLocationToEdit({
      type: 'datacenter',
      id: city.id, // keep using city.id for the datacenter identifier
      name: city.datacenter || '', // the datacenter name is stored in the city's datacenter field
      parentId: city.country_id // may be useful later if we need the parent country
    });
    setShowEditLocationModal(true);
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        activeMenu="Locations"
        toggleSidebar={toggleSidebar}
      />

      <div className="flex-1 flex flex-col bg-gray-100">
        <TopMenu toggleSidebar={toggleSidebar} />

        <div className="p-6 space-y-6 overflow-auto">
          {/* Header */}
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">Locations</h1>
            <div className="flex items-center space-x-2">
              <div className="text-sm text-gray-500">Last updated: {lastUpdated}</div>
              <button
                onClick={fetchLocationData}
                className="p-1.5 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={() => openAddLocationModal('country')}
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Location
              </button>
            </div>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {/* Locations Tree */}
            <div className="bg-white rounded-lg shadow-sm p-3 border">
              <div className="mb-3 relative">
                <input
                  type="text"
                  placeholder="Search countries, cities, racks..."
                  className="pl-8 pr-10 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 search-input"
                  value={searchQuery}
                  onChange={handleSearch}
                />
                <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                <button
                  onClick={() => openAddLocationModal('country')}
                  className="absolute right-1 top-1/2 -translate-y-1/2 p-1.5 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md flex items-center justify-center"
                  title="Add Country"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>

              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <RefreshCw className="w-6 h-6 animate-spin mr-2 text-indigo-700" />
                  <span className="text-gray-600">Loading locations...</span>
                </div>
              ) : error ? (
                <div className="p-4 bg-red-50 text-red-700 rounded-md flex items-center">
                  <AlertTriangle className="w-5 h-5 mr-2 text-red-500" />
                  {error}
                </div>
              ) : filteredCountries.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Globe className="w-12 h-12 mx-auto text-gray-300 mb-3" />
                  <p>No countries found</p>
                  <button
                    onClick={() => openAddLocationModal('country')}
                    className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md text-sm flex items-center mx-auto"
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    Add Country
                  </button>
                </div>
              ) : (
                <div className="overflow-y-auto max-h-[calc(100vh-300px)]">
                  <ul className="space-y-1">
                    {filteredCountries.filter(country => {
                      // If no search query, show all countries
                      if (!searchQuery || searchQuery.trim() === '') {
                        return true;
                      }

                      try {
                        // Check if country matches search query directly
                        const countryMatches = country.country &&
                          country.country.toLowerCase().includes(searchQuery.toLowerCase());

                        // Check if any city in this country matches search query
                        const countryCities = getCitiesForCountry(country.id);
                        const cityMatches = countryCities.some(city =>
                          (city.city && city.city.toLowerCase().includes(searchQuery.toLowerCase())) ||
                          (city.datacenter && city.datacenter.toLowerCase().includes(searchQuery.toLowerCase()))
                        );

                        // Check if any rack in this country matches search query
                        const countryRacks = getRacksForCountry(country.id);
                        const rackMatches = countryRacks.some(rack =>
                          rack.rack_name && rack.rack_name.toLowerCase().includes(searchQuery.toLowerCase())
                        );

                        // Check if any datacenter in this country matches search query
                        const countryDatacenters = datacenters.filter(dc => dc.country_id === country.id);
                        const datacenterMatches = countryDatacenters.some(dc =>
                          dc.name && dc.name.toLowerCase().includes(searchQuery.toLowerCase())
                        );

                        // Only show country if it matches or has matching children
                        return countryMatches || cityMatches || rackMatches || datacenterMatches;
                      } catch (err) {
                        console.error("Error filtering country:", err);
                        return false;
                      }
                    }).map(country => (
                        <li key={country.id} className="border-b pb-2 mb-2 last:border-b-0">
                          {/* Country */}
                          <div
    className="flex items-center justify-between p-2 rounded cursor-pointer hover:bg-gray-50"
    onClick={(e) => {
      e.stopPropagation();
      handleLocationSelect('country', country.id, country.country);
    }}
  >
  <div
    className="flex items-center"
  >
    <button
      onClick={(e) => {
        e.stopPropagation();
        toggleCountryExpansion(country.id, e);
      }}
      className="mr-1 text-gray-500 hover:text-indigo-700 h-6 w-6 flex items-center justify-center rounded hover:bg-gray-100"
    >
      {expandedCountries[country.id] ? (
        <ChevronDown className="w-4 h-4" />
      ) : (
        <ChevronRight className="w-4 h-4" />
      )}
    </button>
    <Globe className="w-4 h-4 mr-1.5 text-indigo-700" />
    <span>{country.country}</span>
  </div>

  <div className="flex items-center space-x-1">
    <button
      className="p-1 text-gray-500 hover:text-indigo-700 hover:bg-gray-100 rounded"
      onClick={(e) => {
        e.stopPropagation();
        handleEditCountry(country);
      }}
      title="Edit Country"
    >
      <Edit className="w-3.5 h-3.5" />
    </button>
    <button
      className={`p-1 rounded ${
        getCitiesForCountry(country.id).length > 0
          ? 'text-gray-300 cursor-not-allowed'
          : 'text-gray-500 hover:text-red-700 hover:bg-gray-100'
      }`}
      onClick={(e) => {
        e.stopPropagation();
        if (getCitiesForCountry(country.id).length === 0) {
          handleDeleteClick('country', country.id, country.country);
        }
      }}
      title={getCitiesForCountry(country.id).length > 0 
        ? "Cannot delete - country has cities" 
        : "Delete Country"}
      disabled={getCitiesForCountry(country.id).length > 0}
    >
      <Trash2 className="w-3.5 h-3.5" />
    </button>
    <button
      className="px-2 py-1 text-xs bg-indigo-100 text-indigo-700 hover:bg-indigo-200 rounded flex items-center"
      onClick={(e) => {
        e.stopPropagation();
        openAddLocationModal('city', country.id);
      }}
      title="Add City"
      style={{minWidth:'62px'}}
    >
      <Plus className="w-3 h-3 mr-1" />
      City
    </button>
  </div>
</div>


                        {/* Cities in this country */}
                        {expandedCountries[country.id] && (
                          <ul className="pl-6 mt-1 space-y-1">
                            {getCitiesForCountry(country.id).length === 0 ? (
                              <li className="text-xs text-gray-500 italic p-2">
                                No cities found in this country
                              </li>
                            ) : (
                              getCitiesForCountry(country.id)
                                .filter(city => {
                                  // If no search query, show all cities
                                  if (!searchQuery || searchQuery.trim() === '') return true;

                                  try {
                                    // Check if city name matches
                                    const cityMatches = city.city &&
                                      city.city.toLowerCase().includes(searchQuery.toLowerCase());

                                    // Check if datacenter matches
                                    const datacenterMatches = city.datacenter &&
                                      city.datacenter.toLowerCase().includes(searchQuery.toLowerCase());

                                    // Check if any rack in this city matches
                                    const cityRacks = getRacksForCity(city.id);
                                    const rackMatches = cityRacks.some(rack =>
                                      rack.rack_name && rack.rack_name.toLowerCase().includes(searchQuery.toLowerCase())
                                    );

                                    // Check if any datacenter in this city matches
                                    const cityDatacenters = getDatacentersForCity(city.id);
                                    const cityDatacenterMatches = cityDatacenters.some(dc =>
                                      dc.name && dc.name.toLowerCase().includes(searchQuery.toLowerCase())
                                    );

                                    return cityMatches || datacenterMatches || rackMatches || cityDatacenterMatches;
                                  } catch (err) {
                                    console.error("Error filtering city:", err);
                                    return false; // Hide the city if there's an error in filtering
                                  }
                                })
                                .map(city => (
                                <li key={city.id} className="border-b border-gray-100 pb-2 mb-2 last:border-b-0">
                                  {/* City */}
                                  <div
  className="flex items-center justify-between p-2 rounded cursor-pointer hover:bg-gray-50"
  onClick={(e) => {
    e.stopPropagation();
    handleLocationSelect('city', city.id, city.city);
  }}
>
  <div
    className="flex items-center"
  >
    {getDatacentersForCity(city.id).length > 0 ? (
      <button
        onClick={(e) => {
          e.stopPropagation();
          toggleCityExpansion(city.id, e);
        }}
        className="mr-1 text-gray-500 hover:text-indigo-700 h-6 w-6 flex items-center justify-center rounded hover:bg-gray-100"
      >
        {expandedCities[city.id] ? (
          <ChevronDown className="w-4 h-4" />
        ) : (
          <ChevronRight className="w-4 h-4" />
        )}
      </button>
    ) : (
      <div className="w-6 h-6"></div>
    )}
    <MapPin className="w-4 h-4 mr-1.5 text-indigo-700" />
    <span>{city.city}</span>
  </div>

  <div className="flex items-center space-x-1">
    <button
      className="p-1 text-gray-500 hover:text-indigo-700 hover:bg-gray-100 rounded"
      onClick={(e) => {
        e.stopPropagation();
        handleEditCity(city);
      }}
      title="Edit City"
    >
      <Edit className="w-3.5 h-3.5" />
    </button>
    <button
      className={`p-1 rounded ${
        (city.datacenter && city.datacenter.trim() !== '') || getRacksForCity(city.id).length > 0
          ? 'text-gray-300 cursor-not-allowed'
          : 'text-gray-500 hover:text-red-700 hover:bg-gray-100'
      }`}
      onClick={(e) => {
        e.stopPropagation();
        if (!((city.datacenter && city.datacenter.trim() !== '') || getRacksForCity(city.id).length > 0)) {
          handleDeleteClick('city', city.id, city.city);
        }
      }}
      title={
        (city.datacenter && city.datacenter.trim() !== '')
          ? "Cannot delete - city has datacenter"
          : getRacksForCity(city.id).length > 0
          ? "Cannot delete - city has racks"
          : "Delete City"
      }
      disabled={(city.datacenter && city.datacenter.trim() !== '') || getRacksForCity(city.id).length > 0}
    >
      <Trash2 className="w-3.5 h-3.5" />
    </button>
    <button
      className="px-2 py-1 text-xs bg-indigo-100 text-indigo-700 hover:bg-indigo-200 rounded flex items-center"
      onClick={(e) => {
        e.stopPropagation();
        openAddLocationModal('datacenter', city.id);
      }}
      title="Add Datacenter"
      style={{minWidth:'62px'}}
    >
      <Plus className="w-3 h-3 mr-1" />
      DC
    </button>
  </div>
</div>

{/* Datacenters in this city */}
{expandedCities[city.id] && (
  <ul className="pl-6 mt-1">
    {getDatacentersForCity(city.id).length === 0 ? (
      <li className="text-xs text-gray-500 italic p-2">
        No datacenters found in this city
      </li>
    ) : (
      getDatacentersForCity(city.id)
        .filter(datacenter => {
          // If no search query, show all datacenters
          if (!searchQuery || searchQuery.trim() === '') return true;

          try {
            // Check if datacenter name matches
            const datacenterMatches = datacenter.name &&
              datacenter.name.toLowerCase().includes(searchQuery.toLowerCase());

            // Check if any rack associated with this datacenter matches
            const datacenterRacks = getRacksForDatacenter(datacenter.id);
            const rackMatches = datacenterRacks.some(rack =>
              rack.rack_name && rack.rack_name.toLowerCase().includes(searchQuery.toLowerCase())
            );

            return datacenterMatches || rackMatches;
          } catch (err) {
            console.error("Error filtering datacenter:", err);
            return false;
          }
        })
        .map(datacenter => (
          <li key={datacenter.id} className="border-b border-gray-100 pb-2 mb-2 last:border-b-0">
            <div
              className={`flex items-center justify-between p-2 rounded cursor-pointer hover:bg-gray-50 ${
                selectedLocation?.type === 'datacenter' && selectedLocation?.id === datacenter.id ?
                'bg-indigo-50 text-indigo-700' : ''
              }`}
              onClick={(e) => {
                e.stopPropagation();
                handleLocationSelect('datacenter', datacenter.id, datacenter.name);
              }}
            >
              <div 
                className="flex items-center" 
              >
                <Building className="w-4 h-4 mr-1.5 text-indigo-700" />
                <span>{datacenter.name}</span>
              </div>
              <div className="flex items-center space-x-1">
                <button
                  className="p-1 text-gray-500 hover:text-indigo-700 hover:bg-gray-100 rounded"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Find the city for this datacenter (datacenter.id is the city ID)
                    const city = cities.find(c => c.id === datacenter.id);
                    if (city) {
                      handleEditDatacenter(city);
                    }
                  }}
                  title="Edit Datacenter"
                >
                  <Edit className="w-3.5 h-3.5" />
                </button>
                <button
                  className={`p-1 rounded ${
                    getRacksForDatacenter(datacenter.id).length > 0
                      ? 'text-gray-300 cursor-not-allowed'
                      : 'text-gray-500 hover:text-red-700 hover:bg-gray-100'
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (getRacksForDatacenter(datacenter.id).length === 0) {
                      handleDeleteClick('datacenter', datacenter.id, datacenter.name);
                    }
                  }}
                  title={getRacksForDatacenter(datacenter.id).length > 0
                    ? "Cannot delete - datacenter has racks"
                    : "Delete Datacenter"}
                  disabled={getRacksForDatacenter(datacenter.id).length > 0}
                >
                  <Trash2 className="w-3.5 h-3.5" />
                </button>
                <button
                  className="px-2 py-1 text-xs bg-indigo-100 text-indigo-700 hover:bg-indigo-200 rounded flex items-center"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Set default values for the new rack (datacenter.id is the city ID)
                    setNewRack({
                      ...newRack,
                      city_id: datacenter.id,
                      country_id: datacenter.country_id,
                      datacenter_id: datacenter.id
                    });
                    openAddRackModal();
                  }}
                  title="Add Rack"
                  style={{minWidth:'62px'}}
                >
                  <Plus className="w-3 h-3 mr-1" />
                  Rack
                </button>
              </div>
            </div>
          </li>
        ))
    )}
  </ul>
)}
                                </li>
                              ))
                            )}
                          </ul>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Content Area */}
            <div className="md:col-span-2">
              {renderContent()}
            </div>
          </div>
        </div>
      </div>

      {/* Add Location Modal */}
      {showAddLocationModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
            <div className="p-3 border-b flex justify-between items-center">
              <h3 className="text-lg font-bold text-gray-800">
                Add New {newLocation.type === 'country' ? 'Country' : newLocation.type === 'city' ? 'City' : 'Datacenter'}
              </h3>
              <button
                onClick={closeAddLocationModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4 space-y-3">
              {/* Country Modal Fields */}
              {newLocation.type === 'country' && (
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm text-gray-700 mb-1">Country Name</label>
                    <input
                      type="text"
                      value={newLocation.name}
                      onChange={(e) => setNewLocation({...newLocation, name: e.target.value})}
                      className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                      placeholder="Enter country name"
                      autoFocus
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-700 mb-1">Flag Code (2 letters)</label>
                    <input
                      type="text"
                      value={newLocation.flagCode}
                      onChange={(e) => setNewLocation({...newLocation, flagCode: e.target.value.toUpperCase().slice(0, 2)})}
                      className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                      placeholder="e.g., US, GB, CA"
                      maxLength="2"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      2-letter ISO country code (e.g., US for United States, GB for United Kingdom)
                    </p>
                  </div>
                </div>
              )}

              {/* City Modal Fields */}
              {newLocation.type === 'city' && (
                <div className="space-y-3">
                  {/* Country Selector - Pre-selected and disabled */}
                  <div>
                    <label className="block text-sm text-gray-700 mb-1">Country</label>
                    <select
                      className="w-full p-2 border rounded-md bg-gray-100 text-sm"
                      value={newLocation.parentId}
                      disabled
                    >
                      {countries.map(country => (
                        <option key={country.id} value={country.id}>
                          {country.country}
                        </option>
                      ))}
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      Adding city to {countries.find(c => c.id === newLocation.parentId)?.country || 'selected country'}
                    </p>
                  </div>

                  {/* City Name Input */}
                  <div>
                    <label className="block text-sm text-gray-700 mb-1">City Name</label>
                    <input
                      type="text"
                      value={newLocation.name}
                      onChange={(e) => setNewLocation({...newLocation, name: e.target.value})}
                      className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                      placeholder="Enter city name"
                      autoFocus
                    />
                  </div>
                </div>
              )}

              {/* Datacenter Modal Fields */}
              {newLocation.type === 'datacenter' && (
                <div className="space-y-3">
                  {/* City - Pre-selected and disabled */}
                  <div>
                    <label className="block text-sm text-gray-700 mb-1">City</label>
                    <input
                      type="text"
                      value={cities.find(c => c.id === newLocation.parentId)?.city || ''}
                      className="w-full p-2 border rounded-md bg-gray-100 text-sm"
                      disabled
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Adding datacenter to {cities.find(c => c.id === newLocation.parentId)?.city || 'selected city'}
                    </p>
                  </div>

                  {/* Datacenter Name Input */}
                  <div>
                    <label className="block text-sm text-gray-700 mb-1">Datacenter Name</label>
                    <input
                      type="text"
                      value={newLocation.name}
                      onChange={(e) => setNewLocation({...newLocation, name: e.target.value})}
                      className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                      placeholder="Enter datacenter name"
                      autoFocus
                    />
                  </div>
                </div>
              )}
            </div>
            <div className="p-3 border-t flex justify-end space-x-2">
              <button
                onClick={closeAddLocationModal}
                className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
              >
                <X className="w-4 h-4 mr-1" />
                Cancel
              </button>
              <button
                onClick={handleAddLocation}
                className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md flex items-center text-sm transition-colors"
                disabled={!newLocation.name || (newLocation.type === 'country' && !newLocation.flagCode)}
              >
                <Save className="w-4 h-4 mr-1" />
                Save
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Rack Modal */}
      {showAddRackModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
            <div className="p-3 border-b flex justify-between items-center">
              <h3 className="text-lg font-bold text-gray-800">Add New Rack</h3>
              <button
                onClick={closeAddRackModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4 space-y-3">
              <div>
                <label className="block text-sm text-gray-700 mb-1">Rack Name*</label>
                <input
                  type="text"
                  value={newRack.rack_name}
                  onChange={(e) => setNewRack({...newRack, rack_name: e.target.value})}
                  className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                  placeholder="e.g. Rack A01"
                  autoFocus
                />
              </div>

              <div>
                <label className="block text-sm text-gray-700 mb-1">Rack Size (U)</label>
                <input
                  type="number"
                  value={newRack.size}
                  onChange={(e) => setNewRack({...newRack, size: parseInt(e.target.value) || 42})}
                  className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                  placeholder="42"
                  min="1"
                  max="100"
                />
              </div>

              {/* Country selection - Preselected and may be disabled */}
              <div>
                <label className="block text-sm text-gray-700 mb-1">Country*</label>
                <select
                  className={`w-full p-2 border rounded-md focus:border-indigo-500 text-sm ${selectedLocation?.type === 'city' || selectedLocation?.type === 'datacenter' ? 'bg-gray-100' : ''}`}
                  value={newRack.country_id || ''}
                  onChange={(e) => {
                    const newCountryId = e.target.value;
                    setNewRack({
                      ...newRack,
                      country_id: newCountryId,
                      // Clear city when country changes
                      city_id: ''
                    });
                  }}
                  disabled={selectedLocation?.type === 'city' || selectedLocation?.type === 'datacenter'}
                >
                  <option value="">Select Country</option>
                  {countries.map(country => (
                    <option key={country.id} value={country.id}>{country.country}</option>
                  ))}
                </select>
                {(selectedLocation?.type === 'city' || selectedLocation?.type === 'datacenter') && (
                  <p className="text-xs text-gray-500 mt-1">Country is predetermined by location selection</p>
                )}
              </div>

              {/* City selection - Preselected and may be disabled */}
              <div>
                <label className="block text-sm text-gray-700 mb-1">City*</label>
                <select
                  className={`w-full p-2 border rounded-md focus:border-indigo-500 text-sm ${selectedLocation?.type === 'city' || selectedLocation?.type === 'datacenter' ? 'bg-gray-100' : ''}`}
                  value={newRack.city_id || ''}
                  onChange={(e) => setNewRack({...newRack, city_id: e.target.value})}
                  disabled={!newRack.country_id || selectedLocation?.type === 'city' || selectedLocation?.type === 'datacenter'}
                >
                  <option value="">Select City</option>
                  {getCitiesForCountry(newRack.country_id).map(city => (
                    <option key={city.id} value={city.id}>{city.city}</option>
                  ))}
                </select>
                {(selectedLocation?.type === 'city' || selectedLocation?.type === 'datacenter') && (
                  <p className="text-xs text-gray-500 mt-1">City is predetermined by location selection</p>
                )}
              </div>

              <div>
                <label className="block text-sm text-gray-700 mb-1">Notes (Optional)</label>
                <textarea
                  value={newRack.notes || ''}
                  onChange={(e) => setNewRack({...newRack, notes: e.target.value})}
                  className="w-full p-2 border rounded-md focus:border-indigo-500 text-sm"
                  placeholder="Additional information about this rack"
                  rows="3"
                ></textarea>
              </div>
            </div>
            <div className="p-3 border-t flex justify-end space-x-2">
              <button
                onClick={closeAddRackModal}
                className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
              >
                <X className="w-4 h-4 mr-1" />
                Cancel
              </button>
              <button
                onClick={handleAddRack}
                className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md flex items-center text-sm transition-colors"
                disabled={!newRack.rack_name || !newRack.city_id || !newRack.country_id}
              >
                <Save className="w-4 h-4 mr-1" />
                Save
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Device Detail Modals */}
      {selectedChassis && (
        <ChassisDetailView
          chassis={selectedChassis}
          onClose={() => setSelectedChassis(null)}
        />
      )}

      {selectedServer && (
        <ServerDetailView
          server={selectedServer}
          onClose={() => setSelectedServer(null)}
        />
      )}

      {selectedSwitch && (
        <SwitchDetailView
          switchDevice={selectedSwitch}
          onClose={() => setSelectedSwitch(null)}
        />
      )}

      {/* Edit Location Modal */}
      {showEditLocationModal && <EditLocationModal />}

      {/* Edit Rack Modal */}
      {showEditRackModal && <EditRackModal />}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && <DeleteConfirmationModal />}
    </div>
  );
};

export default LocationsPage;