import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import { toast } from 'react-toastify';
import { API_URL } from '../config';

const OAuthCallbackPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [status, setStatus] = useState('Processing...');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Get the authorization code from URL query parameters
        const urlParams = new URLSearchParams(location.search);
        const code = urlParams.get('code');

        if (!code) {
          setStatus('Error: No authorization code received');
          toast.error('Authorization failed: No code received');
          setTimeout(() => navigate('/admin/email-settings'), 3000);
          return;
        }

        // Send the code to the backend
        const token = localStorage.getItem('admin_token');
        const response = await axios.post(`${API_URL}/api_admin_settings.php?f=oauth_callback`, {
          token: token,
          code: code
        });

        if (response.data.success) {
          setStatus('Authorization successful! Redirecting...');
          toast.success('Google authorization successful');
          setTimeout(() => navigate('/admin/email-settings?oauth_return=success'), 2000);
        } else {
          setStatus('Error: ' + (response.data.error || 'Unknown error'));
          toast.error('Authorization failed: ' + (response.data.error || 'Unknown error'));
          setTimeout(() => navigate('/admin/email-settings'), 3000);
        }
      } catch (error) {
        console.error('OAuth callback error:', error);
        setStatus('Error processing authorization');
        toast.error('Authorization failed: ' + (error.message || 'Unknown error'));
        setTimeout(() => navigate('/admin/email-settings'), 3000);
      }
    };

    handleCallback();
  }, [location, navigate]);

  return (
    <div className="flex h-screen items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
        <h1 className="text-2xl font-bold text-center mb-6">Google OAuth Authorization</h1>

        <div className="text-center">
          <div className="mb-4">
            {status === 'Processing...' ? (
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-700 mx-auto"></div>
            ) : status.includes('Error') ? (
              <div className="text-red-600 text-xl">❌</div>
            ) : (
              <div className="text-green-600 text-xl">✅</div>
            )}
          </div>

          <p className="text-gray-700">{status}</p>

          {status.includes('Error') && (
            <button
              onClick={() => navigate('/admin/email-settings')}
              className="mt-4 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Return to Email Settings
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default OAuthCallbackPage;
