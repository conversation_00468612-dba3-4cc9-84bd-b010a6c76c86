// src/config.js
// Configuration file for the application

/**
 * Application configuration
 */
const config = {
  // API domain URL - default value, will be updated from server
  apiDomain: 'https://test.x-zoneit.ro',

  // API base path
  apiBasePath: '/New',

  // Full API base URL
  get apiBaseUrl() {
    return `${this.apiDomain}${this.apiBasePath}`;
  }
};

// Function to fetch API domain from server
const fetchApiDomain = async () => {
  try {
    // Use the current domain to fetch the API domain
    const currentDomain = window.location.origin;
    const response = await fetch(`${currentDomain}/get_api_domain.php`);
    const data = await response.json();

    if (data.success && data.api_domain) {
      config.apiDomain = data.api_domain;
      console.log('API domain updated:', config.apiDomain);
    }
  } catch (error) {
    console.error('Failed to fetch API domain:', error);
  }
};

// Try to fetch the API domain when the config is loaded
fetchApiDomain();

// Export the API URL for use in components
export const API_URL = config.apiBaseUrl;

// Export a function to get the latest API URL
export const getApiUrl = () => config.apiBaseUrl;

export default config;
