import React, { useState, useEffect, useCallback } from 'react';
import { Browser<PERSON>outer, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import './App.css';
import PageTransition from './components/PageTransition';
import Browser<PERSON>Handler from './components/BrowserUIHandler';
import ViewportMetaTag from './components/ViewportMetaTag';

// Import auth context
import { AuthProvider, useAuth } from './AuthContext';

// Import all page components
import Dashboard from './pages/Dashboard';
import AccountPage from './pages/AccountPage';
import AccountsPage from './pages/AccountsPage';
import InventoryPage from './pages/InventoryPage';
import ServerView from './pages/ServerView';
import OrdersPage from './pages/OrdersPage';
import ReportsPage from './pages/ReportsPage';
import SubnetsPage from './pages/SubnetsPage';
import InvoicesPage from './pages/InvoicesPage';
import TicketsPage from './pages/TicketsPage';
import TicketDetailPage from './pages/TicketDetailPage';
import SwitchView from './pages/SwitchView';
import LocationsPage from './pages/LocationsPage';
import EmailSettingsPage from './pages/EmailSettingsPage';
import GeneralSettingsPage from './pages/GeneralSettingsPage';
import OAuthCallbackPage from './pages/OAuthCallbackPage';
import MassEmailPage from './pages/MassEmailPage';
import Login from './pages/Login';

// AdminRoute component to ensure only admins can access the admin dashboard
const AdminRoute = ({ children }) => {
  const { isAuthenticated, isLoading, verifyToken } = useAuth();
  const location = useLocation();

  // First check if we already have a valid token in localStorage
  const hasToken = !!localStorage.getItem('admin_token');

  useEffect(() => {
    // Only verify if we think we might be authenticated
    if (hasToken) {
      verifyToken();
    }
  }, [location.pathname, verifyToken, hasToken]);

  // Only show loading screen on initial authentication check, not during navigation
  // This prevents the flash of loading screen when navigating between pages
  if (isLoading && !hasToken) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 to-indigo-900">
        <div className="animate-spin h-12 w-12 border-4 border-t-4 border-indigo-500 rounded-full"></div>
      </div>
    );
  }

  // If not authenticated and not loading, redirect to login
  if (!isAuthenticated && !isLoading) {
    return <Navigate to="/admin/login" replace state={{ from: location }} />;
  }

  // Otherwise render children
  return children;
};

// Separate wrappers for authenticated and non-authenticated pages
function AuthenticatedPageWrapper({ Component, ...rest }) {
  const navigate = useNavigate();
  const { setToken } = useAuth();

  // Navigation function to pass to pages
  const navigateTo = (path) => {
    navigate(path);
  };

  return (
    <AdminRoute>
      <PageTransition>
        <Component
          navigateTo={navigateTo}
          setToken={setToken}
          {...rest}
        />
      </PageTransition>
    </AdminRoute>
  );
}

function PublicPageWrapper({ Component, ...rest }) {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  // Check if we're on the login page but already authenticated
  if (location.pathname === '/admin/login' && isAuthenticated) {
    return <Navigate to="/admin/dashboard" replace />;
  }

  // Navigation function to pass to pages
  const navigateTo = (path) => {
    navigate(path);
  };

  return (
    <PageTransition>
      <Component
        navigateTo={navigateTo}
        {...rest}
      />
    </PageTransition>
  );
}

function App() {
  // Initialize sidebar state based on screen width
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 800);
  // On mobile, sidebar should be collapsed by default
  const [sidebarCollapsed, setSidebarCollapsed] = useState(isMobile);
  // Track if toggle was triggered by a route change
  const [isNavigating, setIsNavigating] = useState(false);

  // Add resize listener to update isMobile state
  useEffect(() => {
    const handleResize = () => {
      const newIsMobile = window.innerWidth <= 800;
      setIsMobile(newIsMobile);

      // If transitioning to mobile, collapse the sidebar
      if (newIsMobile && !isMobile && !sidebarCollapsed) {
        setSidebarCollapsed(true);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobile, sidebarCollapsed]);

  // Toggle sidebar function to pass to all pages - memoized with useCallback
  const toggleSidebar = useCallback(() => {
    console.log("App.js: toggleSidebar called, current state:", sidebarCollapsed);
    // Don't toggle if we're in the middle of navigation
    if (!isNavigating) {
      setSidebarCollapsed(prevState => !prevState);
    }
  }, [isNavigating, sidebarCollapsed]);

  return (
    <BrowserRouter>
      <AuthProvider>
        <ViewportMetaTag />
        <BrowserUIHandler />
        <Routes>
          {/* Admin Login Route - Public */}
          <Route
            path="/admin/login"
            element={
              <PublicPageWrapper
                Component={Login}
              />
            }
          />

          {/* Admin Protected Routes */}
          <Route
            path="/admin"
            element={<Navigate to="/admin/dashboard" replace />}
          />

          <Route
            path="/admin/dashboard"
            element={
              <AuthenticatedPageWrapper
                Component={Dashboard}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          <Route
            path="/admin/account"
            element={
              <AuthenticatedPageWrapper
                Component={AccountPage}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          <Route
            path="/admin/accounts"
            element={
              <AuthenticatedPageWrapper
                Component={AccountsPage}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          <Route
            path="/admin/inventory"
            element={
              <AuthenticatedPageWrapper
                Component={InventoryPage}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          {/* Server View Route */}
          <Route
            path="/admin/inventory/:type/:id"
            element={
              <AuthenticatedPageWrapper
                Component={ServerView}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          <Route
            path="/admin/inventory/switch/:id"
            element={
              <AuthenticatedPageWrapper
                Component={SwitchView}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          <Route
            path="/admin/orders"
            element={
              <AuthenticatedPageWrapper
                Component={OrdersPage}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          <Route
            path="/admin/Locations"
            element={
              <AuthenticatedPageWrapper
                Component={LocationsPage}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          <Route
            path="/admin/reports"
            element={
              <AuthenticatedPageWrapper
                Component={ReportsPage}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          <Route
            path="/admin/subnets"
            element={
              <AuthenticatedPageWrapper
                Component={SubnetsPage}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          <Route
            path="/admin/invoices"
            element={
              <AuthenticatedPageWrapper
                Component={InvoicesPage}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          <Route
            path="/admin/tickets"
            element={
              <AuthenticatedPageWrapper
                Component={TicketsPage}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          <Route
            path="/admin/tickets/:id"
            element={
              <AuthenticatedPageWrapper
                Component={TicketDetailPage}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          {/* Mass Email Route */}
          <Route
            path="/admin/massemail"
            element={
              <AuthenticatedPageWrapper
                Component={MassEmailPage}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          {/* Email Settings Route */}
          <Route
            path="/admin/email-settings"
            element={
              <AuthenticatedPageWrapper
                Component={EmailSettingsPage}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          {/* General Settings Route */}
          <Route
            path="/admin/general-settings"
            element={
              <AuthenticatedPageWrapper
                Component={GeneralSettingsPage}
                sidebarCollapsed={sidebarCollapsed}
                toggleSidebar={toggleSidebar}
              />
            }
          />

          {/* OAuth Callback Route */}
          <Route
            path="/admin/email-settings/oauth-callback"
            element={
              <AuthenticatedPageWrapper
                Component={OAuthCallbackPage}
              />
            }
          />

          {/* Root path redirects to admin dashboard if authenticated */}
          <Route
            path="/"
            element={<Navigate to="/admin/dashboard" replace />}
          />

          {/* Fallback route - redirects to admin dashboard or login */}
          <Route
            path="*"
            element={<Navigate to="/admin/login" replace />}
          />
        </Routes>
      </AuthProvider>
    </BrowserRouter>
  );
}

export default App;