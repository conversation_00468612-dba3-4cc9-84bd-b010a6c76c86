import React, { useState, useEffect } from 'react';
import {
  Server,
  Plus,
  Eye,
  HardDrive,
  Cpu,
  EyeOff,
  Network,
  Settings,
  Wifi,
  Layers,
  Calendar,
  XCircle,
  RefreshCw,
  Edit,
  Trash2,
  Save,
  X,
  Database,
  Globe,
  MapPin,
  Activity,
  AlertTriangle,
  CheckCircle,
  Lock,
  Building,
  User
} from 'lucide-react';
import axios from 'axios';
import { API_URL } from '../config';
import PowerManagement from '../components/PowerManagement';
import MacAddressDetectionButton from '../components/MacAddressDetectionButton';
import StorageDetectionButton from '../components/StorageDetectionButton';
import SwitchPortsManager from '../components/SwitchPortsManager';
import NetworkConnections from '../components/NetworkConnections';
import SelectSubnetModal from '../components/SelectSubnetModal';
import CountryIpSelector from '../components/CountryIpSelector';

// Legacy IpmiSelector component replaced with CountryIpSelector

const ServerDetailsModal = ({
  selectedItem,
  isEditMode,
  setIsEditMode,
  selectedTab,
  handleEditItemChange,
  handleUpdateItem,
  closeItemDetails,
  navigateTo,
  handleUpdateStorage,
  handleUpdateMac,
  handleUnallocateSubnet,
  discoverSwitchPorts,
  fetchSwitchPorts,
  visiblePasswords,
  togglePasswordVisibility,
  getCountryFlag,
  getDatacenterName,
  isSubnetSelectionModalOpen,
  setIsSubnetSelectionModalOpen,
  isSelectingMainSubnet,
  setIsSelectingMainSubnet,
  handleSubnetSelection,
  ramConfigurations,
  cpuModels,
  switchModels,
  setShowAddCpuModal,
  setShowAddRamModal,
  setShowAddSwitchModelModal,
  storageConfigurations,
  cities,
  countries,
  racks,
  chassis,
  bladeServers,
  portsRefreshKey,
  isNestedModal = false // New prop to indicate if this is a nested modal
}) => {
  // Password Field Component
  const PasswordField = ({
    isEditMode,
    fieldName,
    value,
    onChange,
    placeholder = "••••••••",
    label
  }) => {
    const isVisible = visiblePasswords[fieldName] || false;

    return (
      <div>
        <div className="text-xs text-gray-500">{label}</div>
        {isEditMode ? (
          <div className="relative">
            <input
              type={isVisible ? "text" : "password"}
              name={fieldName}
              value={value || ''}
              onChange={(e) => {
                onChange(e);
              }}
              className="font-medium w-full px-2 py-1 pr-8 border border-gray-300 rounded-md text-sm"
              placeholder={placeholder}
            />
            <button
              type="button"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-indigo-700"
              onClick={(e) => {
                e.preventDefault();
                togglePasswordVisibility(fieldName);
              }}
            >
              {isVisible ? (
                <Eye className="w-4 h-4" />
              ) : (
                <EyeOff className="w-4 h-4" />
              )}
            </button>
          </div>
        ) : (
          <div className="font-medium flex items-center">
            <Lock className="w-4 h-4 mr-1.5 text-indigo-700" />
            {value ? (
              <div className="flex items-center">
                <span>{isVisible ? value : placeholder}</span>
                <button
                  type="button"
                  className="ml-2 text-gray-500 hover:text-indigo-700"
                  onClick={() => togglePasswordVisibility(fieldName)}
                >
                  {isVisible ? (
                    <Eye className="w-4 h-4" />
                  ) : (
                    <EyeOff className="w-4 h-4" />
                  )}
                </button>
              </div>
            ) : (
              'Not set'
            )}
          </div>
        )}
      </div>
    );
  };

  // RAM Field Component
  const RamField = ({
    isEditMode,
    value,
    onChange,
    ramConfigurations,
    onAddClick,
    displayValue,
    label = "RAM"
  }) => {
    return (
      <div>
        <div className="text-xs text-gray-500">{label}</div>
        {isEditMode ? (
          <div className="flex">
            <select
              name="ram"
              value={value || ''}
              onChange={onChange}
              className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="">Select RAM Configuration</option>
              {ramConfigurations.map(ram => (
                <option key={ram.id} value={ram.id}>{ram.description}</option>
              ))}
            </select>
            <button
              type="button"
              onClick={onAddClick}
              className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
              title="Add New RAM Configuration"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
        ) : (
          <div className="font-medium flex items-center">
            <Server className="w-4 h-4 mr-1.5 text-indigo-700" />
            {displayValue || 'Not specified'}
          </div>
        )}
      </div>
    );
  };

  // CPU Field Component
  const CpuField = ({
    isEditMode,
    value,
    onChange,
    cpuModels,
    onAddClick,
    label = "CPU"
  }) => {
    return (
      <div>
        <div className="text-xs text-gray-500">{label}</div>
        {isEditMode ? (
          <div className="flex">
            <select
              name="cpu"
              value={value || ''}
              onChange={onChange}
              className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="">Select CPU Model</option>
              {cpuModels.map(cpu => (
                <option key={cpu.id} value={cpu.cpu}>{cpu.cpu}</option>
              ))}
            </select>
            <button
              type="button"
              onClick={onAddClick}
              className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
              title="Add New CPU Model"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
        ) : (
          <div className="font-medium flex items-center">
            <Cpu className="w-4 h-4 mr-1.5 text-indigo-700" />
            {value || 'Not specified'}
          </div>
        )}
      </div>
    );
  };

  // Switch Model Field Component
  const SwitchModelField = ({
    isEditMode,
    value,
    onChange,
    switchModels,
    onAddClick,
    label = "Switch Model"
  }) => {
    return (
      <div>
        <div className="text-xs text-gray-500">{label}</div>
        {isEditMode ? (
          <div className="flex">
            <select
              name="model_id"
              value={value || ''}
              onChange={onChange}
              className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="">Select Switch Model</option>
              {switchModels.map(model => (
                <option key={model.id} value={model.id}>{model.name} ({model.size}U)</option>
              ))}
            </select>
            <button
              type="button"
              onClick={onAddClick}
              className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
              title="Add New Switch Model"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
        ) : (
          <div className="font-medium flex items-center">
            <Network className="w-4 h-4 mr-1.5 text-indigo-700" />
            {value || 'Not specified'}
          </div>
        )}
      </div>
    );
  };

  // SNMP Configuration Component for Switch Details
  const SnmpConfigurationSection = ({
    isEditMode,
    selectedItem,
    handleEditItemChange,
    discoverSwitchPorts
  }) => {
    // Default SNMP versions
    const snmpVersions = [
      { value: 1, label: 'SNMP v1' },
      { value: 2, label: 'SNMP v2c' },
      { value: 3, label: 'SNMP v3' }
    ];

    // Function to handle port discovery when SNMP is configured
    const handleDiscoverPorts = () => {
      if (selectedItem.snmp_community) {
        discoverSwitchPorts(
          selectedItem.id,
          selectedItem.snmp_community,
          selectedItem.snmp_version || 2
        );
      } else {
        // Prompt user for community string if not saved
        const community = prompt('Enter SNMP community string:', 'public');
        if (community) {
          discoverSwitchPorts(selectedItem.id, community, selectedItem.snmp_version || 2);

          // Force ports refresh by emitting the event directly
          const event = new CustomEvent('inventory-data-change', {
            detail: {
              deviceType: 'switch',
              switchId: selectedItem.id,
              timestamp: Date.now(),
              action: 'port-discovery'
            }
          });
          window.dispatchEvent(event);
        }
      }
    };

    return (
      <div className="space-y-3">
        {/* SNMP Community String */}
        <div>
          <div className="text-xs text-gray-500">Community String</div>
          {isEditMode ? (
            <input
              type="text"
              name="snmp_community"
              value={selectedItem.snmp_community || ''}
              onChange={handleEditItemChange}
              className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
              placeholder="e.g. public"
            />
          ) : (
            <div className="font-medium flex items-center">
              {selectedItem.snmp_community ? (
                <>
                  {selectedItem.snmp_community}
                </>
              ) : (
                <span className="text-gray-500">Not configured</span>
              )}
            </div>
          )}
        </div>

        {/* SNMP Version */}
        <div>
          <div className="text-xs text-gray-500">SNMP Version</div>
          {isEditMode ? (
            <select
              name="snmp_version"
              value={selectedItem.snmp_version || 2}
              onChange={handleEditItemChange}
              className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
            >
              {snmpVersions.map(version => (
                <option key={version.value} value={version.value}>
                  {version.label}
                </option>
              ))}
            </select>
          ) : (
            <div className="font-medium">
              {selectedItem.snmp_version ?
                `SNMP v${selectedItem.snmp_version}` :
                'Default (SNMPv2c)'}
            </div>
          )}
        </div>

        {/* Discover Ports Button - Only show in view mode and when SNMP is configured */}
        {!isEditMode && (
          <div className="mt-2">
            <button
              onClick={handleDiscoverPorts}
              className="flex items-center px-2 py-1 bg-indigo-100 hover:bg-indigo-200 text-indigo-700 rounded text-xs transition-colors"
            >
              <Network className="w-3 h-3 mr-1" />
              Discover Ports
            </button>
            {!selectedItem.snmp_community && (
              <div className="mt-1 text-xs text-gray-500">
                Note: You will be prompted for SNMP credentials if not saved.
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // Component for IPMI Access
  const IpmiAccessField = ({ ipmiAddress, rootPassword, isEditMode }) => {
    const [copyStatus, setCopyStatus] = useState('');

    const handleIpmiClick = () => {
      if (!ipmiAddress) return;

      // Copy root password to clipboard if available
      if (rootPassword) {
        navigator.clipboard.writeText(rootPassword)
          .then(() => {
            setCopyStatus('copied');
            // Reset status after 3 seconds
            setTimeout(() => setCopyStatus(''), 3000);
          })
          .catch(err => {
            console.error('Failed to copy password: ', err);
            setCopyStatus('error');
            // Reset status after 3 seconds
            setTimeout(() => setCopyStatus(''), 3000);
          });
      }

      // Open IPMI in new tab
      const ipmiUrl = ipmiAddress.startsWith('http')
        ? ipmiAddress
        : `http://${ipmiAddress}`;
      window.open(ipmiUrl, '_blank');
    };

    if (isEditMode) {
      return null; // Don't render in edit mode
    }

    return (
      <div className="mt-2">
        {ipmiAddress && (
          <button
            onClick={handleIpmiClick}
            className="flex items-center px-2 py-1 bg-indigo-100 hover:bg-indigo-200 text-indigo-700 rounded text-xs font-medium transition-colors"
          >
            <Eye className="w-3 h-3 mr-1" />
            Open IPMI Interface
            {rootPassword && (
              <span className="ml-1">
                {copyStatus === 'copied' ? (
                  <span className="text-green-600 ml-1">(Password copied!)</span>
                ) : copyStatus === 'error' ? (
                  <span className="text-red-600 ml-1">(Copy failed)</span>
                ) : (
                  <span className="text-gray-500 ml-1">(will copy root password)</span>
                )}
              </span>
            )}
          </button>
        )}
      </div>
    );
  };

  // Component for launching iDRAC Console
  const IdracConsoleLauncher = ({ ipmiAddress, username, password, version }) => {
    const [launching, setLaunching] = useState(false);
    const [showInstructions, setShowInstructions] = useState(false);

    // Generate JNLP file content for iDRAC
    const generateJnlpContent = () => {
      // Base URL for iDRAC
      const baseUrl = ipmiAddress.startsWith('http')
        ? ipmiAddress.replace(/^https?:\/\//, '')
        : ipmiAddress;

      // Different JNLP configuration based on iDRAC version
      if (version === 9) {
        return `<?xml version="1.0" encoding="UTF-8"?>
<jnlp spec="1.0+" codebase="https://${baseUrl}:443">
  <information>
    <title>Virtual Console Client</title>
    <vendor>Dell Inc.</vendor>
    <description>Virtual Console Client for iDRAC9</description>
  </information>
  <application-desc main-class="com.avocent.idrac.kvm.Main">
    <argument>IP=${baseUrl}</argument>
    <argument>JNLPSTR=JViewer</argument>
    <argument>JNLPNAME=JViewer.jnlp</argument>
    ${username ? `<argument>user=${username}</argument>` : ''}
    ${password ? `<argument>passwd=${password}</argument>` : ''}
    <argument>kmport=5900</argument>
    <argument>vport=5900</argument>
    <argument>apcp=1</argument>
    <argument>version=2</argument>
  </application-desc>
  <security>
    <all-permissions/>
  </security>
  <resources>
    <j2se version="1.8+" initial-heap-size="512m" max-heap-size="1024m"/>
    <jar href="https://${baseUrl}:443/software/avctKVM.jar" download="eager" main="true"/>
  </resources>
  <resources os="Windows" arch="x86">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin32.jar" download="eager"/>
  </resources>
  <resources os="Windows" arch="amd64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
  </resources>
  <resources os="Windows" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="x86">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i386">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i586">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i686">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="amd64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
  </resources>
  <resources os="Mac OS X" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOMac64.jar" download="eager"/>
  </resources>
</jnlp>`;
      } else {
        // Default to iDRAC 8 format
        return `<?xml version="1.0" encoding="UTF-8"?>
<jnlp spec="1.0+" codebase="https://${baseUrl}:443">
  <information>
    <title>iDRAC8 Virtual Console Client</title>
    <vendor>Dell Inc.</vendor>
    <description>iDRAC8 Virtual Console Client</description>
  </information>
  <application-desc main-class="com.avocent.idrac.kvm.Main">
    <argument>ip=${baseUrl}</argument>
    ${username ? `<argument>user=${username}</argument>` : ''}
    ${password ? `<argument>passwd=${password}</argument>` : ''}
    <argument>kmport=5900</argument>
    <argument>vport=5900</argument>
    <argument>title=iDRAC8 Virtual Console Client</argument>
    <argument>helpurl=https://${baseUrl}:443/help/contents.html</argument>
  </application-desc>
  <security>
    <all-permissions/>
  </security>
  <resources>
    <j2se version="1.6.0_24+" initial-heap-size="512m" max-heap-size="1024m"/>
    <jar href="https://${baseUrl}:443/software/avctKVM.jar" download="eager" main="true"/>
  </resources>
  <resources os="Windows" arch="x86">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin32.jar" download="eager"/>
  </resources>
  <resources os="Windows" arch="amd64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
  </resources>
  <resources os="Windows" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="x86">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i386">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i586">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i686">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="amd64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
  </resources>
  <resources os="Mac OS X" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOMac64.jar" download="eager"/>
  </resources>
</jnlp>`;
      }
    };

    // Download JNLP file function
    const downloadJnlpFile = () => {
      setLaunching(true);

      try {
        const jnlpContent = generateJnlpContent();
        const blob = new Blob([jnlpContent], { type: 'application/x-java-jnlp-file' });
        const url = URL.createObjectURL(blob);

        // Create temporary link element to download the file
        const link = document.createElement('a');
        link.href = url;
        link.download = `idrac${version}-console.jnlp`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => {
          URL.revokeObjectURL(url);
          setLaunching(false);
        }, 1000);
      } catch (error) {
        console.error('Error generating JNLP file:', error);
        setLaunching(false);
      }
    };

    if (!ipmiAddress) return null;

    return (
      <div className="mt-1">
        <div className="flex flex-wrap items-center gap-2">
          <button
            onClick={downloadJnlpFile}
            disabled={launching}
            className={`flex items-center px-2 py-1 text-xs font-medium rounded transition-colors
              ${launching ? 'bg-gray-200 text-gray-500' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'}`}
          >
            <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 17L15 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 6V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 13L15 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 13L9 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M3 15V16C3 18.2091 4.79086 20 7 20H17C19.2091 20 21 18.2091 21 16V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
            {launching ? 'Generating...' : `Launch Java Console`}
          </button>

          <button
            onClick={() => setShowInstructions(!showInstructions)}
            className="flex items-center px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded text-xs transition-colors"
          >
            <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
              <path d="M12 7V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <circle cx="12" cy="17" r="1" fill="currentColor"/>
            </svg>
            {showInstructions ? 'Hide Help' : 'Help'}
          </button>
        </div>

        {showInstructions && (
          <div className="mt-2 p-3 bg-gray-50 rounded-md text-xs">
            <h4 className="font-medium mb-1">How to use iDRAC {version} Java Console:</h4>
            <ol className="list-decimal pl-4 space-y-1">
              <li>Download and install Java if you haven't already
                (<a href="https://www.java.com/download/" target="_blank" rel="noopener noreferrer"
                   className="text-blue-600 hover:underline">www.java.com</a>)</li>
              <li>Save the .jnlp file when prompted</li>
              <li>Right-click the downloaded file and select "Open with" → "Java Web Start"</li>
              <li>If prompted with security warnings, click "Run" to continue</li>
              <li>The iDRAC console should load with your credentials auto-filled</li>
            </ol>
            <p className="mt-2 text-gray-600">Note: You may need to add the iDRAC IP to the Java security exception site list in your Java Control Panel.</p>
          </div>
        )}
      </div>
    );
  };

  // Component for iDRAC auto-detection
  const IdracAutoDetectConsole = ({ ipmiAddress, username, password }) => {
    const [idracVersion, setIdracVersion] = useState(null);
    const [detecting, setDetecting] = useState(true);

    React.useEffect(() => {
      // Auto-detect iDRAC version when component mounts
      if (ipmiAddress) {
        setDetecting(true);

        // Try to extract version from label if available
        const detectedFromLabel = selectedItem?.label?.toLowerCase().match(/idrac\s*(\d+)/i);
        if (detectedFromLabel && (detectedFromLabel[1] === '8' || detectedFromLabel[1] === '9')) {
          setIdracVersion(parseInt(detectedFromLabel[1]));
          setDetecting(false);
          return;
        }

        // Try to detect from notes
        const detectedFromNotes = selectedItem?.notes?.toLowerCase().match(/idrac\s*(\d+)/i);
        if (detectedFromNotes && (detectedFromNotes[1] === '8' || detectedFromNotes[1] === '9')) {
          setIdracVersion(parseInt(detectedFromNotes[1]));
          setDetecting(false);
          return;
        }

        // Try to detect from model information (Dell servers with specific generations)
        const modelInfo = [
          { pattern: /r[0-9]40/i, version: 9 },    // R740, R640, R440, etc. = iDRAC 9
          { pattern: /r[0-9]30/i, version: 8 },    // R730, R630, R430, etc. = iDRAC 8
          { pattern: /r[0-9]20/i, version: 7 },    // R720, R620, R520, etc. = iDRAC 7
          { pattern: /poweredge\s*1[4-9]/i, version: 9 },  // PowerEdge 14G+ = iDRAC 9
          { pattern: /poweredge\s*1[23]/i, version: 8 },   // PowerEdge 12G-13G = iDRAC 8
          { pattern: /g1[4-9]/i, version: 9 },     // Dell G14+ servers = iDRAC 9
          { pattern: /g1[23]/i, version: 8 },      // Dell G12-G13 servers = iDRAC 8
        ];

        // Check against server label and notes
        const serverInfo = (selectedItem?.label || '') + ' ' + (selectedItem?.cpu || '') + ' ' + (selectedItem?.notes || '');

        for (const model of modelInfo) {
          if (model.pattern.test(serverInfo)) {
            setIdracVersion(model.version);
            setDetecting(false);
            return;
          }
        }

        // Default to iDRAC 8 if no detection method succeeds
        setIdracVersion(8);
        setDetecting(false);
      }
    }, [ipmiAddress, selectedItem]);

    if (!ipmiAddress) return null;

    // Only show for iDRAC 8 or 9 (iDRAC 7 and earlier used different mechanisms)
    if (idracVersion !== 8 && idracVersion !== 9) return null;

    return (
      <div className="mt-2 border-t pt-2">
        <div className="text-xs text-gray-500 mb-1">Launch iDRAC Console</div>

        {detecting ? (
          <div className="flex items-center text-xs text-gray-500">
            <RefreshCw className="w-3 h-3 animate-spin mr-1" />
            Detecting iDRAC version...
          </div>
        ) : (
          <IdracConsoleLauncher
            ipmiAddress={ipmiAddress}
            username={username}
            password={password}
            version={idracVersion}
          />
        )}
      </div>
    );
  };

  // Render status badge
  const renderStatusBadge = (status) => {
    const badgeClasses = {
      'Available': 'bg-green-100 text-green-800',
      'Active': 'bg-green-100 text-green-800', // Keep for backward compatibility
      'In use': 'bg-yellow-100 text-yellow-800',
      'Defect': 'bg-red-100 text-red-800'
    };

    const icons = {
      'Available': <CheckCircle className="w-4 h-4 mr-1" />,
      'Active': <CheckCircle className="w-4 h-4 mr-1" />, // Keep for backward compatibility
      'In use': <Activity className="w-4 h-4 mr-1" />,
      'Defect': <AlertTriangle className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertTriangle className="w-4 h-4 mr-1" />}
        {status}
      </span>
    );
  };

  return (
    <div className={`fixed inset-0 flex items-center justify-center z-[60] ${isNestedModal ? '' : 'bg-black bg-opacity-50'}`}>
      <div className="bg-white rounded-lg shadow-lg w-11/12 max-w-6xl flex flex-col max-h-[90vh] mx-4 overflow-hidden">
        {/* Fixed header with rounded top corners */}
        <div className="p-4 border-b flex justify-between items-center bg-gray-50 sticky top-0 z-10 rounded-t-lg">
          <h2 className="text-xl font-bold text-gray-800">
            {isEditMode ? 'Edit' : ''} Item Details
          </h2>
          <button
            onClick={closeItemDetails}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>

        {/* Scrollable content area */}
        <div className="p-5 overflow-y-auto flex-grow">
          {/* Item Header with Order Info */}
          <div className="flex items-center justify-between mb-5">
            <div className="flex items-center">
              <div className="h-14 w-14 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 mr-4">
                {selectedTab === 'dedicated' ? <Server className="w-7 h-7" /> :
                 selectedTab === 'blade' ? <Layers className="w-7 h-7" /> :
                 selectedTab === 'chassis' ? <HardDrive className="w-7 h-7" /> :
                 <Network className="w-7 h-7" />}
              </div>
              <div>
                {isEditMode ? (
                  <input
                    type="text"
                    name="label"
                    value={selectedItem.label || ''}
                    onChange={handleEditItemChange}
                    className="text-xl font-bold px-2 py-1 border border-gray-300 rounded-md w-full"
                  />
                ) : (
                  <h3 className="text-xl font-bold">{selectedItem.label}</h3>
                )}
                <div className="text-gray-500 flex items-center">
                  <MapPin className="w-4 h-4 mr-1.5" />
                  {selectedItem.city_name}, {selectedItem.country_name}<span className="ml-1.5">{getCountryFlag(selectedItem.country_name)}</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div>
                {isEditMode ? (
                  <select
                    name="status"
                    value={selectedItem.status || 'Available'}
                    onChange={handleEditItemChange}
                    className="px-2 py-1 border border-gray-300 rounded-md text-sm"
                  >
                    <option value="Available">Available</option>
                    <option value="In Use">In Use</option>
                    <option value="Defect">Defect</option>
                  </select>
                ) : (
                  renderStatusBadge(selectedItem.status)
                )}
              </div>

              {selectedItem.order_id && (
                <div className="text-right">
                  <a
                    href={`/admin/orders/${selectedItem.order_id}`}
                    className="text-indigo-700 hover:underline flex items-center justify-end"
                    onClick={(e) => {
                      e.preventDefault();
                      navigateTo(`/admin/orders/${selectedItem.order_id}`);
                    }}
                  >
                    <Calendar className="w-4 h-4 mr-1" />
                    Order #{selectedItem.order_id}
                  </a>
                  {selectedItem.client_id && (
                    <a
                      href={`/admin/accounts/${selectedItem.client_id}`}
                      className="text-gray-600 hover:underline flex items-center justify-end mt-1"
                      onClick={(e) => {
                        e.preventDefault();
                        navigateTo(`/admin/accounts/${selectedItem.client_id}`);
                      }}
                    >
                      <User className="w-4 h-4 mr-1" />
                      {selectedItem.company_name || selectedItem.client_name}
                    </a>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Power Management Card - only for dedicated and blade servers */}
          {(selectedTab === 'dedicated' || selectedTab === 'blade') && !isEditMode && (
            <PowerManagement
              server={selectedItem}
              serverType={selectedTab}
              ipmiAddress={selectedItem?.ipmi}
              ipmiRootPassword={selectedItem?.ipmi_root_pass}
              onRefresh={() => {
                // Refresh the server data when power state changes
                // This function is handled by the parent component
              }}
            />
          )}

          {/* Multi-column Information Layout */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-5">
            {/* Basic Info Card */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-gray-800 mb-3 border-b pb-2">Basic Information</h4>
              <div className="space-y-2">
                <div>
                  <div className="text-xs text-gray-500">ID</div>
                  <div className="font-medium">#{selectedItem.id}</div>
                </div>

                <div>
                  <div className="text-xs text-gray-500">Label</div>
                  {isEditMode ? (
                    <input
                      type="text"
                      name="label"
                      value={selectedItem.label || ''}
                      onChange={handleEditItemChange}
                      className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                    />
                  ) : (
                    <div className="font-medium">{selectedItem.label}</div>
                  )}
                </div>

                {selectedTab === 'switch' && (
                  <div>
                    <div className="text-xs text-gray-500">Model</div>
                    {isEditMode ? (
                      <div className="flex">
                        <select
                          name="model_id"
                          value={selectedItem.model_id || ''}
                          onChange={handleEditItemChange}
                          className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                        >
                          <option value="">Select Switch Model</option>
                          {switchModels.map(model => (
                            <option key={model.id} value={model.id}>{model.name} ({model.size}U)</option>
                          ))}
                        </select>
                        <button
                          type="button"
                          onClick={() => setShowAddSwitchModelModal(true)}
                          className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
                          title="Add New Switch Model"
                        >
                          <Plus className="w-4 h-4" />
                        </button>
                      </div>
                    ) : (
                      <div className="font-medium flex items-center">
                        <Network className="w-4 h-4 mr-1.5 text-indigo-700" />
                        {switchModels.find(m => m.id === parseInt(selectedItem.model_id))?.name || 'Not specified'}
                        {selectedItem.size_ru && ` (${selectedItem.size_ru}U)`}
                      </div>
                    )}
                  </div>
                )}

                {(selectedTab === 'dedicated' || selectedTab === 'blade') && (
                  <div>
                    <div className="text-xs text-gray-500">CPU</div>
                    {isEditMode ? (
                      <div className="flex">
                        <select
                          name="cpu"
                          value={selectedItem.cpu || ''}
                          onChange={handleEditItemChange}
                          className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                        >
                          <option value="">Select CPU Model</option>
                          {cpuModels.map(cpu => (
                            <option key={cpu.id} value={cpu.id}>{cpu.cpu}</option>
                          ))}
                        </select>
                        <button
                          type="button"
                          onClick={() => setShowAddCpuModal(true)}
                          className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
                          title="Add New CPU Model"
                        >
                          <Plus className="w-4 h-4" />
                        </button>
                      </div>
                    ) : (
                      <div className="font-medium">{selectedItem.cpu_name || selectedItem.cpu || 'Not specified'}</div>
                    )}
                  </div>
                )}

                {(selectedTab === 'dedicated' || selectedTab === 'blade') && (
                  <div>
                    <div className="text-xs text-gray-500">RAM</div>
                    {isEditMode ? (
                      <div className="flex">
                        <select
                          name="ram"
                          value={selectedItem.ram || ''}
                          onChange={handleEditItemChange}
                          className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                        >
                          <option value="">Select RAM Configuration</option>
                          {ramConfigurations.map(ram => (
                            <option key={ram.id} value={ram.id}>{ram.description}</option>
                          ))}
                        </select>
                        <button
                          type="button"
                          onClick={() => setShowAddRamModal(true)}
                          className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
                          title="Add New RAM Configuration"
                        >
                          <Plus className="w-4 h-4" />
                        </button>
                      </div>
                    ) : (
                      <div className="font-medium">
                        {selectedItem.ram_description ||
                        (selectedItem.ram ? `${selectedItem.ram}GB` : 'Not specified')}
                      </div>
                    )}
                  </div>
                )}

                {selectedTab === 'dedicated' && (
                  <div className="mb-3">
                    <div className="text-xs text-gray-500">Size U</div>
                    {isEditMode ? (
                      <input
                        type="text"
                        name="size"
                        value={selectedItem.size || ''}
                        onChange={handleEditItemChange}
                        className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                        placeholder="e.g. ***********"
                      />
                    ) : (
                      <div className="font-medium flex items-center">
                        {selectedItem.size || 'Not assigned'}
                      </div>
                    )}
                  </div>
                )}

                <div>
                  <div className="text-xs text-gray-500">Status</div>
                  {isEditMode ? (
                    <select
                      name="status"
                      value={selectedItem.status || 'Available'}
                      onChange={handleEditItemChange}
                      className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="Available">Available</option>
                      <option value="In Use">In Use</option>
                      <option value="Defect">Defect</option>
                    </select>
                  ) : (
                    <div className="font-medium">{selectedItem.status}</div>
                  )}
                </div>

                {(selectedTab === 'dedicated' || selectedTab === 'blade') && (
                  <div>
                    {/* Main Subnet Field */}
                    <div>
                      <div className="text-xs text-gray-500">Main Subnet</div>
                      <div className="font-medium flex items-center">
                        {selectedItem.main_ip || 'Not assigned'}
                        <div className="flex items-center ml-2">
                          {selectedItem.main_ip && (
                            <button
                              type="button"
                              onClick={() => handleUnallocateSubnet(selectedItem.main_ip, true)}
                              className="p-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 flex items-center justify-center"
                              title="Unallocate subnet"
                            >
                              <XCircle className="w-4 h-4" />
                            </button>
                          )}
                          <button
                            type="button"
                            onClick={() => {
                              setIsSelectingMainSubnet(true);
                              setIsSubnetSelectionModalOpen(true);
                            }}
                            className="p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center mr-1"
                            title="Select from available subnets"
                            style={{marginLeft:'12px'}}
                          >
                            <Network className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Additional Subnets Field */}
                    <div>
                      <div className="text-xs text-gray-500">Additional Subnets</div>
                      <div className="font-medium">
                        {selectedItem.additional_ips ? (
                          <div className="flex flex-wrap gap-1 items-center">
                            {selectedItem.additional_ips.split(',').map((ip, index) => {
                              const trimmedIp = ip.trim();
                              return trimmedIp ? (
                                <div key={index} className="flex items-center">
                                  <span className="text-sm px-2 py-0.5 bg-gray-100 rounded inline-block mr-1">
                                    {trimmedIp}
                                  </span>
                                  <button
                                    type="button"
                                    onClick={() => handleUnallocateSubnet(trimmedIp, false)}
                                    className="p-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 flex items-center justify-center"
                                    title="Unallocate subnet"
                                  >
                                    <XCircle className="w-4 h-4" />
                                  </button>
                                </div>
                              ) : null;
                            }).filter(Boolean)}
                            <button
                              type="button"
                              onClick={() => {
                                setIsSelectingMainSubnet(false);
                                setIsSubnetSelectionModalOpen(true);
                              }}
                              className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
                              title="Add subnet from available subnets"
                            >
                              <Network className="w-4 h-4" />
                            </button>
                          </div>
                        ) : (
                          <div className="flex flex-wrap gap-1">
                            <span className="text-gray-500">None assigned </span>
                            <button
                              type="button"
                              onClick={() => {
                                setIsSelectingMainSubnet(false);
                                setIsSubnetSelectionModalOpen(true);
                              }}
                              className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
                              title="Add subnet from available subnets"
                            >
                              <Network className="w-4 h-4" />
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Location Card */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-semibold text-gray-800 mb-3 border-b pb-2">Location Information</h4>
              <div className="space-y-2">
                <div>
                  <div className="text-xs text-gray-500">Datacenter</div>
                  {isEditMode && (selectedTab === 'dedicated' || selectedTab === 'chassis' || selectedTab === 'switch') ? (
                    <select
                      name="city_id"
                      value={selectedItem.city_id || ''}
                      onChange={handleEditItemChange}
                      className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="">Select City/Datacenter</option>
                      {cities.map(city => (
                        <option key={city.id} value={city.id}>{city.city} {city.datacenter ? `(${city.datacenter})` : ''}</option>
                      ))}
                    </select>
                  ) : (
                    <div className="font-medium flex items-center">
                      <Building className="w-4 h-4 mr-1.5 text-indigo-700" />
                      {selectedItem.city_name}
                      {(() => {
                        // Get datacenter name consistently for all entity types
                        const datacenter = selectedItem.datacenter || getDatacenterName(selectedItem.city_id);
                        return datacenter ? ` (${datacenter})` : '';
                      })()}

                      {isEditMode && selectedTab === 'blade' && (
                        <span className="ml-2 text-xs text-gray-500 italic">(Location set by chassis)</span>
                      )}
                    </div>
                  )}
                </div>

                <div>
                  <div className="text-xs text-gray-500">Country</div>
                  {isEditMode && (selectedTab === 'dedicated' || selectedTab === 'chassis' || selectedTab === 'switch') ? (
                    <select
                      name="country_id"
                      value={selectedItem.country_id || ''}
                      onChange={handleEditItemChange}
                      className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="">Select Country</option>
                      {countries.map(country => (
                        <option key={country.id} value={country.id}>{country.country}</option>
                      ))}
                    </select>
                  ) : (
                    <div className="font-medium flex items-center">
                      <Globe className="w-4 h-4 mr-1.5 text-indigo-700" />
                      {(() => {
                        // For blade servers, check if country info needs to be fetched from chassis
                        if (selectedTab === 'blade' && !selectedItem.country_name && selectedItem.chassis_id) {
                          const associatedChassis = chassis.find(c => c.id.toString() === selectedItem.chassis_id.toString());
                          if (associatedChassis && associatedChassis.country_name) {
                            return `${associatedChassis.country_name}`;
                          }
                        }
                        return selectedItem.country_name || 'Unknown';
                      })()}
                      {isEditMode && selectedTab === 'blade' && (
                        <span className="ml-2 text-xs text-gray-500 italic">(Country set by chassis)</span>
                      )}
                    </div>
                  )}
                </div>

                <div>
                  <div className="text-xs text-gray-500">Rack</div>
                  {isEditMode && (selectedTab === 'dedicated' || selectedTab === 'chassis' || selectedTab === 'switch') ? (
                    <div className="flex gap-2">
                      <select
                        name="rack_id"
                        value={selectedItem.rack_id || ''}
                        onChange={handleEditItemChange}
                        className="font-medium w-2/3 px-2 py-1 border border-gray-300 rounded-md text-sm"
                      >
                        <option value="">Select Rack</option>
                        {racks.map(rack => (
                          <option key={rack.id} value={rack.id}>{rack.rack_name}</option>
                        ))}
                      </select>
                      <input
                        type="text"
                        name={selectedTab === 'switch' ? 'rack_position' : 'position'}
                        value={selectedTab === 'switch' ? selectedItem.rack_position || '' : selectedItem.position || ''}
                        onChange={handleEditItemChange}
                        className="font-medium w-1/3 px-2 py-1 border border-gray-300 rounded-md text-sm"
                        placeholder="Position"
                      />
                    </div>
                  ) : (
                    <div className="font-medium flex items-center">
                      <Server className="w-4 h-4 mr-1.5 text-indigo-700" />
                      {selectedItem.rack_name}
                      {selectedTab === 'switch' ? `(${selectedItem.rack_position})` :
                      selectedTab === 'blade' ? (() => {
                        if (selectedItem.rack_position) {
                          return `(${selectedItem.rack_position})`;
                        } else if (selectedItem.chassis_id) {
                          const chassisInfo = chassis.find(c => c.id.toString() === selectedItem.chassis_id.toString());
                          return chassisInfo && chassisInfo.position ?
                            `(${chassisInfo.position} - via Chassis)` :
                            '';
                        }
                        return '';
                      })() :
                      selectedItem.position ? `(${selectedItem.position})` : ''}
                      {isEditMode && selectedTab === 'blade' && (
                        <span className="ml-2 text-xs text-gray-500 italic">(Rack set by chassis)</span>
                      )}
                    </div>
                  )}
                </div>

                {(selectedTab === 'dedicated' || selectedTab === 'blade') && (
                  <div>
                    <div>
                      <div className="text-xs text-gray-500">IPMI</div>
                      {isEditMode ? (
                        <CountryIpSelector
                          selectedItem={selectedItem}
                          onChange={handleEditItemChange}
                          name="ipmi"
                          value={selectedItem.ipmi || ''}
                          label="IPMI IP Address"
                          placeholder="IPMI Address"
                          serverType={selectedItem.server_type || 'dedicated'}
                        />
                      ) : (
                        <div className="font-medium">
                          <div className="flex items-center">
                            <Cpu className="w-4 h-4 mr-1.5 text-indigo-700" />
                            {selectedItem.ipmi || 'Not specified'}
                          </div>

                          {/* IPMI interface access button */}
                          <IpmiAccessField
                            ipmiAddress={selectedItem.ipmi}
                            rootPassword={selectedItem.ipmi_root_pass}
                            isEditMode={isEditMode}
                          />

                          {/* Enhanced iDRAC Console selector with auto-detection */}
                          {selectedItem.ipmi && (
                            <IdracAutoDetectConsole
                              ipmiAddress={selectedItem.ipmi}
                              username="root"
                              password={selectedItem.ipmi_root_pass}
                            />
                          )}
                        </div>
                      )}
                    </div>

                    <PasswordField
                      isEditMode={isEditMode}
                      fieldName="ipmi_root_pass"
                      value={selectedItem.ipmi_root_pass || ''}
                      onChange={(e) => {
                        // Directly update the selectedItem state with the new value
                        handleEditItemChange(e);
                      }}
                      placeholder="••••••••"
                      label="IPMI Root Password"
                    />

                    <PasswordField
                      isEditMode={isEditMode}
                      fieldName="ipmi_user_pass"
                      value={selectedItem.ipmi_user_pass || ''}
                      onChange={(e) => {
                        handleEditItemChange(e);
                      }}
                      placeholder="••••••••"
                      label="IPMI User Password"
                    />
                  </div>
                )}
              </div>
            </div>

            {selectedTab === 'switch' && (
              <>
                {/* Network Settings - basic connectivity */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h5 className="text-sm font-semibold text-gray-700 mb-2 border-b pb-1">Network Settings</h5>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-xs text-gray-500">Switch IP</div>
                      {isEditMode ? (
                        <CountryIpSelector
                          selectedItem={selectedItem}
                          onChange={handleEditItemChange}
                          name="switch_ip"
                          value={selectedItem.switch_ip || ''}
                          label="Switch IP Address"
                          placeholder="Select a Switch IP address"
                        />
                      ) : (
                        <div className="font-medium flex items-center">
                          <Network className="w-4 h-4 mr-1.5 text-indigo-700" />
                          {selectedItem.switch_ip || 'Not configured'}
                        </div>
                      )}
                      <SnmpConfigurationSection
                        isEditMode={isEditMode}
                        selectedItem={selectedItem}
                        handleEditItemChange={handleEditItemChange}
                        discoverSwitchPorts={discoverSwitchPorts}
                      />
                    </div>

                    <PasswordField
                      isEditMode={isEditMode}
                      fieldName="root_password"
                      value={selectedItem.root_password || ''}
                      onChange={handleEditItemChange}
                      placeholder="••••••••"
                      label="Root Password"
                    />
                  </div>
                </div>
              </>
            )}

            {/* Network Card for servers */}
            {(selectedTab === 'dedicated' || selectedTab === 'blade') && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-semibold text-gray-800 mb-3 border-b pb-2">Network Information</h4>
                <div className="space-y-4">
                  <NetworkConnections
                    server={selectedItem}
                    serverType={selectedTab === 'blade' ? 'blade' : 'dedicated'}
                    onRefresh={() => {
                      // This is handled by the parent component
                    }}
                    editable={true} // Allow port assignment in both view and edit modes
                    isEditMode={isEditMode} // Pass the edit mode state to control delete button visibility
                  />

                  <div>
                    <div className="text-xs text-gray-500">MAC Address</div>
                    {isEditMode ? (
                      <input
                        type="text"
                        name="mac"
                        value={selectedItem.mac || ''}
                        onChange={handleEditItemChange}
                        className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                      />
                    ) : (
                      <div>
                        <div className="font-medium flex items-center">
                          <Network className="w-4 h-4 mr-1.5 text-indigo-700" />
                          {selectedItem.mac || 'Not specified'}
                        </div>

                        {/* Add MAC Detection Button */}
                        <MacAddressDetectionButton
                          selectedItem={selectedItem}
                          onUpdateMac={handleUpdateMac}
                          isEditMode={isEditMode}
                          serverType={selectedTab}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Type-specific content - blade server */}
          {selectedTab === 'blade' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5">
              {/* Authentication */}
              <div className="bg-white p-3 rounded shadow-sm">
                <h5 className="text-xs font-semibold text-gray-700 mb-2 border-b pb-1">Authentication</h5>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-xs text-gray-500">User</div>
                    {isEditMode ? (
                      <input
                        type="text"
                        name="user"
                        value={selectedItem.user || ''}
                        onChange={handleEditItemChange}
                        className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                      />
                    ) : (
                      <div className="font-medium flex items-center">
                        <User className="w-4 h-4 mr-1.5 text-indigo-700" />
                        {selectedItem.user || 'Not set'}
                      </div>
                    )}
                  </div>
                  <PasswordField
                    isEditMode={isEditMode}
                    fieldName="root"
                    value={selectedItem.root}
                    onChange={handleEditItemChange}
                    placeholder="••••••••••"
                    label="Root"
                  />
                </div>
              </div>

              {/* Bay Configuration */}
              <div className="bg-white p-3 rounded shadow-sm">
                <h5 className="text-xs font-semibold text-gray-700 mb-2 border-b pb-1">Bay Configuration</h5>
                <StorageDetectionButton
                  selectedItem={selectedItem}
                  onUpdateStorage={handleUpdateStorage}
                  isEditMode={isEditMode}
                  serverType="blade"
                />
                <div className="grid grid-cols-5 gap-2">
                  {[1, 2, 3, 4, 5, 6, 7, 8].map(bayNum => {
                    const bayKey = `bay${bayNum}`;
                    const bayNameKey = `${bayKey}_name`;

                    // Check if bay exists and is not '0'
                    const bayValue = selectedItem[bayKey];
                    const bayName = selectedItem[bayNameKey] || bayValue;

                    if (!isEditMode) {
                      return bayValue && bayValue !== '0' ? (
                        <div key={bayNum} className="bg-gray-50 p-2 rounded">
                          <div className="text-xs text-gray-500">Bay {bayNum}</div>
                          <div className="font-medium text-xs">{bayName}</div>
                        </div>
                      ) : null;
                    } else {
                      return (
                        <div key={bayNum} className="bg-gray-50 p-2 rounded">
                          <div className="text-xs text-gray-500">Bay {bayNum}</div>
                          <select
                            name={bayKey}
                            value={selectedItem[bayKey] || '0'}
                            onChange={handleEditItemChange}
                            className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-xs"
                          >
                            <option value="0">Not Used</option>
                            {storageConfigurations.map(config => (
                              <option key={config.id} value={config.id}>{config.label}</option>
                            ))}
                          </select>
                        </div>
                      );
                    }
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Storage Configuration for Dedicated Servers */}
          {selectedTab === 'dedicated' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5">
              {/* Storage Configuration */}
              <div className="bg-white p-3 rounded shadow-sm col-span-full">
                <h5 className="text-xs font-semibold text-gray-700 mb-2 border-b pb-1">Storage Configuration</h5>

                {/* Storage Detection Button */}
                <StorageDetectionButton
                  selectedItem={selectedItem}
                  onUpdateStorage={handleUpdateStorage}
                  isEditMode={isEditMode}
                  serverType="dedicated"
                />

                {/* Display bays in a scrollable container */}
                <div className="overflow-auto max-h-96 pr-2">
                  <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2 mt-3">
                    {Array.from({ length: 26 }, (_, i) => i + 1).map(bayNum => {
                      const bayKey = `bay${bayNum}`;
                      const bayNameKey = `${bayKey}_name`;

                      // Check if bay exists and is not '0'
                      const bayValue = selectedItem[bayKey];
                      const bayName = selectedItem[bayNameKey] || (
                        bayValue && storageConfigurations.find(config =>
                          String(config.id) === String(bayValue)
                        )?.label
                      );

                      if (!isEditMode) {
                        return bayValue && bayValue !== '0' ? (
                          <div key={bayNum} className="bg-gray-50 p-2 rounded">
                            <div className="text-xs text-gray-500">Bay {bayNum}</div>
                            <div className="font-medium text-xs flex items-center">
                              <HardDrive className="w-3 h-3 mr-1 text-indigo-700" />
                              {bayName || `Drive ${bayValue}`}
                            </div>
                          </div>
                        ) : null;
                      } else {
                        return (
                          <div key={bayNum} className="bg-gray-50 p-2 rounded">
                            <div className="text-xs text-gray-500">Bay {bayNum}</div>
                            <select
                              name={bayKey}
                              value={selectedItem[bayKey] || '0'}
                              onChange={handleEditItemChange}
                              className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-xs"
                            >
                              <option value="0">Not Used</option>
                              {storageConfigurations.map(config => (
                                <option key={config.id} value={config.id}>{config.label}</option>
                              ))}
                            </select>
                          </div>
                        );
                      }
                    })}
                  </div>
                </div>

                {/* Show a summary if any storage is configured */}
                {!isEditMode && (() => {
                  const configuredBays = Array.from({ length: 26 }, (_, i) => i + 1)
                    .filter(bayNum => {
                      const bayValue = selectedItem[`bay${bayNum}`];
                      return bayValue && bayValue !== '0';
                    }).length;

                  return configuredBays > 0 ? (
                    <div className="mt-3 text-xs text-gray-600">
                      {configuredBays} of 26 bays configured
                    </div>
                  ) : (
                    <div className="mt-3 text-xs text-gray-500 italic">
                      No storage bays configured yet. Use the Auto-Detect Storage button or edit manually.
                    </div>
                  );
                })()}
              </div>
            </div>
          )}

          {/* Chassis Bay Information */}
          {selectedTab === 'chassis' && (
            <div className="bg-white p-3 rounded shadow-sm mb-5">
              <h5 className="text-xs font-semibold text-gray-700 mb-2 border-b pb-1">Bay Configuration</h5>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {[1, 2, 3, 4].map(bayNum => {
                  const bayKey = `bay${bayNum}`;
                  const bladeId = selectedItem[bayKey];

                  if (!isEditMode) {
                    // View mode
                    if (!bladeId || bladeId === "0") {
                      return (
                        <div key={bayNum} className="bg-gray-50 p-2 rounded">
                          <div className="text-xs text-gray-500">Bay {bayNum}</div>
                          <div className="font-medium text-gray-400 flex items-center">
                            <Layers className="w-4 h-4 mr-1.5 text-gray-400" />
                            Unassigned
                          </div>
                        </div>
                      );
                    }

                    // Find the blade server by ID from our loaded bladeServers array
                    const bladeServer = bladeServers.find(blade => blade.id.toString() === bladeId.toString());
                    const bladeLabel = bladeServer ? bladeServer.label : `Blade ${bladeId}`;

                    return (
                      <div key={bayNum} className="bg-gray-50 p-2 rounded">
                        <div className="text-xs text-gray-500">Bay {bayNum}</div>
                        <div className="font-medium flex items-center">
                          <Layers className="w-4 h-4 mr-1.5 text-indigo-700" />
                          {bladeLabel}
                        </div>
                      </div>
                    );
                  } else {
                    // Edit mode
                    return (
                      <div key={bayNum} className="bg-gray-50 p-2 rounded">
                        <div className="text-xs text-gray-500">Bay {bayNum}</div>
                        <select
                          name={bayKey}
                          value={selectedItem[bayKey] || '0'}
                          onChange={handleEditItemChange}
                          className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                        >
                          <option value="0">Unassigned</option>
                          {bladeServers.map(blade => (
                            <option key={blade.id} value={blade.id}>{blade.label}</option>
                          ))}
                        </select>
                      </div>
                    );
                  }
                })}
              </div>
            </div>
          )}

          {selectedTab === 'switch' && (
            !isEditMode && (
              <div className="bg-white p-3 rounded shadow-sm mb-5">
                <h5 className="text-xs font-semibold text-gray-700 mb-2 border-b pb-1">Switch Ports</h5>
                <div className="text-xs text-gray-500 mb-2">
                  Manage ports for this switch. Click "Select Port" to assign a port to a server.
                </div>

                {/* For individual port assignment from switch view */}
                <SwitchPortsManager
                  key={`switch-ports-${selectedItem.id}-${portsRefreshKey}`}
                  switchId={selectedItem.id}
                  onPortSelected={(port) => {
                    console.log("Selected port:", port);
                    // Implement port assignment logic here or open assignment modal
                  }}
                />
              </div>
            )
          )}

          {/* Notes */}
          <div className="bg-white p-3 rounded shadow-sm mb-4">
            <h5 className="text-xs font-semibold text-gray-700 mb-2 border-b pb-1">Notes</h5>
            {isEditMode ? (
              <textarea
                name="notes"
                value={selectedItem.notes || ''}
                onChange={handleEditItemChange}
                rows="3"
                className="w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
              ></textarea>
            ) : (
              <div className="text-sm text-gray-700 whitespace-pre-line">{selectedItem.notes || 'No notes available'}</div>
            )}
          </div>
        </div>

        {/* Fixed footer with action buttons */}
        <div className="p-4 border-t bg-white sticky bottom-0 z-10">
          <div className="flex flex-wrap gap-3 justify-end">
            {!isEditMode ? (
              <>
                {(selectedTab === 'dedicated' || selectedTab === 'blade') && (
                  <button
                    className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                    onClick={() => {
                      // Close the modal
                      closeItemDetails();
                      // Navigate to the server details page
                      navigateTo(`/admin/inventory/${selectedTab}/${selectedItem.id}`);
                    }}
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    View Details
                  </button>
                )}
                {selectedTab === 'switch' && (
                  <button
                    className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                    onClick={() => {
                      // Close the modal
                      closeItemDetails();
                      // Navigate to the switch details page
                      navigateTo(`/admin/inventory/switch/${selectedItem.id}`);
                    }}
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    View Switch
                  </button>
                )}

                <button
                  className="px-3 py-1.5 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
                  onClick={() => {
                    setIsEditMode(true);
                  }}
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </button>
              </>
            ) : (
              <>
                <button
                  className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                  onClick={() => {
                    setIsEditMode(false);
                    // Original item will be maintained by parent component
                  }}
                >
                  <X className="w-4 h-4 mr-1" />
                  Cancel
                </button>

                <button
                  className="px-3 py-1.5 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
                  onClick={() => {
                    handleUpdateItem();
                    setIsEditMode(false);
                  }}
                >
                  <Save className="w-4 h-4 mr-1" />
                  Save Changes
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Subnet Selection Modal */}
      {isSubnetSelectionModalOpen && (
        <SelectSubnetModal
          onClose={() => setIsSubnetSelectionModalOpen(false)}
          onSelectSubnet={handleSubnetSelection}
          isMainSubnet={isSelectingMainSubnet}
          currentValue={isSelectingMainSubnet ? selectedItem?.main_ip : selectedItem?.additional_ips}
          serverType={selectedTab}
          serverId={selectedItem?.id}
        />
      )}
    </div>
  );
};

export default ServerDetailsModal;