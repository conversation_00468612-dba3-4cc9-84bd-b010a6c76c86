import { useEffect } from 'react';

/**
 * Component to ensure proper viewport meta tag is set for mobile devices
 * This is especially important for iOS devices to handle the browser UI elements correctly
 * and to ensure modals are not cut off on phone view
 */
const ViewportMetaTag = () => {
  useEffect(() => {
    // Check if viewport meta tag exists
    let viewportMeta = document.querySelector('meta[name="viewport"]');

    // If it doesn't exist, create it
    if (!viewportMeta) {
      viewportMeta = document.createElement('meta');
      viewportMeta.name = 'viewport';
      document.head.appendChild(viewportMeta);
    }

    // Set the content attribute with the appropriate values
    // Using viewport-fit=cover to handle notches and rounded corners on modern phones
    viewportMeta.content = 'width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no';

    // Add additional meta tag for Apple devices
    let appleMobileWebAppCapable = document.querySelector('meta[name="apple-mobile-web-app-capable"]');
    if (!appleMobileWebAppCapable) {
      appleMobileWebAppCapable = document.createElement('meta');
      appleMobileWebAppCapable.name = 'apple-mobile-web-app-capable';
      appleMobileWebAppCapable.content = 'yes';
      document.head.appendChild(appleMobileWebAppCapable);
    }

    // Add status bar style meta tag
    let statusBarStyle = document.querySelector('meta[name="apple-mobile-web-app-status-bar-style"]');
    if (!statusBarStyle) {
      statusBarStyle = document.createElement('meta');
      statusBarStyle.name = 'apple-mobile-web-app-status-bar-style';
      statusBarStyle.content = 'black-translucent';
      document.head.appendChild(statusBarStyle);
    }

    // Function to update the dynamic viewport height variable
    const updateVhVariable = () => {
      // Get the viewport height
      const vh = window.innerHeight * 0.01;
      // Set the --vh custom property
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    // Update the --vh variable on initial load
    updateVhVariable();

    // Update the --vh variable when the window is resized or orientation changes
    window.addEventListener('resize', updateVhVariable);
    window.addEventListener('orientationchange', updateVhVariable);

    // Handle iOS Safari's dynamic toolbar
    let lastHeight = window.innerHeight;

    const handleResize = () => {
      const currentHeight = window.innerHeight;
      const diff = Math.abs(currentHeight - lastHeight);

      // If height changed significantly (browser UI appeared/disappeared)
      if (diff > 50) {
        // Update the --vh variable
        updateVhVariable();

        // Update compensation variables for browser UI
        const browserUICompensationTop = window.scrollY > 50 ? '0px' : '0px';
        const browserUICompensationBottom = currentHeight < lastHeight ? '70px' : '0px';

        document.documentElement.style.setProperty('--browser-ui-compensation-top', browserUICompensationTop);
        document.documentElement.style.setProperty('--browser-ui-compensation-bottom', browserUICompensationBottom);

        lastHeight = currentHeight;
      }
    };

    // Listen for scroll events to detect browser UI changes
    window.addEventListener('scroll', handleResize);

    // Clean up event listeners on component unmount
    return () => {
      window.removeEventListener('resize', updateVhVariable);
      window.removeEventListener('orientationchange', updateVhVariable);
      window.removeEventListener('scroll', handleResize);
    };
  }, []);

  // This component doesn't render anything
  return null;
};

export default ViewportMetaTag;
