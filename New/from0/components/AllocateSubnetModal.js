import React, { useState } from 'react';
import { XCircle, AlertCircle, Tag, Check } from 'lucide-react';

const AllocateSubnetModal = ({ subnet, onClose, onAllocateSubnet }) => {
  const [formData, setFormData] = useState({
    subnet_id: subnet.id,
    manual_alocation: '',
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.manual_alocation.trim()) {
      setError('Please enter a description');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      const allocateData = {
        ...formData,
        subnet_id: typeof subnet.id === 'string' 
          ? parseInt(subnet.id.replace(/\D/g, ''), 10) 
          : subnet.id,
      };

      console.log('Allocating subnet with data:', allocateData);

      await onAllocateSubnet(allocateData);
      console.log('Subnet allocated successfully');
      onClose();
    } catch (err) {
      console.error('Full error object:', err);
      setError(
        err.response?.data?.error || 
        err.message || 
        'An error occurred while allocating the subnet'
      );
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full">
        <div className="p-6 border-b flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-800">Reserve Subnet</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {error && (
            <div className="p-3 bg-red-50 text-red-700 rounded-md flex items-center">
              <AlertCircle className="w-5 h-5 mr-2" />
              {error}
            </div>
          )}
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Subnet
            </label>
            <div className="p-2 bg-gray-100 rounded-md font-mono">
              {subnet.cidr}
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              name="manual_alocation"
              value={formData.manual_alocation}
              onChange={handleInputChange}
              placeholder="Enter reservation purpose or notes"
              className="w-full p-2 border rounded-md h-24"
              required
            />
            <p className="mt-1 text-xs text-gray-500">
              Describe what this subnet is reserved for (e.g., "Reserved for future expansion", "Customer XYZ pending setup")
            </p>
          </div>
          
          <div className="pt-4 border-t flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="mr-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`px-4 py-2 text-sm font-medium text-white rounded-md flex items-center ${
                formData.manual_alocation.trim() && !loading
                  ? 'bg-indigo-700 hover:bg-indigo-800'
                  : 'bg-indigo-400 cursor-not-allowed'
              }`}
              disabled={!formData.manual_alocation.trim() || loading}
            >
              <Tag className="w-4 h-4 mr-1" />
              {loading ? 'Reserving...' : 'Reserve Subnet'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AllocateSubnetModal;
