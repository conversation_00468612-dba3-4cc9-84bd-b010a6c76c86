import React, { useState, useEffect } from 'react';
import {
  RefreshCw,
  ArrowUp,
  ArrowDown,
  Server,
  Eye,
  AlertTriangle,
  CheckCircle,
  Filter,
  Search,
  Clock,
  AlertCircle,
  Globe,
  Settings
} from 'lucide-react';
import ServiceDetailModal from '../components/ServiceDetailModal';
import { API_URL } from '../config';

const ServicesTab = ({ userId, navigateTo }) => {
  // Helper function for consistent price formatting
  const formatPrice = (price) => {
    if (!price && price !== 0) return '—';
    // Remove any existing € symbol and trim whitespace
    const cleanPrice = String(price).replace('€', '').trim();
    // Add € symbol
    return `€${cleanPrice}`;
  };

  // State for services data
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [serverLabels, setServerLabels] = useState({});
  const [cpuLabels, setCpuLabels] = useState({});

  // State for service modal
  const [selectedServiceId, setSelectedServiceId] = useState(null);
  const [serviceModalOpen, setServiceModalOpen] = useState(false);

  // Sorting and filtering state
  const [sortField, setSortField] = useState('created');
  const [sortDirection, setSortDirection] = useState('desc'); // Default to newest first
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [typeFilter, setTypeFilter] = useState('All');

  // Stats
  const [stats, setStats] = useState({
    totalServices: 0,
    activeServices: 0,
    pendingServices: 0,
    suspendedServices: 0
  });

  // API Base URL
  const API_BASE_URL = API_URL;

  // Function to fetch CPU labels
  const fetchCpuLabels = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_BASE_URL}/api_admin_inventory.php?f=get_dedicated_cpus`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        // Create a mapping of CPU IDs to CPU labels
        const cpuMapping = {};
        data.forEach(cpu => {
          cpuMapping[cpu.id] = cpu.cpu;
        });
        setCpuLabels(cpuMapping);
        console.log('CPU labels loaded:', cpuMapping);
      }
    } catch (err) {
      console.error("Error fetching CPU labels:", err);
    }
  };

  // Fetch services on component mount or when userId changes
  useEffect(() => {
    if (userId) {
      console.log('ServicesTab: Fetching services for user ID:', userId);
      fetchServices();
      fetchCpuLabels(); // Fetch CPU labels when component mounts
    } else {
      console.log('ServicesTab: No user ID provided');
    }
  }, [userId]);

  // Fetch services data directly from the database
  const fetchServices = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get token from localStorage
      const token = localStorage.getItem('admin_token');
      console.log('ServicesTab: Using token:', token ? 'Token found' : 'No token found');

      if (!token) {
        setError("Authentication token not found. Please log in again.");
        setLoading(false);
        setServices([]);
        return;
      }

      if (!userId) {
        setError("No user ID provided. Please select a valid user account.");
        setLoading(false);
        setServices([]);
        return;
      }

      console.log('ServicesTab: Fetching services for user ID:', userId);

      // Create the request to fetch services from the database
      const adminToken = token;

      console.log('ServicesTab: Making API request with token and user_id:', { token: adminToken ? 'Token provided' : 'No token', user_id: userId });

      const response = await fetch(`${API_URL}/api_admin_orders.php?f=get_user_services`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: adminToken,
          user_id: userId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      // Get the response data
      const responseText = await response.text();
      console.log('ServicesTab: Raw API response:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log('ServicesTab: Parsed services data:', data);
      } catch (parseError) {
        console.error('ServicesTab: Failed to parse JSON response:', parseError);
        throw new Error(`Invalid JSON response from server: ${responseText}`);
      }

      if (!data) {
        setError("No data received from server. Please try again later.");
        setServices([]);
        setStats({
          totalServices: 0,
          activeServices: 0,
          pendingServices: 0,
          suspendedServices: 0
        });
        setLoading(false);
        return;
      }

      // Check if the response indicates an error
      if (data.error || data.success === false) {
        const errorMessage = data.message || data.error || 'Unknown error';
        console.error('ServicesTab: API returned error:', errorMessage);
        setError(`Error: ${errorMessage}. Please try again later.`);
        setServices([]);
        setStats({
          totalServices: 0,
          activeServices: 0,
          pendingServices: 0,
          suspendedServices: 0
        });
        setLoading(false);
        return;
      }

      // Check if the data is empty
      if (Array.isArray(data) && data.length === 0) {
        setError("No services found for this user. Please create an order to see services here.");
        setServices([]);
        setStats({
          totalServices: 0,
          activeServices: 0,
          pendingServices: 0,
          suspendedServices: 0
        });
        setLoading(false);
        return;
      }

      // Process the services data from the database
      const processedServices = Array.isArray(data) ? data.map(service => {
        // Get the service details from the database fields
        return {
          // Basic service information
          id: service.id || 0,
          user_id: service.user_id || userId,
          order_id: service.order_id || '',
          invoice_item_id: service.invoice_item_id || '',
          server_id: service.server_id || null,

          // Service configuration IDs directly from orders_items table
          cpu_id: service.cpu_id || '',
          storage_id: service.storage_id || '',
          bandwidth_id: service.bandwidth_id || '',
          location_id: service.location_id || '',
          subnet_id: service.subnet_id || '',
          additional_ips_id: service.additional_ips_id || '',

          // Service details
          type: service.type || 'Dedicated Server',
          hostname: service.hostname || `srv${service.id}.example.com`,
          status: service.status || 'Active',

          // Display names for configuration items
          cpu: service.cpu_name || service.cpu || 'Not specified',
          storage: service.storage_name || service.storage || 'Not specified',
          bandwidth: service.bandwidth_name || service.bandwidth || 'Not specified',
          location: service.location_name || service.location || 'Not specified',

          // Dates from orders_items table
          created: service.created_at || service.date_ordered || new Date().toISOString(),
          date_ordered: service.date_ordered || service.created_at || '',
          due_date: service.due_date || '',
          expiry_date: service.expiry_date || service.due_date || '',

          // Pricing from orders_items table
          order_price: service.order_price || 0,
          requirement_price: service.requirement_price || service.order_price || 0,
          price: formatPrice(service.order_price || 0),
          recurring_price: formatPrice(service.requirement_price || service.order_price || 0),
          payment_period: service.payment_period || 'monthly',

          // Important: Add the database_id for consistent API call handling
          database_id: service.id || 0,
          item_id: service.id || 0,

          // Additional information
          description: service.description || `Server with ${service.cpu_name || 'CPU'}, ${service.storage_name || 'Storage'}, ${service.bandwidth_name || 'Bandwidth'}`
        };
      }) : [];

      console.log('ServicesTab: Processed services:', processedServices);
      setServices(processedServices);

      // Calculate stats
      const activeServices = processedServices.filter(service =>
        service.status === 'Active' || service.status === 'Completed').length;
      const pendingServices = processedServices.filter(service =>
        service.status === 'Pending').length;
      const suspendedServices = processedServices.filter(service =>
        service.status === 'Suspended' || service.status === 'Cancelled').length;

      setStats({
        totalServices: processedServices.length,
        activeServices,
        pendingServices,
        suspendedServices
      });

      // Fetch server labels for services with server_id
      await fetchServerLabels(processedServices);

    } catch (err) {
      console.error("ServicesTab: Error fetching services:", err);

      // No more mock data - just show the error
      setError(`Error fetching services: ${err.message}. Please try again later.`);
      setServices([]);
      setStats({
        totalServices: 0,
        activeServices: 0,
        pendingServices: 0,
        suspendedServices: 0
      });
    } finally {
      setLoading(false);
    }
  };

  // Helper function to fetch server labels for services with server_id
  const fetchServerLabels = async (services) => {
    const labelsToFetch = {};

    // Collect unique server IDs that we need to fetch labels for
    services.forEach(service => {
      if (service.server_id && !serverLabels[service.server_id]) {
        labelsToFetch[service.server_id] = true;
      }
    });

    const serverIdsToFetch = Object.keys(labelsToFetch);

    if (serverIdsToFetch.length === 0) {
      return; // No labels to fetch
    }

    console.log('Fetching server labels for IDs:', serverIdsToFetch);

    const newLabels = { ...serverLabels };

    // Fetch each server's details
    for (const serverId of serverIdsToFetch) {
      try {
        const serverType = 'dedicated'; // Default to dedicated, could be determined by service.type
        const serverData = await fetchServerByIdDirectly(serverId, serverType);

        if (serverData && serverData.label) {
          newLabels[serverId] = serverData.label;
        } else {
          newLabels[serverId] = `Server #${serverId}`;
        }
      } catch (error) {
        console.error(`Error fetching server label for ID ${serverId}:`, error);
        newLabels[serverId] = `Server #${serverId}`;
      }
    }

    setServerLabels(newLabels);
  };

  // Helper function to fetch server details directly
  const fetchServerByIdDirectly = async (serverId, serverType = 'dedicated') => {
    if (!serverId) {
      console.log('No server ID provided, skipping server fetch');
      return null;
    }

    console.log(`Fetching ${serverType} server with ID: ${serverId}`);

    let apiEndpoint = '';
    let requestBody = {
      token: localStorage.getItem('admin_token')
    };

    if (serverType === 'dedicated') {
      apiEndpoint = 'get_inventory_dedicated';
      requestBody.filter = { id: serverId };
    } else if (serverType === 'blade') {
      apiEndpoint = 'get_blade_server_inventory_single';
      requestBody.server_id = serverId;
    } else {
      // Default to dedicated if type is unknown
      apiEndpoint = 'get_inventory_dedicated';
      requestBody.filter = { id: serverId };
    }

    const apiUrl = `${API_BASE_URL}/api_admin_inventory.php?f=${apiEndpoint}`;
    console.log(`Server fetch API URL: ${apiUrl}`);
    console.log('Server fetch request body:', JSON.stringify(requestBody));

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status} fetching server details`);
      }

      const responseText = await response.text();
      console.log('Server fetch raw API response:', responseText);

      if (!responseText || responseText.trim() === '') {
        throw new Error('Server request returned an empty response');
      }

      const data = JSON.parse(responseText);
      console.log('Parsed server data:', data);

      // Handle array response from dedicated inventory
      if (apiEndpoint === 'get_inventory_dedicated' && Array.isArray(data)) {
        const server = data.find(s => s.id == serverId);
        if (server) {
          console.log(`Found server with ID ${serverId} in inventory response:`, server);
          return server;
        } else {
          throw new Error(`Server with ID ${serverId} not found in inventory response`);
        }
      }

      return data;
    } catch (error) {
      console.error(`Error fetching server details:`, error);
      throw error;
    }
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle status filter
  const handleStatusFilter = (e) => {
    setStatusFilter(e.target.value);
  };

  // Handle type filter
  const handleTypeFilter = (e) => {
    setTypeFilter(e.target.value);
  };

  // Handle sort
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Handle service click - open service detail modal
  const handleServiceClick = (service) => {
    console.log('Opening service detail modal for service:', service);
    setSelectedServiceId(service.id);
    setServiceModalOpen(true);
  };

  // Close service detail modal
  const handleCloseServiceModal = () => {
    setServiceModalOpen(false);
    setSelectedServiceId(null);
  };

  // Get sort icon
  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <ArrowUp className="w-4 h-4 inline ml-1" /> :
      <ArrowDown className="w-4 h-4 inline ml-1" />;
  };

  // Render status badge
  const renderStatusBadge = (status) => {
    const badgeClasses = {
      'Active': 'bg-green-100 text-green-800',
      'Pending': 'bg-yellow-100 text-yellow-800',
      'Suspended': 'bg-red-100 text-red-800',
      'Cancelled': 'bg-gray-100 text-gray-800'
    };

    const icons = {
      'Active': <CheckCircle className="w-4 h-4 mr-1" />,
      'Pending': <Clock className="w-4 h-4 mr-1" />,
      'Suspended': <AlertTriangle className="w-4 h-4 mr-1" />,
      'Cancelled': <AlertCircle className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertTriangle className="w-4 h-4 mr-1" />}
        {status}
      </span>
    );
  };

  // Format date safely in DD/MM/YYYY format
  const formatDate = (dateString) => {
    try {
      if (!dateString) return 'N/A';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid date';

      // Format as DD/MM/YYYY
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Month is 0-indexed
      const year = date.getFullYear();

      return `${day}/${month}/${year}`;
    } catch (e) {
      console.error('Date formatting error:', e, dateString);
      return 'Invalid date';
    }
  };

  // Filter services
  const filteredServices = services.filter(service => {
    const matchesSearch = searchQuery.toLowerCase().trim() === '' || [
      service.cpu,
      service.hostname,
      service.description,
      service.location,
      service.ip_address,
      service.id?.toString()
    ].some(field => field && field.toString().toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = statusFilter === 'All' || service.status === statusFilter;
    const matchesType = typeFilter === 'All' || service.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  // Sort services
  const sortedServices = [...filteredServices].sort((a, b) => {
    let comparison = 0;

    if (sortField === 'cpu') {
      comparison = (a.cpu || '').localeCompare(b.cpu || '');
    } else if (sortField === 'hostname') {
      comparison = (a.hostname || '').localeCompare(b.hostname || '');
    } else if (sortField === 'type') {
      comparison = (a.type || '').localeCompare(b.type || '');
    } else if (sortField === 'status') {
      comparison = (a.status || '').localeCompare(b.status || '');
    } else if (sortField === 'location') {
      comparison = (a.location || '').localeCompare(b.location || '');
    } else if (sortField === 'created') {
      const dateA = new Date(a.created || 0);
      const dateB = new Date(b.created || 0);
      comparison = dateA - dateB;
    } else if (sortField === 'due_date') {
      const dateA = new Date(a.due_date || 0);
      const dateB = new Date(b.due_date || 0);
      comparison = dateA - dateB;
    } else if (sortField === 'expiry_date') {
      const dateA = new Date(a.expiry_date || 0);
      const dateB = new Date(b.expiry_date || 0);
      comparison = dateA - dateB;
    } else {
      comparison = (a.id || 0) - (b.id || 0);
    }

    return sortDirection === 'asc' ? comparison : -comparison;
  });

  // Generate service stats cards
  const generateServiceStatsCards = () => {
    const statsCards = [
      {
        title: 'Total Services',
        value: stats.totalServices,
        icon: <Server className="text-indigo-700" size={36} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-info'
      },
      {
        title: 'Active Services',
        value: stats.activeServices,
        icon: <CheckCircle className="text-success" size={36} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-success'
      },
      {
        title: 'Pending Services',
        value: stats.pendingServices,
        icon: <Clock className="text-warning" size={36} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-warning'
      },
      {
        title: 'Suspended Services',
        value: stats.suspendedServices,
        icon: <AlertTriangle className="text-danger" size={36} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-danger'
      }
    ];

    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
        {statsCards.map((stat, index) => (
          <div
            key={index}
            className="bg-white border rounded p-4 flex items-center justify-between"
          >
            <div>
              <div className="text-xs text-gray-600">{stat.title}</div>
              <div className="text-lg font-bold mt-1">{stat.value}</div>
            </div>
            <div className={`card-custom-icon ${stat.iconClass}`}>
              {stat.icon}
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Service Stats Cards */}
      {generateServiceStatsCards()}

      {/* Services Table */}
      <div className="bg-white border rounded-md">
        <div className="p-3 border-b flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
          <div className="flex flex-wrap gap-2">
            <div className="relative">
              <select
                className="pl-3 pr-8 py-1.5 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                value={statusFilter}
                onChange={handleStatusFilter}
              >
                <option value="All">All Status</option>
                <option value="Active">Active</option>
                <option value="Pending">Pending</option>
                <option value="Suspended">Suspended</option>
                <option value="Cancelled">Cancelled</option>
              </select>
              <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none search-icon-position" />
            </div>
            <div className="relative">
              <select
                className="pl-3 pr-8 py-1.5 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                value={typeFilter}
                onChange={handleTypeFilter}
              >
                <option value="All">All Types</option>
                <option value="Dedicated Server">Dedicated Server</option>
                <option value="VPS">VPS</option>
                <option value="Cloud Storage">Cloud Storage</option>
                <option value="Hosting">Hosting</option>
              </select>
              <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none search-icon-position" />
            </div>
            <button
              onClick={() => {
                fetchServices();
                fetchCpuLabels();
              }}
              className="p-2 border rounded-md text-gray-500 hover:bg-gray-50"
              title="Refresh services"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </button>
          </div>
          <div className="relative w-full sm:w-64">
            <input
              type="text"
              placeholder="Search services..."
              className="pl-8 pr-3 py-1.5 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200"
              value={searchQuery}
              onChange={handleSearch}
            />
            <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 search-icon-position" />
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full table-fixed">
            <thead>
              <tr className="text-gray-500 text-xs border-b">
                <th className="p-2 text-left font-medium cursor-pointer w-1/6" onClick={() => handleSort('cpu')}>
                  CPU {getSortIcon('cpu')}
                </th>
                <th className="p-2 text-left font-medium cursor-pointer w-1/6 hide-xs" onClick={() => handleSort('hostname')}>
                  HOSTNAME {getSortIcon('hostname')}
                </th>
                <th className="p-2 text-left font-medium cursor-pointer w-1/12 hide-sm" onClick={() => handleSort('location')}>
                  LOCATION {getSortIcon('location')}
                </th>
                <th className="p-2 text-left font-medium cursor-pointer w-1/12 hide-md" onClick={() => handleSort('due_date')}>
                  DUE DATE {getSortIcon('due_date')}
                </th>
                <th className="p-2 text-left font-medium cursor-pointer w-1/12 hide-lg" onClick={() => handleSort('expiry_date')}>
                  EXPIRY {getSortIcon('expiry_date')}
                </th>
                <th className="p-2 text-left font-medium cursor-pointer w-1/12" onClick={() => handleSort('status')}>
                  STATUS {getSortIcon('status')}
                </th>
                <th className="p-2 text-left font-medium w-1/12">ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                Array(3).fill(0).map((_, index) => (
                  <tr key={index} className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                    <td className="p-2"><div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div></td>
                    <td className="p-2 hide-xs"><div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div></td>
                    <td className="p-2 hide-sm"><div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div></td>
                    <td className="p-2 hide-md"><div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div></td>
                    <td className="p-2 hide-lg"><div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div></td>
                    <td className="p-2"><div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div></td>
                    <td className="p-2"><div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div></td>
                  </tr>
                ))
              ) : error ? (
                <tr>
                  <td colSpan="7" className="p-2 text-center text-red-600">
                    {error}
                  </td>
                </tr>
              ) : sortedServices.length > 0 ? (
                sortedServices.map((service, index) => (
                  <tr
                    key={service.id}
                    className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-gray-50 cursor-pointer`}
                    onClick={() => handleServiceClick(service)}
                  >
                    <td className="p-2 font-medium text-indigo-700 truncate" title={cpuLabels[service.cpu_id] || service.cpu || 'CPU information not available'}>{cpuLabels[service.cpu_id] || service.cpu || 'CPU information not available'}</td>
                    <td className="p-2 text-gray-700 hide-xs truncate">
                      <div className="flex items-center">
                        <Server className="w-4 h-4 mr-1 text-gray-500 flex-shrink-0" />
                        <span className="truncate" title={service.hostname || `srv${service.id}.example.com`}>
                          {service.hostname || `srv${service.id}.example.com`}
                        </span>
                      </div>
                    </td>
                    <td className="p-2 text-gray-700 hide-sm truncate">
                      <div className="flex items-center">
                        <Globe className="w-4 h-4 mr-1 text-gray-500 flex-shrink-0" />
                        <span className="truncate" title={service.location}>{service.location}</span>
                      </div>
                    </td>
                    <td className="p-2 text-gray-700 hide-md truncate" title={formatDate(service.due_date)}>{formatDate(service.due_date)}</td>
                    <td className="p-2 text-gray-700 hide-lg truncate" title={formatDate(service.expiry_date)}>{formatDate(service.expiry_date)}</td>
                    <td className="p-2">{renderStatusBadge(service.status)}</td>
                    <td className="p-2" >
                      <div className="flex space-x-1">
                        <button
                          className="p-1 text-gray-400 hover:text-indigo-600"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleServiceClick(service);
                          }}
                        >
                          <Eye className="w-4 h-4"/>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="7" className="p-2 text-center text-gray-500">
                    No services found for this user. Please create an order to see services here.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Table Footer */}
        <div className="p-3 border-t flex justify-between items-center">
          <div className="text-xs text-gray-500">
            Showing {sortedServices.length} of {services.length} services
          </div>
          {sortedServices.length > 10 && (
            <div className="flex space-x-1">
              <button className="px-2 py-1 border rounded bg-indigo-700 text-white">1</button>
              <button className="px-2 py-1 border rounded bg-white text-gray-700 hover:bg-gray-50">2</button>
            </div>
          )}
        </div>
      </div>

      {/* Service Detail Modal */}
      <ServiceDetailModal
        isOpen={serviceModalOpen}
        onClose={handleCloseServiceModal}
        serviceId={selectedServiceId}
        API_BASE_URL={API_BASE_URL}
      />
    </div>
  );
};

export default ServicesTab;