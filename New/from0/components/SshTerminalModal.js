import React, { useEffect, useRef, useState } from 'react';
import { Terminal } from 'xterm';
import 'xterm/css/xterm.css';
import { XCircle, RefreshCw } from 'lucide-react';
import { FitAddon } from '@xterm/addon-fit';
import { getSshWebsocketUrl } from '../config.js';

/**
 * Modal component that opens an in-browser SSH terminal connected to the
 * selected switch. It relies on a websocket backend (see ssh_server.js) that
 * proxies the SSH session.
 */
const SshTerminalModal = ({ switchIp = '', onClose }) => {
  const termContainerRef = useRef(null);
  const termRef = useRef(null);
  const socketRef = useRef(null);
  const fitAddonRef = useRef(null);
  const isMountedRef = useRef(false);
  const isTerminalReadyRef = useRef(false);

  const [hostname, setHostname] = useState(switchIp);
  const [username, setUsername] = useState('root');
  const [password, setPassword] = useState('');
  const [connecting, setConnecting] = useState(false);
  const [connected, setConnected] = useState(false);

  // More robust terminal ready check
  const isTerminalReady = () => {
    if (!isMountedRef.current || !termRef.current || !fitAddonRef.current) {
      return false;
    }

    // Check if terminal container has dimensions
    if (!termContainerRef.current || 
        termContainerRef.current.offsetWidth === 0 || 
        termContainerRef.current.offsetHeight === 0) {
      return false;
    }

    // Check if terminal has been opened and has proper internal structure
    try {
      const terminal = termRef.current;
      return terminal.element && 
             terminal.element.offsetParent !== null &&
             terminal._core &&
             terminal._core._renderService &&
             terminal._core._renderService._renderer &&
             terminal._core._renderService._renderer.value &&
             terminal._core._renderService._renderer.value.dimensions;
    } catch (e) {
      return false;
    }
  };

  // Safe fit function with comprehensive checks
  const safeFit = () => {
    if (!isTerminalReady()) {
      return false;
    }

    try {
      fitAddonRef.current.fit();
      return true;
    } catch (error) {
      console.debug('Terminal fit operation failed:', error.message);
      return false;
    }
  };

  // Wait for terminal to be ready with exponential backoff
  const waitForTerminalAndFit = (maxAttempts = 10, attempt = 0) => {
    if (!isMountedRef.current || attempt >= maxAttempts) {
      return;
    }

    if (safeFit()) {
      isTerminalReadyRef.current = true;
      return;
    }

    // Exponential backoff: 50ms, 100ms, 200ms, 400ms, etc.
    const delay = Math.min(50 * Math.pow(2, attempt), 1000);
    setTimeout(() => {
      waitForTerminalAndFit(maxAttempts, attempt + 1);
    }, delay);
  };

  // Initialise xterm once on mount
  useEffect(() => {
    isMountedRef.current = true;
    isTerminalReadyRef.current = false;
    
    if (!termContainerRef.current) {
      return;
    }

    termRef.current = new Terminal({
      cursorBlink: true,
      fontSize: 14
    });

    // Fit addon to auto-resize terminal
    fitAddonRef.current = new FitAddon();
    termRef.current.loadAddon(fitAddonRef.current);

    // Wait for the container to be ready before opening terminal
    const openTerminal = () => {
      if (!isMountedRef.current || !termContainerRef.current) {
        return;
      }

      termRef.current.open(termContainerRef.current);
      termRef.current.options.theme = { background: '#1e1e1e' };
      
      // Use terminal's onRender event to know when it's safe to fit
      const disposeOnRender = termRef.current.onRender(() => {
        if (!isTerminalReadyRef.current && isMountedRef.current) {
          // First render, try to fit
          setTimeout(() => {
            waitForTerminalAndFit();
          }, 50);
        }
      });

      // Store dispose function for cleanup
      termRef.current._disposeOnRender = disposeOnRender;

      termRef.current.focus();
    };

    // Defer terminal opening to next tick to ensure container is ready
    setTimeout(openTerminal, 0);

    const handleResize = (() => {
      let resizeTimeout;
      return () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          if (isTerminalReadyRef.current) {
            safeFit();
          } else {
            waitForTerminalAndFit();
          }
        }, 150);
      };
    })();

    window.addEventListener('resize', handleResize);

    return () => {
      // Remove listeners first
      window.removeEventListener('resize', handleResize);

      // Dispose onRender listener
      if (termRef.current && termRef.current._disposeOnRender) {
        termRef.current._disposeOnRender.dispose();
      }

      // Close websocket connection
      if (socketRef.current) {
        socketRef.current.close();
        socketRef.current = null;
      }

      // Dispose terminal
      if (termRef.current) {
        termRef.current.dispose();
        termRef.current = null;
      }
      
      // Clear references
      fitAddonRef.current = null;
      isTerminalReadyRef.current = false;
      
      // Flag unmounted
      isMountedRef.current = false;
    };
  }, []);

  // Connect to websocket backend and wire streams
  const handleConnect = () => {
    if (!hostname) {
      alert('Hostname/IP is required');
      return;
    }

    setConnecting(true);

    // Use configured SSH websocket URL
    const baseSocketUrl = getSshWebsocketUrl();
    const socketUrl = `${baseSocketUrl}/?host=${encodeURIComponent(
      hostname
    )}&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`;

    console.log('Attempting websocket connection to:', socketUrl);

    const ws = new WebSocket(socketUrl);
    socketRef.current = ws;

    ws.binaryType = 'arraybuffer';

    ws.onopen = () => {
      if (!isMountedRef.current) return;
      setConnecting(false);
      setConnected(true);
      termRef.current?.writeln('\r\n*** Connected ***\r\n');
      // Ensure terminal is properly fitted after connection
      setTimeout(() => {
        if (isTerminalReadyRef.current) {
          safeFit();
        } else {
          waitForTerminalAndFit();
        }
      }, 100);
    };

    ws.onmessage = (event) => {
      if (!isMountedRef.current || !termRef.current) return;
      if (event.data instanceof ArrayBuffer) {
        termRef.current.write(new Uint8Array(event.data));
      } else {
        termRef.current.write(event.data);
      }
    };

    ws.onclose = () => {
      if (!isMountedRef.current) return;
      setConnected(false);
      termRef.current?.writeln('\r\n*** Session closed ***');
    };

    ws.onerror = () => {
      if (!isMountedRef.current) return;
      setConnecting(false);
      termRef.current?.writeln('\r\n*** Connection error ***');
    };

    // Forward terminal input to websocket
    termRef.current.onData((data) => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(data);
      }
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="p-3 border-b flex items-center justify-between">
          <h3 className="font-bold text-lg">SSH Terminal</h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <XCircle className="w-5 h-5" />
          </button>
        </div>

        {/* Connection form (only visible until connected) */}
        {!connected && (
          <div className="p-4 space-y-3">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700">Host/IP</label>
                <input
                  type="text"
                  value={hostname}
                  onChange={(e) => setHostname(e.target.value)}
                  className="mt-1 block w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                  placeholder="************"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Username</label>
                <input
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="mt-1 block w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                  placeholder="root"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Password</label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="mt-1 block w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>
            <div className="flex justify-end">
              <button
                onClick={handleConnect}
                disabled={connecting}
                className={`px-4 py-1.5 rounded-md text-white flex items-center text-sm ${
                  connecting ? 'bg-indigo-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'
                }`}
              >
                {connecting && <RefreshCw className="w-4 h-4 mr-1.5 animate-spin" />}
                {connecting ? 'Connecting...' : 'Connect'}
              </button>
            </div>
          </div>
        )}

        {/* Terminal */}
        <div className="flex-grow bg-black" ref={termContainerRef} />
      </div>
    </div>
  );
};

export default SshTerminalModal; 