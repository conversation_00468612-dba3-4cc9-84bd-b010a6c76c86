import React, { useState, useEffect } from 'react';
import {
  X,
  RefreshCw,
  Server,
  Network,
  Globe,
  AlertTriangle,
  CheckCircle,
  Settings,
  Wifi,
  Router,
  Database
} from 'lucide-react';
import { API_URL } from '../config';

const PXENetworkConfigModal = ({ 
  isOpen, 
  onClose, 
  server, 
  serverType, 
  osTemplate,
  onConfigComplete 
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [step, setStep] = useState(1); // 1: Network Config, 2: DHCP Setup, 3: PXE Config, 4: Complete
  const [networkConfig, setNetworkConfig] = useState({
    ip_address: '',
    subnet_mask: '*************',
    gateway: '',
    dns_primary: '*******',
    dns_secondary: '*******',
    hostname: ''
  });
  const [dhcpConfig, setDhcpConfig] = useState({
    dhcp_server: '',
    tftp_server: '',
    pxe_filename: 'pxelinux.0',
    next_server: ''
  });
  const [pxeProgress, setPxeProgress] = useState('');

  // Auto-populate network configuration based on server's current IP
  useEffect(() => {
    if (isOpen && server) {
      const hostname = server.label ? server.label.toLowerCase().replace(/[^a-z0-9-]/g, '-') : `server-${server.id}`;
      
      setNetworkConfig(prev => ({
        ...prev,
        ip_address: server.main_ip || '',
        hostname: hostname
      }));

      // Try to auto-detect gateway from main IP
      if (server.main_ip && server.main_ip.includes('/')) {
        const [networkIp] = server.main_ip.split('/');
        const ipParts = networkIp.split('.');
        if (ipParts.length === 4) {
          const gateway = `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}.1`;
          setNetworkConfig(prev => ({
            ...prev,
            gateway: gateway
          }));
        }
      }
    }
  }, [isOpen, server]);

  const handleNetworkConfigChange = (field, value) => {
    setNetworkConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDhcpConfigChange = (field, value) => {
    setDhcpConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateNetworkConfig = () => {
    const required = ['ip_address', 'subnet_mask', 'gateway', 'hostname'];
    const missing = required.filter(field => !networkConfig[field]);
    
    if (missing.length > 0) {
      setError(`Please fill in: ${missing.join(', ')}`);
      return false;
    }

    // Validate IP format
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!ipRegex.test(networkConfig.ip_address)) {
      setError('Invalid IP address format');
      return false;
    }

    return true;
  };

  const setupDHCPReservation = async () => {
    try {
      setLoading(true);
      setError('');
      setPxeProgress('Setting up DHCP reservation...');

      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=setup_pxe_dhcp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          server_id: server.id,
          server_type: serverType,
          mac_address: server.mac,
          network_config: networkConfig,
          dhcp_config: dhcpConfig,
          os_template: osTemplate
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setPxeProgress('DHCP reservation created successfully');
        setStep(3);
      } else {
        throw new Error(result.error || 'DHCP setup failed');
      }

    } catch (err) {
      console.error('Error setting up DHCP:', err);
      setError(`DHCP setup failed: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const setupPXEServer = async () => {
    try {
      setLoading(true);
      setError('');
      setPxeProgress('Configuring PXE server...');

      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=setup_pxe_server`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          server_id: server.id,
          server_type: serverType,
          mac_address: server.mac,
          network_config: networkConfig,
          dhcp_config: dhcpConfig,
          os_template: osTemplate
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setPxeProgress('PXE server configuration complete');
        setStep(4);
        
        // Complete the setup
        setTimeout(() => {
          if (onConfigComplete) {
            onConfigComplete(networkConfig, dhcpConfig);
          }
        }, 1500);
      } else {
        throw new Error(result.error || 'PXE server setup failed');
      }

    } catch (err) {
      console.error('Error setting up PXE server:', err);
      setError(`PXE server setup failed: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    if (step === 1) {
      if (!validateNetworkConfig()) return;
      setStep(2);
    } else if (step === 2) {
      setupDHCPReservation();
    } else if (step === 3) {
      setupPXEServer();
    }
  };

  const handleClose = () => {
    setStep(1);
    setError('');
    setPxeProgress('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-lg mx-4">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b">
          <h3 className="text-lg font-bold flex items-center">
            <Network className="w-5 h-5 mr-2 text-indigo-700" />
            PXE Network Configuration
          </h3>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
            disabled={loading}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Progress Steps */}
          <div className="mb-4">
            <div className="flex items-center justify-between">
              <div className={`flex items-center ${step >= 1 ? 'text-indigo-600' : 'text-gray-400'}`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
                  step >= 1 ? 'bg-indigo-600 text-white' : 'bg-gray-300 text-gray-600'
                }`}>1</div>
                <span className="ml-2 text-xs">Network</span>
              </div>
              <div className={`flex items-center ${step >= 2 ? 'text-indigo-600' : 'text-gray-400'}`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
                  step >= 2 ? 'bg-indigo-600 text-white' : 'bg-gray-300 text-gray-600'
                }`}>2</div>
                <span className="ml-2 text-xs">DHCP</span>
              </div>
              <div className={`flex items-center ${step >= 3 ? 'text-indigo-600' : 'text-gray-400'}`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
                  step >= 3 ? 'bg-indigo-600 text-white' : 'bg-gray-300 text-gray-600'
                }`}>3</div>
                <span className="ml-2 text-xs">PXE</span>
              </div>
              <div className={`flex items-center ${step >= 4 ? 'text-indigo-600' : 'text-gray-400'}`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
                  step >= 4 ? 'bg-green-600 text-white' : 'bg-gray-300 text-gray-600'
                }`}>4</div>
                <span className="ml-2 text-xs">Complete</span>
              </div>
            </div>
          </div>

          {/* Server Info */}
          <div className="mb-4 p-3 bg-gray-50 rounded-md">
            <div className="flex items-center mb-2">
              <Server className="w-4 h-4 mr-2 text-indigo-700" />
              <span className="font-medium">{server?.label || `Server ${server?.id}`}</span>
            </div>
            <div className="text-sm text-gray-600">
              <div>MAC: {server?.mac || 'Not configured'}</div>
              <div>OS Template: {osTemplate}</div>
            </div>
          </div>

          {/* Step 1: Network Configuration */}
          {step === 1 && (
            <div>
              <h4 className="font-medium mb-3">Network Configuration</h4>
              
              <div className="grid grid-cols-2 gap-3 mb-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    IP Address <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={networkConfig.ip_address}
                    onChange={(e) => handleNetworkConfigChange('ip_address', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    placeholder="***********00"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subnet Mask <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={networkConfig.subnet_mask}
                    onChange={(e) => handleNetworkConfigChange('subnet_mask', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3 mb-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Gateway <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={networkConfig.gateway}
                    onChange={(e) => handleNetworkConfigChange('gateway', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    placeholder="***********"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Hostname <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={networkConfig.hostname}
                    onChange={(e) => handleNetworkConfigChange('hostname', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    placeholder="server-01"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3 mb-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Primary DNS
                  </label>
                  <input
                    type="text"
                    value={networkConfig.dns_primary}
                    onChange={(e) => handleNetworkConfigChange('dns_primary', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Secondary DNS
                  </label>
                  <input
                    type="text"
                    value={networkConfig.dns_secondary}
                    onChange={(e) => handleNetworkConfigChange('dns_secondary', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 2: DHCP Configuration */}
          {step === 2 && (
            <div>
              <h4 className="font-medium mb-3">DHCP Server Configuration</h4>
              
              <div className="mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  DHCP Server IP
                </label>
                <input
                  type="text"
                  value={dhcpConfig.dhcp_server}
                  onChange={(e) => handleDhcpConfigChange('dhcp_server', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                  placeholder="*********** (auto-detected if empty)"
                />
              </div>

              <div className="grid grid-cols-2 gap-3 mb-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    TFTP Server
                  </label>
                  <input
                    type="text"
                    value={dhcpConfig.tftp_server}
                    onChange={(e) => handleDhcpConfigChange('tftp_server', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    placeholder="***********0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Next Server
                  </label>
                  <input
                    type="text"
                    value={dhcpConfig.next_server}
                    onChange={(e) => handleDhcpConfigChange('next_server', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    placeholder="Same as TFTP if empty"
                  />
                </div>
              </div>

              <div className="mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  PXE Boot Filename
                </label>
                <input
                  type="text"
                  value={dhcpConfig.pxe_filename}
                  onChange={(e) => handleDhcpConfigChange('pxe_filename', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                />
              </div>

              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div className="text-sm text-blue-800">
                  <div className="font-medium mb-1">DHCP Reservation Details:</div>
                  <ul className="list-disc list-inside space-y-1">
                    <li>MAC Address: {server?.mac}</li>
                    <li>IP Address: {networkConfig.ip_address}</li>
                    <li>Hostname: {networkConfig.hostname}</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Step 3: PXE Server Setup */}
          {step === 3 && (
            <div>
              <h4 className="font-medium mb-3">PXE Server Configuration</h4>
              
              <div className="mb-4 flex items-center justify-center">
                <RefreshCw className="w-8 h-8 animate-spin text-indigo-600" />
              </div>
              
              <div className="text-center mb-4">
                <div className="font-medium text-gray-900">
                  {loading ? 'Configuring PXE Server...' : 'PXE Server Ready'}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {pxeProgress}
                </div>
              </div>

              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="text-sm text-yellow-800">
                  <div className="font-medium mb-1">PXE Configuration:</div>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Boot menu entry created for {networkConfig.hostname}</li>
                    <li>OS template: {osTemplate}</li>
                    <li>Kickstart/Preseed file generated</li>
                    <li>Network configuration embedded</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Step 4: Complete */}
          {step === 4 && (
            <div>
              <div className="mb-4 flex items-center justify-center">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              
              <div className="text-center mb-4">
                <div className="font-medium text-gray-900">
                  PXE Network Configuration Complete
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Server is ready for PXE boot installation
                </div>
              </div>

              <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                <div className="text-sm text-green-800">
                  <div className="font-medium mb-1">Configuration Summary:</div>
                  <ul className="list-disc list-inside space-y-1">
                    <li>DHCP reservation: {networkConfig.ip_address}</li>
                    <li>Hostname: {networkConfig.hostname}</li>
                    <li>PXE boot menu configured</li>
                    <li>Installation files ready</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-start">
                <AlertTriangle className="w-4 h-4 mr-2 text-red-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-red-800">{error}</div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-4 border-t">
          {step < 4 && (
            <>
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                disabled={loading}
              >
                Cancel
              </button>
              
              {step > 1 && (
                <button
                  type="button"
                  onClick={() => setStep(step - 1)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                  disabled={loading}
                >
                  Back
                </button>
              )}
              
              <button
                type="button"
                onClick={handleNext}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm hover:bg-indigo-700 flex items-center"
                disabled={loading || (step === 1 && (!networkConfig.ip_address || !server?.mac))}
              >
                {loading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    {step === 2 ? 'Setting up DHCP...' : 'Configuring...'}
                  </>
                ) : (
                  <>
                    {step === 1 && 'Configure DHCP'}
                    {step === 2 && 'Setup PXE Server'}
                    {step === 3 && 'Complete Setup'}
                  </>
                )}
              </button>
            </>
          )}

          {step === 4 && (
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700"
            >
              Done
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PXENetworkConfigModal; 