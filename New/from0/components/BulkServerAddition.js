import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, <PERSON>Circle, Save, RefreshCw, Eye, EyeOff, Network, Wifi, CheckCircle,
  AlertTriangle, ChevronLeft, ChevronRight, Copy, Server, HardDrive, Cpu, Layout, Globe, Memory, Link, Search, ChevronDown } from 'lucide-react';
import SwitchPortsManager from './SwitchPortsManager'; // Import the new component
import DirectIpSelector from './DirectIpSelector'; // Import the new direct IP selector
import { API_URL } from '../config';

// SearchableIpDropdown component for IP address selection with search functionality
const SearchableIpDropdown = ({ 
  value, 
  onChange, 
  options = [], 
  placeholder = "Select an IP address",
  disabled = false,
  loading = false 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredOptions, setFilteredOptions] = useState(options);

  // Update filtered options when search term or options change
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredOptions(options);
    } else {
      const filtered = options.filter(option => {
        const searchLower = searchTerm.toLowerCase();
        return (
          option.ip_address.toLowerCase().includes(searchLower) ||
          (option.subnet_name && option.subnet_name.toLowerCase().includes(searchLower)) ||
          (option.subnet_cidr && option.subnet_cidr.toLowerCase().includes(searchLower)) ||
          (option.city && option.city.toLowerCase().includes(searchLower)) ||
          (option.country && option.country.toLowerCase().includes(searchLower))
        );
      });
      setFilteredOptions(filtered);
    }
  }, [searchTerm, options]);

  // Find selected option for display
  const selectedOption = options.find(option => option.ip_address === value);

  const handleSelect = (option) => {
    onChange(option.ip_address);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleInputChange = (e) => {
    setSearchTerm(e.target.value);
    if (!isOpen) {
      setIsOpen(true);
    }
  };

  const handleInputFocus = () => {
    setIsOpen(true);
  };

  const handleInputBlur = (e) => {
    // Delay closing to allow clicks on dropdown items
    setTimeout(() => {
      setIsOpen(false);
      setSearchTerm('');
    }, 200);
  };

  const handleDropdownToggle = () => {
    if (disabled || loading) return;
    setIsOpen(!isOpen);
    if (!isOpen) {
      setSearchTerm('');
    }
  };

  return (
    <div className="relative w-full">
      <div className="relative">
        <div 
          className={`w-full p-1.5 border ${
            disabled ? 'bg-gray-100 border-gray-200' : 'border-gray-300 hover:border-gray-400'
          } rounded-md text-sm cursor-pointer flex items-center justify-between`}
          onClick={handleDropdownToggle}
        >
          <div className="flex-1 min-w-0">
            {selectedOption ? (
              <span className="block truncate">
                {selectedOption.ip_address}
                {selectedOption.subnet_name && ` - ${selectedOption.subnet_name}`}
                {selectedOption.city && ` (${selectedOption.city}${selectedOption.country ? `, ${selectedOption.country}` : ''})`}
              </span>
            ) : (
              <span className="text-gray-500">{placeholder}</span>
            )}
          </div>
          {loading ? (
            <RefreshCw className="w-4 h-4 text-gray-400 animate-spin ml-2" />
          ) : (
            <ChevronDown className={`w-4 h-4 text-gray-400 ml-2 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          )}
        </div>

        {/* Dropdown */}
        {isOpen && !disabled && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-hidden">
            {/* Search input */}
            <div className="p-2 border-b border-gray-200">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={handleInputChange}
                  onFocus={handleInputFocus}
                  onBlur={handleInputBlur}
                  placeholder="Search IP addresses..."
                  className="w-full pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  autoFocus
                />
              </div>
            </div>

            {/* Options list */}
            <div className="max-h-40 overflow-y-auto">
              {filteredOptions.length === 0 ? (
                <div className="px-3 py-2 text-sm text-gray-500">
                  {searchTerm ? 'No IP addresses found matching your search' : 'No IP addresses available'}
                </div>
              ) : (
                filteredOptions.map((option, idx) => (
                  <div
                    key={`${option.ip_address}-${idx}`}
                    className={`px-3 py-2 text-sm cursor-pointer hover:bg-indigo-50 hover:text-indigo-700 ${
                      option.ip_address === value ? 'bg-indigo-100 text-indigo-700' : 'text-gray-900'
                    }`}
                    onMouseDown={(e) => {
                      e.preventDefault(); // Prevent blur from firing before click
                      handleSelect(option);
                    }}
                  >
                    <div className="flex flex-col">
                      <div className="font-medium">{option.ip_address}</div>
                      <div className="text-xs text-gray-500">
                        {option.subnet_name || option.subnet_cidr}
                        {option.city && ` • ${option.city}${option.country ? `, ${option.country}` : ''}`}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// BulkIpSelector component for selecting IPs in bulk
const BulkIpSelector = ({ index, value, onChange, cityId, deviceType, label, placeholder }) => {
  const [availableIps, setAvailableIps] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [availableSubnets, setAvailableSubnets] = useState([]);
  const [generatingIps, setGeneratingIps] = useState(false);
  const [generatingSubnetId, setGeneratingSubnetId] = useState(null);

  // Fetch available IPs when the component mounts or when cityId changes
  useEffect(() => {
    if (cityId) {
      fetchAvailableIps();
    }
  }, [cityId]);

  // Function to fetch available IPs for the selected city
  const fetchAvailableIps = async () => {
    if (!cityId) return;

    try {
      setLoading(true);
      setError('');

      console.log(`Fetching available IPs for city ID: ${cityId}`);

      const token = localStorage.getItem('admin_token');

      // Get city and country information
      const cityResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_cities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      const cities = await cityResponse.json();
      const city = cities.find(c => c.id == cityId);

      if (!city) {
        console.error(`City with ID ${cityId} not found`);
        setError(`City with ID ${cityId} not found`);
        setLoading(false);
        return;
      }

      const cityName = city.city;
      const countryId = city.country_id;

      // Get country name
      const countryResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_countries`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      const countries = await countryResponse.json();
      const country = countries.find(c => c.id == countryId);
      const countryName = country ? country.country : '';

      console.log(`City: ${cityName}, Country: ${countryName} (ID: ${countryId})`);

      // Get all subnets without filtering
      const response = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnets`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      const allSubnets = await response.json();

      if (!Array.isArray(allSubnets)) {
        console.error('Failed to fetch subnets');
        setError('Failed to load subnets');
        setLoading(false);
        return;
      }

      console.log(`Fetched ${allSubnets.length} total subnets`);

      // Log all unique city and country values in subnets for debugging
      const uniqueCities = new Set();
      const uniqueCountries = new Set();

      allSubnets.forEach(subnet => {
        if (subnet.city) uniqueCities.add(subnet.city);
        if (subnet.country) uniqueCountries.add(subnet.country);
      });

      console.log('Unique cities in subnets:', Array.from(uniqueCities));
      console.log('Unique countries in subnets:', Array.from(uniqueCountries));
      console.log('Looking for city:', cityName);
      console.log('Looking for country:', countryName);

      // Create a more flexible matching function
      const isLocationMatch = (subnet) => {
        // Normalize strings for comparison (lowercase, trim whitespace)
        const normalizeStr = (str) => str ? str.toLowerCase().trim() : '';

        const subnetCity = normalizeStr(subnet.city);
        const subnetCountry = normalizeStr(subnet.country);
        const targetCity = normalizeStr(cityName);
        const targetCountry = normalizeStr(countryName);

        // Check for exact matches
        if (subnetCity === targetCity) return true;
        if (subnetCountry === targetCountry) return true;

        // Check if city is stored in country field or vice versa
        if (subnetCity === targetCountry) return true;
        if (subnetCountry === targetCity) return true;

        // Check if city contains country or vice versa
        if (targetCity.includes(subnetCity) && subnetCity.length > 3) return true;
        if (targetCountry.includes(subnetCountry) && subnetCountry.length > 3) return true;

        // Check for partial matches (if the strings are long enough to avoid false positives)
        if (subnetCity.includes(targetCity) && targetCity.length > 3) return true;
        if (subnetCountry.includes(targetCountry) && targetCountry.length > 3) return true;

        return false;
      };

      // First filter for available subnets that match the location
      const availableSubnets = allSubnets.filter(subnet =>
        subnet.status === 'Available' && isLocationMatch(subnet)
      );

      console.log(`Found ${availableSubnets.length} available subnets matching location ${cityName}/${countryName}`);

      // Log detailed information about each available subnet
      availableSubnets.forEach((subnet, index) => {
        console.log(`Subnet ${index + 1}:`, {
          id: subnet.id,
          subnet: subnet.subnet,
          cidr: subnet.cidr,
          city: subnet.city,
          country: subnet.country,
          status: subnet.status
        });
      });

      // If no available subnets found, check for any subnets that match the location
      if (availableSubnets.length === 0) {
        const matchingSubnets = allSubnets.filter(subnet => isLocationMatch(subnet));

        console.log(`Found ${matchingSubnets.length} total subnets matching location ${cityName}/${countryName}`);

        if (matchingSubnets.length > 0) {
          // Group subnets by status for better error message
          const subnetsByStatus = {};
          matchingSubnets.forEach(subnet => {
            const status = subnet.status || 'Unknown';
            subnetsByStatus[status] = (subnetsByStatus[status] || 0) + 1;
          });

          const statusCounts = Object.entries(subnetsByStatus)
            .map(([status, count]) => `${count} ${status}`)
            .join(', ');

          setError(`Found ${matchingSubnets.length} subnets in ${cityName}/${countryName}, but none are marked as Available. Status counts: ${statusCounts}`);
        } else {
          setError(`No subnets found for ${cityName}/${countryName}. You may need to create subnets for this location.`);
        }
      }

      // Fetch IPs for each available subnet that matches the location
      const allIps = [];

      for (const subnet of availableSubnets) {
        try {
          // Extract subnet ID
          const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
            ? subnet.id.replace('SUB-', '')
            : subnet.id;

          console.log(`Fetching IPs for subnet ${subnet.subnet || subnet.cidr || 'Unnamed'} (ID: ${subnetId})`);

          const ipResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnet_ips`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              subnet_id: subnetId,
              token: token,
              include_used: true // Include used IPs in the response to get a complete picture
            })
          });

          const ipData = await ipResponse.json();

          if (ipData && ipData.success) {
            // Filter for available IPs (not used) or IPs that are already allocated to this item
            const availableSubnetIps = ipData.ips.filter(ip => {
              // Convert to string and then to number to handle both string and number types
              const isUsed = parseInt(String(ip.is_used), 10);

              // Include IPs that are not used or are the current value
              return isUsed === 0 || ip.ip_address === value;
            });

            console.log(`Found ${availableSubnetIps.length} available IPs in subnet ${subnet.subnet || subnet.cidr || 'Unnamed'} (ID: ${subnetId})`);

            // Add subnet info to each IP
            const ipsWithSubnetInfo = availableSubnetIps.map(ip => ({
              ...ip,
              subnet_cidr: ipData.subnet_cidr,
              subnet_id: subnet.id, // Store the original subnet ID format
              city: subnet.city,
              country: subnet.country,
              subnet_name: subnet.subnet
            }));

            allIps.push(...ipsWithSubnetInfo);
          } else {
            console.error(`Error getting IPs for subnet ${subnetId}:`, ipData);
          }
        } catch (err) {
          console.error(`Error fetching IPs for subnet ${subnet.id}:`, err);
        }
      }

      console.log(`Total available IPs found: ${allIps.length}`);

      // If we found subnets but no IPs, provide a specific error message
      if (availableSubnets.length > 0 && allIps.length === 0) {
        // Create a list of subnet information for the error message
        const subnetList = availableSubnets.map(subnet =>
          `${subnet.subnet || 'Unnamed subnet'} (ID: ${subnet.id})`
        ).join(', ');

        // Check if any of the subnets have IPs but they're all allocated
        let hasAllocatedIps = false;
        let totalIps = 0;
        let allocatedIps = 0;
        let subnetsWithNoIps = 0;

        for (const subnet of availableSubnets) {
          try {
            // Extract subnet ID
            const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
              ? subnet.id.replace('SUB-', '')
              : subnet.id;

            const ipResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnet_ips`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                subnet_id: subnetId,
                token: token,
                include_used: true, // Include used IPs in the response
                include_all: true   // Try to include all possible IPs in the subnet
              })
            });

            const ipData = await ipResponse.json();

            if (ipData && ipData.success && ipData.ips && ipData.ips.length > 0) {
              totalIps += ipData.ips.length;

              // Count allocated IPs
              const usedIps = ipData.ips.filter(ip => {
                const isUsed = parseInt(String(ip.is_used), 10);
                return isUsed === 1;
              });

              allocatedIps += usedIps.length;

              if (usedIps.length > 0) {
                hasAllocatedIps = true;
              }
            } else {
              subnetsWithNoIps++;
            }
          } catch (err) {
            console.error(`Error checking if subnet ${subnet.id} has allocated IPs:`, err);
            subnetsWithNoIps++;
          }
        }

        // Store subnet details for the UI
        const subnetDetails = availableSubnets.map(subnet => {
          const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
            ? subnet.id.replace('SUB-', '')
            : subnet.id;

          return {
            id: subnet.id,
            subnetId,
            name: subnet.subnet || subnet.cidr || 'Unnamed subnet',
            cidr: subnet.cidr
          };
        });

        // Create a structured error object and stringify it
        const errorObj = {
          type: 'subnet_details',
          subnets: subnetDetails,
          message: hasAllocatedIps
            ? `Found ${availableSubnets.length} available subnets with ${allocatedIps}/${totalIps} IPs already allocated. All IPs are currently in use. Please check the database for free IPs or contact an administrator.`
            : subnetsWithNoIps > 0
              ? `Found ${availableSubnets.length} available subnets but ${subnetsWithNoIps} of them have no IPs. Please check the database for free IPs or contact an administrator.`
              : `Found ${availableSubnets.length} available subnets but they have no available IPs. Please check the database for free IPs or contact an administrator.`
        };

        // Set the error as a JSON string
        setError(JSON.stringify(errorObj));
      }

      // Store the available subnets for later use
      setAvailableSubnets(availableSubnets);

      // If we have a current value but it's not in the available IPs, add it as a special option
      if (value && !allIps.some(ip => ip.ip_address === value)) {
        console.log(`Current IP ${value} not found in available IPs, adding it as a special option`);

        // Add the current IP as a special option
        allIps.push({
          ip_address: value,
          is_used: '1', // Mark as used
          subnet_cidr: "Current IP",
          subnet_id: 'current', // Special marker
          is_current: true, // Flag to identify this as the current IP
          city: cityName,
          country: countryName
        });
      }

      setAvailableIps(allIps);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching available IPs:', err);
      setError('Failed to load available IPs: ' + (err.message || 'Unknown error'));
      setLoading(false);
    }
  };

  // Function to check for available IPs in the database
  const generateIpsForSubnets = async () => {
    if (availableSubnets.length === 0) {
      setError('No available subnets found to check for IPs');
      return;
    }

    setGeneratingIps(true);
    setError('');

    try {
      const token = localStorage.getItem('admin_token');

      // Refresh the available IPs with a more thorough search
      await fetchAvailableIps();

      // If we still don't have any IPs, show a message
      if (availableIps.length === 0) {
        alert("No available IPs found in the database. Please contact an administrator to check the database directly.");
      } else {
        alert(`Found ${availableIps.length} available IPs in the database.`);
      }
    } catch (err) {
      console.error('Error checking for IPs:', err);
      setError('Failed to check for IPs: ' + (err.message || 'Unknown error'));
    } finally {
      setGeneratingIps(false);
    }
  };

  // Handle IP selection change
  const handleIpChange = (newValue) => {
    // Call the parent's onChange handler
    onChange(index, deviceType === 'server' ? 'ipmi' : 'switch_ip', newValue);
  };

  return (
    <div className="flex flex-col bulk-ip-selector">
      <div className="flex">
        <SearchableIpDropdown
          value={value || ''}
          onChange={handleIpChange}
          options={availableIps}
          placeholder={placeholder || 'Select an IP address'}
          disabled={loading || !cityId}
          loading={loading}
        />
        <button
          type="button"
          onClick={fetchAvailableIps}
          className={`ml-1 p-1 ${loading ? 'bg-gray-100 text-gray-400' : 'bg-indigo-100 text-indigo-700 hover:bg-indigo-200'} rounded-md flex items-center justify-center`}
          title="Refresh available IPs"
          disabled={loading || !cityId}
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {error && (
        <div className="text-xs mt-1">
          {(() => {
            try {
              // Try to parse the error as JSON
              const errorObj = JSON.parse(error);

              // Check if it's a subnet_details error
              if (errorObj && errorObj.type === 'subnet_details') {
                const subnetDetails = errorObj.subnets;
                const errorMessage = errorObj.message;

                return (
                  <div>
                    <div className="text-red-500 mb-2">{errorMessage}</div>

                    <div className="bg-amber-50 border border-amber-200 rounded-md p-2 mt-1">
                      <div className="font-medium text-amber-700 mb-1">Available Subnets:</div>
                      <div className="space-y-2">
                        {subnetDetails.map((subnet, idx) => (
                          <div key={idx} className="flex flex-col">
                            <div className="text-amber-700">
                              {subnet.name} {subnet.cidr && `(${subnet.cidr})`}
                            </div>
                            <div className="flex mt-1">
                              <div className="text-xs text-amber-700">
                                This subnet may have IPs in the database that are not showing up. Please contact an administrator to check the database.
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                );
              }

              // If it's not a subnet_details error, just display the message
              return <div className="text-red-500">{errorObj.message || JSON.stringify(errorObj)}</div>;
            } catch (e) {
              // If parsing fails, just display the error as is
              return <div className="text-red-500">{error}</div>;
            }
          })()}
        </div>
      )}

      {!cityId && (
        <div className="text-xs text-amber-500 mt-1">
          Select a city first
        </div>
      )}

      {cityId && !loading && !error && availableIps.length === 0 && (
        <div className="text-xs text-amber-500 mt-1">
          No available IPs found. You may need to:
          <ul className="list-disc ml-4 mt-1">
            <li>Create subnets in this city/country</li>
            <li>Generate IPs for existing subnets</li>
            <li>Mark subnets as "Available" status</li>
          </ul>

          {availableSubnets.length > 0 && (
            <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded-md">
              <p className="font-medium text-green-700">
                Found {availableSubnets.length} available subnet(s) without IPs
              </p>
              <button
                onClick={generateIpsForSubnets}
                disabled={generatingIps}
                className={`mt-2 px-3 py-1.5 rounded-md text-white text-xs font-medium flex items-center ${
                  generatingIps ? 'bg-gray-400' : 'bg-green-600 hover:bg-green-700'
                }`}
              >
                {generatingIps ? (
                  <>
                    <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                    Generating IPs...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Check Database for IPs
                  </>
                )}
              </button>
            </div>
          )}

          <div className="mt-2 p-2 bg-blue-50 rounded-md">
            <p className="font-medium text-blue-700">Quick Guide:</p>
            <ol className="list-decimal ml-4 mt-1 text-blue-700">
              <li>Go to Subnets Management</li>
              <li>Find subnets for {cityId ? "this city" : "your location"}</li>
              <li>Click "Generate IPs" for each subnet</li>
              <li>Return here and refresh the IP list</li>
            </ol>
          </div>

          <div className="mt-2 flex space-x-2">
            <a
              href="/admin/subnets"
              target="_blank"
              className="text-blue-600 hover:underline inline-block font-medium"
            >
              Go to Subnets Management →
            </a>

            <button
              onClick={fetchAvailableIps}
              disabled={loading}
              className="text-indigo-600 hover:underline inline-block font-medium"
            >
              Refresh IP List
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
const BulkDeviceAddition = ({
  isOpen,
  onClose,
  deviceType, // 'blade', 'dedicated', 'chassis', or 'switch'
  onAddItems,
  cities = [],
  racks = [],
  chassis = [],
  switches = [],
  storageConfigurations = []
}) => {
  // State definitions
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [templateItem, setTemplateItem] = useState(null);
  const [errorMessages, setErrorMessages] = useState({});
  const [cpuOptions, setCpuOptions] = useState([]);
  const [ramConfigurations, setRamConfigurations] = useState([]);
  const [passwordVisibility, setPasswordVisibility] = useState({});
  const [activeTab, setActiveTab] = useState("basic");
  const [availablePorts, setAvailablePorts] = useState({});
  const [showAddSwitchModelModal, setShowAddSwitchModelModal] = useState(false);
  const [newSwitchModel, setNewSwitchModel] = useState({ name: '', size: 1 });
  const [selectedDeviceForPorts, setSelectedDeviceForPorts] = useState(null);
  const [processingMacs, setProcessingMacs] = useState(false);
  const [macResults, setMacResults] = useState({});
  const [currentPage, setCurrentPage] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [chassisModels, setChassisModels] = useState([])
  const [switchModels, setSwitchModels] = useState([]);
  const [showAddChassisModelModal, setShowAddChassisModelModal] = useState(false);
  const [newChassisModel, setNewChassisModel] = useState({ name: '', size: 2, bay_count: 4 });
  const [fetchingData, setFetchingData] = useState({
    cpus: false,
    switches: false,
    chassis: false,
    chassisModels: false,
    switchModels: false,
    ramConfigurations: false
  });

  // New state for add new modals
  const [showAddCpuModal, setShowAddCpuModal] = useState(false);
  const [newCpuModel, setNewCpuModel] = useState('');
  const [showAddRamModal, setShowAddRamModal] = useState(false);
  const [newRamConfig, setNewRamConfig] = useState({ size: '', description: '' });


  function fetchChassisModels() {
    console.log('Fetching chassis models...');
    setFetchingData(prev => ({ ...prev, chassisModels: true }));

    const token = localStorage.getItem('admin_token');

    fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_chassis_models`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token })
    })
    .then(response => {
      if (!response.ok) throw new Error(`HTTP error ${response.status}`);
      return response.json();
    })
    .then(data => {
      console.log('Chassis models received:', data);
      if (Array.isArray(data)) {
        setChassisModels(data);
      } else {
        console.warn('Data is not an array, using fallback data');
        // Fall back to some common chassis models
        setChassisModels([
          { id: 1, name: 'PowerEdge c6320', size: 2, bay_count: 8 },
          { id: 2, name: 'PowerEdge c6420', size: 2, bay_count: 8 },
          { id: 3, name: 'PowerEdge M1000e', size: 10, bay_count: 16 }
        ]);
      }
      setFetchingData(prev => ({ ...prev, chassisModels: false }));
    })
    .catch(error => {
      console.error("Error fetching chassis models:", error);
      // Set fallback data
      setChassisModels([
        { id: 1, name: 'PowerEdge c6320', size: 2, bay_count: 8 },
        { id: 2, name: 'PowerEdge c6420', size: 2, bay_count: 8 },
        { id: 3, name: 'PowerEdge M1000e', size: 10, bay_count: 16 }
      ]);
      setFetchingData(prev => ({ ...prev, chassisModels: false }));
    });
  }
  // Function to fetch available ports for a switch
// Function to fetch available ports for a switch
const fetchAvailablePorts = async (switchId) => {
  if (!switchId) return;

  console.log("Fetching ports for switch ID:", switchId);

  try {
    setLoading(true);
    const token = localStorage.getItem('admin_token');

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_switch_ports`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        token,
        switch_id: switchId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const data = await response.json();
    console.log(`Fetched ${data.length} ports for switch ${switchId}`, data);

    // Do not filter by status, include all ports
    setAvailablePorts(prev => ({
      ...prev,
      [switchId]: data
    }));

    setLoading(false);
  } catch (error) {
    console.error("Error fetching available ports:", error);
    setAvailablePorts(prev => ({
      ...prev,
      [switchId]: []
    }));
    setLoading(false);
  }
};
const handleAddChassisModel = async () => {
  try {
    if (!newChassisModel.name.trim()) {
      alert('Chassis model name is required');
      return;
    }

    if (!newChassisModel.size || newChassisModel.size < 1) {
      alert('Size must be at least 1U');
      return;
    }

    setLoading(true);
    const token = localStorage.getItem('admin_token');

    // Debug: Log what we're sending
    console.log("Sending chassis model data:", {
      token: token ? "present" : "missing",
      name: newChassisModel.name,
      size: newChassisModel.size,
      bay_count: newChassisModel.bay_count
    });

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_chassis_model`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token,
        name: newChassisModel.name,
        size: newChassisModel.size,
        bay_count: newChassisModel.bay_count
      })
    });

    // Get the full response text for better debugging
    const responseText = await response.text();
    let result;

    // Check if the responseText is empty or contains non-JSON content
    if (!responseText.trim()) {
        console.error("Server returned an empty response. HTTP Status:", response.status);
        throw new Error(`Server returned an empty response (HTTP ${response.status}). This usually indicates a server-side error or misconfiguration. Please check server logs and the network tab.`);
    }

    try {
      // Try to parse as JSON
      result = JSON.parse(responseText);
    } catch (e) {
      console.error("Server response is not valid JSON. Raw response:", responseText);
      throw new Error("Server returned invalid response format. Check your network tab for details. Raw response snippet: " + responseText.substring(0, 200) + "...");
    }

    // Now check if the HTTP response itself was successful (e.g., 200 OK)
    // and if the parsed JSON indicates success
    if (!response.ok) {
        throw new Error(`HTTP Error ${response.status}: ${result.error || 'Unknown server error.'}`);
    }

    if (result.success) {
      setShowAddChassisModelModal(false);
      setNewChassisModel({ name: '', size: 2, bay_count: 8 });

      // Refresh chassis models
      await fetchChassisModels();

      alert('Chassis model added successfully');
    } else {
      alert(result.error || 'Failed to add chassis model');
    }

    setLoading(false);
  } catch (err) {
    setLoading(false);
    console.error("Error adding chassis model:", err);
    alert('Failed to add chassis model: ' + err.message);
  }
};

// Create a new separate function for handling switch selection
const handleSwitchSelection = (index, switchId) => {
  console.log(`Switch selected for row ${index}: ${switchId}`);

  // Update the item with the new switch ID
  const newItems = [...items];
  newItems[index] = {
    ...newItems[index],
    switch_id: switchId,
    // Reset port values
    port1: '',
    port2: '',
    port3: '',
    port4: '',
    port1_speed: '',
    port2_speed: '',
    port3_speed: '',
    port4_speed: ''
  };

  setItems(newItems);

  // Clear any error for this field if it exists
  if (errorMessages[index]?.switch_id) {
    const newErrors = {...errorMessages};
    delete newErrors[index]?.switch_id;
    if (Object.keys(newErrors[index] || {}).length === 0) {
      delete newErrors[index];
    }
    setErrorMessages(newErrors);
  }

  // Fetch available ports when switch is selected
  if (switchId) {
    console.log("Calling fetchAvailablePorts from handleSwitchSelection");
    fetchAvailablePorts(switchId);
  }
};

  // Function to fetch switch models from the database
  function fetchSwitchModels() {
    setFetchingData(prev => ({ ...prev, switchModels: true }));

    const token = localStorage.getItem('admin_token');

    fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_switch_models`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token })
    })
    .then(response => {
      if (!response.ok) throw new Error(`HTTP error ${response.status}`);
      return response.json();
    })
    .then(data => {
      if (Array.isArray(data)) {
        setSwitchModels(data);
      } else {
        // Fall back to some common switch models
        setSwitchModels([
          { id: 1, name: 'Arista DCS-7504N', size: 1 },
          { id: 2, name: 'Cisco Nexus 9396TX', size: 2 },
          { id: 3, name: 'Juniper EX4600', size: 1 }
        ]);
      }
      setFetchingData(prev => ({ ...prev, switchModels: false }));
    })
    .catch(error => {
      console.error("Error fetching switch models:", error);
      // Set fallback data
      setSwitchModels([
        { id: 1, name: 'Arista DCS-7504N', size: 1 },
        { id: 2, name: 'Cisco Nexus 9396TX', size: 2 },
        { id: 3, name: 'Juniper EX4600', size: 1 }
      ]);
      setFetchingData(prev => ({ ...prev, switchModels: false }));
    });
  }

  // Helper function to create empty item based on device type
  function createEmptyItem() {
    // Common fields for all device types
    const baseItem = {
      label: '',
      status: 'Available',
      notes: '',
      city_id: '',
      rack_id: '',
      position: ''
    };

    // Add device-specific fields
    switch (deviceType) {
      case 'blade':
      case 'dedicated':
        return {
          ...baseItem,
          cpu: '',  // This is correct - should be empty string initially
          ram: ''   // This is correct - should be empty string initially
        };
      case 'chassis':
        return {
          ...baseItem,
          size_ur: '',
          model: '',
          model_id: '',
          max_power: '',
          psu_count: '2',
          bay_count: ''
        };
      case 'switch':
        return {
          ...baseItem,
          switch_ip: '',
          root_password: '',
          snmp_community: '',
          model: '',
          model_id: '',
          port_speed: '',
          manufacturer: ''
        };
      default:
        return baseItem;
    }
  }

  // Initialize items state with empty items when deviceType changes
  useEffect(() => {
    if (isOpen) {
      setItems(Array.from({ length: 10 }, () => createEmptyItem()));
    }
  }, [isOpen, deviceType]);

  // Function to add multiple rows
  function addRows(count = 1) {
    const newItems = [...items];
    for (let i = 0; i < count; i++) {
      if (templateItem) {
        newItems.push({...templateItem, label: ''});
      } else {
        newItems.push(createEmptyItem());
      }
    }
    setItems(newItems);
  }

  // Remove a row
  function removeRow(index) {
    if (items.length > 1) {
      const newItems = [...items];
      newItems.splice(index, 1);
      setItems(newItems);

      // Remove any errors for this row
      const newErrors = {...errorMessages};
      delete newErrors[index];
      setErrorMessages(newErrors);
    }
  }

  // Handle input change for a specific row and field
// 1. First, update your handleItemChange function to handle city selection
// Find your existing handleItemChange function and modify it to include this logic
function handleItemChange(index, field, value) {
  const newItems = [...items];
  const oldValue = newItems[index][field];

  // Special handling for IP fields (IPMI and switch_ip)
  if ((field === 'ipmi' || field === 'switch_ip') && oldValue && oldValue !== value) {
    // If changing from one IP to another, deallocate the old IP
    deallocateIp(oldValue, field === 'ipmi' ? 'server' : 'switch', newItems[index].id);
  }

  // Special handling for foreign key fields
  if (['rack_id', 'city_id', 'country_id', 'cpu', 'ram', 'switch_id', 'model_id', 'chassis_id'].includes(field)) {
    if (value === '' || value === '0') {
      // Empty values should be empty strings, not numbers
      newItems[index] = {
        ...newItems[index],
        [field]: ''
      };
    } else {
      // Convert to number for non-empty foreign key fields
      const numericValue = parseInt(value, 10);
      newItems[index] = {
        ...newItems[index],
        [field]: numericValue
      };

      // Special handling when city changes - clear rack selection
      if (field === 'city_id') {
        newItems[index]['rack_id'] = '';
      }

      // When a chassis is selected for a blade, set the city_id from the chassis
      if (field === 'chassis_id' && deviceType === 'blade') {
        const selectedChassis = chassis.find(c => c.id === numericValue);
        if (selectedChassis) {
          newItems[index]['city_id'] = selectedChassis.city_id;
        } else {
          newItems[index]['city_id'] = ''; // Clear city if chassis is deselected
        }
      }
    }
  } else {
    // For non-foreign key fields, just use the value as is
    newItems[index] = {
      ...newItems[index],
      [field]: value
    };
  }

  // Special handling for switch_id selection
  if (field === 'switch_id' && newItems[index]['switch_id'] !== value) {
    newItems[index]['port1'] = '';
    newItems[index]['port2'] = '';
    newItems[index]['port3'] = '';
    newItems[index]['port4'] = '';

    // Fetch available ports when switch is selected
    if (value) {
      fetchAvailablePorts(value);
    }
  }

  setItems(newItems);

  // Clear any error for this field if it exists
  if (errorMessages[index]?.[field]) {
    const newErrors = {...errorMessages};
    delete newErrors[index][field];
    if (Object.keys(newErrors[index]).length === 0) {
      delete newErrors[index];
    }
    setErrorMessages(newErrors);
  }
}

// Function to deallocate an IP address
async function deallocateIp(ipAddress, deviceType, deviceId) {
  if (!ipAddress) return;

  console.log(`Deallocating ${ipAddress} for ${deviceType} ${deviceId || 'new'}`);

  try {
    const token = localStorage.getItem('admin_token');

    // Call the force_deallocate_ip endpoint to deallocate the IP
    const response = await fetch(`${API_URL}/api_admin_subnets.php?f=force_deallocate_ip`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token,
        ip_address: ipAddress,
        for_server_ipmi: deviceType === 'server',
        for_switch: deviceType === 'switch',
        server_id: deviceType === 'server' ? deviceId : undefined,
        switch_id: deviceType === 'switch' ? deviceId : undefined
      })
    });

    const result = await response.json();

    if (result.success) {
      console.log(`Successfully deallocated IP ${ipAddress}`);
    } else {
      console.error(`Failed to deallocate IP ${ipAddress}:`, result.error || 'Unknown error');
    }
  } catch (err) {
    console.error(`Error deallocating IP ${ipAddress}:`, err);
  }
}

  // Set the current row as template for future rows
  function setRowAsTemplate(index) {
    setTemplateItem({...items[index]});
  }

  // Apply template to all existing rows
  function applyTemplateToAll() {
    if (templateItem) {
      const newItems = items.map(item => ({
        ...templateItem,
        label: item.label, // Preserve existing labels
      }));
      setItems(newItems);
    }
  }

  // Generate sequential names with a prefix
  function generateSequentialNames() {
    let defaultPrefix = '';
    switch (deviceType) {
      case 'blade': defaultPrefix = 'XR-BL'; break;
      case 'dedicated': defaultPrefix = 'XR-'; break;
      case 'chassis': defaultPrefix = 'XR-BL'; break;
      case 'switch': defaultPrefix = 'BUC-AR'; break;
    }

    const prefix = prompt("Enter device name prefix:", defaultPrefix);
    if (!prefix) return;

    const newItems = items.map((item, index) => {
      const paddedNum = String(index + 1).padStart(2, '0');
      return {
        ...item,
        label: `${prefix}${paddedNum}`
      };
    });

    setItems(newItems);
  }

  // Toggle password visibility
  function togglePasswordVisibility(index, field) {
    setPasswordVisibility(prev => ({
      ...prev,
      [`${index}-${field}`]: !prev[`${index}-${field}`]
    }));
  }

// Fixed function to handle adding a new switch model
const handleAddSwitchModel = async () => {
  try {
    if (!newSwitchModel.name.trim()) {
      alert('Switch model name is required');
      return;
    }

    // Make sure size is a valid number
    const size = parseInt(newSwitchModel.size);
    if (isNaN(size) || size < 1) {
      alert('Size must be a valid number (at least 1U)');
      return;
    }

    setLoading(true);
    const token = localStorage.getItem('admin_token');

    // Debug: Log what we're sending
    console.log("Sending switch model data:", {
      token: token ? "present" : "missing",
      name: newSwitchModel.name,
      size: size
    });

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_switch_model`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token,
        name: newSwitchModel.name.trim(),
        size: size // Send as number, not string
      })
    });

    // Get the full response text for better debugging
    const responseText = await response.text();
    let result;

    try {
      // Try to parse as JSON
      result = JSON.parse(responseText);
    } catch (e) {
      console.error("Server response is not valid JSON:", responseText);
      throw new Error("Server returned invalid response format. Check your network tab for details.");
    }

    if (result.success) {
      setShowAddSwitchModelModal(false);
      setNewSwitchModel({ name: '', size: 1 });

      // Refresh switch models
      await fetchSwitchModels();

      alert('Switch model added successfully');
    } else {
      alert(result.error || 'Failed to add switch model');
    }

    setLoading(false);
  } catch (err) {
    setLoading(false);
    console.error("Error adding switch model:", err);
    alert('Failed to add switch model: ' + err.message);
  }
};

  // Detect MAC addresses for multiple servers
  const detectAllMacAddresses = async () => {
    if (deviceType !== 'blade' && deviceType !== 'dedicated') return;

    // Filter items that have IPMI addresses but no MAC
    const itemsToUpdate = items.filter(item =>
      item.ipmi && item.ipmi.trim() !== '' &&
      (!item.mac || item.mac.trim() === '')
    );

    if (itemsToUpdate.length === 0) {
      alert('No items with IPMI addresses and missing MAC addresses found');
      return;
    }

    if (!window.confirm(`Detect MAC addresses for ${itemsToUpdate.length} servers with IPMI addresses?`)) {
      return;
    }

    setProcessingMacs(true);
    setMacResults({}); // Clear previous results
    const token = localStorage.getItem('admin_token');
    const updatedItems = [...items];

    try {
      // Prepare tracking for all items being processed
      itemsToUpdate.forEach((item) => {
        const itemIndex = items.findIndex(i => i === item);
        if (itemIndex !== -1) {
          setMacResults(prev => ({
            ...prev,
            [`${itemIndex}-progress`]: true
          }));
        }
      });

      // Create an array of promises for all MAC address detections
      const detectionPromises = itemsToUpdate.map(async (item) => {
        const itemIndex = items.findIndex(i => i === item);
        if (itemIndex === -1) return null;

        try {
          const response = await fetch(`${API_URL}/api_admin_inventory.php?f=detect_mac_address`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              token: token,
              ipmi_address: item.ipmi,
              ipmi_username: 'root',
              ipmi_password: item.ipmi_root_pass || '',
              idrac_version: 8, // Default to iDRAC 8
              server_type: deviceType
            })
          });

          if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
          }

          const data = await response.json();

          // Enhanced MAC address processing
          if (data.success) {
            // Check if we have multiple MAC addresses
            if (data.all_mac_addresses && data.all_mac_addresses.length > 0) {
              // Filter out WWN/WWPN addresses
              const filteredMacs = data.all_mac_addresses.filter(mac => {
                const macAddress = mac.mac;
                const parts = macAddress.split(/[:-]/);

                if (parts.length === 8) return false;
                if (macAddress.startsWith('20:00:') ||
                    macAddress.startsWith('21:00:') ||
                    macAddress.startsWith('10:00:')) return false;

                return true;
              });

              // Look for PermanentMACAddress in raw output
              const rawOutput = data.raw_output || "";
              let permanentMacMatch = null;

              const permanentMacRegex = /PermanentMACAddress\s*=\s*([0-9A-Fa-f]{2}(?::[0-9A-Fa-f]{2}){5})/g;
              const macMatches = [...rawOutput.matchAll(permanentMacRegex)];

              if (macMatches.length > 0) {
                permanentMacMatch = macMatches[0][1];

                if (!filteredMacs.some(mac => mac.mac.toLowerCase() === permanentMacMatch.toLowerCase())) {
                  filteredMacs.unshift({
                    mac: permanentMacMatch,
                    interface: "PermanentMACAddress",
                    is_permanent: true,
                    link_status: "Unknown"
                  });
                }
              }

              // If no valid MAC addresses after filtering, use primary MAC if available
              if (filteredMacs.length === 0) {
                if (data.mac_address) {
                  return {
                    itemIndex,
                    success: true,
                    mac: data.mac_address,
                    error: null
                  };
                }
                return {
                  itemIndex,
                  success: false,
                  mac: null,
                  error: "No valid MAC addresses detected"
                };
              }

              // Find the best MAC to use
              let bestMacIndex = 0;

              // Try to use PermanentMACAddress if found
              if (permanentMacMatch) {
                const exactMatchIndex = filteredMacs.findIndex(
                  mac => mac.mac.toLowerCase() === permanentMacMatch.toLowerCase()
                );
                if (exactMatchIndex >= 0) {
                  bestMacIndex = exactMatchIndex;
                }
              }

              // Otherwise, try to find any permanent MAC
              if (!permanentMacMatch) {
                const permanentMacIndex = filteredMacs.findIndex(mac => mac.is_permanent);
                if (permanentMacIndex >= 0) {
                  bestMacIndex = permanentMacIndex;
                }
              }

              // If no permanent MAC, look for physical interfaces
              if (bestMacIndex === 0 && !filteredMacs[0].is_permanent) {
                for (let i = 0; i < filteredMacs.length; i++) {
                  const mac = filteredMacs[i];
                  const interfaceName = mac.interface?.toLowerCase() || '';

                  if (interfaceName.includes('physical') ||
                      interfaceName.includes('integrated') ||
                      interfaceName.includes('nic.integrated') ||
                      interfaceName.includes('embedded') ||
                      interfaceName.includes('lom') ||
                      (interfaceName.includes('nic') && !interfaceName.includes('virtual'))
                  ) {
                    bestMacIndex = i;
                    break;
                  }
                }
              }

              const bestMac = filteredMacs[bestMacIndex].mac;

              return {
                itemIndex,
                success: true,
                mac: bestMac,
                error: null,
                allMacs: filteredMacs
              };
            } else if (data.mac_address) {
              // Fall back to primary MAC if all_mac_addresses is not available
              return {
                itemIndex,
                success: true,
                mac: data.mac_address,
                error: null
              };
            }
          }

          // If we get here, no usable MAC address was found
          return {
            itemIndex,
            success: false,
            mac: null,
            error: data.error || "No MAC address detected"
          };
        } catch (e) {
          console.error(`Error detecting MAC for item at index ${itemIndex}:`, e);
          return {
            itemIndex,
            success: false,
            mac: null,
            error: e.message
          };
        }
      });

      // Wait for all promises to resolve
      const results = await Promise.all(detectionPromises);

      // Process all results
      let successCount = 0;
      let failCount = 0;

      results.forEach(result => {
        if (!result) return;

        const { itemIndex, success, mac, error, allMacs } = result;

        // Clear progress indicator
        setMacResults(prev => {
          const updated = {...prev};
          delete updated[`${itemIndex}-progress`];
          return updated;
        });

        if (success && mac) {
          // Update the item with the detected MAC
          updatedItems[itemIndex] = {
            ...updatedItems[itemIndex],
            mac: mac
          };

          // Update the results display
          setMacResults(prev => ({
            ...prev,
            [itemIndex]: {
              success: true,
              mac: mac,
              allMacs: allMacs
            }
          }));

          successCount++;
        } else {
          // Update failure state
          setMacResults(prev => ({
            ...prev,
            [itemIndex]: {
              success: false,
              error: error || 'No MAC address found'
            }
          }));

          failCount++;
        }
      });

      // Update all items at once
      setItems(updatedItems);

      // Show summary alert with results
      if (successCount > 0 || failCount > 0) {
        alert(`MAC address detection completed:
- Successfully detected: ${successCount}
- Failed: ${failCount}`);
      }
    } catch (e) {
      console.error('Error in bulk MAC detection:', e);
      alert('Error during MAC address detection: ' + e.message);
    } finally {
      setProcessingMacs(false);
    }
  };

  // New function to fetch RAM configurations
  function fetchRamConfigurations() {
    setFetchingData(prev => ({ ...prev, ramConfigurations: true }));

    const token = localStorage.getItem('admin_token');

    fetch(`${API_URL}/api_admin_inventory.php?f=get_ram_configurations`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token })
    })
    .then(response => {
      if (!response.ok) throw new Error(`HTTP error ${response.status}`);
      return response.json();
    })
    .then(data => {
      if (Array.isArray(data)) {
        setRamConfigurations(data);
        console.log(`Loaded ${data.length} RAM configurations`);
      } else {
        // Fall back to some common RAM configurations
        setRamConfigurations([
          { id: 1, size: 16, description: '16GB (2x8GB) DDR4-2400' },
          { id: 2, size: 32, description: '32GB (2x16GB) DDR4-2400' },
          { id: 3, size: 64, description: '64GB (4x16GB) DDR4-2400' },
          { id: 4, size: 128, description: '128GB (8x16GB) DDR4-2666' },
          { id: 5, size: 256, description: '256GB (8x32GB) DDR4-2933' }
        ]);
      }
      setFetchingData(prev => ({ ...prev, ramConfigurations: false }));
    })
    .catch(error => {
      console.error("Error fetching RAM configurations:", error);
      // Set fallback data
      setRamConfigurations([
        { id: 1, size: 16, description: '16GB (2x8GB) DDR4-2400' },
        { id: 2, size: 32, description: '32GB (2x16GB) DDR4-2400' },
        { id: 3, size: 64, description: '64GB (4x16GB) DDR4-2400' },
        { id: 4, size: 128, description: '128GB (8x16GB) DDR4-2666' },
        { id: 5, size: 256, description: '256GB (8x32GB) DDR4-2933' }
      ]);
      setFetchingData(prev => ({ ...prev, ramConfigurations: false }));
    });
  }

  // Function to handle adding a new CPU model
  const handleAddCpuModel = async () => {
    try {
      if (!newCpuModel.trim()) {
        alert('CPU model name is required');
        return;
      }

      setLoading(true);
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_cpu_model`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          cpu: newCpuModel
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setShowAddCpuModal(false);
        setNewCpuModel('');

        // Refresh CPU models
        await fetchCpuOptions();

        alert('CPU model added successfully');
      } else {
        alert(result.error || 'Failed to add CPU model');
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error("Error adding CPU model:", err);
      alert('Failed to add CPU model: ' + err.message);
    }
  };

  // Function to handle adding a new RAM configuration
  const handleAddRamConfig = async () => {
    try {
      if (!newRamConfig.size || !newRamConfig.description) {
        alert('RAM size and description are required');
        return;
      }

      setLoading(true);
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_ram_configuration`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          size: newRamConfig.size,
          description: newRamConfig.description
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setShowAddRamModal(false);
        setNewRamConfig({ size: '', description: '' });

        // Refresh RAM configurations
        await fetchRamConfigurations();

        alert('RAM configuration added successfully');
      } else {
        alert(result.error || 'Failed to add RAM configuration');
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error("Error adding RAM configuration:", err);
      alert('Failed to add RAM configuration: ' + err.message);
    }
  };

  // Fetch required data
  useEffect(() => {
    if (isOpen) {
      if (deviceType === 'blade' || deviceType === 'dedicated') {
        fetchCpuOptions();
        fetchRamConfigurations();
      }
      if (deviceType === 'chassis' || deviceType === 'blade') {
        fetchChassisModels();
      }
      if (deviceType === 'switch') {
        fetchSwitchModels();
      }
    }
  }, [isOpen, deviceType]);


  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setItems(Array.from({ length: 10 }, () => createEmptyItem()));
      setErrorMessages({});
      setTemplateItem(null);
      setPasswordVisibility({});
      setActiveTab("basic");
      setCurrentPage(0);

      // Set appropriate initial tab based on device type
      switch (deviceType) {
        case 'blade':
        case 'dedicated':
          setActiveTab("basic");
          break;
        case 'chassis':
          setActiveTab("basic");
          break;
        case 'switch':
          setActiveTab("basic");
          break;
      }
    }
  }, [isOpen, deviceType]);


// Update the handleChassisModelChange function to set bay_count too
function handleChassisModelChange(index, modelId) {
  const selectedModel = chassisModels.find(model => model.id.toString() === modelId.toString());
  if (selectedModel) {
    const newItems = [...items];
    newItems[index] = {
      ...newItems[index],
      model_id: modelId,
      model: selectedModel.name,
      size_ur: selectedModel.size, // Auto-set the size based on the model
      bay_count: selectedModel.bay_count // Auto-set the bay count based on the model
    };
    setItems(newItems);

    // Clear any error for model, size, or bay_count if it exists
    if (errorMessages[index]?.model_id || errorMessages[index]?.size_ur || errorMessages[index]?.bay_count) {
      const newErrors = {...errorMessages};
      delete newErrors[index]?.model_id;
      delete newErrors[index]?.size_ur;
      delete newErrors[index]?.bay_count;
      if (Object.keys(newErrors[index] || {}).length === 0) {
        delete newErrors[index];
      }
      setErrorMessages(newErrors);
    }
  }
}

  // Add a handler for model selection for switches
  function handleSwitchModelChange(index, modelId) {
    const selectedModel = switchModels.find(model => model.id.toString() === modelId.toString());
    if (selectedModel) {
      const newItems = [...items];
      newItems[index] = {
        ...newItems[index],
        model_id: modelId,
        model: selectedModel.name,
        size_ur: selectedModel.size // Auto-set the size based on the model
      };
      setItems(newItems);

      // Clear any error for model or size if it exists
      if (errorMessages[index]?.model_id || errorMessages[index]?.size_ur) {
        const newErrors = {...errorMessages};
        delete newErrors[index]?.model_id;
        delete newErrors[index]?.size_ur;
        if (Object.keys(newErrors[index] || {}).length === 0) {
          delete newErrors[index];
        }
        setErrorMessages(newErrors);
      }
    }
  }

  const getFilteredRacks = (cityId) => {
    if (!cityId) return [];
    return racks.filter(rack => {
      // Handle different data structures - some might use 'city' and others 'city_id'
      return rack.city == cityId || rack.city_id == cityId;
    });
  };

  // 4. You can use this function when you need to check if there are available racks
  const noRacksAvailable = (cityId) => {
    const filteredRacks = getFilteredRacks(cityId);
    return filteredRacks.length === 0;
  };

  // Function to fetch CPU options from the dedicated_cpu table
  function fetchCpuOptions() {
    setFetchingData(prev => ({ ...prev, cpus: true }));

    const token = localStorage.getItem('admin_token');

    fetch(`${API_URL}/api_admin_inventory.php?f=get_dedicated_cpus`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token })
    })
    .then(response => {
      if (!response.ok) throw new Error(`HTTP error ${response.status}`);
      return response.json();
    })
    .then(data => {
      if (Array.isArray(data)) {
        setCpuOptions(data);
      } else {
        // Fall back to some common CPU options
        setCpuOptions([
          { id: 1, cpu: "Intel Xeon E5-2650 v4" },
          { id: 2, cpu: "Intel Xeon E5-2680 v4" },
          { id: 3, cpu: "AMD EPYC 7302" },
          { id: 4, cpu: "Intel Xeon Gold 6230R" },
          { id: 5, cpu: "AMD EPYC 7502" }
        ]);
      }
      setFetchingData(prev => ({ ...prev, cpus: false }));
    })
    .catch(error => {
      console.error("Error fetching CPU options:", error);
      setCpuOptions([
        { id: 1, cpu: "Intel Xeon E5-2650 v4" },
        { id: 2, cpu: "Intel Xeon E5-2680 v4" },
        { id: 3, cpu: "AMD EPYC 7302" },
        { id: 4, cpu: "Intel Xeon Gold 6230R" },
        { id: 5, cpu: "AMD EPYC 7502" }
      ]);
      setFetchingData(prev => ({ ...prev, cpus: false }));
    });
  }

  // Validate all items before submission
// Update the validateItems function to include validation for model_id
function validateItems() {
  const errors = {};
  let hasErrors = false;

  items.forEach((item, index) => {
    // Skip empty rows (they will be filtered out)
    if (!item.label || item.label.trim() === '') {
      return;
    }

    const rowErrors = {};

    // Required fields for all device types
    if (!item.label || item.label.trim() === '') {
      rowErrors.label = 'Required';
      hasErrors = true;
    }

    // Device-specific validation
    switch (deviceType) {
      case 'chassis':
        if (!item.city_id || item.city_id === '') {
          rowErrors.city_id = 'Required';
          hasErrors = true;
        }

        if (!item.rack_id || item.rack_id === '') {
          rowErrors.rack_id = 'Required';
          hasErrors = true;
        }

        // Now validate model_id instead of size_ur
        if (!item.model_id || item.model_id === '') {
          rowErrors.model_id = 'Required';
          hasErrors = true;
        }
        break;

      case 'switch':
        if (!item.city_id || item.city_id === '') {
          rowErrors.city_id = 'Required';
          hasErrors = true;
        }

        if (!item.rack_id || item.rack_id === '') {
          rowErrors.rack_id = 'Required';
          hasErrors = true;
        }

        if (!item.switch_ip || item.switch_ip === '') {
          rowErrors.switch_ip = 'Required';
          hasErrors = true;
        }

        // Also validate model_id for switches
        if (!item.model_id || item.model_id === '') {
          rowErrors.model_id = 'Required';
          hasErrors = true;
        }
        break;

      // Other cases remain unchanged
    }

    if (Object.keys(rowErrors).length > 0) {
      errors[index] = rowErrors;
    }
  });

  setErrorMessages(errors);
  return !hasErrors;
}

  // Function to generate IPs for all available subnets
  async function generateIpsForAllSubnets() {
    try {
      setLoading(true);

      const token = localStorage.getItem('admin_token');

      // Get all subnets
      const subnetsResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnets`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      const subnets = await subnetsResponse.json();

      if (!Array.isArray(subnets)) {
        console.error('Failed to fetch subnets');
        alert('Failed to fetch subnets');
        return;
      }

      // Filter for available subnets
      const availableSubnets = subnets.filter(subnet => subnet.status === 'Available');

      if (availableSubnets.length === 0) {
        alert('No available subnets found');
        return;
      }

      console.log(`Found ${availableSubnets.length} available subnets, checking IP status...`);

      // Check each subnet for existing IPs
      const subnetsToGenerate = [];
      const subnetsWithIPs = [];

      for (const subnet of availableSubnets) {
        try {
          // Extract subnet ID
          const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
            ? subnet.id.replace('SUB-', '')
            : subnet.id;

          // Check if the subnet already has IPs
          const ipResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnet_ips`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              subnet_id: subnetId,
              token: token,
              include_used: true
            })
          });

          const ipData = await ipResponse.json();

          if (ipData && ipData.success && ipData.ips && ipData.ips.length > 0) {
            // Count available IPs
            const availableIps = ipData.ips.filter(ip => {
              const isUsed = parseInt(String(ip.is_used), 10);
              return isUsed === 0;
            });

            if (availableIps.length > 0) {
              console.log(`Subnet ${subnet.subnet || subnet.cidr || 'Unnamed'} (ID: ${subnetId}) already has ${availableIps.length} available IPs`);
              subnetsWithIPs.push({
                ...subnet,
                availableIps: availableIps.length,
                totalIps: ipData.ips.length
              });
            } else {
              console.log(`Subnet ${subnet.subnet || subnet.cidr || 'Unnamed'} (ID: ${subnetId}) has IPs but none are available`);
              subnetsToGenerate.push(subnet);
            }
          } else {
            console.log(`Subnet ${subnet.subnet || subnet.cidr || 'Unnamed'} (ID: ${subnetId}) has no IPs`);
            subnetsToGenerate.push(subnet);
          }
        } catch (err) {
          console.error(`Error checking IPs for subnet ${subnet.id}:`, err);
          subnetsToGenerate.push(subnet);
        }
      }

      if (subnetsWithIPs.length > 0) {
        const subnetsWithIPsList = subnetsWithIPs.map(subnet =>
          `${subnet.subnet || subnet.cidr || 'Unnamed subnet'} (ID: ${subnet.id}): ${subnet.availableIps}/${subnet.totalIps} available`
        ).join('\n');

        console.log(`Subnets with available IPs:\n${subnetsWithIPsList}`);
      }

      if (subnetsToGenerate.length === 0) {
        alert(`All ${availableSubnets.length} subnets already have available IPs. No need to generate more.`);
        return;
      }

      console.log(`Generating IPs for ${subnetsToGenerate.length} subnets...`);

      let successCount = 0;

      // Generate IPs for each subnet that needs them
      for (const subnet of subnetsToGenerate) {
        try {
          // Extract subnet ID
          const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
            ? subnet.id.replace('SUB-', '')
            : subnet.id;

          console.log(`Generating IPs for subnet ${subnet.subnet || subnet.cidr || 'Unnamed'} (ID: ${subnetId})`);

          const response = await fetch(`${API_URL}/api_admin_subnets.php?f=generate_ips`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: token,
              subnet_id: subnetId
            })
          });

          const result = await response.json();

          if (result.success) {
            console.log(`Successfully generated IPs for subnet ${subnetId}`);
            successCount++;
          } else {
            console.error(`Failed to generate IPs for subnet ${subnetId}:`, result.error || 'Unknown error');
          }
        } catch (err) {
          console.error(`Error generating IPs for subnet ${subnet.id}:`, err);
        }
      }

      if (successCount > 0) {
        alert(`Successfully generated IPs for ${successCount} subnet(s)`);

        // Refresh the IP list in any BulkIpSelector components
        const ipSelectors = document.querySelectorAll('.bulk-ip-selector');
        if (ipSelectors.length > 0) {
          console.log(`Refreshing ${ipSelectors.length} IP selectors...`);

          // Click the refresh button in each IP selector
          ipSelectors.forEach(selector => {
            const refreshButton = selector.querySelector('button[title="Refresh available IPs"]');
            if (refreshButton) {
              refreshButton.click();
            }
          });
        }
      } else {
        alert('Failed to generate IPs for any subnets');
      }
    } catch (err) {
      console.error('Error generating IPs:', err);
      alert('Error generating IPs: ' + (err.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  }

  // Handle form submission
  async function handleSubmit() {
    if (!validateItems()) {
      alert("Please fix the validation errors before submitting.");
      return;
    }

    // Filter out rows with empty labels
    const itemsToAdd = items.filter(item => item.label && item.label.trim() !== '');

    if (itemsToAdd.length === 0) {
      alert(`Please provide at least one ${deviceType} with a label`);
      return;
    }

    // Store the current device type in a global variable for use in the allocateIp function
    window.currentDeviceType = deviceType;
    console.log(`Setting current device type to: ${deviceType}`);

    setLoading(true);
    try {
      // First, deallocate any IPs that are going to be used
      // This ensures they're free before we try to allocate them
      for (const item of itemsToAdd) {
        if ((deviceType === 'blade' || deviceType === 'dedicated') && item.ipmi) {
          try {
            console.log(`Pre-deallocating IPMI IP ${item.ipmi} before server creation`);

            const token = localStorage.getItem('admin_token');
            const deallocateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=force_deallocate_ip`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                token: token,
                ip_address: item.ipmi,
                for_server_ipmi: true
              })
            });

            const deallocateResult = await deallocateResponse.json();

            if (deallocateResult.success) {
              console.log(`Successfully pre-deallocated IPMI IP ${item.ipmi}`);
            } else {
              console.log(`IPMI IP ${item.ipmi} was not allocated or deallocation failed:`, deallocateResult.error || 'Unknown error');
            }
          } catch (err) {
            console.error(`Error pre-deallocating IPMI IP ${item.ipmi}:`, err);
          }
        }

        if (deviceType === 'switch' && item.switch_ip) {
          try {
            console.log(`Pre-deallocating switch IP ${item.switch_ip} before switch creation`);

            const token = localStorage.getItem('admin_token');
            const deallocateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=force_deallocate_ip`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                token: token,
                ip_address: item.switch_ip,
                for_switch: true
              })
            });

            const deallocateResult = await deallocateResponse.json();

            if (deallocateResult.success) {
              console.log(`Successfully pre-deallocated switch IP ${item.switch_ip}`);
            } else {
              console.log(`Switch IP ${item.switch_ip} was not allocated or deallocation failed:`, deallocateResult.error || 'Unknown error');
            }
          } catch (err) {
            console.error(`Error pre-deallocating switch IP ${item.switch_ip}:`, err);
          }
        }
      }

      // Now submit the items to be added to get their IDs
      const addItemsResult = await onAddItems(itemsToAdd, deviceType);

      console.log('Items added successfully, result:', addItemsResult);

      // Try to get the added items with their IDs
      let addedItems = [];

      // Check if the result is an array
      if (Array.isArray(addItemsResult)) {
        addedItems = addItemsResult;
      } else if (addItemsResult && typeof addItemsResult === 'object') {
        // If it's an object, try to extract items from it
        if (addItemsResult.items && Array.isArray(addItemsResult.items)) {
          addedItems = addItemsResult.items;
        } else if (addItemsResult.success && addItemsResult.data && Array.isArray(addItemsResult.data)) {
          addedItems = addItemsResult.data;
        } else {
          // Try to convert object to array if it has numeric keys
          const possibleItems = Object.values(addItemsResult).filter(item =>
            item && typeof item === 'object' && (item.id || item.label)
          );

          if (possibleItems.length > 0) {
            addedItems = possibleItems;
          }
        }
      }

      console.log('Extracted added items:', addedItems);

      // If we still don't have added items with IDs, try to fetch them from the server
      if (addedItems.length === 0 || !addedItems.some(item => item.id)) {
        console.log('No added items with IDs found, trying to fetch them from the server');

        try {
          const token = localStorage.getItem('admin_token');

          // Determine which endpoint to use based on device type
          const endpoint = deviceType === 'dedicated' || deviceType === 'blade'
            ? `get_${deviceType === 'blade' ? 'blade_server' : 'inventory_dedicated'}`
            : `get_${deviceType}s`;

          const response = await fetch(`${API_URL}/api_admin_inventory.php?f=${endpoint}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: token
            })
          });

          const data = await response.json();

          if (Array.isArray(data)) {
            console.log(`Fetched ${data.length} ${deviceType}s from server`);

            // Try to match the added items by label
            const matchedItems = [];

            for (const originalItem of itemsToAdd) {
              const matchedItem = data.find(item => item.label === originalItem.label);

              if (matchedItem) {
                console.log(`Found matching item for ${originalItem.label} with ID ${matchedItem.id}`);
                matchedItems.push({
                  ...originalItem,
                  id: matchedItem.id
                });
              } else {
                console.warn(`Could not find matching item for ${originalItem.label}`);
                matchedItems.push(originalItem);
              }
            }

            if (matchedItems.length > 0) {
              addedItems = matchedItems;
            }
          }
        } catch (err) {
          console.error('Error fetching items from server:', err);
        }
      }

      // Now allocate IPs for the added items with their new IDs
      if (addedItems.length > 0) {
        for (let i = 0; i < addedItems.length; i++) {
          const addedItem = addedItems[i];
          const originalItem = itemsToAdd[i < itemsToAdd.length ? i : itemsToAdd.length - 1];

          // Get the new ID from the added item
          const newId = addedItem.id || null;

          if (newId) {
            console.log(`Item ${originalItem.label} added with ID: ${newId}`);

            // Handle IPMI IPs for servers (only if not empty)
            if ((deviceType === 'blade' || deviceType === 'dedicated') && originalItem.ipmi && originalItem.ipmi.trim()) {
              // Try multiple times with different approaches
              let success = await allocateIp(originalItem.ipmi, 'server', newId, originalItem.label);

              if (!success) {
                console.error(`Failed to allocate IPMI IP ${originalItem.ipmi} for server ${newId}, trying direct force allocation`);

                // Try direct force allocation as a last resort
                try {
                  const token = localStorage.getItem('admin_token');
                  // Create description and notes - assigned_to should only contain the device ID
                  const deviceLabel = originalItem.label || 'New';
                  // assigned_to field contains only the device ID for clean data management
                  const manual_alocation = newId ? newId.toString() : 'unknown';
                  // notes field can contain descriptive information
                  const notes = `IPMI for ${deviceLabel} (ID: ${newId})`;

                  // Prepare the request data
                  const requestData = {
                    token: token,
                    ip_address: originalItem.ipmi,
                    manual_alocation: manual_alocation,
                    notes: notes,
                    for_server_ipmi: true,
                    server_id: newId,
                    server_type: deviceType === 'blade' ? 'blade' : 'dedicated'
                  };

                  console.log('Sending force allocation request:', requestData);

                  let forceAllocateResult = { success: false, error: 'Unknown error' };

                  try {
                    const forceAllocateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=force_allocate_ip`, {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json'
                      },
                      body: JSON.stringify(requestData)
                    });

                    forceAllocateResult = await forceAllocateResponse.json();
                  } catch (err) {
                    console.error(`Error parsing force_allocate_ip response:`, err);
                    forceAllocateResult = {
                      success: false,
                      error: `Failed to parse response: ${err.message || 'Unknown error'}`
                    };
                  }

                  if (forceAllocateResult.success) {
                    console.log(`Successfully force allocated IPMI IP ${originalItem.ipmi} for server ${newId}`);
                    success = true;
                  } else {
                    console.error(`Failed to force allocate IPMI IP ${originalItem.ipmi}:`, forceAllocateResult.error || 'Unknown error');

                    // Try one more approach - direct database update
                    try {
                      console.log(`Trying direct database update for IP ${originalItem.ipmi}...`);

                      // Prepare the request data
                      const requestData = {
                        token: token,
                        ip_address: originalItem.ipmi,
                        is_used: 1,
                        manual_alocation: manual_alocation,
                        notes: notes,
                        for_server_ipmi: true,
                        server_id: newId,
                        server_type: deviceType === 'blade' ? 'blade' : 'dedicated'
                      };

                      console.log('Sending direct update request:', requestData);

                      let directUpdateResult = { success: false, error: 'Unknown error' };

                      try {
                        const directUpdateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=direct_update_ip`, {
                          method: 'POST',
                          headers: {
                            'Content-Type': 'application/json'
                          },
                          body: JSON.stringify(requestData)
                        });

                        directUpdateResult = await directUpdateResponse.json();
                      } catch (err) {
                        console.error(`Error parsing direct_update_ip response:`, err);
                        directUpdateResult = {
                          success: false,
                          error: `Failed to parse response: ${err.message || 'Unknown error'}`
                        };
                      }

                      if (directUpdateResult.success) {
                        console.log(`Successfully updated IP ${originalItem.ipmi} directly in database`);
                        success = true;
                      } else {
                        console.error(`Failed to update IP ${originalItem.ipmi} directly:`, directUpdateResult.error || 'Unknown error');

                        // As an absolute last resort, try a direct SQL update
                        try {
                          console.log(`Trying direct SQL update for IP ${originalItem.ipmi}...`);

                          // First try the sql_update_ip endpoint
                          try {
                            const sqlUpdateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=sql_update_ip`, {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json'
                              },
                              body: JSON.stringify({
                                token: token,
                                ip_address: originalItem.ipmi,
                                is_used: 1,
                                manual_alocation: manual_alocation,
                                notes: notes,
                                for_server_ipmi: true,
                                server_id: newId,
                                server_type: deviceType === 'blade' ? 'blade' : 'dedicated'
                              })
                            });

                            try {
                              const sqlUpdateResult = await sqlUpdateResponse.json();

                              if (sqlUpdateResult.success) {
                                console.log(`Successfully updated IP ${originalItem.ipmi} with direct SQL`);
                                success = true;
                                return;
                              } else {
                                console.error(`Failed to update IP ${originalItem.ipmi} with direct SQL:`, sqlUpdateResult.error || 'Unknown error');
                              }
                            } catch (jsonErr) {
                              console.error(`Error parsing SQL update response:`, jsonErr);
                            }
                          } catch (sqlErr) {
                            console.error(`Error making SQL update request:`, sqlErr);
                          }

                          // If that fails, try a custom endpoint
                          try {
                            console.log(`Trying custom update endpoint for IP ${originalItem.ipmi}...`);

                            // Use a different API endpoint that's less likely to have parsing issues
                            const customUpdateResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=update_ip_status`, {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json'
                              },
                              body: JSON.stringify({
                                token: token,
                                ip_address: originalItem.ipmi,
                                is_used: 1,
                                manual_alocation: manual_alocation,
                                notes: notes,
                                for_server_ipmi: true,
                                server_id: newId,
                                server_type: deviceType === 'blade' ? 'blade' : 'dedicated'
                              })
                            });

                            // Don't try to parse the response, just check if the request was successful
                            if (customUpdateResponse.ok) {
                              console.log(`Successfully updated IP ${originalItem.ipmi} with custom endpoint`);
                              success = true;
                            } else {
                              console.error(`Failed to update IP ${originalItem.ipmi} with custom endpoint:`, customUpdateResponse.status);

                              // As a very last resort, try a simple GET request
                              try {
                                console.log(`Trying simple GET request for IP ${originalItem.ipmi}...`);

                                // Use a simple GET request with parameters in the URL
                                const simpleGetResponse = await fetch(
                                  `${API_URL}/api_admin_inventory.php?f=mark_ip_used&token=${token}&ip=${originalItem.ipmi}&server_id=${newId}&server_type=${deviceType === 'blade' ? 'blade' : 'dedicated'}`,
                                  { method: 'GET' }
                                );

                                if (simpleGetResponse.ok) {
                                  console.log(`Successfully updated IP ${originalItem.ipmi} with simple GET request`);
                                  success = true;
                                } else {
                                  console.error(`Failed to update IP ${originalItem.ipmi} with simple GET request:`, simpleGetResponse.status);
                                }
                              } catch (getErr) {
                                console.error(`Error making simple GET request:`, getErr);
                              }
                            }
                          } catch (customErr) {
                            console.error(`Error making custom update request:`, customErr);
                          }
                        } catch (sqlErr) {
                          console.error(`Error in SQL update block:`, sqlErr);
                        }
                      }
                    } catch (err) {
                      console.error(`Error updating IP ${originalItem.ipmi} directly:`, err);
                    }
                  }
                } catch (err) {
                  console.error(`Error force allocating IPMI IP ${originalItem.ipmi}:`, err);
                }
              }

              if (!success) {
                console.error(`All attempts to allocate IPMI IP ${originalItem.ipmi} for server ${newId} failed`);

                // As an absolute last resort, try a direct database update using a custom endpoint
                try {
                  console.log(`Trying direct database update for IP ${originalItem.ipmi} as last resort...`);

                  // Create a direct update function
                  const directUpdate = async () => {
                    try {
                      // Try to create a simple update function that doesn't rely on the API
                      const updateIp = async (method) => {
                        try {
                          const token = localStorage.getItem('admin_token');
                          // Create description and notes in the same format as CountryIpSelector.js
                          const deviceType_str = 'Server';
                          const deviceLabel = originalItem.label || 'New';
                          // For IPMI IPs, set the description to "IPMI for X (ID: Y)" to match CompactSubnetTree.js display
                          const manual_alocation = `IPMI for ${deviceLabel} (ID: ${newId})`;
                          const notes = `IPMI for ${deviceLabel} (ID: ${newId})`;

                          // Use a different API endpoint
                          let url = `${API_URL}/api_admin_inventory.php?f=mark_ip_used&token=${token}&ip=${originalItem.ipmi}&server_id=${newId}&server_type=${deviceType}`;

                          // Try to directly update the database with a POST request
                          if (method === 'POST') {
                            const response = await fetch(`${API_URL}/api_admin_subnets.php?f=direct_update_ip_server`, {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json'
                              },
                              body: JSON.stringify({
                                token: token,
                                ip_address: originalItem.ipmi,
                                is_used: 1,
                                manual_alocation: manual_alocation,
                                notes: notes,
                                for_server_ipmi: true,
                                server_id: newId,
                                server_type: deviceType === 'blade' ? 'blade' : 'dedicated',
                                force: true
                              })
                            });

                            return response.ok;
                          }

                          const response = await fetch(url, { method });

                          return response.ok;
                        } catch (err) {
                          console.error(`Error in updateIp (${method}):`, err);
                          return false;
                        }
                      };

                      // Try different HTTP methods
                      const methods = ['GET', 'POST', 'PUT'];

                      for (const method of methods) {
                        const result = await updateIp(method);

                        if (result) {
                          console.log(`Successfully updated IP ${originalItem.ipmi} with ${method} request`);
                          return true;
                        }
                      }

                      return false;
                    } catch (err) {
                      console.error('Error in directUpdate:', err);
                      return false;
                    }
                  };

                  // Execute the direct update function
                  const directUpdateResult = await directUpdate();

                  if (directUpdateResult) {
                    console.log(`Successfully updated IP ${originalItem.ipmi} with direct update`);
                    success = true;
                  } else {
                    console.error(`Failed to update IP ${originalItem.ipmi} with direct update`);

                    // As a final desperate attempt, try a direct SQL update
                    try {
                      console.log(`Making final attempt to update IP ${originalItem.ipmi} with direct SQL...`);

                      const token = localStorage.getItem('admin_token');
                      // Create description and notes in the same format as CountryIpSelector.js
                      const deviceType_str = 'Server';
                      const deviceLabel = originalItem.label || 'New';
                      // For IPMI IPs, set the description to "IPMI for X (ID: Y)" to match CompactSubnetTree.js display
                      const manual_alocation = `IPMI for ${deviceLabel} (ID: ${newId})`;
                      const notes = `IPMI for ${deviceLabel} (ID: ${newId})`;

                      // Try multiple endpoints
                      const endpoints = [
                        'direct_update_ip_server',
                        'update_ip_server',
                        'force_update_ip_server',
                        'sql_update_ip_server'
                      ];

                      for (const endpoint of endpoints) {
                        try {
                          const sqlResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=${endpoint}`, {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                              token: token,
                              ip_address: originalItem.ipmi,
                              is_used: 1,
                              manual_alocation: manual_alocation,
                              notes: notes,
                              for_server_ipmi: true,
                              server_id: newId,
                              server_type: deviceType === 'blade' ? 'blade' : 'dedicated',
                              force: true
                            })
                          });

                          if (sqlResponse.ok) {
                            console.log(`Successfully updated IP ${originalItem.ipmi} with ${endpoint}`);
                            success = true;
                            break;
                          }
                        } catch (endpointErr) {
                          console.error(`Error with endpoint ${endpoint}:`, endpointErr);
                        }
                      }

                      // If all else fails, try a direct database query
                      if (!success) {
                        try {
                          const directQueryResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=execute_sql`, {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                              token: token,
                              query: `UPDATE ips SET is_used = 1, description = '${description.replace(/'/g, "\\'")}', notes = '${notes.replace(/'/g, "\\'")}', server_id = ${newId}, server_type = '${deviceType === 'blade' ? 'blade' : 'dedicated'}' WHERE ip_address = '${originalItem.ipmi}'`
                            })
                          });

                          if (directQueryResponse.ok) {
                            console.log(`Successfully updated IP ${originalItem.ipmi} with direct SQL query`);
                            success = true;
                          }
                        } catch (queryErr) {
                          console.error(`Error with direct SQL query:`, queryErr);
                        }
                      }
                    } catch (finalErr) {
                      console.error(`Error in final attempt:`, finalErr);
                    }
                  }
                } catch (err) {
                  console.error(`Error in direct database update:`, err);
                }
              }
            }

            // Handle switch IPs
            if (deviceType === 'switch' && originalItem.switch_ip && originalItem.switch_ip.trim()) {
              // Try multiple times with different approaches
              let success = await allocateIp(originalItem.switch_ip, 'switch', newId, originalItem.label);

              if (!success) {
                console.error(`Failed to allocate IP ${originalItem.switch_ip} for switch ${newId}, trying direct force allocation`);

                // Try direct force allocation as a last resort
                try {
                  const token = localStorage.getItem('admin_token');
                  // Create description and notes in the same format as CountryIpSelector.js
                  const deviceType_str = 'Switch';
                  const deviceLabel = originalItem.label || 'New';
                  const manual_alocation = `${deviceType_str} ${deviceLabel} (ID: ${newId})`;
                  const notes = `${deviceLabel} (ID: ${newId})`;

                  const forceAllocateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=force_allocate_ip`, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                      token: token,
                      ip_address: originalItem.switch_ip,
                      manual_alocation: manual_alocation,
                      notes: notes,
                      for_switch: true,
                      switch_id: newId
                    })
                  });

                  const forceAllocateResult = await forceAllocateResponse.json();

                  if (forceAllocateResult.success) {
                    console.log(`Successfully force allocated IP ${originalItem.switch_ip} for switch ${newId}`);
                    success = true;
                  } else {
                    console.error(`Failed to force allocate IP ${originalItem.switch_ip}:`, forceAllocateResult.error || 'Unknown error');

                    // Try one more approach - direct database update
                    try {
                      console.log(`Trying direct database update for IP ${originalItem.switch_ip}...`);

                      const directUpdateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=direct_update_ip`, {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                          token: token,
                          ip_address: originalItem.switch_ip,
                          is_used: 1,
                          manual_alocation: manual_alocation,
                          notes: notes,
                          for_switch: true,
                          switch_id: newId
                        })
                      });

                      const directUpdateResult = await directUpdateResponse.json();

                      if (directUpdateResult.success) {
                        console.log(`Successfully updated IP ${originalItem.switch_ip} directly in database`);
                        success = true;
                      } else {
                        console.error(`Failed to update IP ${originalItem.switch_ip} directly:`, directUpdateResult.error || 'Unknown error');
                      }
                    } catch (err) {
                      console.error(`Error updating IP ${originalItem.switch_ip} directly:`, err);
                    }
                  }
                } catch (err) {
                  console.error(`Error force allocating IP ${originalItem.switch_ip}:`, err);
                }
              }

              if (!success) {
                console.error(`All attempts to allocate IP ${originalItem.switch_ip} for switch ${newId} failed`);

                // As an absolute last resort, try a direct database update using a custom endpoint
                try {
                  console.log(`Trying direct database update for IP ${originalItem.switch_ip} as last resort...`);

                  // Create a direct update function
                  const directUpdate = async () => {
                    try {
                      // Try to create a simple update function that doesn't rely on the API
                      const updateIp = async (method) => {
                        try {
                          const token = localStorage.getItem('admin_token');
                          // Create description and notes - assigned_to should only contain the device ID
                          const deviceLabel = originalItem.label || 'New';
                          // assigned_to field contains only the device ID for clean data management
                          const manual_alocation = newId ? newId.toString() : 'unknown';
                          // notes field can contain descriptive information
                          const notes = `Switch ${deviceLabel} (ID: ${newId})`;

                          // Use a different API endpoint
                          let url = `${API_URL}/api_admin_inventory.php?f=mark_ip_used&token=${token}&ip=${originalItem.switch_ip}&switch_id=${newId}`;

                          // Try to directly update the database with a POST request
                          if (method === 'POST') {
                            const response = await fetch(`${API_URL}/api_admin_subnets.php?f=direct_update_ip_switch`, {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json'
                              },
                              body: JSON.stringify({
                                token: token,
                                ip_address: originalItem.switch_ip,
                                is_used: 1,
                                manual_alocation: manual_alocation,
                                notes: notes,
                                for_switch: true,
                                switch_id: newId,
                                force: true
                              })
                            });

                            return response.ok;
                          }

                          const response = await fetch(url, { method });

                          return response.ok;
                        } catch (err) {
                          console.error(`Error in updateIp (${method}):`, err);
                          return false;
                        }
                      };

                      // Try different HTTP methods
                      const methods = ['GET', 'POST', 'PUT'];

                      for (const method of methods) {
                        const result = await updateIp(method);

                        if (result) {
                          console.log(`Successfully updated IP ${originalItem.switch_ip} with ${method} request`);
                          return true;
                        }
                      }

                      return false;
                    } catch (err) {
                      console.error('Error in directUpdate:', err);
                      return false;
                    }
                  };

                  // Execute the direct update function
                  const directUpdateResult = await directUpdate();

                  if (directUpdateResult) {
                    console.log(`Successfully updated IP ${originalItem.switch_ip} with direct update`);
                    success = true;
                  } else {
                    console.error(`Failed to update IP ${originalItem.switch_ip} with direct update`);

                    // As a final desperate attempt, try a direct SQL update
                    try {
                      console.log(`Making final attempt to update IP ${originalItem.switch_ip} with direct SQL...`);

                      const token = localStorage.getItem('admin_token');
                      // Create description and notes - assigned_to should only contain the device ID
                      const deviceLabel = originalItem.label || 'New';
                      // assigned_to field contains only the device ID for clean data management
                      const manual_alocation = newId ? newId.toString() : 'unknown';
                      // notes field can contain descriptive information
                      const notes = `Switch ${deviceLabel} (ID: ${newId})`;

                      // Try multiple endpoints
                      const endpoints = [
                        'direct_update_ip_switch',
                        'update_ip_switch',
                        'force_update_ip_switch',
                        'sql_update_ip_switch'
                      ];

                      for (const endpoint of endpoints) {
                        try {
                          const sqlResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=${endpoint}`, {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                              token: token,
                              ip_address: originalItem.switch_ip,
                              is_used: 1,
                              manual_alocation: manual_alocation,
                              notes: notes,
                              for_switch: true,
                              switch_id: newId,
                              force: true
                            })
                          });

                          if (sqlResponse.ok) {
                            console.log(`Successfully updated IP ${originalItem.switch_ip} with ${endpoint}`);
                            success = true;
                            break;
                          }
                        } catch (endpointErr) {
                          console.error(`Error with endpoint ${endpoint}:`, endpointErr);
                        }
                      }

                      // If all else fails, try a direct database query
                      if (!success) {
                        try {
                          const directQueryResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=execute_sql`, {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                              token: token,
                              query: `UPDATE ips SET is_used = 1, description = '${description.replace(/'/g, "\\'")}', notes = '${notes.replace(/'/g, "\\'")}', switch_id = ${newId} WHERE ip_address = '${originalItem.switch_ip}'`
                            })
                          });

                          if (directQueryResponse.ok) {
                            console.log(`Successfully updated IP ${originalItem.switch_ip} with direct SQL query`);
                            success = true;
                          }
                        } catch (queryErr) {
                          console.error(`Error with direct SQL query:`, queryErr);
                        }
                      }
                    } catch (finalErr) {
                      console.error(`Error in final attempt:`, finalErr);
                    }
                  }
                } catch (err) {
                  console.error(`Error in direct database update:`, err);
                }
              }
            }
          } else {
            console.warn(`Item ${originalItem.label} added but no ID was returned`);

            // Try to allocate IPs anyway without an ID
            if ((deviceType === 'blade' || deviceType === 'dedicated') && originalItem.ipmi) {
              await allocateIp(originalItem.ipmi, 'server', null, originalItem.label);
            }

            if (deviceType === 'switch' && originalItem.switch_ip) {
              await allocateIp(originalItem.switch_ip, 'switch', null, originalItem.label);
            }
          }
        }
      } else {
        console.warn('Could not get added items with IDs');

        // Fall back to allocating IPs without IDs
        for (const item of itemsToAdd) {
          // Handle IPMI IPs for servers
          if ((deviceType === 'blade' || deviceType === 'dedicated') && item.ipmi) {
            await allocateIp(item.ipmi, 'server', null, item.label);
          }

          // Handle switch IPs
          if (deviceType === 'switch' && item.switch_ip) {
            await allocateIp(item.switch_ip, 'switch', null, item.label);
          }
        }
      }

      onClose();
    } catch (error) {
      console.error(`Error adding ${deviceType}:`, error);
      alert(`Failed to add ${deviceType}: ${error.message}`);
    } finally {
      setLoading(false);
    }
  }

  // Function to allocate an IP address
  async function allocateIp(ipAddress, deviceType, deviceId, deviceLabel) {
    if (!ipAddress) return;

    console.log(`Allocating ${ipAddress} for ${deviceType} ${deviceId || 'new'} (${deviceLabel})`);

    try {
      const token = localStorage.getItem('admin_token');

      // First, find the subnet ID for this IP
      const subnetsResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnets`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      const subnets = await subnetsResponse.json();

      if (!Array.isArray(subnets)) {
        console.error('Failed to fetch subnets');
        return false;
      }

      // Search for the IP in each subnet
      let foundSubnet = false;
      let foundSubnetId = null;

      for (const subnet of subnets) {
        try {
          // Extract subnet ID
          const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
            ? subnet.id.replace('SUB-', '')
            : subnet.id;

          const ipResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnet_ips`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              subnet_id: subnetId,
              token: token
            })
          });

          const ipData = await ipResponse.json();

          if (ipData && ipData.success) {
            // Check if the IP exists in this subnet
            const ipInfo = ipData.ips.find(ip => ip.ip_address === ipAddress);

            if (ipInfo) {
              foundSubnet = true;
              foundSubnetId = subnetId;
              console.log(`Found IP ${ipAddress} in subnet ${subnet.subnet || subnet.cidr || 'Unnamed'} (ID: ${subnetId})`);
              break;
            }
          }
        } catch (err) {
          console.error(`Error checking subnet ${subnet.id}:`, err);
        }
      }

      if (foundSubnet && foundSubnetId) {
        // Create description and notes - assigned_to should only contain the device ID
        const isServer = deviceType === 'server';
        const isSwitch = deviceType === 'switch';

        // assigned_to field contains only the device ID for clean data management
        const manual_alocation = deviceId ? deviceId.toString() : 'unknown';
        
        // notes field can contain descriptive information
        const notes = isServer ? `IPMI for ${deviceLabel} (ID: ${deviceId})` : `${deviceType === 'switch' ? 'Switch' : 'Device'} ${deviceLabel} (ID: ${deviceId})`;

        console.log(`Setting assigned_to: "${manual_alocation}" and notes: "${notes}" for IP ${ipAddress}`);

        // First, try to deallocate the IP if it's already in use
        try {
          console.log(`Deallocating IP ${ipAddress} before allocation`);

          const deallocateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=force_deallocate_ip`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              token: token,
              ip_address: ipAddress,
              for_server_ipmi: deviceType === 'server',
              for_switch: deviceType === 'switch'
            })
          });

          const deallocateResult = await deallocateResponse.json();

          if (deallocateResult.success) {
            console.log(`Successfully deallocated IP ${ipAddress}`);
          } else {
            console.log(`IP ${ipAddress} was not allocated or deallocation failed:`, deallocateResult.error || 'Unknown error');
          }
        } catch (err) {
          console.error(`Error deallocating IP ${ipAddress}:`, err);
        }

        // Now allocate the IP
        console.log(`Allocating IP ${ipAddress} in subnet ${foundSubnetId} for ${deviceType} ${deviceId || 'new'}`);

        // Prepare API request data
        const requestData = {
          token: token,
          subnet_id: foundSubnetId,
          ip_address: ipAddress,
          manual_alocation: manual_alocation, // This is the required field for the API

          notes: notes,
          for_server_ipmi: deviceType === 'server',
          for_switch: deviceType === 'switch',
          server_id: deviceType === 'server' ? deviceId : undefined,
          switch_id: deviceType === 'switch' ? deviceId : undefined
        };

        // Add server_type if this is a server
        if (deviceType === 'server') {
          // Get the current device type from the parent component
          const serverType = window.currentDeviceType || deviceType;
          requestData.server_type = serverType === 'blade' ? 'blade' : 'dedicated';
        }

        console.log('Sending IP allocation request:', requestData);

        // Call the API to allocate the IP
        let allocateResult = { success: false, error: 'Unknown error' };

        try {
          const allocateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=allocate_ip`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
          });

          allocateResult = await allocateResponse.json();
        } catch (err) {
          console.error(`Error parsing allocate_ip response:`, err);
          allocateResult = {
            success: false,
            error: `Failed to parse response: ${err.message || 'Unknown error'}`
          };
        }

        if (allocateResult.success) {
          console.log(`Successfully allocated IP ${ipAddress} for ${deviceType} ${deviceId || 'new'}`);
          
          // Log additional success information
          if (deviceType === 'server' && allocateResult.server_updated) {
            console.log(`✅ Server IPMI updated in database for ${deviceType} ${deviceId}`);
          }
          
          if (deviceType === 'switch' && allocateResult.switch_updated) {
            console.log(`✅ Switch IP updated in inventory_switches table for switch ${deviceId}`);
          }
          
          console.log(`✅ IP marked as used (is_used = 1) in ip_addresses table: ${ipAddress}`);
          
          return true;
        } else {
          console.error(`Failed to allocate IP ${ipAddress}:`, allocateResult.error || 'Unknown error');

          // Try using the direct update method as a fallback
          console.log(`Trying direct update method for IP ${ipAddress}...`);

          const updateData = {
            token: token,
            subnet_id: foundSubnetId,
            ip_address: ipAddress,
            manual_alocation: manual_alocation, // This is the required field for the API
            is_used: 1,

            notes: notes,
            for_server_ipmi: deviceType === 'server',
            for_switch: deviceType === 'switch',
            server_id: deviceType === 'server' ? deviceId : undefined,
            switch_id: deviceType === 'switch' ? deviceId : undefined
          };

          // Add server_type if this is a server
          if (deviceType === 'server') {
            // Get the current device type from the parent component
            const serverType = window.currentDeviceType || deviceType;
            updateData.server_type = serverType === 'blade' ? 'blade' : 'dedicated';
          }

          let updateResult = { success: false, error: 'Unknown error' };

          try {
            const updateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=update_ip_status`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(updateData)
            });

            updateResult = await updateResponse.json();
          } catch (err) {
            console.error(`Error parsing update_ip_status response:`, err);
            updateResult = {
              success: false,
              error: `Failed to parse response: ${err.message || 'Unknown error'}`
            };
          }

          if (updateResult.success) {
            console.log(`Successfully updated IP ${ipAddress} status to used`);
            return true;
          } else {
            console.error(`Failed to update IP ${ipAddress} status:`, updateResult.error || 'Unknown error');
          }
        }
      } else {
        console.error(`Could not find subnet for IP ${ipAddress}`);

        // As a last resort, try to directly mark the IP as used without knowing the subnet
        try {
          console.log(`Trying to mark IP ${ipAddress} as used without subnet information...`);

          // Create description and notes - assigned_to should only contain the device ID
          const isServer = deviceType === 'server';
          const isSwitch = deviceType === 'switch';

          // assigned_to field contains only the device ID for clean data management
          const manual_alocation = deviceId ? deviceId.toString() : 'unknown';
          
          // notes field can contain descriptive information
          const notes = isServer ? `IPMI for ${deviceLabel} (ID: ${deviceId})` : `${deviceType === 'switch' ? 'Switch' : 'Device'} ${deviceLabel} (ID: ${deviceId})`;

          // Prepare the request data
          const directUpdateData = {
            token: token,
            ip_address: ipAddress,
            manual_alocation: manual_alocation,
            notes: notes,
            for_server_ipmi: deviceType === 'server',
            for_switch: deviceType === 'switch',
            server_id: deviceType === 'server' ? deviceId : undefined,
            switch_id: deviceType === 'switch' ? deviceId : undefined
          };

          // Add server_type if this is a server
          if (deviceType === 'server') {
            // Get the current device type from the parent component
            const serverType = window.currentDeviceType || deviceType;
            directUpdateData.server_type = serverType === 'blade' ? 'blade' : 'dedicated';
          }

          // Try the mark_ip_used endpoint
          let directUpdateResult = { success: false, error: 'Unknown error' };

          try {
            const directUpdateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=mark_ip_used`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(directUpdateData)
            });

            directUpdateResult = await directUpdateResponse.json();
          } catch (err) {
            console.error(`Error parsing mark_ip_used response:`, err);
            directUpdateResult = {
              success: false,
              error: `Failed to parse response: ${err.message || 'Unknown error'}`
            };
          }

          if (directUpdateResult.success) {
            console.log(`Successfully marked IP ${ipAddress} as used without subnet information`);
            return true;
          } else {
            console.error(`Failed to mark IP ${ipAddress} as used:`, directUpdateResult.error || 'Unknown error');

            // Try the force_allocate_ip endpoint as a last resort
            console.log(`Trying force_allocate_ip for IP ${ipAddress}...`);

            // Prepare the force allocate data
            const forceAllocateData = {
              token: token,
              ip_address: ipAddress,
              manual_alocation: manual_alocation,
              notes: notes,
              for_server_ipmi: deviceType === 'server',
              for_switch: deviceType === 'switch',
              server_id: deviceType === 'server' ? deviceId : undefined,
              switch_id: deviceType === 'switch' ? deviceId : undefined
            };

            // Add server_type if this is a server
            if (deviceType === 'server') {
              // Get the current device type from the parent component
              const serverType = window.currentDeviceType || deviceType;
              forceAllocateData.server_type = serverType === 'blade' ? 'blade' : 'dedicated';
            }

            let forceAllocateResult = { success: false, error: 'Unknown error' };

            try {
              const forceAllocateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=force_allocate_ip`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify(forceAllocateData)
              });

              forceAllocateResult = await forceAllocateResponse.json();
            } catch (err) {
              console.error(`Error parsing force_allocate_ip response:`, err);
              forceAllocateResult = {
                success: false,
                error: `Failed to parse response: ${err.message || 'Unknown error'}`
              };
            }

            if (forceAllocateResult.success) {
              console.log(`Successfully force allocated IP ${ipAddress}`);
              return true;
            } else {
              console.error(`Failed to force allocate IP ${ipAddress}:`, forceAllocateResult.error || 'Unknown error');

              // Try one more approach - direct database update
              try {
                console.log(`Trying direct database update for IP ${ipAddress}...`);

                // Prepare the direct update data
                const directDbUpdateData = {
                  token: token,
                  ip_address: ipAddress,
                  is_used: 1,
                  manual_alocation: manual_alocation,
                  notes: notes,
                  for_server_ipmi: deviceType === 'server',
                  for_switch: deviceType === 'switch',
                  server_id: deviceType === 'server' ? deviceId : undefined,
                  switch_id: deviceType === 'switch' ? deviceId : undefined
                };

                // Add server_type if this is a server
                if (deviceType === 'server') {
                  // Get the current device type from the parent component
                  const serverType = window.currentDeviceType || deviceType;
                  directDbUpdateData.server_type = serverType === 'blade' ? 'blade' : 'dedicated';
                }

                let directDbUpdateResult = { success: false, error: 'Unknown error' };

                try {
                  const directDbUpdateResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=direct_update_ip`, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(directDbUpdateData)
                  });

                  directDbUpdateResult = await directDbUpdateResponse.json();
                } catch (err) {
                  console.error(`Error parsing direct_update_ip response:`, err);
                  directDbUpdateResult = {
                    success: false,
                    error: `Failed to parse response: ${err.message || 'Unknown error'}`
                  };
                }

                if (directDbUpdateResult.success) {
                  console.log(`Successfully updated IP ${ipAddress} directly in database`);
                  return true;
                } else {
                  console.error(`Failed to update IP ${ipAddress} directly:`, directDbUpdateResult.error || 'Unknown error');

                  // Try a custom endpoint as a last resort
                  try {
                    console.log(`Trying custom update endpoint for IP ${ipAddress}...`);

                    // Use a different API endpoint that's less likely to have parsing issues
                    const customUpdateResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=update_ip_status`, {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json'
                      },
                      body: JSON.stringify({
                        token: token,
                        ip_address: ipAddress,
                        is_used: 1,
                        manual_alocation: manual_alocation,
                        notes: notes,
                        for_server_ipmi: deviceType === 'server',
                        for_switch: deviceType === 'switch',
                        server_id: deviceType === 'server' ? deviceId : undefined,
                        switch_id: deviceType === 'switch' ? deviceId : undefined,
                        server_type: deviceType === 'server' ? (window.currentDeviceType === 'blade' ? 'blade' : 'dedicated') : undefined
                      })
                    });

                    // Don't try to parse the response, just check if the request was successful
                    if (customUpdateResponse.ok) {
                      console.log(`Successfully updated IP ${ipAddress} with custom endpoint`);
                      return true;
                    } else {
                      console.error(`Failed to update IP ${ipAddress} with custom endpoint:`, customUpdateResponse.status);

                      // As a very last resort, try a simple GET request
                      try {
                        console.log(`Trying simple GET request for IP ${ipAddress}...`);

                        // Use a simple GET request with parameters in the URL
                        let url = `${API_URL}/api_admin_inventory.php?f=mark_ip_used&token=${token}&ip=${ipAddress}`;

                        if (deviceType === 'server') {
                          url += `&server_id=${deviceId || 0}&server_type=${window.currentDeviceType === 'blade' ? 'blade' : 'dedicated'}`;
                        } else if (deviceType === 'switch') {
                          url += `&switch_id=${deviceId || 0}`;
                        }

                        const simpleGetResponse = await fetch(url, { method: 'GET' });

                        if (simpleGetResponse.ok) {
                          console.log(`Successfully updated IP ${ipAddress} with simple GET request`);
                          return true;
                        } else {
                          console.error(`Failed to update IP ${ipAddress} with simple GET request:`, simpleGetResponse.status);

                          // Try a direct SQL update as an absolute last resort
                          try {
                            console.log(`Trying direct SQL update for IP ${ipAddress}...`);

                            // Determine which endpoint to use based on device type
                            const endpoint = deviceType === 'server' ? 'direct_update_ip_server' : 'direct_update_ip_switch';

                            // Prepare the request data
                            const sqlData = {
                              token: token,
                              ip_address: ipAddress,
                              is_used: 1,
                              manual_alocation: manual_alocation,
                              notes: notes,
                              force: true
                            };

                            // Add device-specific parameters
                            if (deviceType === 'server') {
                              sqlData.for_server_ipmi = true;
                              sqlData.server_id = deviceId;
                              sqlData.server_type = window.currentDeviceType === 'blade' ? 'blade' : 'dedicated';
                            } else if (deviceType === 'switch') {
                              sqlData.for_switch = true;
                              sqlData.switch_id = deviceId;
                            }

                            // Make the request
                            const sqlResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=${endpoint}`, {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json'
                              },
                              body: JSON.stringify(sqlData)
                            });

                            if (sqlResponse.ok) {
                              console.log(`Successfully updated IP ${ipAddress} with direct SQL update`);
                              return true;
                            } else {
                              console.error(`Failed to update IP ${ipAddress} with direct SQL update:`, sqlResponse.status);
                            }
                          } catch (sqlErr) {
                            console.error(`Error making direct SQL update:`, sqlErr);
                          }
                        }
                      } catch (getErr) {
                        console.error(`Error making simple GET request:`, getErr);
                      }
                    }
                  } catch (customErr) {
                    console.error(`Error making custom update request:`, customErr);
                  }
                }
              } catch (err) {
                console.error(`Error updating IP ${ipAddress} directly:`, err);
              }
            }
          }
        } catch (err) {
          console.error(`Error marking IP ${ipAddress} as used:`, err);
        }
      }

      return false;
    } catch (err) {
      console.error(`Error allocating IP ${ipAddress}:`, err);
      return false;
    }
  }

  // Get items for current page
  const getCurrentPageItems = () => {
    const start = currentPage * itemsPerPage;
    const end = start + itemsPerPage;
    return items.slice(start, end);
  };

  // Calculate page count
  const pageCount = Math.ceil(items.length / itemsPerPage);

  // Next/previous page handlers
  const goToNextPage = () => {
    if (currentPage < pageCount - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  if (!isOpen) return null;

  // Helper function to get display name for device type
  const getDeviceTypeName = () => {
    switch (deviceType) {
      case 'blade': return 'Blade Server';
      case 'dedicated': return 'Dedicated Server';
      case 'chassis': return 'Chassis';
      case 'switch': return 'Switch';
      default: return 'Device';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-2">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-6xl max-h-[95vh] flex flex-col">
        {/* Header */}
        <div className="p-4 border-b flex justify-between items-center sticky top-0 bg-white z-10">
          <h2 className="text-lg font-bold text-gray-800">
            Bulk Add {getDeviceTypeName()}s
          </h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <XCircle className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Loading state */}
        {(fetchingData.cpus || fetchingData.switches || fetchingData.chassis || fetchingData.ramConfigurations) && (
          <div className="bg-blue-50 text-blue-700 p-2 text-sm flex items-center">
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            Loading reference data...
          </div>
        )}

        {/* Tabs */}
<div className="flex border-b">
  <button
    className={`py-2 px-4 font-medium text-sm ${activeTab === 'basic'
      ? 'text-indigo-700 border-b-2 border-indigo-700'
      : 'text-gray-500 hover:text-gray-700'}`}
    onClick={() => setActiveTab('basic')}
  >
    {deviceType === 'switch' ? <Network className="w-4 h-4 inline mr-1" /> :
     deviceType === 'chassis' ? <HardDrive className="w-4 h-4 inline mr-1" /> :
     <Server className="w-4 h-4 inline mr-1" />}
    Basic Info
  </button>

  {(deviceType === 'blade' || deviceType === 'dedicated') && (
    <button
      className={`py-2 px-4 font-medium text-sm ${activeTab === 'network'
        ? 'text-indigo-700 border-b-2 border-indigo-700'
        : 'text-gray-500 hover:text-gray-700'}`}
      onClick={() => setActiveTab('network')}
    >
      <Network className="w-4 h-4 inline mr-1" />
      Network
    </button>
  )}

  {/* Only show Location tab for non-blade device types */}
  {deviceType !== 'blade' && (
    <button
      className={`py-2 px-4 font-medium text-sm ${activeTab === 'location'
        ? 'text-indigo-700 border-b-2 border-indigo-700'
        : 'text-gray-500 hover:text-gray-700'}`}
      onClick={() => setActiveTab('location')}
    >
      <Globe className="w-4 h-4 inline mr-1" />
      Location
    </button>
  )}

  {deviceType === 'blade' && (
    <button
      className={`py-2 px-4 font-medium text-sm ${activeTab === 'chassis'
        ? 'text-indigo-700 border-b-2 border-indigo-700'
        : 'text-gray-500 hover:text-gray-700'}`}
      onClick={() => setActiveTab('chassis')}
    >
      <HardDrive className="w-4 h-4 inline mr-1" />
      Chassis
    </button>
  )}

  {(deviceType === 'blade' || deviceType === 'dedicated') && (
    <button
      className={`py-2 px-4 font-medium text-sm ${activeTab === 'ipmi'
        ? 'text-indigo-700 border-b-2 border-indigo-700'
        : 'text-gray-500 hover:text-gray-700'}`}
      onClick={() => setActiveTab('ipmi')}
    >
      <Cpu className="w-4 h-4 inline mr-1" />
      IPMI
    </button>
  )}

{deviceType === 'switch' && (
    <button
      className={`py-2 px-4 font-medium text-sm ${activeTab === 'snmp'
        ? 'text-indigo-700 border-b-2 border-indigo-700'
        : 'text-gray-500 hover:text-gray-700'}`}
      onClick={() => setActiveTab('snmp')}
    >
      <Wifi className="w-4 h-4 inline mr-1" />
      SNMP Settings
    </button>
  )}

</div>
{/* Table Toolbar */}
<div className="bg-gray-50 p-3 border-b flex flex-wrap justify-between items-center">
  <div className="flex flex-wrap gap-2 items-center">
    <button
      onClick={() => addRows(1)}
      className="px-3 py-1.5 bg-indigo-700 text-white rounded-md flex items-center text-sm hover:bg-indigo-800"
    >
      <Plus className="w-4 h-4 mr-1" />
      Add Row
    </button>

    <button
      onClick={() => addRows(5)}
      className="px-3 py-1.5 bg-indigo-600 text-white rounded-md flex items-center text-sm hover:bg-indigo-700"
    >
      <Layout className="w-4 h-4 mr-1" />
      Add 5 Rows
    </button>

    {/* Only show Generate Sequential Names button in the Basic Info tab */}
    {activeTab === 'basic' && (
      <button
        onClick={generateSequentialNames}
        className="px-3 py-1.5 border border-gray-300 bg-white rounded-md flex items-center text-sm hover:bg-gray-50"
      >
        Generate Sequential Names
      </button>
    )}

    {(deviceType === 'blade' || deviceType === 'dedicated') && activeTab === 'ipmi' && (
      <button
        onClick={detectAllMacAddresses}
        disabled={processingMacs}
        className={`px-3 py-1.5 border ${processingMacs ? 'bg-gray-100 text-gray-500' : 'bg-white text-gray-700 hover:bg-gray-50'} rounded-md flex items-center text-sm`}
      >
        {processingMacs ?
          <RefreshCw className="w-4 h-4 mr-1 animate-spin" /> :
          <Wifi className="w-4 h-4 mr-1" />
        }
        Detect MAC Addresses
      </button>
    )}

    {templateItem && (
      <div className="flex gap-2">
        <button
          onClick={applyTemplateToAll}
          className="px-3 py-1.5 border border-gray-300 bg-white rounded-md flex items-center text-sm hover:bg-gray-50"
        >
          <Copy className="w-4 h-4 mr-1" />
          Apply Template to All
        </button>

        <div className="px-3 py-1.5 text-xs text-gray-600 bg-gray-100 rounded flex items-center">
          <span className="font-medium">Template Active</span>
        </div>
      </div>
    )}
  </div>

  {/* Pagination Controls */}
  {items.length > itemsPerPage && (
    <div className="flex items-center text-sm text-gray-500">
      <span className="mr-2">Page</span>
      <button
        onClick={goToPrevPage}
        disabled={currentPage === 0}
        className={`p-1 rounded ${currentPage === 0 ? 'text-gray-400' : 'text-gray-700 hover:bg-gray-200'}`}
      >
        <ChevronLeft className="w-4 h-4" />
      </button>
      <span className="mx-1 font-medium">{currentPage + 1}</span>
      <button
        onClick={goToNextPage}
        disabled={currentPage === pageCount - 1}
        className={`p-1 rounded ${currentPage === pageCount - 1 ? 'text-gray-400' : 'text-gray-700 hover:bg-gray-200'}`}
      >
        <ChevronRight className="w-4 h-4" />
      </button>
      <span className="ml-1">of {pageCount}</span>
      <span className="ml-2 text-gray-400">({items.length} items)</span>
    </div>
  )}
</div>

        {/* Table Container */}
        <div className="overflow-auto flex-grow p-4">
          {/* Basic Info Tab - Different for each device type */}
          {activeTab === 'basic' && (
            <table className="w-full">
              <thead>
                <tr className="text-xs text-gray-500 border-b">
                  <th className="p-2 text-left font-medium w-12">#</th>
                  <th className="p-2 text-left font-medium">Label*</th>

                  {/* Device-specific columns */}
                  {(deviceType === 'blade' || deviceType === 'dedicated') && (
                    <>
                      <th className="p-2 text-left font-medium">CPU*</th>
                      <th className="p-2 text-left font-medium">RAM</th>
                    </>
                  )}

                  {deviceType === 'chassis' && (
  <>
    <th className="p-2 text-left font-medium">Model*</th>

  </>
)}

                  {deviceType === 'switch' && (
  <>
    <th className="p-2 text-left font-medium">IP Address*</th>
    <th className="p-2 text-left font-medium">Password*</th>
    <th className="p-2 text-left font-medium">Model*</th>
  </>
)}


                  <th className="p-2 text-left font-medium">Status</th>
                  <th className="p-2 text-left font-medium w-20">Actions</th>
                </tr>
              </thead>
              <tbody>
                {getCurrentPageItems().map((item, index) => {
                  const itemIndex = currentPage * itemsPerPage + index;
                  return (
                    <tr key={itemIndex} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} h-14`}>
                      <td className="p-2">{itemIndex + 1}</td>
                      <td className="p-2">
                        <input
                          type="text"
                          value={item.label}
                          onChange={(e) => handleItemChange(itemIndex, 'label', e.target.value)}
                          className={`w-full p-1.5 border ${
                            errorMessages[itemIndex]?.label ? 'border-red-500' : 'border-gray-300'
                          } rounded-md text-sm`}
                          placeholder={`${getDeviceTypeName()} Label`}
                        />
                        {errorMessages[itemIndex]?.label && (
                          <div className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].label}</div>
                        )}
                      </td>

                      {/* Device-specific fields */}
                      {(deviceType === 'blade' || deviceType === 'dedicated') && (
                        <>
                          <td className="p-2">
                            <div className="flex">
                            <select
  value={item.cpu || ''}
  onChange={(e) => handleItemChange(itemIndex, 'cpu', e.target.value)}
  className={`w-full p-1.5 border ${
    errorMessages[itemIndex]?.cpu ? 'border-red-500' : 'border-gray-300'
  } rounded-md text-sm`}
>
  <option value="">Select CPU Model</option>
  {cpuOptions.map(cpu => (
    <option key={cpu.id} value={cpu.id}>{cpu.cpu}</option>
  ))}
</select>
                              <button
                                type="button"
                                onClick={() => setShowAddCpuModal(true)}
                                className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
                                title="Add New CPU Model"
                              >
                                <Plus className="w-4 h-4" />
                              </button>
                            </div>
                            {errorMessages[itemIndex]?.cpu && (
                              <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].cpu}</p>
                            )}
                          </td>
                          <td className="p-2">
                            <div className="flex">
                            <select
  value={item.ram || ''}
  onChange={(e) => handleItemChange(itemIndex, 'ram', e.target.value)}
  className="w-full p-1.5 border border-gray-300 rounded-md text-sm"
>
  <option value="">Select RAM Configuration</option>
  {ramConfigurations.map(ram => (
    <option key={ram.id} value={ram.id}>{ram.description}</option>
  ))}
</select>
                              <button
                                type="button"
                                onClick={() => setShowAddRamModal(true)}
                                className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
                                title="Add New RAM Configuration"
                              >
                                <Plus className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </>
                      )}

                      {deviceType === 'chassis' && (
  <>
    <td className="p-2">

{/* Replace your existing chassis model dropdown with this */}
<div className="flex">
  <select
    value={item.model_id || ''}
    onChange={(e) => handleChassisModelChange(itemIndex, e.target.value)}
    className={`w-full p-1.5 border ${
      errorMessages[itemIndex]?.model_id ? 'border-red-500' : 'border-gray-300'
    } rounded-md text-sm`}
  >
    <option value="">Select Chassis Model</option>
    {fetchingData.chassisModels ? (
      <option disabled>Loading models...</option>
    ) : chassisModels.length > 0 ? (
      chassisModels.map(model => (
        <option key={model.id} value={model.id}>
          {model.name} ({model.size}U, {model.bay_count} bays)
        </option>
      ))
    ) : (
      <option disabled>No models available</option>
    )}
  </select>
  <button
    type="button"
    onClick={() => setShowAddChassisModelModal(true)}
    className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
    title="Add New Chassis Model"
  >
    <Plus className="w-4 h-4" />
  </button>
</div>
    </td>

  </>
)}

                      {deviceType === 'switch' && (
  <>
                        <td className="p-2">
                      <DirectIpSelector
                        index={itemIndex}
                        value={item.switch_ip}
                        onChange={handleItemChange}
                        cityId={item.city_id}
                        deviceType="switch"
                        placeholder="Select Switch IP address"
                        cities={cities}
                        excludeIps={items
                          .filter((otherItem, otherIndex) => otherIndex !== itemIndex && otherItem.switch_ip)
                          .map(otherItem => otherItem.switch_ip)
                        }
                      />
                      {errorMessages[itemIndex]?.switch_ip && (
                        <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].switch_ip}</p>
                      )}
 
                    </td>
    {/* Add this new field */}

    <td className="p-2">
      <input
        type="text"
        value={item.root_password}
        onChange={(e) => handleItemChange(itemIndex, 'root_password', e.target.value)}
        className={`w-full p-1.5 border ${
          errorMessages[itemIndex]?.root_password ? 'border-red-500' : 'border-gray-300'
        } rounded-md text-sm`}

      />
      {errorMessages[itemIndex]?.root_password && (
        <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].root_password}</p>
      )}
    </td>


    <td className="p-2">
  <div className="flex">
    <select
      value={item.model_id || ''}
      onChange={(e) => handleSwitchModelChange(itemIndex, e.target.value)}
      className={`w-full p-1.5 border ${
        errorMessages[itemIndex]?.model_id ? 'border-red-500' : 'border-gray-300'
      } rounded-md text-sm`}
    >
      <option value="">Select Switch Model</option>
      {fetchingData.switchModels ? (
        <option disabled>Loading models...</option>
      ) : switchModels.length > 0 ? (
        switchModels.map(model => (
          <option key={model.id} value={model.id}>
            {model.name} ({model.size}U)
          </option>
        ))
      ) : (
        <option disabled>No models available</option>
      )}
    </select>
    <button
      type="button"
      onClick={() => setShowAddSwitchModelModal(true)}
      className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
      title="Add New Switch Model"
    >
      <Plus className="w-4 h-4" />
    </button>
  </div>
  {errorMessages[itemIndex]?.model_id && (
    <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].model_id}</p>
  )}
</td>

  </>
)}

                      <td className="p-2">
                        <select
                          value={item.status}
                          onChange={(e) => handleItemChange(itemIndex, 'status', e.target.value)}
                          className="w-full p-1.5 border border-gray-300 rounded-md text-sm"
                        >
                          <option value="Available">Available</option>
                          <option value="In Use">In Use</option>
                          <option value="Defect">Defect</option>


                        </select>
                      </td>
                      <td className="p-2">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setRowAsTemplate(itemIndex)}
                            className="p-1 text-indigo-700 hover:text-indigo-900 transition-colors"
                            title="Use as template for new rows"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => removeRow(itemIndex)}
                            className="p-1 text-red-600 hover:text-red-800 transition-colors"
                            disabled={items.length === 1}
                            title="Remove row"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}


          {activeTab === 'network' && (deviceType === 'blade' || deviceType === 'dedicated') && (
  <>
    <table className="w-full">
      <thead>
        <tr className="text-xs text-gray-500 border-b">
          <th className="p-2 text-left font-medium w-12">#</th>
          <th className="p-2 text-left font-medium">Label</th>
          <th className="p-2 text-left font-medium">Switch</th>
          <th className="p-2 text-left font-medium">Ports</th>
          <th className="p-2 text-left font-medium w-20">Actions</th>
        </tr>
      </thead>
      <tbody>
        {getCurrentPageItems().map((item, index) => {
          const itemIndex = currentPage * itemsPerPage + index;
          return (
            <tr key={itemIndex} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} h-14`}>
              <td className="p-2">{itemIndex + 1}</td>
              <td className="p-2 font-medium">{item.label || `Device ${itemIndex + 1}`}</td>
              <td className="p-2">
  <select
    value={item.switch_id || ''}
    onChange={(e) => handleSwitchSelection(itemIndex, e.target.value)}
    className="w-full p-1.5 border border-gray-300 rounded-md text-sm"
  >
    <option value="">Select Switch</option>
    {switches.map(switchItem => (
      <option key={switchItem.id} value={switchItem.id}>
        {switchItem.label} {switchItem.switch_ip ? `(${switchItem.switch_ip})` : ''}
      </option>
    ))}
  </select>
</td>
<td className="p-2">
  <div className="flex flex-col gap-2">
    {/* Display the selected port if exists */}


{/* Display the selected port if exists */}
{item.port1 ? (
  <div className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs flex items-center" style={{ width: "150px" }}>
    Port {item.port1}
    {item.port1_speed && <span className="ml-1 text-blue-600">({item.port1_speed}M)</span>}
    <button
      className="ml-auto text-blue-500 hover:text-blue-700"
      onClick={() => {
        const newItems = [...items];
        newItems[itemIndex] = {
          ...newItems[itemIndex],
          port1: '',
          port1_speed: ''
        };
        setItems(newItems);
      }}
    >
      <X className="w-3 h-3" />
    </button>
  </div>
) : (
  /* Show port selection dropdown only if no port is selected */
  !item.switch_id ? (
    <div className="text-xs text-gray-500" style={{ width: "150px" }}>Select a switch first</div>
  ) : availablePorts[item.switch_id] === undefined ? (
    <div className="flex items-center gap-2" style={{ width: "150px" }}>
      <span className="text-xs text-gray-500">Loading ports...</span>
      <RefreshCw className="w-3 h-3 animate-spin text-indigo-600" />
    </div>
  ) : availablePorts[item.switch_id]?.length === 0 ? (
    <div className="text-xs text-amber-600 flex items-center gap-2" style={{ width: "150px" }}>
      <span>No available ports</span>
      <button
        onClick={() => fetchAvailablePorts(item.switch_id)}
        className="p-1 text-gray-500 hover:text-indigo-700 rounded"
        title="Refresh available ports"
      >
        <RefreshCw className="w-3 h-3" />
      </button>
    </div>
  ) : (
    <div className="flex items-center gap-2" style={{ width: "150px" }}>
      <select
        className="p-1.5 border border-gray-300 rounded-md text-sm"
        style={{ width: "120px" }}
        onChange={(e) => {
          if (!e.target.value) return;

          // Get port details
          const portId = e.target.value;
          const port = availablePorts[item.switch_id].find(p => p.id.toString() === portId);

          if (!port) {
            console.error("Port not found:", portId);
            return;
          }

          console.log("Selected port:", port);

          // Update the item with port details
          const newItems = [...items];
          newItems[itemIndex] = {
            ...newItems[itemIndex],
            port1: port.port_number,
            port1_speed: port.max_speed
          };
          setItems(newItems);

          // Reset dropdown selection
          e.target.value = '';

          // Refresh ports list since one is now used
          fetchAvailablePorts(item.switch_id);
        }}
      >
        <option value="">Select Port</option>
        {availablePorts[item.switch_id]
          .filter(port => {
            // Exclude ports already allocated in the DB
            if (port.connected_device_id !== null && port.connected_device_id !== undefined && port.connected_device_id !== '' && port.connected_device_id !== 0) {
              return false;
            }
            // Exclude ports already selected in the form
            const selectedPortNumbers = items
              .filter(server => server.switch_id === item.switch_id)
              .flatMap(server => [
                server.port1,
                server.port2,
                server.port3,
                server.port4
              ])
              .filter(portNum => portNum);
            return !selectedPortNumbers.includes(port.port_number);
          })
          // Improved natural numeric sort
          .sort((a, b) => {
            function extractNumeric(val, fallback) {
              if (!val) return fallback;
              const match = String(val).match(/(\d+)/);
              return match ? parseInt(match[1], 10) : fallback;
            }
            const aNum = extractNumeric(a.port_name, extractNumeric(a.port_number, null));
            const bNum = extractNumeric(b.port_name, extractNumeric(b.port_number, null));
            if (aNum !== null && bNum !== null) return aNum - bNum;
            if (aNum !== null) return -1;
            if (bNum !== null) return 1;
            // Fallback to string compare
            const aStr = a.port_name || String(a.port_number);
            const bStr = b.port_name || String(b.port_number);
            return aStr.localeCompare(bStr, undefined, { numeric: true, sensitivity: 'base' });
          })
          .map(port => (
            <option key={port.id} value={port.id}>
              {port.port_name || `Port ${port.port_number}`}{port.max_speed ? ` (${port.max_speed} Mbps)` : ''}
            </option>
          ))}
      </select>
      <button
        onClick={() => fetchAvailablePorts(item.switch_id)}
        className="p-1 text-gray-500 hover:text-indigo-700 rounded"
        title="Refresh available ports"
      >
        <RefreshCw className={`w-3 h-3 ${loading ? 'animate-spin' : ''}`} />
      </button>
    </div>
  )
)}
  </div>
</td>
              <td className="p-2">
                <div className="flex space-x-2">
                  <button
                    onClick={() => setSelectedDeviceForPorts(itemIndex)}
                    className="p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200"
                    disabled={!item.switch_id}
                    title={!item.switch_id ? "Select a switch first" : "Select ports"}
                  >
                    <Link className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setRowAsTemplate(itemIndex)}
                    className="p-1 text-indigo-700 hover:text-indigo-900 transition-colors"
                    title="Use as template for new rows"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => removeRow(itemIndex)}
                    className="p-1 text-red-600 hover:text-red-800 transition-colors"
                    disabled={items.length === 1}
                    title="Remove row"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          );
        })}
      </tbody>
    </table>

    {/* Port Selection Modal */}
    {selectedDeviceForPorts !== null && (
  <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-2">
    <div className="bg-white rounded-lg shadow-lg w-full max-w-3xl max-h-[95vh] flex flex-col">
      <div className="p-4 border-b flex justify-between items-center">
        <h3 className="font-bold">
          Select Ports for {items[selectedDeviceForPorts]?.label || `Device ${selectedDeviceForPorts + 1}`}
        </h3>
        <button
          onClick={() => setSelectedDeviceForPorts(null)}
          className="text-gray-500 hover:text-gray-700"
        >
          <XCircle className="w-5 h-5" />
        </button>
      </div>

      <div className="p-4 flex-grow overflow-auto">
        <SwitchPortsManager
          switchId={items[selectedDeviceForPorts]?.switch_id}
          onPortSelected={(port) => {
            // Determine which port field to use based on what's already assigned
            const currentItem = items[selectedDeviceForPorts];
            let portField = 'port1';
            let portSpeedField = 'port1_speed';



            // Check if this port is already assigned to ANY server
            const isPortAlreadyAssigned = items.some(item => {
              // Skip the current item we're editing
              if (item === items[selectedDeviceForPorts]) return false;

              // Check all port fields
              return [item.port1, item.port2, item.port3, item.port4].includes(port.port_number);
            });

            if (isPortAlreadyAssigned) {
              alert(`Port ${port.port_number} is already assigned to another server in this form.`);
              return;
            }

            // Update the item with the selected port
            const newItems = [...items];
            newItems[selectedDeviceForPorts] = {
              ...newItems[selectedDeviceForPorts],
              [portField]: port.port_number,
              [portSpeedField]: port.max_speed
            };
            setItems(newItems);

            // Optionally close the modal if all required ports are assigned
            const updatedItem = newItems[selectedDeviceForPorts];
            if (
              (deviceType === 'blade' && updatedItem.port1 && updatedItem.port2) ||
              (deviceType === 'dedicated' && updatedItem.port1 && updatedItem.port2 && updatedItem.port3 && updatedItem.port4)
            ) {
              setSelectedDeviceForPorts(null);
            }
          }}
          // Pass already assigned ports to be filtered out
          alreadyAssignedPorts={items.flatMap(item =>
            [item.port1, item.port2, item.port3, item.port4].filter(Boolean)
          )}
        />
      </div>

      <div className="p-4 border-t">
        <div className="flex justify-end">
          <button
            onClick={() => setSelectedDeviceForPorts(null)}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
)}
  </>
)}
          {/* SNMP Settings Tab - Only for switches */}
{activeTab === 'snmp' && deviceType === 'switch' && (
  <table className="w-full">
    <thead>
      <tr className="text-xs text-gray-500 border-b">
        <th className="p-2 text-left font-medium w-12">#</th>
        <th className="p-2 text-left font-medium">Label</th>

        <th className="p-2 text-left font-medium">SNMP Community</th>
        <th className="p-2 text-left font-medium">SNMP Version</th>
        <th className="p-2 text-left font-medium w-20">Actions</th>
      </tr>
    </thead>
    <tbody>
      {getCurrentPageItems().map((item, index) => {
        const itemIndex = currentPage * itemsPerPage + index;
        return (
          <tr key={itemIndex} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} h-14`}>
            <td className="p-2">{itemIndex + 1}</td>
            <td className="p-2 font-medium">{item.label || `Switch ${itemIndex + 1}`}</td>

            <td className="p-2">
              <div className="relative">
                <input
                  type={passwordVisibility[`${itemIndex}-snmp_community`] ? 'text' : 'password'}
                  value={item.snmp_community || ''}
                  onChange={(e) => handleItemChange(itemIndex, 'snmp_community', e.target.value)}
                  className="w-full p-1.5 border border-gray-300 rounded-md text-sm pr-8"
                  placeholder="Private Community String"
                />
                <button
                  type="button"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-indigo-700"
                  onClick={() => togglePasswordVisibility(itemIndex, 'snmp_community')}
                >
                  {passwordVisibility[`${itemIndex}-snmp_community`] ? (
                    <Eye className="w-4 h-4" />
                  ) : (
                    <EyeOff className="w-4 h-4" />
                  )}
                </button>
              </div>

            </td>
            <td className="p-2">
              <select
                value={item.snmp_version || '2c'}
                onChange={(e) => handleItemChange(itemIndex, 'snmp_version', e.target.value)}
                className="w-full p-1.5 border border-gray-300 rounded-md text-sm"
              >
                <option value="1">Version 1</option>
                <option value="2c">Version 2c</option>
                <option value="3">Version 3</option>
              </select>
            </td>
            <td className="p-2">
              <div className="flex space-x-2">
                <button
                  onClick={() => setRowAsTemplate(itemIndex)}
                  className="p-1 text-indigo-700 hover:text-indigo-900 transition-colors"
                  title="Use as template for new rows"
                >
                  <Copy className="w-4 h-4" />
                </button>
                <button
                  onClick={() => removeRow(itemIndex)}
                  className="p-1 text-red-600 hover:text-red-800 transition-colors"
                  disabled={items.length === 1}
                  title="Remove row"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </td>
          </tr>
        );
      })}
    </tbody>
  </table>
)}
          {/* Location Tab - For all except blade servers */}
          {activeTab === 'location' && (
            <table className="w-full">
              <thead>
                <tr className="text-xs text-gray-500 border-b">
                  <th className="p-2 text-left font-medium w-12">#</th>
                  <th className="p-2 text-left font-medium">Label</th>
                  <th className="p-2 text-left font-medium">City*</th>
                  <th className="p-2 text-left font-medium">Rack*</th>
                  <th className="p-2 text-left font-medium">Top Position</th>
                  {(deviceType === 'dedicated') && (
                  <th className="p-2 text-left font-medium">Size U</th>

                  )}
                  <th className="p-2 text-left font-medium w-20">Actions</th>
                </tr>
              </thead>
              <tbody>
                {getCurrentPageItems().map((item, index) => {
                  const itemIndex = currentPage * itemsPerPage + index;
                  return (
                    <tr key={itemIndex} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} h-14`}>
                      <td className="p-2">{itemIndex + 1}</td>
                      <td className="p-2 font-medium">{item.label || `Device ${itemIndex + 1}`}</td>
                      <td className="p-2">
                        <select
                          value={item.city_id}
                          onChange={(e) => handleItemChange(itemIndex, 'city_id', e.target.value)}
                          className={`w-full p-1.5 border ${
                            errorMessages[itemIndex]?.city_id ? 'border-red-500' : 'border-gray-300'
                          } rounded-md text-sm`}
                          disabled={deviceType === 'blade'} // Disabled for blade servers, they inherit from chassis
                        >
                          <option value="">Select City</option>
                          {cities.map(city => (
                            <option key={city.id} value={city.id}>
                              {city.city} {city.datacenter ? `(${city.datacenter})` : ''}
                            </option>
                          ))}
                        </select>
                        {errorMessages[itemIndex]?.city_id && (
                          <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].city_id}</p>
                        )}
                        {deviceType === 'blade' && (
                          <p className="text-xs text-gray-500 mt-1">Inherits from chassis</p>
                        )}
                      </td>
{/* Rack dropdown with city filtering */}
<td className="p-2">
  <select
    value={item.rack_id || ''}
    onChange={(e) => handleItemChange(itemIndex, 'rack_id', e.target.value)}
    className={`w-full p-1.5 border ${
      errorMessages[itemIndex]?.rack_id ? 'border-red-500' : 'border-gray-300'
    } rounded-md text-sm`}
    disabled={!item.city_id} // Disable if no city is selected
  >
    <option value="">Select Rack</option>
    {!item.city_id ? (
      <option disabled>Please select a city first</option>
    ) : (
      racks
        .filter(rack => rack.city == item.city_id) // Filter racks by selected city
        .map(rack => (
          <option key={rack.id} value={rack.id}>{rack.rack_name}</option>
        ))
    )}
  </select>
  {errorMessages[itemIndex]?.rack_id && (
    <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].rack_id}</p>
  )}

</td>
                      <td className="p-2">
                        <input
                          type="text"
                          value={item.position}
                          onChange={(e) => handleItemChange(itemIndex, 'position', e.target.value)}
                          className="w-full p-1.5 border border-gray-300 rounded-md text-sm"
                          placeholder={deviceType === 'switch' ? "e.g. 38" : "e.g. 10"}
                          disabled={deviceType === 'blade'} // Disabled for blade servers, they are in chassis
                        />
                        {deviceType === 'blade' && (
                          <p className="text-xs text-gray-500 mt-1">Defined by chassis bay</p>
                        )}
                      </td>
                      {(deviceType === 'dedicated') && (
                         <td className="p-2">
                <input
                type="text"
                value={item.size}
                onChange={(e) => handleItemChange(itemIndex, 'size', e.target.value)}
                className="w-full p-1.5 border border-gray-300 rounded-md text-sm"
                placeholder={'e.g. 2 '}

              />
 </td>
                  )}
                      <td className="p-2">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setRowAsTemplate(itemIndex)}
                            className="p-1 text-indigo-700 hover:text-indigo-900 transition-colors"
                            title="Use as template for new rows"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => removeRow(itemIndex)}
                            className="p-1 text-red-600 hover:text-red-800 transition-colors"
                            disabled={items.length === 1}
                            title="Remove row"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}

          {/* Chassis Tab - Only for blade servers */}
          {activeTab === 'chassis' && deviceType === 'blade' && (
            <table className="w-full">
              <thead>
                <tr className="text-xs text-gray-500 border-b">
                  <th className="p-2 text-left font-medium w-12">#</th>
                  <th className="p-2 text-left font-medium">Label</th>
                  <th className="p-2 text-left font-medium">Chassis*</th>
                  <th className="p-2 text-left font-medium w-20">Actions</th>
                </tr>
              </thead>
              <tbody>
                {getCurrentPageItems().map((item, index) => {
                  const itemIndex = currentPage * itemsPerPage + index;
                  return (
                    <tr key={itemIndex} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} h-14`}>
                      <td className="p-2">{itemIndex + 1}</td>
                      <td className="p-2 font-medium">{item.label || `Server ${itemIndex + 1}`}</td>
                      <td className="p-2">
                        <select
                          value={item.chassis_id}
                          onChange={(e) => handleItemChange(itemIndex, 'chassis_id', e.target.value)}
                          className={`w-full p-1.5 border ${
                            errorMessages[itemIndex]?.chassis_id ? 'border-red-500' : 'border-gray-300'
                          } rounded-md text-sm`}
                        >
                          <option value="">Select Chassis</option>
                          {chassis.length > 0 ? (
                            chassis
                              .filter(c => {
                                // Find the chassis model to determine the total number of bays.
                                const model = chassisModels.find(m => m.id == c.model_id);
                                const totalBays = model ? (parseInt(model.bay_count, 10) || 0) : (parseInt(c.bay_count, 10) || 0);

                                // Dynamically count occupied bays from the DB.
                                const occupiedBaysInDB = Object.keys(c)
                                  .filter(key => key.startsWith('bay') && c[key] && c[key] !== '0' && c[key] !== 0)
                                  .length;

                                const initiallyFreeBays = totalBays - occupiedBaysInDB;

                                // Count how many times this chassis has been selected in the current form.
                                const baysSelectedInForm = items.filter(formItem => formItem.chassis_id === c.id).length;

                                const currentFreeBays = initiallyFreeBays - baysSelectedInForm;
                                const isSelectedInCurrentRow = item.chassis_id === c.id;

                                // Only show chassis with free bays or the currently selected one
                                return currentFreeBays > 0 || isSelectedInCurrentRow;
                              })
                              .map(c => {
                                const model = chassisModels.find(m => m.id == c.model_id);
                                const totalBays = model ? (parseInt(model.bay_count, 10) || 0) : (parseInt(c.bay_count, 10) || 0);
                                const occupiedBaysInDB = Object.keys(c)
                                  .filter(key => key.startsWith('bay') && c[key] && c[key] !== '0' && c[key] !== 0)
                                  .length;
                                const initiallyFreeBays = totalBays - occupiedBaysInDB;
                                const baysSelectedInForm = items.filter(formItem => formItem.chassis_id === c.id).length;
                                const currentFreeBays = initiallyFreeBays - baysSelectedInForm;
                                return (
                                  <option key={c.id} value={c.id}>
                                    {c.label} {c.city_name && `(${c.city_name})`} - {currentFreeBays} free bay{currentFreeBays !== 1 ? 's' : ''}
                                    {currentFreeBays <= 0 ? ' (Full)' : ''}
                                  </option>
                                );
                              })
                          ) : (
                            <option value="" disabled>No chassis available</option>
                          )}
                        </select>
                        {errorMessages[itemIndex]?.chassis_id && (
                          <p className="text-xs text-red-500 mt-1">{errorMessages[itemIndex].chassis_id}</p>
                        )}
                      </td>
                      <td className="p-2">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setRowAsTemplate(itemIndex)}
                            className="p-1 text-indigo-700 hover:text-indigo-900 transition-colors"
                            title="Use as template for new rows"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => removeRow(itemIndex)}
                            className="p-1 text-red-600 hover:text-red-800 transition-colors"
                            disabled={items.length === 1}
                            title="Remove row"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}

          {/* IPMI Tab - Only for servers */}
          {activeTab === 'ipmi' && (deviceType === 'blade' || deviceType === 'dedicated') && (
            <table className="w-full">
              <thead>
                <tr className="text-xs text-gray-500 border-b">
                  <th className="p-2 text-left font-medium w-12">#</th>
                  <th className="p-2 text-left font-medium">Label</th>
                  <th className="p-2 text-left font-medium">IPMI Address</th>
                  <th className="p-2 text-left font-medium">IPMI Root Password</th>
                  <th className="p-2 text-left font-medium">MAC Address</th>
                  <th className="p-2 text-left font-medium w-20">Actions</th>
                </tr>
              </thead>
              <tbody>
                {getCurrentPageItems().map((item, index) => {
                  const itemIndex = currentPage * itemsPerPage + index;
                  return (
                    <tr key={itemIndex} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} h-14`}>
                      <td className="p-2">{itemIndex + 1}</td>
                      <td className="p-2 font-medium">{item.label || `Device ${itemIndex + 1}`}</td>
                      <td className="p-2">
                        <DirectIpSelector
                          index={itemIndex}
                          value={item.ipmi}
                          onChange={handleItemChange}
                          cityId={item.city_id}
                          deviceType="server"
                          placeholder="IPMI address"
                          cities={cities}
                          excludeIps={items
                            .filter((otherItem, otherIndex) => otherIndex !== itemIndex && otherItem.ipmi)
                            .map(otherItem => otherItem.ipmi)
                          }
                        />
                      </td>
                      <td className="p-2">
                        <div className="relative">
                          <input
                            type={passwordVisibility[`${itemIndex}-ipmi_root_pass`] ? 'text' : 'password'}
                            value={item.ipmi_root_pass || ''}
                            onChange={(e) => handleItemChange(itemIndex, 'ipmi_root_pass', e.target.value)}
                            className="w-full p-1.5 border border-gray-300 rounded-md text-sm pr-8"
                            placeholder="Root Password"
                          />
                          <button
                            type="button"
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-indigo-700"
                            onClick={() => togglePasswordVisibility(itemIndex, 'ipmi_root_pass')}
                          >
                            {passwordVisibility[`${itemIndex}-ipmi_root_pass`] ? (
                              <Eye className="w-4 h-4" />
                            ) : (
                              <EyeOff className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      </td>
                      <td className="p-2">
                        <div className="relative">
                          <input
                            type="text"
                            value={item.mac}
                            onChange={(e) => handleItemChange(itemIndex, 'mac', e.target.value)}
                            className={`w-full p-1.5 border ${
                              macResults[itemIndex]?.success ? 'border-green-500 bg-green-50' :
                              macResults[itemIndex]?.error ? 'border-red-300 bg-red-50' :
                              'border-gray-300'
                            } rounded-md text-sm`}
                            placeholder="MAC Address"
                          />

                          {/* Status indicators */}
                          {macResults[`${itemIndex}-progress`] && (
                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center">
                              <RefreshCw className="w-4 h-4 animate-spin text-blue-600" />
                            </div>
                          )}

                          {macResults[itemIndex]?.success && (
                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center">
                              <CheckCircle className="w-4 h-4 text-green-600" />
                            </div>
                          )}

                          {macResults[itemIndex]?.error && (
                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center"
                                title={macResults[itemIndex].error}>
                              <AlertTriangle className="w-4 h-4 text-red-500" />
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-2">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setRowAsTemplate(itemIndex)}
                            className="p-1 text-indigo-700 hover:text-indigo-900 transition-colors"
                            title="Use as template for new rows"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => removeRow(itemIndex)}
                            className="p-1 text-red-600 hover:text-red-800 transition-colors"
                            disabled={items.length === 1}
                            title="Remove row"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}
        </div>

        {/* Footer Actions */}
        <div className="p-4 border-t flex justify-between items-center bg-white sticky bottom-0">
          <div className="text-sm text-gray-500">
            {items.filter(item => item.label.trim() !== '').length} {getDeviceTypeName()}{items.filter(item => item.label.trim() !== '').length !== 1 ? 's' : ''} configured
          </div>

          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
            >
              {loading ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Adding...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Add {items.filter(item => item.label.trim() !== '').length} {getDeviceTypeName()}{items.filter(item => item.label.trim() !== '').length !== 1 ? 's' : ''}
                </>
              )}
            </button>
          </div>
        </div>

        {/* Add CPU Model Modal */}
        {showAddCpuModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-bold">Add New CPU Model</h3>
                <button
                  onClick={() => {
                    setShowAddCpuModal(false);
                    setNewCpuModel('');
                  }}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <XCircle className="w-5 h-5" />
                </button>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">CPU Model Name</label>
                <input
                  type="text"
                  value={newCpuModel}
                  onChange={(e) => setNewCpuModel(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="e.g. Intel Xeon E5-2680 v4 (14 cores, 2.4GHz)"
                />
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddCpuModal(false);
                    setNewCpuModel('');
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleAddCpuModel}
                  className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Adding...
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4 mr-2" />
                      Add CPU Model
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Add RAM Configuration Modal */}
        {showAddRamModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-bold">Add New RAM Configuration</h3>
                <button
                  onClick={() => {
                    setShowAddRamModal(false);
                    setNewRamConfig({ size: '', description: '' });
                  }}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <XCircle className="w-5 h-5" />
                </button>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">RAM Size (GB)</label>
                <input
                  type="number"
                  value={newRamConfig.size}
                  onChange={(e) => setNewRamConfig({...newRamConfig, size: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="e.g. 64"
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <input
                  type="text"
                  value={newRamConfig.description}
                  onChange={(e) => setNewRamConfig({...newRamConfig, description: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="e.g. 64GB DDR4"
                />
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddRamModal(false);
                    setNewRamConfig({ size: '', description: '' });
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleAddRamConfig}
                  className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Adding...
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4 mr-2" />
                      Add RAM Configuration
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Add Switch Model Modal */}
{showAddSwitchModelModal && (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Add New Switch Model</h3>
        <button
          onClick={() => {
            setShowAddSwitchModelModal(false);
            setNewSwitchModel({ name: '', size: 1 });
          }}
          className="text-gray-500 hover:text-gray-700"
        >
          <XCircle className="w-5 h-5" />
        </button>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Switch Model Name</label>
        <input
          type="text"
          value={newSwitchModel.name}
          onChange={(e) => setNewSwitchModel({...newSwitchModel, name: e.target.value})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. Cisco Nexus 9396TX"
        />
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Size (U)</label>
        <input
          type="number"
          min="1"
          max="10"
          value={newSwitchModel.size}
          onChange={(e) => setNewSwitchModel({...newSwitchModel, size: parseInt(e.target.value)})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. 1"
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          type="button"
          onClick={() => {
            setShowAddSwitchModelModal(false);
            setNewSwitchModel({ name: '', size: 1 });
          }}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleAddSwitchModel}
          className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
          disabled={loading}
        >
          {loading ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Adding...
            </>
          ) : (
            <>
              <Plus className="w-4 h-4 mr-2" />
              Add Switch Model
            </>
          )}
        </button>
      </div>
    </div>
  </div>
)}
      </div>

      {/* Add this modal component at the end of your component, near the other modals */}
{showAddChassisModelModal && (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Add New Chassis Model</h3>
        <button
          onClick={() => {
            setShowAddChassisModelModal(false);
            setNewChassisModel({ name: '', size: 2, bay_count: 8 });
          }}
          className="text-gray-500 hover:text-gray-700"
        >
          <XCircle className="w-5 h-5" />
        </button>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Chassis Model Name</label>
        <input
          type="text"
          value={newChassisModel.name}
          onChange={(e) => setNewChassisModel({...newChassisModel, name: e.target.value})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. PowerEdge M1000e"
        />
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Size (U)</label>
        <input
          type="number"
          min="1"
          max="42"
          value={newChassisModel.size}
          onChange={(e) => setNewChassisModel({...newChassisModel, size: parseInt(e.target.value)})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. 10"
        />
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Bay Count</label>
        <input
          type="number"
          min="1"
          max="32"
          value={newChassisModel.bay_count}
          onChange={(e) => setNewChassisModel({...newChassisModel, bay_count: parseInt(e.target.value)})}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
          placeholder="e.g. 16"
        />
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <button
          type="button"
          onClick={() => {
            setShowAddChassisModelModal(false);
            setNewChassisModel({ name: '', size: 2, bay_count: 8 });
          }}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleAddChassisModel}
          className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
          disabled={loading}
        >
          {loading ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Adding...
            </>
          ) : (
            <>
              <Plus className="w-4 h-4 mr-2" />
              Add Chassis Model
            </>
          )}
        </button>
      </div>
    </div>
  </div>
)}
    </div>
  );
};

export default BulkDeviceAddition;