import React, { useState, useEffect, useCallback } from 'react';
import { 
  RefreshCw, 
  Link, 
  AlertTriangle, 
  Info, 
  Edit, 
  Check, 
  ArrowDown, 
  ArrowUp, 
  Power, 
  Lock, 
  Wifi,
  Activity,
  ArrowUpCircle,
  ArrowDownCircle,
  Circle,
  Unlink,
  Link2Off,
  Server,
  X,
  LineChart, 
  BarChart2, 
  ChevronRight,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import PortTrafficGraph from './PortTrafficGraph';
import MiniPortTrafficGraph from './MiniPortTrafficGraph';
import { API_URL } from '../config';
const SwitchPortsManager = ({ 
  switchId, 
  onPortSelected,
  onPortUnassigned, // Optional callback for parent components
  alreadyAssignedPorts = [] // Add this prop to filter out already assigned ports
}) => {
  const [ports, setPorts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [discoveryInProgress, setDiscoveryInProgress] = useState(false);
  const [sortField, setSortField] = useState('port_number');
  const [sortDirection, setSortDirection] = useState('asc');
  const [editingPortId, setEditingPortId] = useState(null);
  const [editingSpeed, setEditingSpeed] = useState('');
  const [toggleConfirmOpen, setToggleConfirmOpen] = useState(false);
  const [portToToggle, setPortToToggle] = useState(null);
  const [toggleAction, setToggleAction] = useState(null);
  const [toggleInProgress, setToggleInProgress] = useState(false);
  const [operationalStatusMap, setOperationalStatusMap] = useState({});
  const [refreshingStatus, setRefreshingStatus] = useState(false);
  const [selectedPortForTraffic, setSelectedPortForTraffic] = useState(null);
  const [showTrafficModal, setShowTrafficModal] = useState(false);
  // Set this to true by default to show all graphs right away
  const [showAllMiniGraphs, setShowAllMiniGraphs] = useState(true);
  
  // Device allocation states
  const [availableDevices, setAvailableDevices] = useState([]);
  const [selectedDeviceId, setSelectedDeviceId] = useState('');
  const [deviceLoading, setDeviceLoading] = useState(false);
  const [allocationInProgress, setAllocationInProgress] = useState(false);
  
  // Manual port addition states
  const [showAddPortModal, setShowAddPortModal] = useState(false);
  const [addPortInProgress, setAddPortInProgress] = useState(false);
  const [newPortData, setNewPortData] = useState({
    port_number: '',
    port_name: '',
    port_type: 'Ethernet',
    max_speed: '',
    status: 'Available'
  });
  
  // Initial load of ports when switchId changes
  useEffect(() => {
    if (switchId) {
      fetchPorts();
      fetchAvailableDevices();
    }
  }, [switchId]);
  
  // Debug effect to track modal state changes
  useEffect(() => {
    console.log('=== MODAL STATE CHANGED ===', { 
      showAddPortModal, 
      timestamp: new Date().toISOString() 
    });
  }, [showAddPortModal]);
  
  // Toggle all mini graphs
  const toggleAllMiniGraphs = () => {
    setShowAllMiniGraphs(!showAllMiniGraphs);
  };
  
  // Helper function to reset stuck states (for debugging)
  const resetStates = () => {
    console.log('Resetting states...');
    setDiscoveryInProgress(false);
    setAddPortInProgress(false);
    setShowAddPortModal(false);
    setAllocationInProgress(false);
    setToggleInProgress(false);
    setEditingPortId(null);
  };
  
  const handleShowTraffic = (port, e) => {
    e.stopPropagation();
    setSelectedPortForTraffic(port);
    setShowTrafficModal(true);
  };

  const formatPortSpeed = (speed) => {
    if (!speed) return 'Unknown';
    
    if (speed >= 1000) {
      return `${speed / 1000} Gbps`;
    } else {
      return `${speed} Mbps`;
    }
  };
  // Fetch ports with operational status
  const fetchPorts = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_switch_ports`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token, switch_id: switchId })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const data = await response.json();
      
      // Process operational status for each port
      const statusMap = {};
      
      if (Array.isArray(data)) {
        data.forEach(port => {
          const opStatus = determineOperationalStatus(port);
          statusMap[port.id] = {
            status: opStatus,
            lastChecked: new Date().toISOString()
          };
        });
      }
      
      setPorts(data);
      setOperationalStatusMap(statusMap);
    } catch (error) {
      console.error("Error fetching switch ports:", error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to determine operational status from port data
  const determineOperationalStatus = (port) => {
    if (!port) return "Unknown";
    
    if (port.status === "Disabled") {
      return "Administratively Down";
    } else if (port.status === "Down") {
      return "Link Down";
    } else if (port.status === "Available" || port.status === "Used") {
      // The port is administratively up, but we need to check if it's actually linked
      return "Link Up";
    }
    
    return "Unknown";
  };
  
  // Refresh port operational status
  const refreshPortStatus = async (portId) => {
    if (!portId || !switchId) return;
    
    try {
      setOperationalStatusMap(prev => ({
        ...prev,
        [portId]: {
          ...prev[portId],
          status: "Refreshing...",
          lastChecked: new Date().toISOString()
        }
      }));
      
      const token = localStorage.getItem('admin_token');
      
      // Use the dedicated refresh_port_status endpoint
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=refresh_port_status`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          token, 
          switch_id: switchId,
          port_id: portId 
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success && result.ports.length > 0) {
        const portData = result.ports[0];
        
        // Determine operational status based on the returned data
        let opStatus = "Unknown";
        
        if (portData.status === "Disabled") {
          opStatus = "Administratively Down";
        } else if (portData.operational_status === "Down") {
          opStatus = "Link Down";
        } else if (portData.operational_status === "Up") {
          opStatus = "Link Up";
        }
        
        // Update operational status in state
        setOperationalStatusMap(prev => ({
          ...prev,
          [portId]: {
            status: opStatus,
            lastChecked: new Date().toISOString(),
            adminStatus: portData.admin_status,
            operStatus: portData.operational_status
          }
        }));
        
        // Also update the port in the ports list
        setPorts(prevPorts => 
          prevPorts.map(port => 
            port.id === portId ? { ...port, ...portData } : port
          )
        );
      } else {
        throw new Error(result.error || "Port data not found in response");
      }
    } catch (error) {
      console.error(`Error refreshing port status for port ${portId}:`, error);
      setOperationalStatusMap(prev => ({
        ...prev,
        [portId]: {
          status: "Error",
          lastChecked: new Date().toISOString(),
          error: error.message
        }
      }));
    }
  };

  // Refresh all port statuses
// Refresh all port statuses
const refreshAllPortStatus = async () => {
  if (!switchId || !ports.length) return;
  
  setRefreshingStatus(true);
  
  try {
    const token = localStorage.getItem('admin_token');
    
    // Use the refresh_port_status endpoint for all ports
    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=refresh_port_status`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        token,
        switch_id: switchId
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.success) {
      // Create new operational status map
      const newStatusMap = {...operationalStatusMap};
      
      // Update port statuses without modifying other port details
      const updatedPorts = result.ports.map(updatedPort => {
        // Find the corresponding existing port to preserve its original details
        const existingPort = ports.find(p => p.id === updatedPort.id);
        
        // Determine operational status
        let opStatus = "Unknown";
        
        if (updatedPort.status === "Disabled") {
          opStatus = "Administratively Down";
        } else if (updatedPort.operational_status === "Down") {
          opStatus = "Link Down";
        } else if (updatedPort.operational_status === "Up") {
          opStatus = "Link Up";
        }
        
        // Update operational status map
        newStatusMap[updatedPort.id] = {
          status: opStatus,
          lastChecked: new Date().toISOString(),
          adminStatus: updatedPort.admin_status,
          operStatus: updatedPort.operational_status
        };
        
        // Return the existing port with only the updated status
        return {
          ...existingPort,
          status: updatedPort.status,
          operational_status: updatedPort.operational_status
        };
      });
      
      // Update state
      setOperationalStatusMap(newStatusMap);
      setPorts(updatedPorts);
    } else {
      throw new Error(result.error || "Failed to refresh port statuses");
    }
  } catch (error) {
    console.error("Error refreshing all port statuses:", error);
    setError("Failed to refresh port statuses: " + error.message);
  } finally {
    setRefreshingStatus(false);
  }
};
  
  const triggerPortDiscovery = async () => {
    if (!switchId) return;
    
    // Check if any ports are already assigned to servers
    try {
      setError(null);
      
      // Check if any ports for this switch are assigned to servers
      const token = localStorage.getItem('admin_token');
      
      const portsResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_switch_ports`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          token,
          switch_id: switchId
        })
      });
      
      if (!portsResponse.ok) {
        throw new Error(`HTTP error ${portsResponse.status}`);
      }
      
      const ports = await portsResponse.json();
      
      // Check if any ports have a connected_device_id
      const portsAssigned = ports.some(port => port.connected_device_id);
      
      if (portsAssigned) {
        setError(
          "Some ports on this switch are assigned to servers. " +
          "Rediscovery is not allowed to prevent data loss. " +
          "Please unassign all ports from servers before rediscovering."
        );
        return; // Stop here
      }
    } catch (checkError) {
      console.error("Error checking port assignments:", checkError);
      // Continue with discovery, but log the error
    }
    
    // Let user know this needs SNMP private community string
    const snmpCommunity = prompt('Enter SNMP Private Community string (required for read access):', '');
    if (!snmpCommunity) {
      alert('SNMP Community string is required for port discovery');
      return;
    }
    
    setDiscoveryInProgress(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=discover_switch_ports`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          token,
          switch_id: switchId,
          snmp_community: snmpCommunity
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Refresh the port list
        await fetchPorts();
        alert(`Ports discovered successfully! Added ${data.ports_added} ports.`);
      } else {
        throw new Error(data.error || 'Failed to discover ports');
      }
    } catch (error) {
      console.error("Error discovering switch ports:", error);
      setError(`Discovery error: ${error.message}`);
      alert(`Port discovery failed: ${error.message}\n\nThis could be due to:\n- Incorrect SNMP community string\n- Switch not supporting standard SNMP OIDs\n- Firewall blocking SNMP traffic\n\nYou can still manually edit port speeds.`);
    } finally {
      setDiscoveryInProgress(false);
    }
  };
  
  // Function to add a port manually
  const addPortManually = async () => {
    if (!switchId) return;
    
    // Validate required fields
    if (!newPortData.port_number.trim()) {
      alert('Port number is required');
      return;
    }
    
    setAddPortInProgress(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('admin_token');
      
      const payload = {
        token,
        switch_id: switchId,
        port_number: newPortData.port_number.trim(),
        port_name: newPortData.port_name.trim(),
        port_type: newPortData.port_type,
        status: newPortData.status
      };
      
      // Add max_speed if provided
      if (newPortData.max_speed && !isNaN(parseInt(newPortData.max_speed))) {
        payload.max_speed = parseInt(newPortData.max_speed);
      }
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_manual_port`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Add the new port to the local state
        setPorts(prevPorts => [...prevPorts, data.port_data]);
        
        // Update operational status for the new port
        setOperationalStatusMap(prev => ({
          ...prev,
          [data.port_data.id]: {
            status: determineOperationalStatus(data.port_data),
            lastChecked: new Date().toISOString()
          }
        }));
        
        // Close modal and reset form
        setShowAddPortModal(false);
        setNewPortData({
          port_number: '',
          port_name: '',
          port_type: 'Ethernet',
          max_speed: '',
          status: 'Available'
        });
        
        // Trigger data change event
        const event = new CustomEvent('inventory-data-change', {
          detail: {
            deviceType: 'port',
            switchId: switchId,
            portId: data.port_data.id,
            timestamp: Date.now(),
            action: 'port-added'
          }
        });
        window.dispatchEvent(event);
        
        alert(`Port ${data.port_data.port_number} added successfully!`);
      } else {
        throw new Error(data.error || 'Failed to add port');
      }
    } catch (error) {
      console.error("Error adding port manually:", error);
      setError(`Add port error: ${error.message}`);
      alert(`Failed to add port: ${error.message}`);
    } finally {
      setAddPortInProgress(false);
    }
  };
  
  // Function to fetch all available devices (both blade and dedicated)
  const fetchAvailableDevices = async () => {
    setDeviceLoading(true);
    
    try {
      const token = localStorage.getItem('admin_token');
      
      // Fetch blade servers
      const bladeResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_blade_server_inventory`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token })
      });
      
      // Fetch dedicated servers
      const dedicatedResponse = await fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_dedicated`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token })
      });
      
      if (!bladeResponse.ok || !dedicatedResponse.ok) {
        throw new Error(`HTTP error fetching servers`);
      }
      
      const bladeServers = await bladeResponse.json();
      const dedicatedServers = await dedicatedResponse.json();
      
      console.log('Fetched servers:', { 
        bladeCount: Array.isArray(bladeServers) ? bladeServers.length : 0,
        dedicatedCount: Array.isArray(dedicatedServers) ? dedicatedServers.length : 0
      });
      
      // Combine servers into a single array with type information
      const combinedDevices = [
        { id: 'unallocate', label: '-- Unallocate Port --', type: 'action' },
        ...Array.isArray(bladeServers) 
          ? bladeServers.map(server => ({ 
              id: `blade-${server.id}`, 
              label: `Blade: ${server.label || 'Unnamed'} (ID: ${server.id})`,
              type: 'blade',
              serverId: server.id
            })) 
          : [],
        ...Array.isArray(dedicatedServers) 
          ? dedicatedServers.map(server => ({ 
              id: `dedicated-${server.id}`, 
              label: `Dedicated: ${server.label || 'Unnamed'} (ID: ${server.id})`,
              type: 'dedicated',
              serverId: server.id
            })) 
          : []
      ];
      
      setAvailableDevices(combinedDevices);
    } catch (error) {
      console.error("Error fetching available devices:", error);
      // Just set a simple list with unallocate option
      setAvailableDevices([
        { id: 'unallocate', label: '-- Unallocate Port --', type: 'action' }
      ]);
    } finally {
      setDeviceLoading(false);
    }
  };
  
  const updatePortSpeed = async (portId, speed) => {
    if (!switchId || !portId) return;
    
    try {
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=update_port_speed`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          token,
          port_id: portId,
          max_speed: speed
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Update the local port data
        setPorts(prevPorts => 
          prevPorts.map(port => 
            port.id === portId 
              ? {...port, max_speed: speed} 
              : port
          )
        );
        
        return true;
      } else {
        throw new Error(data.error || 'Failed to update port speed');
      }
    } catch (error) {
      console.error("Error updating port speed:", error);
      alert(`Failed to update port speed: ${error.message}`);
      return false;
    }
  };
  
  const togglePortStatus = async (port, action) => {
    if (!port || !action) return;
    
    // If port is used by a device, require confirmation
    if (port.connected_device_id) {
      setPortToToggle(port);
      setToggleAction(action);
      setToggleConfirmOpen(true);
      return;
    }
    
    // Otherwise proceed directly
    performTogglePort(port, action);
  };
  
  const performTogglePort = async (port, action) => {
    try {
      setToggleInProgress(true);
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=toggle_port_status`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          token,
          port_id: port.id,
          action: action
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Update port status in local state
        setPorts(prevPorts => 
          prevPorts.map(p => 
            p.id === port.id 
              ? {...p, status: result.new_status} 
              : p
          )
        );
        
        // Update operational status to show transition
        const transitionStatus = action === 'enable' ? 'Link Activating...' : 'Shutting Down...';
        setOperationalStatusMap(prev => ({
          ...prev,
          [port.id]: {
            status: transitionStatus,
            lastChecked: new Date().toISOString()
          }
        }));
        
        // Close confirmation dialog if open
        setToggleConfirmOpen(false);
        
        // Trigger data change event to update other components
        const event = new CustomEvent('inventory-data-change', {
          detail: {
            deviceType: 'port',
            switchId: port.switch_id,
            portId: port.id,
            timestamp: Date.now(),
            action: 'port-status-changed',
            newStatus: result.new_status
          }
        });
        window.dispatchEvent(event);
        
        // For enable actions, implement a retry mechanism with increasing delays
        if (action === 'enable') {
          // Create a sequence of refresh attempts
          const scheduleRefresh = async (delay, attempt = 1, maxAttempts = 3) => {
            if (attempt > maxAttempts) return;
            
            // Show visual indicator
            if (attempt === 1) {
              setTimeout(() => {
                setOperationalStatusMap(prev => ({
                  ...prev,
                  [port.id]: {
                    ...prev[port.id],
                    status: `Checking link (${attempt}/${maxAttempts})...`,
                    lastChecked: new Date().toISOString()
                  }
                }));
              }, delay - 500); // Show just before refresh
            }
            
            // Actual refresh after delay
            setTimeout(async () => {
              try {
                await refreshPortStatus(port.id);
                
                // Check status after refresh
                const currentStatus = operationalStatusMap[port.id]?.status;
                if (currentStatus === "Link Up") {
                  // Success, no more refreshes needed
                  return;
                }
                
                // Schedule next attempt if needed
                if (attempt < maxAttempts) {
                  scheduleRefresh(delay * 1.5, attempt + 1, maxAttempts);
                }
              } catch (error) {
                console.error(`Error in scheduled refresh attempt ${attempt}:`, error);
                // Try next attempt
                if (attempt < maxAttempts) {
                  scheduleRefresh(delay * 1.5, attempt + 1, maxAttempts);
                }
              }
            }, delay);
          };
          
          // Start the sequence with initial 3-second delay
          scheduleRefresh(3000);
        } else {
          // For disabling, a single delayed refresh is sufficient
          setTimeout(() => refreshPortStatus(port.id), 1500);
        }
      } else {
        throw new Error(result.error || "Unknown error occurred");
      }
    } catch (error) {
      console.error("Error toggling port status:", error);
      alert(`Failed to ${action} port: ${error.message}`);
    } finally {
      setToggleInProgress(false);
      // Reset toggle state
      setPortToToggle(null);
      setToggleAction(null);
    }
  };
  
  // Function to unassign a port from a server
  const unassignPort = async (port) => {
    if (!port || !port.connected_device_id) return;
    
    try {
      setAllocationInProgress(true);
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=unassign_port_from_server`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          token,
          device_id: port.connected_device_id,
          device_type: port.connected_device_type,
          port_number: port.port_number
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Update port status in local state
        setPorts(prevPorts => 
          prevPorts.map(p => 
            p.id === port.id 
              ? {
                  ...p, 
                  status: 'Available',
                  connected_device_id: null,
                  connected_device_type: null
                } 
              : p
          )
        );
        
        // Trigger data change event
        const event = new CustomEvent('inventory-data-change', {
          detail: {
            deviceType: 'port',
            switchId: port.switch_id,
            portId: port.id,
            timestamp: Date.now(),
            action: 'port-unassigned'
          }
        });
        window.dispatchEvent(event);
        
        // Call the callback if provided
        if (onPortUnassigned && typeof onPortUnassigned === 'function') {
          onPortUnassigned(port);
        }
        
        // Show success message
        alert(`Port ${port.port_number} has been successfully unassigned`);
        
        return true;
      } else {
        throw new Error(result.error || "Unknown error occurred");
      }
    } catch (error) {
      console.error("Error unassigning port:", error);
      alert(`Failed to unassign port: ${error.message}`);
      return false;
    } finally {
      setAllocationInProgress(false);
      setEditingPortId(null);
      setSelectedDeviceId('');
    }
  };
  
  // Function to assign a port to a server
  const assignPort = async (port, deviceType, deviceId) => {
    if (!port || !deviceType || !deviceId) return;
    
    try {
      setAllocationInProgress(true);
      const token = localStorage.getItem('admin_token');
      
      // Convert the deviceId to a pure numeric value if it's not already
      const numericDeviceId = parseInt(deviceId);
      
      if (isNaN(numericDeviceId)) {
        throw new Error('Invalid device ID format. Please contact support.');
      }
      
      console.log(`Assigning port ${port.id} to ${deviceType} device ${numericDeviceId}`);
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=assign_port_to_server_modal`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          token,
          port_id: port.id,
          device_id: numericDeviceId,
          device_type: deviceType
        })
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Server response:", errorText);
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Update port status in local state
        setPorts(prevPorts => 
          prevPorts.map(p => 
            p.id === port.id 
              ? {
                  ...p, 
                  status: 'Used',
                  connected_device_id: numericDeviceId,
                  connected_device_type: deviceType
                } 
              : p
          )
        );
        
        // Trigger data change event
        const event = new CustomEvent('inventory-data-change', {
          detail: {
            deviceType: 'port',
            switchId: port.switch_id,
            portId: port.id,
            timestamp: Date.now(),
            action: 'port-assigned',
            serverType: deviceType,
            serverId: numericDeviceId
          }
        });
        window.dispatchEvent(event);
        
        // Show success message
        alert(`Port ${port.port_number} has been successfully assigned to ${deviceType} server #${numericDeviceId}`);
        
        return true;
      } else {
        throw new Error(result.error || "Unknown error occurred");
      }
    } catch (error) {
      console.error("Error assigning port:", error);
      alert(`Failed to assign port: ${error.message}`);
      return false;
    } finally {
      setAllocationInProgress(false);
      setEditingPortId(null);
      setSelectedDeviceId('');
    }
  };
  
  const handlePortSelect = (port) => {
    // Skip if this port is already assigned to another server in the form
    if (alreadyAssignedPorts.includes(port.port_number)) {
      alert(`Port ${port.port_number} is already assigned to another server in this form.`);
      return;
    }
    
    if (port.status === 'Available' && onPortSelected) {
      onPortSelected(port);
    }
  };
  
  // Start editing a port - this now just sets the port ID for editing
  const handleEdit = (port) => {
    setEditingPortId(port.id);
    setEditingSpeed(port.max_speed || '');
    setSelectedDeviceId('');
  };
  
  // Separate handler for saving port speed 
  const handleSaveSpeed = async (port) => {
    if (!editingSpeed) return false;
    
    const speed = parseInt(editingSpeed);
    if (isNaN(speed) || speed < 0) {
      alert('Please enter a valid port speed (positive number)');
      return false;
    }
    
    return await updatePortSpeed(port.id, speed);
  };
  
  // Handle device allocation or unallocation from dropdown
  const handleDeviceAction = async (port, deviceValue) => {
    if (deviceValue === 'unallocate') {
      return await unassignPort(port);
    }
    
    // Parse the device string from our dropdown value
    // Format is "type-id" (like "blade-123" or "dedicated-456")
    try {
      const parts = deviceValue.split('-');
      if (parts.length !== 2) {
        throw new Error('Invalid device selection format');
      }
      
      const [type, serverId] = parts;
      
      if (!type || !serverId) {
        throw new Error('Missing device type or ID');
      }
      
      if (!['blade', 'dedicated'].includes(type)) {
        throw new Error(`Invalid device type: ${type}`);
      }
      
      return await assignPort(port, type, serverId);
    } catch (error) {
      console.error("Error processing device selection:", error);
      alert(`Failed to process device selection: ${error.message}`);
      return false;
    }
  };
  
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  // Separate function to complete all edits and close edit mode
  const finishEditing = () => {
    setEditingPortId(null);
    setSelectedDeviceId('');
    setEditingSpeed('');
    setAllocationInProgress(false);
  };
  
  // Sort the ports based on current sort settings
  const getSortedPorts = () => {
    if (!ports.length) return [];
    
    return [...ports].sort((a, b) => {
      let aValue, bValue;
      
      if (sortField === 'port_number') {
        // Try to extract numeric part for numeric sorting of port numbers
        const aMatch = a.port_number.match(/(\d+)/);
        const bMatch = b.port_number.match(/(\d+)/);
        
        if (aMatch && bMatch) {
          aValue = parseInt(aMatch[0]);
          bValue = parseInt(bMatch[0]);
        } else {
          aValue = a.port_number;
          bValue = b.port_number;
        }
      } else if (sortField === 'max_speed') {
        aValue = parseInt(a.max_speed) || 0;
        bValue = parseInt(b.max_speed) || 0;
      } else if (sortField === 'status') {
        aValue = a.status;
        bValue = b.status;
      } else if (sortField === 'operational_status') {
        aValue = operationalStatusMap[a.id]?.status || "Unknown";
        bValue = operationalStatusMap[b.id]?.status || "Unknown";
      } else {
        aValue = a[sortField];
        bValue = b[sortField];
      }
      
      // Handle string vs number comparison
      let result;
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        result = aValue.localeCompare(bValue);
      } else {
        result = aValue - bValue;
      }
      
      return sortDirection === 'asc' ? result : -result;
    });
  };
  
  // Count ports by speed for summary stats
  const getPortSpeedStats = (sortedPorts) => {
    const speedStats = {};
    
    sortedPorts.forEach(port => {
      // If max_speed is 0, null, or undefined, use 'Unknown'
      const speed = (port.max_speed && port.max_speed !== '0') ? parseInt(port.max_speed) : 'Unknown';
      
      if (!speedStats[speed]) {
        speedStats[speed] = 0;
      }
      speedStats[speed]++;
    });
    
    return speedStats;
  };
  
  // Get standard speed options for dropdowns
  const getSpeedOptions = () => {
    return [
      { value: 100, label: '100 Mbps (Fast Ethernet)' },
      { value: 1000, label: '1000 Mbps (Gigabit)' },
      { value: 2500, label: '2.5 Gbps' },
      { value: 5000, label: '5 Gbps' },
      { value: 10000, label: '10 Gbps' },
      { value: 25000, label: '25 Gbps' },
      { value: 40000, label: '40 Gbps' },
      { value: 100000, label: '100 Gbps' }
    ];
  };
  
  // Render operational status icon and label
  const renderOperationalStatus = (portId) => {
    const status = operationalStatusMap[portId]?.status || "Unknown";
    
    let statusIcon;
    let statusClass;
    let titleText;
    
    switch(status) {
      case "Link Up":
        statusIcon = <ArrowUpCircle className="w-3 h-3 mr-1" />;
        statusClass = "bg-green-100 text-green-800";
        titleText = "Link is up and operational";
        break;
      case "Link Down":
        statusIcon = <ArrowDownCircle className="w-3 h-3 mr-1" />;
        statusClass = "bg-red-100 text-red-800";
        titleText = "Link is physically down";
        break;
      case "Administratively Down":
        statusIcon = <Power className="w-3 h-3 mr-1" />;
        statusClass = "bg-gray-200 text-gray-800";
        titleText = "Port is administratively disabled";
        break;
      case "Refreshing...":
        statusIcon = <RefreshCw className="w-3 h-3 mr-1 animate-spin" />;
        statusClass = "bg-blue-100 text-blue-800";
        titleText = "Getting current status...";
        break;
      case "Unknown":
      default:
        statusIcon = <Circle className="w-3 h-3 mr-1" />;
        statusClass = "bg-yellow-100 text-yellow-800";
        titleText = "Status unknown";
        break;
    }
    
    return (
      <div 
        className={`flex items-center px-2 py-0.5 rounded-full text-xs ${statusClass}`}
        title={titleText}
      >
        {statusIcon}
        <span>{status}</span>
      </div>
    );
  };
  
  // Confirmation dialog for port toggle
  const renderConfirmationDialog = () => {
    if (!toggleConfirmOpen || !portToToggle || !toggleAction) return null;
    
    const action = toggleAction === 'enable' ? 'enable' : 'disable';
    const serverText = portToToggle.connected_device_id ? 
      `This port is connected to device #${portToToggle.connected_device_id}. ` : 
      '';
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-4">
          <h3 className="text-lg font-bold mb-2">Confirm Port {action.charAt(0).toUpperCase() + action.slice(1)}</h3>
          <p className="mb-4 text-sm">
            {serverText}Are you sure you want to {action} port {portToToggle.port_number}?
            {action === 'disable' && ' This will interrupt network connectivity for any connected device.'}
          </p>
          
          <div className="flex justify-end space-x-2">
            <button
              className="px-3 py-1.5 border border-gray-300 bg-white rounded-md text-gray-700 text-sm hover:bg-gray-50"
              onClick={() => setToggleConfirmOpen(false)}
              disabled={toggleInProgress}
            >
              Cancel
            </button>
            <button
              className={`px-3 py-1.5 rounded-md text-white text-sm flex items-center
                ${toggleInProgress ? 'bg-indigo-400 cursor-not-allowed' : 'bg-indigo-600 hover:bg-indigo-700'}`}
              onClick={() => performTogglePort(portToToggle, toggleAction)}
              disabled={toggleInProgress}
            >
              {toggleInProgress ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-1.5 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Power className="w-4 h-4 mr-1.5" />
                  {action.charAt(0).toUpperCase() + action.slice(1)} Port
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <RefreshCw className="w-5 h-5 animate-spin text-indigo-700 mr-2" />
        <span>Loading ports...</span>
      </div>
    );
  }

  // Debug states - temporary for troubleshooting
  console.log('SwitchPortsManager states:', {
    discoveryInProgress,
    addPortInProgress,
    showAddPortModal,
    switchId
  });
  
  if (error) {
    return (
      <div className="flex items-center justify-center p-4 text-red-600">
        <AlertTriangle className="w-5 h-5 mr-2" />
        <span>Error: {error}</span>
      </div>
    );
  }
  
  if (ports.length === 0) {
    return (
      <div className="p-4 text-gray-500 text-center">
        <div className="mb-4">
          No ports found for this switch. You can discover them via SNMP or add them manually.
        </div>
        <div className="flex flex-col sm:flex-row gap-2 justify-center">
          <button 
            className="px-4 py-2 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center justify-center"
            onClick={triggerPortDiscovery}
            disabled={discoveryInProgress}
          >
            {discoveryInProgress ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Discovering...
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4 mr-2" />
                Discover Ports via SNMP
              </>
            )}
          </button>
          <button 
            className={`px-4 py-2 text-white rounded-md text-sm flex items-center justify-center ${
              discoveryInProgress || addPortInProgress 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-green-600 hover:bg-green-700'
            }`}
            onClick={() => {
              console.log('Add Port button clicked, current states:', {
                discoveryInProgress,
                addPortInProgress,
                showAddPortModal
              });
              setShowAddPortModal(true);
            }}
            disabled={discoveryInProgress || addPortInProgress}
            title={discoveryInProgress || addPortInProgress ? 'Please wait for current operation to complete' : 'Add a new port manually'}
          >
            <Server className="w-4 h-4 mr-2" />
            {discoveryInProgress ? 'Discovery in progress...' : 
             addPortInProgress ? 'Adding port...' : 
             'Add Port Manually'}
          </button>
        </div>
      </div>
    );
  }
  
  // Sort ports
  const sortedPorts = getSortedPorts();
  
  // Get port stats for summary
  const speedStats = getPortSpeedStats(sortedPorts);
  
  // Check if there are any unknown speeds
  const hasUnknownSpeeds = 'Unknown' in speedStats;
  
  // Get speed options for dropdown
  const speedOptions = getSpeedOptions();
  
  return (
    <div className="max-h-96 overflow-y-auto">
      <div className="p-2 bg-gray-100 border-b flex justify-between items-center sticky top-0 z-10">
        <div className="text-sm font-medium">{ports.length} ports found</div>
        <div className="flex space-x-2">
          {/* Add refresh all button */}
          <button 
            className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 flex items-center"
            onClick={refreshAllPortStatus}
            disabled={refreshingStatus}
            title="Refresh all port statuses"
          >
            {refreshingStatus ? (
              <RefreshCw className="w-3 h-3 animate-spin" />
            ) : (
              <>
                <Activity className="w-3 h-3 mr-1" />
                Status
              </>
            )}
          </button>
          <button 
            className="px-3 py-1 text-xs bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center"
            onClick={triggerPortDiscovery}
            disabled={discoveryInProgress}
            title="Re-discover ports via SNMP"
          >
            {discoveryInProgress ? (
              <RefreshCw className="w-3 h-3 animate-spin" />
            ) : (
              <RefreshCw className="w-3 h-3" />
            )}
          </button>
          <button 
            className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-md hover:bg-green-200 flex items-center"
            onClick={() => setShowAddPortModal(true)}
            disabled={discoveryInProgress || addPortInProgress}
            title="Add port manually"
          >
            <Server className="w-3 h-3 mr-1" />
            Add Port
          </button>
          <button 
            className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center"
            onClick={fetchPorts}
            disabled={loading}
            title="Refresh ports list"
          >
            {loading ? (
              <RefreshCw className="w-3 h-3 animate-spin" />
            ) : (
              <RefreshCw className="w-3 h-3" />
            )}
          </button>
          {/* Debug reset button - temporary for troubleshooting */}
          <button 
            className="px-3 py-1 text-xs bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200 flex items-center"
            onClick={resetStates}
            title="Reset all states (debug)"
          >
            Reset States
          </button>
        </div>
      </div>
      
      {/* Alert for Unknown Speed ports */}
      {hasUnknownSpeeds && (
        <div className="p-2 bg-yellow-50 border-b border-yellow-100 text-xs text-yellow-700 flex items-start">
          <AlertTriangle className="w-4 h-4 mr-1 flex-shrink-0 mt-0.5" />
          <div>
            <p className="font-medium">Some ports have unknown speeds</p>
            <p>SNMP discovery couldn't determine the speed for some ports. You can manually set speeds by clicking the edit icon.</p>
          </div>
        </div>
      )}
      
      {/* Table view */}
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead className="sticky top-0 bg-gray-50 text-xs text-gray-500">
            <tr className="border-b">
              <th className="text-left p-2 whitespace-nowrap cursor-pointer" onClick={() => handleSort('port_number')}>
                <div className="flex items-center">
                  Port
                  {sortField === 'port_number' && (
                    sortDirection === 'asc' ? <ArrowUp className="w-3 h-3 ml-1" /> : <ArrowDown className="w-3 h-3 ml-1" />
                  )}
                </div>
              </th>
              <th className="text-left p-2 whitespace-nowrap cursor-pointer" onClick={() => handleSort('port_name')}>
                <div className="flex items-center">
                  Description
                </div>
              </th>
              <th className="text-left p-2 whitespace-nowrap cursor-pointer" onClick={() => handleSort('max_speed')}>
                <div className="flex items-center">
                  Speed
                  {sortField === 'max_speed' && (
                    sortDirection === 'asc' ? <ArrowUp className="w-3 h-3 ml-1" /> : <ArrowDown className="w-3 h-3 ml-1" />
                  )}
                </div>
              </th>
              <th className="text-left p-2 whitespace-nowrap cursor-pointer" onClick={() => handleSort('status')}>
                <div className="flex items-center">
                  Status
                  {sortField === 'status' && (
                    sortDirection === 'asc' ? <ArrowUp className="w-3 h-3 ml-1" /> : <ArrowDown className="w-3 h-3 ml-1" />
                  )}
                </div>
              </th>

              <th className="text-left p-2 whitespace-nowrap cursor-pointer" onClick={() => handleSort('operational_status')}>
                <div className="flex items-center">
                  Operational Status
                  {sortField === 'operational_status' && (
                    sortDirection === 'asc' ? <ArrowUp className="w-3 h-3 ml-1" /> : <ArrowDown className="w-3 h-3 ml-1" />
                  )}
                </div>
              </th>
              
                            {/* Traffic column */}
                            <th className="text-left p-2 whitespace-nowrap">
                <div className="flex items-center">
                  Traffic
                  <button
                    onClick={toggleAllMiniGraphs}
                    className={`ml-1 p-0.5 rounded ${
                      showAllMiniGraphs 
                        ? 'bg-gray-100 text-gray-700 hover:bg-gray-200' 
                        : 'bg-indigo-600 text-white hover:bg-indigo-700'
                    }`}
                    title={showAllMiniGraphs ? "Hide all graphs" : "Show all graphs"}
                  >
                    {showAllMiniGraphs ? <ChevronUp className="w-3 h-3" /> : <BarChart2 className="w-3 h-3" />}
                  </button>
                </div>
              </th>

              <th className="text-left p-2 whitespace-nowrap">Connected To</th>

              <th className="text-left p-2 whitespace-nowrap">Actions</th>
            </tr>
          </thead>
          <tbody>
            {sortedPorts.map((port) => {
              const isAvailable = port.status === 'Available' && !alreadyAssignedPorts.includes(port.port_number);
              const isUsed = port.connected_device_id != null;
              const isDisabled = port.status === 'Disabled';
              const isSelectable = isAvailable && !editingPortId;
              const isEditing = editingPortId === port.id;
              
              return (
                <tr 
                  key={port.id} 
                  className={`border-b hover:bg-gray-50 ${!isSelectable ? 'opacity-90' : ''} ${isEditing ? 'bg-indigo-50' : ''}`}
                  onClick={() => isSelectable ? handlePortSelect(port) : null}
                  style={{ cursor: isSelectable ? 'pointer' : 'default' }}
                >
                  <td className="p-2 font-medium">{port.port_number}</td>
                  <td className="p-2">{port.port_name || '-'}</td>
                  <td className="p-2">
                    {isEditing ? (
                      <div className="flex space-x-1" onClick={(e) => e.stopPropagation()}>
                        {/* Speed Selection */}
                        <select 
                          className="text-xs p-1 border border-gray-300 rounded w-24"
                          value={editingSpeed}
                          onChange={(e) => setEditingSpeed(e.target.value)}
                        >
                          <option value="">Select Speed</option>
                          {speedOptions.map(option => (
                            <option key={option.value} value={option.value}>{option.value}</option>
                          ))}
                        </select>
                        
                        {/* Save and cancel buttons for speed */}
                        <div className="flex space-x-1">
                          <button 
                            className="p-1 bg-green-100 text-green-700 rounded hover:bg-green-200 flex items-center justify-center"
                            onClick={async () => {
                              if (editingSpeed) {
                                const success = await handleSaveSpeed(port);
                                if (success) {
                                  finishEditing();
                                }
                              } else {
                                finishEditing();
                              }
                            }}
                            disabled={allocationInProgress}
                          >
                            {allocationInProgress ? (
                              <RefreshCw className="w-3 h-3 animate-spin" />
                            ) : (
                              <Check className="w-3 h-3" />
                            )}
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        {port.max_speed ? `${port.max_speed} Mbps` : (
                          <span className="text-yellow-600">Unknown</span>
                        )}
                      </div>
                    )}
                  </td>
                  <td className="p-2">
                    <div className={`px-2 py-1 rounded-full text-xs w-fit font-medium flex items-center
                      ${port.status === 'Available' ? 'bg-green-100 text-green-800' :
                        port.status === 'Used' ? 'bg-red-100 text-red-800' : 
                        port.status === 'Down' ? 'bg-yellow-100 text-yellow-800' :
                        port.status === 'Disabled' ? 'bg-gray-200 text-gray-700' :
                        'bg-gray-100 text-gray-800'}`}
                    >
                      <span className={`w-2 h-2 rounded-full mr-1.5
                        ${port.status === 'Available' ? 'bg-green-500' :
                          port.status === 'Used' ? 'bg-red-500' : 
                          port.status === 'Down' ? 'bg-yellow-500' :
                          port.status === 'Disabled' ? 'bg-gray-500' :
                          'bg-gray-400'}`}
                      />
                      {port.status}
                    </div>
                  </td>

                  {/* Operational Status column */}
                  <td className="p-2">
                    <div className="flex items-center justify-between">
                      {renderOperationalStatus(port.id)}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          refreshPortStatus(port.id);
                        }}
                        className="ml-1 p-1 text-gray-500 hover:text-indigo-600 rounded-full hover:bg-indigo-50"
                        title="Refresh operational status"
                      >
                        <RefreshCw className="w-3 h-3" />
                      </button>
                    </div>
                  </td>
                                    {/* Traffic Graph Column - Always show the graph when showAllMiniGraphs is true */}
                                    <td className="p-2" onClick={(e) => e.stopPropagation()}>
                    {showAllMiniGraphs ? (
                      <MiniPortTrafficGraph port_id={port.id} />
                    ) : (
                      <button
                        onClick={toggleAllMiniGraphs}
                        className="p-1 text-xs rounded bg-indigo-100 text-indigo-700 hover:bg-indigo-200 flex items-center"
                        title="Show traffic graphs"
                      >
                        <BarChart2 className="w-3 h-3" />
                      </button>
                    )}
                  </td>
                  <td className="p-2">
                    {isEditing ? (
                      <div className="flex items-center space-x-1" onClick={(e) => e.stopPropagation()}>
                        {/* Device Selection Dropdown */}
                        <select
                          className="text-xs p-1 border border-gray-300 rounded w-52"
                          value={selectedDeviceId}
                          onChange={(e) => setSelectedDeviceId(e.target.value)}
                          disabled={deviceLoading || allocationInProgress}
                        >
                          <option value="">Select Device</option>
                          {availableDevices.map(device => (
                            <option 
                              key={device.id} 
                              value={device.id}
                              disabled={device.type === 'action' && !isUsed && device.id === 'unallocate'}
                            >
                              {device.label}
                            </option>
                          ))}
                        </select>
                        
                        {/* Save and cancel buttons for device */}
                        <div className="flex space-x-1">
                          <button 
                            className="p-1 bg-green-100 text-green-700 rounded hover:bg-green-200 flex items-center justify-center"
                            onClick={async () => {
                              if (selectedDeviceId) {
                                const success = await handleDeviceAction(port, selectedDeviceId);
                                if (success) {
                                  finishEditing();
                                }
                              } else {
                                finishEditing();
                              }
                            }}
                            disabled={allocationInProgress}
                          >
                            {allocationInProgress ? (
                              <RefreshCw className="w-3 h-3 animate-spin" />
                            ) : (
                              <Check className="w-3 h-3" />
                            )}
                          </button>
                          <button 
                            className="p-1 bg-red-100 text-red-700 rounded hover:bg-red-200 flex items-center justify-center"
                            onClick={() => finishEditing()}
                            disabled={allocationInProgress}
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      </div>
                    ) : port.connected_device_id ? (
                      <div className="flex items-center justify-between">
                        <span className="text-indigo-600 font-medium">
                        <a href={`/admin/inventory/${port.connected_device_type}/${port.connected_device_id}`}>
    {port.connected_device_label} #{port.connected_device_id}
</a>
                        </span>
                        <button 
                          className="ml-1 p-1 text-gray-500 hover:text-indigo-600 rounded-full hover:bg-indigo-50"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEdit(port);
                          }}
                          title="Edit device assignment"
                        >
                          <Edit className="w-3 h-3" />
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">-</span>
                        <button 
                          className="ml-1 p-1 text-gray-500 hover:text-indigo-600 rounded-full hover:bg-indigo-50"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEdit(port);
                          }}
                          title="Assign device"
                        >
                          <Edit className="w-3 h-3" />
                        </button>
                      </div>
                    )}
                  </td>
                  <td className="p-2">
                    <div className="flex space-x-1" onClick={(e) => e.stopPropagation()}>
                      {/* Edit Speed Button */}
                      <button
                        onClick={() => handleEdit(port)}
                        title="Edit port speed"
                        className="p-1 text-xs rounded bg-blue-100 text-blue-700 hover:bg-blue-200 flex items-center"
                      >
                        <Edit className="w-3 h-3" />
                      </button>
                      
                      {/* Power Button (Enable/Disable) */}
                      {isDisabled ? (
                        <button
                          onClick={() => togglePortStatus(port, 'enable')}
                          title="Enable port"
                          className="p-1 text-xs rounded bg-green-100 text-green-700 hover:bg-green-200 flex items-center"
                        >
                          <Power className="w-3 h-3" />
                        </button>
                      ) : (
                        <button
                          onClick={() => togglePortStatus(port, 'disable')}
                          title="Disable port"
                          className="p-1 text-xs rounded bg-gray-100 text-gray-700 hover:bg-gray-200 flex items-center"
                        >
                          <Power className="w-3 h-3" />
                        </button>
                      )}
                      {/* Full traffic graph button */}
                      <button
                        onClick={(e) => handleShowTraffic(port, e)}
                        title="View full traffic graph"
                        className="p-1 text-xs rounded bg-indigo-100 text-indigo-700 hover:bg-indigo-200 flex items-center"
                      >
                        <LineChart className="w-3 h-3" />
                      </button>
                    </div>
                  </td>
                </tr>
              );
            })}

            {sortedPorts.length === 0 && (
              <tr>
                <td colSpan="8" className="p-4 text-center text-gray-500">
                  No ports found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      
      <div className="p-2 text-xs text-gray-500 border-t flex items-center">
        <Info className="w-3 h-3 mr-1" />
        <span>Click the edit button in the "Connected To" column to assign/unassign devices</span>
      </div>
      
      {/* Render dialogs */}
      {renderConfirmationDialog()}

      {/* Manual Add Port Modal - SUPER SIMPLE VERSION */}
      {showAddPortModal && (
        <div 
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            backgroundColor: 'rgba(255, 0, 0, 0.8)', // RED background to see if it shows
            zIndex: 999999,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          onClick={(e) => {
            console.log('Backdrop clicked - closing modal');
            setShowAddPortModal(false);
          }}
        >
          <div 
            style={{
              backgroundColor: 'white',
              padding: '20px',
              borderRadius: '8px',
              border: '3px solid blue', // Blue border to make it super visible
              width: '400px',
              height: '300px',
              textAlign: 'center'
            }}
            onClick={(e) => {
              e.stopPropagation();
              console.log('Modal content clicked');
            }}
                      >
            <h1 style={{ color: 'black', fontSize: '24px', marginBottom: '20px' }}>
              🚀 MODAL IS WORKING! 🚀
            </h1>
            <p style={{ color: 'black', fontSize: '16px', marginBottom: '20px' }}>
              If you can see this, the modal is rendering correctly!
            </p>
            <button 
              onClick={() => {
                console.log('Closing modal via close button');
                setShowAddPortModal(false);
              }}
              style={{
                backgroundColor: 'red',
                color: 'white',
                padding: '10px 20px',
                border: 'none',
                borderRadius: '4px',
                fontSize: '16px',
                cursor: 'pointer'
              }}
            >
              Close Modal
            </button>
          </div>
        </div>
      )}
              {/* Port Number */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Port Number *
                </label>
                <input
                  type="text"
                  value={newPortData.port_number}
                  onChange={(e) => setNewPortData(prev => ({ ...prev, port_number: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="e.g., 1, Gi0/1, Te1/0/1"
                  disabled={addPortInProgress}
                />
              </div>
              
              {/* Port Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Port Description
                </label>
                <input
                  type="text"
                  value={newPortData.port_name}
                  onChange={(e) => setNewPortData(prev => ({ ...prev, port_name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Optional description"
                  disabled={addPortInProgress}
                />
              </div>
              
              {/* Port Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Port Type
                </label>
                <select
                  value={newPortData.port_type}
                  onChange={(e) => setNewPortData(prev => ({ ...prev, port_type: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  disabled={addPortInProgress}
                >
                  <option value="Ethernet">Ethernet</option>
                  <option value="FastEthernet">Fast Ethernet</option>
                  <option value="GigabitEthernet">Gigabit Ethernet</option>
                  <option value="TenGigabitEthernet">10 Gigabit Ethernet</option>
                  <option value="Management">Management</option>
                </select>
              </div>
              
              {/* Max Speed */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Port Speed (Mbps)
                </label>
                <select
                  value={newPortData.max_speed}
                  onChange={(e) => setNewPortData(prev => ({ ...prev, max_speed: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  disabled={addPortInProgress}
                >
                  <option value="">Select Speed</option>
                  <option value="100">100 Mbps (Fast Ethernet)</option>
                  <option value="1000">1000 Mbps (Gigabit)</option>
                  <option value="2500">2.5 Gbps</option>
                  <option value="5000">5 Gbps</option>
                  <option value="10000">10 Gbps</option>
                  <option value="25000">25 Gbps</option>
                  <option value="40000">40 Gbps</option>
                  <option value="100000">100 Gbps</option>
                </select>
              </div>
              
              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Initial Status
                </label>
                <select
                  value={newPortData.status}
                  onChange={(e) => setNewPortData(prev => ({ ...prev, status: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  disabled={addPortInProgress}
                >
                  <option value="Available">Available</option>
                  <option value="Disabled">Disabled</option>
                  <option value="Down">Down</option>
                </select>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                className="px-4 py-2 border border-gray-300 bg-white rounded-md text-gray-700 hover:bg-gray-50"
                onClick={() => setShowAddPortModal(false)}
                disabled={addPortInProgress}
              >
                Cancel
              </button>
              <button
                className={`px-4 py-2 rounded-md text-white flex items-center
                  ${addPortInProgress ? 'bg-green-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'}`}
                onClick={addPortManually}
                disabled={addPortInProgress}
              >
                {addPortInProgress ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <Server className="w-4 h-4 mr-2" />
                    Add Port
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Traffic Modal */}
      {showTrafficModal && selectedPortForTraffic && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-11/12 max-w-6xl mx-4 overflow-hidden flex flex-col max-h-[90vh]">
            <div className="p-4 border-b bg-gray-50 flex justify-between items-center sticky top-0 z-10">
              <h2 className="text-lg font-bold flex items-center">
                <LineChart className="w-5 h-5 mr-2 text-indigo-600" />
                Port {selectedPortForTraffic.port_number} Traffic
              </h2>
              <button 
                onClick={() => setShowTrafficModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="p-5 overflow-y-auto flex-grow">
              <div className="bg-gray-50 p-3 rounded-md mb-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <div className="text-xs text-gray-500">Port Number</div>
                    <div className="font-medium">{selectedPortForTraffic.port_number}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Port Name</div>
                    <div className="font-medium">{selectedPortForTraffic.port_name || '-'}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Port Speed</div>
                    <div className="font-medium">
                      {selectedPortForTraffic.max_speed 
                        ? `${formatPortSpeed(selectedPortForTraffic.max_speed)}`
                        : 'Unknown'}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Status</div>
                    <div className="font-medium">
                      {renderOperationalStatus(selectedPortForTraffic.id)}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Connected Device</div>
                    <div className="font-medium">
                      {selectedPortForTraffic.connected_device_id
                        ? `${selectedPortForTraffic.connected_device_type === 'blade' ? 'Blade' : 'Dedicated'} Server #${selectedPortForTraffic.connected_device_id}`
                        : 'Not connected'}
                    </div>
                  </div>
                </div>
              </div>
              
              <PortTrafficGraph port_id={selectedPortForTraffic.id} days={30} />
            </div>
          </div>
        </div>
      )}

      {/* SUPER SIMPLE TEST MODAL */}
      {showAddPortModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundColor: 'rgba(255, 0, 0, 0.9)',
          zIndex: 999999,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '40px',
            borderRadius: '10px',
            border: '5px solid blue',
            textAlign: 'center'
          }}>
            <h1 style={{ color: 'black', fontSize: '32px', marginBottom: '20px' }}>
              🎉 MODAL WORKS! 🎉
            </h1>
            <button 
              onClick={() => setShowAddPortModal(false)}
              style={{
                backgroundColor: 'red',
                color: 'white',
                padding: '15px 30px',
                border: 'none',
                borderRadius: '5px',
                fontSize: '18px',
                cursor: 'pointer'
              }}
            >
              CLOSE
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SwitchPortsManager;