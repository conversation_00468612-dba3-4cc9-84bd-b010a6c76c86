import { useEffect } from 'react';

/**
 * Component to handle browser UI elements on mobile devices (especially iPhone)
 * This component detects when the browser's top and bottom UI elements appear/disappear
 * and adds padding to compensate for the space they take.
 * It also ensures modals are properly positioned and not cut off.
 */
const BrowserUIHandler = () => {
  useEffect(() => {
    // Initial viewport height
    let initialViewportHeight = window.innerHeight;
    let lastScrollPosition = window.scrollY;
    let isModalOpen = false;

    // Function to check if a modal is open
    const checkForOpenModal = () => {
      // Look for modal elements that are visible
      const modalElements = document.querySelectorAll('.fixed.inset-0.bg-black.bg-opacity-50');
      isModalOpen = modalElements.length > 0;
      return isModalOpen;
    };

    // Function to update CSS variables based on viewport height changes
    const handleResize = () => {
      const currentViewportHeight = window.innerHeight;
      const currentScrollPosition = window.scrollY;
      const hasScrolled = Math.abs(currentScrollPosition - lastScrollPosition) > 10;

      // Always update the dynamic viewport height variable
      const vh = currentViewportHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);

      // Check if a modal is open
      const modalOpen = checkForOpenModal();

      // If a modal is open, ensure it's properly positioned
      if (modalOpen) {
        // For modals, we want to minimize compensation to prevent cutting off content
        document.documentElement.style.setProperty('--browser-ui-compensation-top', '0px');
        document.documentElement.style.setProperty('--browser-ui-compensation-bottom', '0px');

        // Force modals to use the dynamic viewport height
        const modalContainers = document.querySelectorAll('.fixed.inset-0.flex.items-center.justify-center');
        modalContainers.forEach(container => {
          container.style.height = `calc(100 * var(--vh))`;
          container.style.maxHeight = `calc(100 * var(--vh))`;

          // Ensure modal content is scrollable if needed
          const modalContent = container.querySelector('div > div');
          if (modalContent) {
            modalContent.style.maxHeight = `calc(100 * var(--vh) - 20px)`;
            modalContent.style.overflowY = 'auto';
            modalContent.style.WebkitOverflowScrolling = 'touch';
          }
        });
      }
      // If no modal is open, handle normal browser UI compensation
      else if (hasScrolled || Math.abs(currentViewportHeight - initialViewportHeight) > 50) {
        // If the viewport height decreased significantly or user has scrolled, it means browser UI elements appeared
        if (currentViewportHeight < initialViewportHeight - 50 ||
            (hasScrolled && currentScrollPosition > 50)) {
          // Calculate the height difference - use smaller values for our more compact UI
          const topDifference = Math.min(30, (initialViewportHeight - currentViewportHeight) / 3);
          const bottomDifference = Math.min(40, (initialViewportHeight - currentViewportHeight) / 2);

          // Update CSS variables
          document.documentElement.style.setProperty('--browser-ui-compensation-top', `${topDifference}px`);
          document.documentElement.style.setProperty('--browser-ui-compensation-bottom', `${bottomDifference}px`);
        } else if (currentViewportHeight >= initialViewportHeight - 20 || currentScrollPosition < 10) {
          // Reset CSS variables when browser UI elements disappear or user scrolls to top
          document.documentElement.style.setProperty('--browser-ui-compensation-top', '0px');
          document.documentElement.style.setProperty('--browser-ui-compensation-bottom', '0px');

          // Update the initial viewport height if it's larger
          if (currentViewportHeight > initialViewportHeight) {
            initialViewportHeight = currentViewportHeight;
          }
        }
      }

      // Update last scroll position
      lastScrollPosition = currentScrollPosition;
    };

    // Add event listeners
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize);
    window.addEventListener('orientationchange', () => {
      // Reset initial height after orientation change
      setTimeout(() => {
        initialViewportHeight = window.innerHeight;
        handleResize();
      }, 300);
    });

    // Listen for modal open/close events by observing DOM changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' &&
            (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0)) {
          // Check if a modal was added or removed
          const wasModalOpen = isModalOpen;
          const isNowModalOpen = checkForOpenModal();

          // If modal state changed, update the UI
          if (wasModalOpen !== isNowModalOpen) {
            handleResize();
          }
        }
      });
    });

    // Start observing the document body for modal changes
    observer.observe(document.body, { childList: true, subtree: true });

    // Initial call to set up
    handleResize();

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleResize);
      window.removeEventListener('orientationchange', handleResize);
      observer.disconnect();
    };
  }, []);

  // This component doesn't render anything
  return null;
};

export default BrowserUIHandler;
