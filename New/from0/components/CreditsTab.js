import React, { useState, useEffect } from 'react';
import {
  RefreshCw,
  ArrowUp,
  ArrowDown,
  Plus,
  Minus,
  Eye,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Filter,
  Search,
  FileText,
  Clock,
  Wallet,
  X,
  XCircle
} from 'lucide-react';
import InvoiceView from './InvoiceView'; // Import InvoiceView component
import { API_URL } from '../config';
const CreditsTab = ({ userId, token }) => {
  // State for credits data
  const [credits, setCredits] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Sorting and filtering state
  const [sortField, setSortField] = useState('created');
  const [sortDirection, setSortDirection] = useState('desc');
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('All');
  const [statusFilter, setStatusFilter] = useState('All');

  // Stats
  const [stats, setStats] = useState({
    totalCredits: 0,
    pendingCredits: 0,
    usedCredits: 0,
    availableCredits: 0
  });

  // Add Funds Modal
  const [addFundsModalOpen, setAddFundsModalOpen] = useState(false);
  const [addFundsData, setAddFundsData] = useState({
    amount: '',
    description: 'Add funds to account'
  });

  // Invoice view modal state
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [invoiceModalOpen, setInvoiceModalOpen] = useState(false);

  // Fetch credits on component mount or when userId changes
  useEffect(() => {
    if (userId) {
      fetchCredits();
    }
  }, [userId]);

  // Fetch credits data
  const fetchCredits = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_URL}/api_accounts.php?f=get_user_credits`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          user_id: userId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      // Process credits data
      setCredits(data);

      // Calculate stats
      let totalCredits = 0;
      let pendingCredits = 0;
      let usedCredits = 0;
      let availableCredits = 0;

      data.forEach(credit => {
        const amount = parseFloat(credit.amount || 0);

        if (credit.status === 'Pending') {
          pendingCredits += amount;
        } else if (credit.status === 'Used') {
          usedCredits += amount;
        } else if (credit.status === 'Available') {
          availableCredits += amount;
        }

        totalCredits += amount;
      });

      setStats({
        totalCredits: totalCredits.toFixed(2),
        pendingCredits: pendingCredits.toFixed(2),
        usedCredits: usedCredits.toFixed(2),
        availableCredits: availableCredits.toFixed(2)
      });

      setLoading(false);
    } catch (err) {
      console.error("Error fetching credits:", err);
      setError("Failed to load credit data. Please try again.");
      setLoading(false);

      // Set sample data for preview
      const sampleData = generateSampleCredits(userId);
      setCredits(sampleData);

      // Calculate sample stats
      let totalCredits = 0;
      let pendingCredits = 0;
      let usedCredits = 0;
      let availableCredits = 0;

      sampleData.forEach(credit => {
        const amount = parseFloat(credit.amount || 0);

        if (credit.status === 'Pending') {
          pendingCredits += amount;
        } else if (credit.status === 'Used') {
          usedCredits += amount;
        } else if (credit.status === 'Available') {
          availableCredits += amount;
        }

        totalCredits += amount;
      });

      setStats({
        totalCredits: totalCredits.toFixed(2),
        pendingCredits: pendingCredits.toFixed(2),
        usedCredits: usedCredits.toFixed(2),
        availableCredits: availableCredits.toFixed(2)
      });
    }
  };

  // Generate sample credit data for preview
  const generateSampleCredits = (userId) => {
    const types = ['Purchase', 'Refund', 'Bonus', 'Adjustment'];
    const statuses = ['Available', 'Used', 'Pending', 'Expired'];

    return Array.from({ length: 12 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i * 5);
      const type = types[i % types.length];
      const status = statuses[i % statuses.length];
      let amount = 0;

      if (type === 'Purchase' || type === 'Bonus') {
        amount = (100 + i * 10).toFixed(2);
      } else if (type === 'Refund') {
        amount = (50 + i * 5).toFixed(2);
      } else {
        amount = (i % 2 === 0 ? 1 : -1) * (25 + i * 2).toFixed(2);
      }

      return {
        id: 5000 + i,
        user_id: userId,
        amount: amount,
        type: type,
        status: status,
        created: date.toISOString(),
        invoice_id: type === 'Purchase' ? `${1000 + i}` : null,
        description: getDescription(type, amount, i)
      };
    });
  };

  // Helper function to generate sample descriptions
  const getDescription = (type, amount, index) => {
    switch (type) {
      case 'Purchase':
        return `Credit purchase #${1000 + index}`;
      case 'Refund':
        return `Refund for order #${2000 + index}`;
      case 'Bonus':
        return `Loyalty bonus - ${index % 3 === 0 ? 'Monthly' : 'Special'} reward`;
      case 'Adjustment':
        return `Manual adjustment by admin`;
      default:
        return `Credit transaction`;
    }
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle type filter
  const handleTypeFilter = (e) => {
    setTypeFilter(e.target.value);
  };

  // Handle status filter
  const handleStatusFilter = (e) => {
    setStatusFilter(e.target.value);
  };

  // Handle sort
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get sort icon
  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <ArrowUp className="w-4 h-4 inline ml-1" /> :
      <ArrowDown className="w-4 h-4 inline ml-1" />;
  };

  // Render status badge
  const renderStatusBadge = (status) => {
    const badgeClasses = {
      'Available': 'bg-green-100 text-green-800',
      'Used': 'bg-blue-100 text-blue-800',
      'Pending': 'bg-yellow-100 text-yellow-800',
      'Expired': 'bg-gray-100 text-gray-800',
      'Cancelled': 'bg-red-100 text-red-800'
    };

    const icons = {
      'Available': <CheckCircle className="w-4 h-4 mr-1" />,
      'Used': <DollarSign className="w-4 h-4 mr-1" />,
      'Pending': <Clock className="w-4 h-4 mr-1" />,
      'Expired': <AlertTriangle className="w-4 h-4 mr-1" />,
      'Cancelled': <AlertTriangle className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertTriangle className="w-4 h-4 mr-1" />}
        {status}
      </span>
    );
  };

  // Render type badge
  const renderTypeBadge = (type) => {
    const badgeClasses = {
      'Purchase': 'bg-indigo-100 text-indigo-800',
      'Refund': 'bg-emerald-100 text-emerald-800',
      'Bonus': 'bg-purple-100 text-purple-800',
      'Adjustment': 'bg-amber-100 text-amber-800'
    };

    const icons = {
      'Purchase': <Plus className="w-4 h-4 mr-1" />,
      'Refund': <Minus className="w-4 h-4 mr-1" />,
      'Bonus': <Plus className="w-4 h-4 mr-1" />,
      'Adjustment': <DollarSign className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[type] || 'bg-gray-100 text-gray-800'}`}>
        {icons[type] || <DollarSign className="w-4 h-4 mr-1" />}
        {type}
      </span>
    );
  };

  // Filter credits
  const filteredCredits = credits.filter(credit => {
    const matchesSearch =
      (credit.description && credit.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (credit.invoice_id && credit.invoice_id.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesType = typeFilter === 'All' || credit.type === typeFilter;
    const matchesStatus = statusFilter === 'All' || credit.status === statusFilter;

    return matchesSearch && matchesType && matchesStatus;
  });

  // Handle opening the Add Funds modal
  const handleOpenAddFundsModal = () => {
    setAddFundsModalOpen(true);
  };

  // Handle closing the Add Funds modal
  const handleCloseAddFundsModal = () => {
    setAddFundsModalOpen(false);
    setAddFundsData({
      amount: '',
      description: 'Add funds to account'
    });
  };

  // Handle changes in Add Funds form
  const handleAddFundsChange = (e) => {
    const { name, value } = e.target;
    setAddFundsData({
      ...addFundsData,
      [name]: value
    });
  };

  // Handle Add Funds submission
  const handleAddFundsSubmit = async () => {
    if (!addFundsData.amount || parseFloat(addFundsData.amount) <= 0) {
      alert('Please enter a valid amount');
      return;
    }

    try {
      setLoading(true);

      // Log what we're about to send
      const requestData = {
        token: token,
        user_id: userId,
        amount: parseFloat(addFundsData.amount),
        description: addFundsData.description || 'Add funds to account'
      };
      console.log('Sending request to generate_credit_invoice:', requestData);

      const response = await fetch(`${API_URL}/api_admin_invoices.php?f=generate_credit_invoice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      // Log the raw response status
      console.log('Response status:', response.status, response.statusText);

      // Get the response text regardless of status code
      const responseText = await response.text();
      console.log('Raw API Response:', responseText);

      // If response is not OK, try to parse it anyway to get error details
      if (!response.ok) {
        let errorDetails = 'Unknown server error';
        try {
          const errorJson = JSON.parse(responseText);
          errorDetails = errorJson.error || errorJson.message || `HTTP error ${response.status}`;
          if (errorJson.trace) {
            console.error('Server error trace:', errorJson.trace);
          }
        } catch (parseError) {
          errorDetails = `HTTP error ${response.status}. Raw response: ${responseText.substring(0, 200)}`;
        }
        throw new Error(errorDetails);
      }

      // Try to parse the response as JSON
      let result;
      try {
        result = JSON.parse(responseText);
        console.log('Parsed API Response:', result);
      } catch (parseError) {
        console.error('Error parsing response:', parseError);
        throw new Error(`Server returned invalid JSON. Response: ${responseText.substring(0, 200)}...`);
      }

      if (result.success) {
        alert(`Invoice #${result.invoice_id} generated successfully! The credits will become available once the invoice is paid.`);
        handleCloseAddFundsModal();
        fetchCredits(); // Refresh credits to show the new pending credit
      } else {
        throw new Error(result.error || 'Unknown error occurred');
      }
    } catch (err) {
      console.error("Error generating invoice:", err);
      alert('Failed to generate invoice: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  // Sort credits
  const sortedCredits = [...filteredCredits].sort((a, b) => {
    let comparison = 0;

    if (sortField === 'amount') {
      comparison = parseFloat(a.amount) - parseFloat(b.amount);
    } else if (sortField === 'created') {
      const dateA = new Date(a.created);
      const dateB = new Date(b.created);
      comparison = dateA - dateB;
    } else if (sortField === 'type') {
      comparison = a.type.localeCompare(b.type);
    } else if (sortField === 'status') {
      comparison = a.status.localeCompare(b.status);
    } else {
      comparison = a.id - b.id;
    }

    return sortDirection === 'asc' ? comparison : -comparison;
  });

  // Generate credit stats cards
  const generateCreditStatsCards = () => {
    const statsCards = [
      {
        title: 'Total Credits',
        value: `$${stats.totalCredits}`,
        icon: <Wallet className="text-indigo-700" size={36} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-info'
      },
      {
        title: 'Available Credits',
        value: `$${stats.availableCredits}`,
        icon: <CheckCircle className="text-success" size={36} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-success'
      },
      {
        title: 'Used Credits',
        value: `$${stats.usedCredits}`,
        icon: <DollarSign className="text-blue-600" size={36} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-info'
      },
      {
        title: 'Pending Credits',
        value: `$${stats.pendingCredits}`,
        icon: <Clock className="text-warning" size={36} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-warning'
      }
    ];

    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
        {statsCards.map((stat, index) => (
          <div
            key={index}
            className="bg-white border rounded p-4 flex items-center justify-between"
          >
            <div>
              <div className="text-xs text-gray-600">{stat.title}</div>
              <div className="text-lg font-bold mt-1">{stat.value}</div>
            </div>
            <div className={`card-custom-icon ${stat.iconClass}`}>
              {stat.icon}
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Handle view invoice
  const handleViewInvoice = async (credit) => {
    if (!credit.invoice_id) {
      return; // Skip if no invoice_id is available
    }

    try {
      setLoading(true);

      // Temporary loading state for invoice modal
      const tempInvoice = {
        id: credit.invoice_id,
        invoice_id: credit.invoice_id,
        status: 'Loading...',
        description: 'Loading invoice details...',
        user_id: userId,
        amount: credit.amount,
        client: 'Client #' + userId,
        clientId: userId
      };

      // Open the modal with loading state
      setSelectedInvoice(tempInvoice);
      setInvoiceModalOpen(true);

      // Fetch the invoice data - using invoice_id as a number if possible
      const invoiceId = parseInt(credit.invoice_id, 10) || credit.invoice_id;
      console.log('Fetching invoice with ID:', invoiceId);

      const response = await fetch(`${API_URL}/api_accounts.php?f=get_user_invoice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          user_id: userId,
          invoice_id: invoiceId
        })
      });

      // Log the raw response for debugging
      const responseText = await response.text();
      console.log('Invoice API Response:', responseText);

      // If the response is empty or invalid, try the alternative approach
      if (!responseText || responseText.trim() === '') {
        console.log('Empty response, trying alternative API call with id parameter');
        const altResponse = await fetch(`${API_URL}/api_accounts.php?f=get_user_invoice`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: token,
            user_id: userId,
            id: invoiceId
          })
        });

        const altResponseText = await altResponse.text();
        console.log('Alternative API Response:', altResponseText);

        if (altResponseText && altResponseText.trim() !== '') {
          try {
            const invoiceData = JSON.parse(altResponseText);
            updateInvoiceModal(invoiceData, credit);
            return;
          } catch (parseError) {
            console.error('Error parsing alternative response:', parseError);
          }
        }
      }

      // If we got a response from the first API call, try to parse it
      try {
        const invoiceData = JSON.parse(responseText);
        updateInvoiceModal(invoiceData, credit);
      } catch (parseError) {
        console.error('Error parsing invoice response:', parseError);
        // Fall back to creating an invoice from credit data
        createFallbackInvoice(credit);
      }
    } catch (err) {
      console.error("Error fetching invoice:", err);
      // Fall back to creating an invoice from credit data
      createFallbackInvoice(credit);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to update the invoice modal with data
  const updateInvoiceModal = (invoiceData, credit) => {
    if (!invoiceData || typeof invoiceData !== 'object') {
      createFallbackInvoice(credit);
      return;
    }

    // Process invoice data
    const processedInvoice = {
      id: invoiceData.id || credit.invoice_id,
      invoice_id: invoiceData.id || credit.invoice_id,
      user_id: invoiceData.user_id || userId,
      invoice_type: invoiceData.invoice_type || 'invoice',
      amount: invoiceData.amount || credit.amount,
      payment_date: invoiceData.payment_date || credit.created,
      payment_method: invoiceData.payment_method || 'Unknown',
      status: invoiceData.status || credit.status,
      order_id: invoiceData.order_id || null,
      description: invoiceData.description || credit.description || '',
      full_description: invoiceData.full_description || invoiceData.description || credit.description || '',
      // Additional fields needed for InvoiceView
      client: 'Client #' + userId,
      clientId: userId,
      clientEmail: '',
      date: invoiceData.payment_date || credit.created,
      dueDate: invoiceData.due_date || '',
      paymentDate: invoiceData.paid_date || '',
      items: [{
        description: invoiceData.description || credit.description || 'Invoice item',
        quantity: 1,
        unitPrice: '€' + parseFloat(invoiceData.amount || credit.amount).toFixed(2),
        total: '€' + parseFloat(invoiceData.amount || credit.amount).toFixed(2)
      }],
      subtotal: '€' + parseFloat(invoiceData.amount || credit.amount).toFixed(2),
      tax: '€0.00',
      total: '€' + parseFloat(invoiceData.amount || credit.amount).toFixed(2),
      notes: invoiceData.description || credit.description || '',
      // Flag to identify if this is a proforma invoice
      isProforma: invoiceData.type === 'proforma'
    };

    // Update the selected invoice
    setSelectedInvoice(processedInvoice);
  };

  // Helper function to create a fallback invoice from credit data
  const createFallbackInvoice = (credit) => {
    const fallbackInvoice = {
      id: credit.invoice_id,
      invoice_id: credit.invoice_id,
      user_id: userId,
      amount: credit.amount,
      payment_date: credit.created,
      payment_method: 'Unknown',
      status: credit.status,
      description: credit.description || '',
      // Additional fields needed for InvoiceView
      client: 'Client #' + userId,
      clientId: userId,
      clientEmail: '',
      date: credit.created,
      items: [{
        description: credit.description || 'Credit purchase',
        quantity: 1,
        unitPrice: '€' + parseFloat(credit.amount).toFixed(2),
        total: '€' + parseFloat(credit.amount).toFixed(2)
      }],
      subtotal: '€' + parseFloat(credit.amount).toFixed(2),
      tax: '€0.00',
      total: '€' + parseFloat(credit.amount).toFixed(2),
      notes: 'Created from credit transaction data.'
    };

    setSelectedInvoice(fallbackInvoice);
  };

  // Handle invoice status update
  const handleInvoiceStatusUpdate = async (id, status) => {
    try {
      setLoading(true);

      const response = await fetch(`${API_URL}/api_admin_invoices.php?f=update_invoice_status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          id: id,
          status: status
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // If this was a credit invoice and it was marked as paid
        if (result.credit_activated) {
          alert(`Invoice has been marked as ${status} and credits have been activated.`);
        } else if (result.type === 'conversion' && result.new_id) {
          alert(`Proforma invoice has been paid and converted to invoice #${result.new_id}`);
        } else {
          alert(`Invoice status has been updated to ${status}.`);
        }

        // Close the invoice modal and refresh credits
        handleCloseInvoiceModal();
        fetchCredits();
      } else {
        throw new Error(result.error || 'Unknown error occurred');
      }
    } catch (err) {
      console.error("Error updating invoice status:", err);
      alert('Failed to update invoice status: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle close invoice modal
  const handleCloseInvoiceModal = () => {
    setInvoiceModalOpen(false);
    setSelectedInvoice(null);
  };

  return (
    <div className="space-y-4">
      {/* Credit Stats Cards */}
      {generateCreditStatsCards()}

      {/* Credits Table */}
      <div className="bg-white border rounded-md">
        <div className="p-3 border-b flex flex-wrap justify-between items-center gap-3">
          <div className="flex flex-wrap gap-2">
            <div className="relative">
              <select
                className="pl-3 pr-8 py-1.5 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                value={typeFilter}
                onChange={handleTypeFilter}
              >
                <option value="All">All Types</option>
                <option value="Purchase">Purchase</option>
                <option value="Refund">Refund</option>
                <option value="Bonus">Bonus</option>
                <option value="Adjustment">Adjustment</option>
              </select>
              <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
            </div>

            <div className="relative">
              <select
                className="pl-3 pr-8 py-1.5 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                value={statusFilter}
                onChange={handleStatusFilter}
              >
                <option value="All">All Statuses</option>
                <option value="Available">Available</option>
                <option value="Used">Used</option>
                <option value="Pending">Pending</option>
                <option value="Expired">Expired</option>
              </select>
              <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
            </div>

            <button
              onClick={fetchCredits}
              className="p-2 border rounded-md text-gray-500 hover:bg-gray-50"
              title="Refresh credits"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </button>
          </div>

          <div className="relative w-full sm:w-64">
            <input
              type="text"
              placeholder="Search credits..."
              className="pl-8 pr-3 py-1.5 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200"
              value={searchQuery}
              onChange={handleSearch}
            />
            <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-gray-500 text-xs border-b">
                <th className="p-3 text-left font-medium cursor-pointer" onClick={() => handleSort('id')}>
                  ID {getSortIcon('id')}
                </th>
                <th className="p-3 text-left font-medium cursor-pointer" onClick={() => handleSort('amount')}>
                  AMOUNT {getSortIcon('amount')}
                </th>
                <th className="p-3 text-left font-medium cursor-pointer" onClick={() => handleSort('type')}>
                  TYPE {getSortIcon('type')}
                </th>
                <th className="p-3 text-left font-medium cursor-pointer" onClick={() => handleSort('status')}>
                  STATUS {getSortIcon('status')}
                </th>
                <th className="p-3 text-left font-medium cursor-pointer" onClick={() => handleSort('created')}>
                  DATE {getSortIcon('created')}
                </th>
                <th className="p-3 text-left font-medium">DESCRIPTION</th>
                <th className="p-3 text-left font-medium">INVOICE</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan="7" className="p-3 text-center">
                    <RefreshCw className="w-5 h-5 animate-spin inline mr-2 text-indigo-700" />
                    Loading credits...
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td colSpan="7" className="p-3 text-center text-red-600">
                    {error}
                  </td>
                </tr>
              ) : sortedCredits.length > 0 ? (
                sortedCredits.map((credit, index) => (
                  <tr
                    key={credit.id}
                    className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-gray-50 ${credit.invoice_id ? 'cursor-pointer' : ''}`}
                    onClick={() => credit.invoice_id && handleViewInvoice(credit)}
                  >
                    <td className="p-3 font-medium text-gray-600">#{credit.id}</td>
                    <td className="p-3 font-medium">
                      <span className={parseFloat(credit.amount) >= 0 ? 'text-green-600' : 'text-red-600'}>
                        {parseFloat(credit.amount) >= 0 ? '+' : ''}{credit.amount}
                      </span>
                    </td>
                    <td className="p-3">{renderTypeBadge(credit.type)}</td>
                    <td className="p-3">{renderStatusBadge(credit.status)}</td>
                    <td className="p-3 text-gray-600">
                      {new Date(credit.created).toLocaleDateString()}
                    </td>
                    <td className="p-3 text-gray-700 truncate max-w-xs">
                      {credit.description || '-'}
                    </td>
                    <td className="p-3" onClick={(e) => credit.invoice_id && e.stopPropagation()}>
                      {credit.invoice_id ? (
                        <button
                          className="p-1 text-gray-500 hover:text-indigo-700 transition-colors flex items-center text-xs"
                          title="View Invoice"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewInvoice(credit);
                          }}
                        >
                          <FileText className="w-4 h-4 mr-1" />
                          {credit.invoice_id}
                        </button>
                      ) : (
                        <span className="text-gray-400 text-xs">-</span>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="7" className="p-3 text-center text-gray-500">
                    No credits found for this user
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Table Footer */}
        <div className="p-3 border-t flex justify-between items-center">
          <div className="text-xs text-gray-500">
            Showing {sortedCredits.length} of {credits.length} credit transactions
          </div>
          <div className="flex items-center space-x-2">
            {sortedCredits.length > 10 && (
              <div className="flex space-x-1">
                <button className="px-2 py-1 border rounded bg-white">1</button>
                <button className="px-2 py-1 border rounded bg-white">2</button>
              </div>
            )}
            <button
              onClick={handleOpenAddFundsModal}
              className="px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white rounded-md flex items-center text-sm whitespace-nowrap"
            >
              <Plus className="w-4 h-4 mr-1" />
              Add Funds Invoice
            </button>
          </div>
        </div>
      </div>

      {/* Add Funds Modal */}
      {addFundsModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
            <div className="p-4 border-b flex justify-between items-center">
              <h2 className="text-lg font-bold text-gray-800">
                Generate Invoice
              </h2>
              <button
                onClick={handleCloseAddFundsModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4">
              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Amount <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                    €
                    </span>
                    <input
                      type="number"
                      name="amount"
                      value={addFundsData.amount}
                      onChange={handleAddFundsChange}
                      min="0.01"
                      step="0.01"
                      className="w-full pl-7 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <input
                    type="text"
                    name="description"
                    value={addFundsData.description}
                    onChange={handleAddFundsChange}
                    className="w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>

                <div className="bg-yellow-50 p-3 rounded-md text-sm text-yellow-800 border border-yellow-200">
                  <AlertTriangle className="w-4 h-4 inline-block mr-1" />
                  This will generate a proforma invoice for the customer to pay. Credits will be added to the account only after payment is confirmed.
                </div>

                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <button
                    type="button"
                    onClick={handleCloseAddFundsModal}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleAddFundsSubmit}
                    className="px-4 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700 flex items-center"
                    disabled={loading}
                  >
                    {loading ? (
                      <RefreshCw className="w-4 h-4 mr-1 animate-spin" />
                    ) : (
                      <FileText className="w-4 h-4 mr-1" />
                    )}
                    {loading ? 'Generating...' : 'Generate Invoice'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Invoice View Modal */}
      {invoiceModalOpen && selectedInvoice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {/* Custom header with close button */}
            <div className="p-6 border-b flex justify-between items-center">
              <div className="flex items-center">
                <h2 className="text-xl font-bold text-gray-800">
                  {selectedInvoice.isProforma ? 'Proforma Invoice' : 'Invoice'} #{selectedInvoice.id}
                </h2>
                <div className="ml-4">{renderStatusBadge(selectedInvoice.status)}</div>
              </div>
              <button
                onClick={handleCloseInvoiceModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>

            {/* InvoiceView Component */}
            <InvoiceView
              invoice={selectedInvoice}
              onEdit={() => {
                console.log('Edit invoice:', selectedInvoice.id);
              }}
              onUpdateStatus={(id, status, conversionData = null) => {
                handleInvoiceStatusUpdate(id, status);
              }}
              onSend={(invoice) => console.log('Send invoice:', invoice.id)}
              onPrint={(invoice) => console.log('Print invoice:', invoice.id)}
              onDownload={(invoice) => console.log('Download invoice:', invoice.id)}
              className="border-t-0 rounded-t-none"
              hideHeader={true}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default CreditsTab;