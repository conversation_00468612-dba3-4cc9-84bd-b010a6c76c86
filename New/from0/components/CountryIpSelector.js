import React from 'react';
import DirectIpSelector from './DirectIpSelector';

/**
 * Wrapper component that preserves the historical CountryIpSelector API but
 * internally renders the new DirectIpSelector (searchable IP dropdown)
 * used by BulkServerAddition. This lets existing pages such as InventoryPage,
 * ServerDetailsModal, SwitchView, etc. get the upgraded UI/behaviour (search,
 * location-aware suggestions, automatic marking of IP usage) without any
 * further code changes.
 *
 * Props expected by legacy callers:
 *   - selectedItem : the device object currently being edited (needs city_id)
 *   - onChange     : handler – either the classic event style (e) => {...}
 *                    or the Bulk style (idx, field, value)
 *   - name         : field name ("ipmi" | "switch_ip")
 *   - value        : current IP string
 *   - placeholder  : optional placeholder text
 *   - label        : optional label text (ignored – kept for compat)
 *   - serverType   : when name === 'ipmi', informs blade vs dedicated
 *   - cities / excludeIps ... any extra props are forwarded
 */
const CountryIpSelector = ({
  selectedItem = {},
  onChange,
  name = 'ipmi',
  value = '',
  placeholder = 'Select an IP address',
  label = '', // not used but accepted for compatibility
  serverType,
  cities,
  excludeIps = [],
  ...rest
}) => {
  // Determine context
  const deviceType = name === 'ipmi' ? 'server' : 'switch';
  const cityId = selectedItem.city_id || null;

  // Normalise change callback so it works for both legacy (event-style)
  // and Bulk (index, field, value) handlers.
  const handleDirectIpChange = (index, field, newVal) => {
    if (typeof onChange !== 'function') return;

    // If the handler expects three parameters (Bulk style), use that.
    if (onChange.length >= 3) {
      onChange(index, field, newVal);
      return;
    }

    // Fallback to event-style signature expected by InventoryPage / ServerDetailsModal
    const syntheticEvent = { target: { name: field, value: newVal } };
    onChange(syntheticEvent);
  };

  return (
    <DirectIpSelector
      index={0}
      value={value}
      onChange={handleDirectIpChange}
      cityId={cityId}
      deviceType={deviceType}
      placeholder={placeholder}
      cities={cities}
      excludeIps={excludeIps}
      {...rest}
    />
  );
};

export default CountryIpSelector; 