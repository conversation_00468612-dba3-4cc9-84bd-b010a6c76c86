import React, { useEffect, useState } from 'react';
import avatar from '../assets/default.png';
import { useAuth } from '../AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { API_URL } from '../config';

// Helper function to get profile picture path
const getProfilePicturePath = (picturePath) => {
  if (!picturePath || picturePath.trim() === '') {
    return avatar;
  }

  // If it's a full URL, use it as is
  if (picturePath.startsWith('http://') || picturePath.startsWith('https://')) {
    return picturePath;
  }

  // For all other cases, serve through PHP API
  const cleanPath = picturePath.startsWith('/') ? picturePath : '/' + picturePath;
  return `${API_URL}/api_admin_profile.php?f=get_profile_image&path=${encodeURIComponent(cleanPath)}`;
};

// Keep all your original icon components
const DashboardIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="4" y="4" width="7" height="7" rx="1" fill="#6366F1" />
    <rect x="13" y="4" width="7" height="7" rx="1" fill="#6366F1" />
    <rect x="4" y="13" width="7" height="7" rx="1" fill="#6366F1" />
    <rect x="13" y="13" width="7" height="7" rx="1" fill="#6366F1" />
  </svg>
);

const OrdersIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4 6H20L18 16H6L4 6Z" stroke="#8B93CB" strokeWidth="2" />
    <circle cx="9" cy="19" r="2" fill="#8B93CB" />
    <circle cx="15" cy="19" r="2" fill="#8B93CB" />
  </svg>
);

const TicketsIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4 4H20V16C20 17.1046 19.1046 18 18 18H6C4.89543 18 4 17.1046 4 16V4Z" fill="#8B93CB" />
    <path d="M8 8H16" stroke="white" strokeWidth="1.5" strokeLinecap="round" />
    <path d="M8 12H16" stroke="white" strokeWidth="1.5" strokeLinecap="round" />
    <path d="M4 4L12 18L20 4" fill="#8B93CB" />
  </svg>
);

const InvoicesIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6 4C6 2.89543 6.89543 2 8 2H16C17.1046 2 18 2.89543 18 4V20C18 21.1046 17.1046 22 16 22H8C6.89543 22 6 21.1046 6 20V4Z" fill="#8B93CB" />
    <path d="M9 7H15" stroke="white" strokeWidth="1.5" strokeLinecap="round" />
    <path d="M9 11H15" stroke="white" strokeWidth="1.5" strokeLinecap="round" />
    <path d="M9 15H13" stroke="white" strokeWidth="1.5" strokeLinecap="round" />
  </svg>
);

const AccountsIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="12" cy="8" r="5" fill="#8B93CB" />
    <path d="M4 18C4 14.6863 7.58172 12 12 12C16.4183 12 20 14.6863 20 18V22H4V18Z" fill="#8B93CB" />
  </svg>
);

const ReportsIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4 20H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V20Z" fill="#8B93CB" />
    <rect x="6" y="12" width="3" height="8" fill="#8B93CB" />
    <rect x="11" y="8" width="3" height="12" fill="#8B93CB" />
    <rect x="16" y="4" width="3" height="16" fill="#8B93CB" />
  </svg>
);

const InventoryIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="4" y="6" width="16" height="3" rx="1" fill="#8B93CB" />
    <rect x="4" y="11" width="16" height="3" rx="1" fill="#8B93CB" />
    <rect x="4" y="16" width="16" height="3" rx="1" fill="#8B93CB" />
  </svg>
);

const LocationIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M12 2C8.13 2 5 5.13 5 9C5 13.25 9 18.5 11.1 21.1C11.6 21.7 12.4 21.7 12.9 21.1C15 18.5 19 13.25 19 9C19 5.13 15.87 2 12 2ZM12 11.5C10.62 11.5 9.5 10.38 9.5 9C9.5 7.62 10.62 6.5 12 6.5C13.38 6.5 14.5 7.62 14.5 9C14.5 10.38 13.38 11.5 12 11.5Z"
      fill="#8B93CB"
    />
  </svg>
);

const SubnetsIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="8" y="4" width="8" height="6" rx="1" fill="#8B93CB" />
    <rect x="4" y="14" width="6" height="6" rx="1" fill="#8B93CB" />
    <rect x="14" y="14" width="6" height="6" rx="1" fill="#8B93CB" />
    <path d="M12 10V12M12 12H8M12 12H16M8 14V12M16 14V12" stroke="#8B93CB" strokeWidth="1.5" />
  </svg>
);

const MailIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="3" y="5" width="18" height="14" rx="2" fill="#8B93CB" />
    <path d="M3 7L12 13L21 7" stroke="white" strokeWidth="1.5" />
  </svg>
);


// Add a new LogoutIcon component
const LogoutIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="#FF5C5C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M16 17L21 12L16 7" stroke="#FF5C5C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M21 12H9" stroke="#FF5C5C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const Sidebar = ({ activeMenu = 'Dashboard', toggleSidebar = () => {}, sidebarCollapsed = false }) => {
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 800);
  const { logout, adminProfile, fetchAdminProfile, token } = useAuth(); // Use the auth context for admin profile and logout
  const navigate = useNavigate(); // Use React Router's navigate function
  const location = useLocation(); // Get current location to detect route changes
  const [supportStaff, setSupportStaff] = useState({
    provisioning: [],
    l2Support: [],
    l3Support: []
  });

  // Fetch support staff information
  useEffect(() => {
    const fetchSupportStaff = async () => {
      try {
        const response = await fetch(`${API_URL}/api_accounts.php?f=get_admins`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token })
        });

        if (!response.ok) {
          throw new Error('Failed to fetch admin data');
        }

        const data = await response.json();

        // Filter admins by their function
        const provisioning = data.filter(admin =>
          admin.function && admin.function.toLowerCase().includes('ceo') ||
          admin.function && admin.function.toLowerCase().includes('founder')
        );

        // Filter L2 support staff
        let l2Support = data.filter(admin =>
          admin.function && admin.function.toLowerCase().includes('l2') ||
          admin.function && admin.function.toLowerCase().includes('advanced support')
        );

        // Sort L2 support staff to ensure Mario appears before Sorin
        l2Support = l2Support.sort((a, b) => {
          // Convert names to lowercase for case-insensitive comparison
          const aName = a.first_name.toLowerCase();
          const bName = b.first_name.toLowerCase();

          // If one is Mario, put him first
          if (aName === 'mario') return -1;
          if (bName === 'mario') return 1;

          // If one is Sorin, put him last
          if (aName === 'sorin') return 1;
          if (bName === 'sorin') return -1;

          // Otherwise sort alphabetically
          return aName.localeCompare(bName);
        });

        // Log the sorted L2 support staff for debugging
        console.log('Sorted L2 Support Staff:', l2Support.map(admin => admin.first_name));

        const l3Support = data.filter(admin =>
          admin.function && admin.function.toLowerCase().includes('l3') ||
          admin.function && admin.function.toLowerCase().includes('senior') ||
          admin.first_name && admin.first_name.toLowerCase() === 'shakib'
        );

        setSupportStaff({
          provisioning,
          l2Support,
          l3Support
        });
      } catch (error) {
        console.error('Error fetching support staff:', error);
      }
    };

    if (token) {
      fetchSupportStaff();
    }
  }, [token]);

  // Initialize mobile state on component mount
  useEffect(() => {
    setIsMobile(window.innerWidth <= 800);
  }, []);

  // Track previous path to detect actual navigation changes
  const [prevPath, setPrevPath] = useState(location.pathname);
  // Track if sidebar was just toggled to prevent immediate closing
  const [justToggled, setJustToggled] = useState(false);

  // Close sidebar only on actual route changes when on mobile, not when the sidebar is toggled
  useEffect(() => {
    console.log(`Path changed from ${prevPath} to ${location.pathname}, justToggled: ${justToggled}, sidebarCollapsed: ${sidebarCollapsed}`);

    // Special handling for specific pages
    const isSpecialPage = ['/orders', '/tickets', '/invoices'].some(path =>
      location.pathname.includes(path)
    );

    // Only close the sidebar if the path has actually changed (navigation occurred)
    if (prevPath !== location.pathname) {
      console.log(`Path changed to ${location.pathname}, updating prevPath`);
      setPrevPath(location.pathname);

      // If we're on mobile and the sidebar is open, close it
      // But only if we're not in the middle of a toggle operation
      if (isMobile && !sidebarCollapsed && !justToggled && typeof toggleSidebar === 'function') {
        console.log("Closing sidebar due to navigation");
        toggleSidebar();
      }

      // Special handling for special pages - force close sidebar
      if (isSpecialPage && isMobile && !sidebarCollapsed && typeof toggleSidebar === 'function') {
        console.log(`Special page detected (${location.pathname}), forcing sidebar close`);
        // Set a small timeout to ensure this happens after any other operations
        setTimeout(() => {
          if (!sidebarCollapsed) {
            toggleSidebar();
          }
        }, 100);
      }
    }

    // Reset the justToggled flag after a short delay
    if (justToggled) {
      const timer = setTimeout(() => {
        console.log("Resetting justToggled flag");
        setJustToggled(false);
      }, 500); // Increased timeout to ensure navigation completes
      return () => clearTimeout(timer);
    }
  }, [location.pathname, isMobile, sidebarCollapsed, toggleSidebar, prevPath, justToggled]);

  // Updated menu items with admin prefixes
  const menuItems = [
    { icon: DashboardIcon, label: 'Dashboard', page: 'admin/dashboard' },
    { icon: OrdersIcon, label: 'Orders', page: 'admin/orders' },
    { icon: TicketsIcon, label: 'Tickets', page: 'admin/tickets' },
    { icon: InvoicesIcon, label: 'Invoices', page: 'admin/invoices' },
    { icon: AccountsIcon, label: 'Accounts', page: 'admin/accounts' },
    { icon: MailIcon, label: 'MassEmail', page: 'admin/massemail' },
    { icon: ReportsIcon, label: 'Reports', page: 'admin/reports' },
    { icon: InventoryIcon, label: 'Inventory', page: 'admin/inventory' },
    { icon: LocationIcon, label: 'Locations', page: 'admin/locations' },
    { icon: SubnetsIcon, label: 'Subnets', page: 'admin/subnets' },
  ];

  useEffect(() => {
    // Handle resize events
    const handleResize = () => {
      const width = window.innerWidth;
      const newIsMobile = width <= 800;

      // Only update if the mobile state has changed
      if (newIsMobile !== isMobile) {
        setIsMobile(newIsMobile);

        // On mobile, ensure sidebar is not visible by default
        if (newIsMobile && typeof toggleSidebar === 'function') {
          // If we're transitioning to mobile, collapse the sidebar
          if (!sidebarCollapsed) {
            // Set justToggled to prevent immediate closing
            setJustToggled(true);
            toggleSidebar();
          }
        }
      }
    };

    // Add debounce to prevent excessive resize events
    let resizeTimer;
    const debouncedResize = () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(handleResize, 100);
    };

    window.addEventListener('resize', debouncedResize);
    return () => {
      window.removeEventListener('resize', debouncedResize);
      clearTimeout(resizeTimer);
    };
  }, [isMobile, sidebarCollapsed, toggleSidebar, justToggled]);

  // Fetch admin profile if not already loaded
  useEffect(() => {
    if (!adminProfile) {
      fetchAdminProfile();
    }
  }, [adminProfile, fetchAdminProfile]);

  // Check if sidebar has mobile-active class
  useEffect(() => {
    const checkMobileActive = () => {
      // This function can be used to check if the sidebar has mobile-active class
      // Currently not needed but kept for future use
    };

    // Initial check
    checkMobileActive();

    // Set up a mutation observer to detect class changes on the sidebar
    const sidebarContainer = document.querySelector('.sidebar-container');
    if (sidebarContainer) {
      const observer = new MutationObserver(checkMobileActive);
      observer.observe(sidebarContainer, { attributes: true, attributeFilter: ['class'] });
      return () => observer.disconnect();
    }
  }, []);

  // Handle clicked menu item with admin paths
  const handleMenuClick = (page) => {
    console.log(`Navigating to page: ${page}, isMobile: ${isMobile}, sidebarCollapsed: ${sidebarCollapsed}`);
  
    // If we're navigating away from the Locations page, ensure all modals and selections are cleared
    if (location.pathname.includes('/admin/locations') && !page.includes('locations')) {
      // You might want to emit an event or use a context to tell LocationsPage to reset
      // For now, we'll just ensure smooth navigation
    }
  
    // Special pages that need immediate navigation
    const specialPages = ['admin/orders', 'admin/tickets', 'admin/invoices', 'admin/locations'];
    const isSpecialPage = specialPages.includes(page);
  
    if (isSpecialPage) {
      console.log(`${page} is a special page, using direct navigation`);
      
      // Force navigation immediately for special pages
      navigate(`/${page}`);
      
      // Handle mobile sidebar after navigation
      if (isMobile && !sidebarCollapsed && typeof toggleSidebar === 'function') {
        setTimeout(() => {
          toggleSidebar();
        }, 100);
      }
      return;
    }
  
    // For regular pages, handle mobile sidebar
    if (isMobile && typeof toggleSidebar === 'function') {
      console.log(`Closing sidebar for navigation to ${page}`);
  
      // Set justToggled to prevent immediate reopening
      setJustToggled(true);
  
      // If sidebar is open, close it
      if (!sidebarCollapsed) {
        console.log("Sidebar is open, closing it now");
        toggleSidebar();
      }
    }
  
    // Navigate immediately for better UX
    navigate(`/${page}`);
  };

  // Handle logout
  const handleLogout = () => {
    console.log("Logout clicked");

    // On mobile, close sidebar before navigation
    if (isMobile && !sidebarCollapsed && typeof toggleSidebar === 'function') {
      // Set justToggled to prevent immediate reopening
      setJustToggled(true);
      toggleSidebar();
    }

    logout();

    // Small delay to allow sidebar animation to start before navigation
    setTimeout(() => {
      // Use React Router's navigate function for client-side navigation
      navigate('/admin/login');
    }, 50);
  };

  // Use props to check if sidebar is collapsed
  const isCollapsed = () => {
    return sidebarCollapsed;
  };

  const sidebarBaseClass = "bg-white border-r shadow-sm flex flex-col transition-all duration-300 sidebar-container";
  let sidebarClasses = sidebarBaseClass;

  if (isMobile) {
    // On mobile, add the mobile-active class when sidebar is toggled
    sidebarClasses += " w-64"; // Fixed width on mobile
    if (!sidebarCollapsed) {
      sidebarClasses += " sidebar-mobile-active";
    }
  } else {
    sidebarClasses += isCollapsed() ? " sidebar-collapsed" : " sidebar-expanded";
  }

  return (
    <>
      {/* Add overlay when sidebar is active on mobile */}
      {isMobile && !sidebarCollapsed && (
        <div
          className="sidebar-overlay"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("Overlay clicked, closing sidebar");
            if (typeof toggleSidebar === 'function') {
              // Set justToggled to prevent immediate closing
              setJustToggled(true);
              // Small delay to ensure the event is processed
              setTimeout(() => {
                toggleSidebar();
              }, 10);
            }
          }}
        ></div>
      )}

      <div className={sidebarClasses}>
        {/* Logo */}
        <div className="p-4 flex justify-center items-center border-b">
          <svg viewBox="0 0 24 24" width="28" height="28" className="text-indigo-700 mx-auto">
            <path fill="currentColor" d="M4.848 7.757l7.17-4.265 7.17 4.35-7.17 4.095L4.848 7.757zm0 4.514l7.17-4.264 7.17 4.35-7.17 4.095L4.848 12.27zm7.17 4.351l7.17-4.265v4.35l-7.17 4.095v-4.18z"/>
          </svg>
        </div>

        {/* Profile */}
        {(!isCollapsed() || isMobile) ? (
          <div className="p-6 flex flex-col items-center border-b profile-container">
            <img
              src={adminProfile?.picture_url ? getProfilePicturePath(adminProfile.picture_url) : avatar}
              alt="Profile"
              className="rounded-full mb-4 profile-image object-cover"
              width="76"
              height="76"
              onError={(e) => { e.target.src = avatar; }}
            />
            <div className="text-center profile-text">
              <div className="font-bold text-lg text-gray-800">
                {adminProfile ? `${adminProfile.first_name} ${adminProfile.last_name}` : 'Loading...'}
              </div>
              <div className="text-sm text-gray-500">
                {adminProfile?.function || adminProfile?.department_name || 'Administrator'}
              </div>
            </div>
          </div>
        ) : (
          <div className="py-4 flex justify-center border-b profile-container collapsed-profile">
            <img
              src={adminProfile?.picture_url ? getProfilePicturePath(adminProfile.picture_url) : avatar}
              alt="Profile"
              className="w-10 h-10 rounded-full profile-image object-cover"
              onError={(e) => { e.target.src = avatar; }}
            />
          </div>
        )}

        {/* Menu Items */}
        <div className="flex-grow py-4 overflow-y-auto">
          {menuItems.map((item) => (
            <div
              key={item.label}
              className={`
                flex items-center px-6 py-4 cursor-pointer text-base transition-all duration-200 sidebar-item
                ${activeMenu === item.label
                  ? 'sidebar-item active'
                  : 'text-gray-500'}
              `}
              onClick={() => {
                // Special handling for Orders and Tickets pages
                if (item.label === 'Orders' || item.label === 'Tickets' || item.label === 'Invoices') {
                  console.log(`${item.label} menu item clicked`);

                  // Force close sidebar on mobile
                  if (isMobile && typeof toggleSidebar === 'function') {
                    console.log(`Forcing sidebar close for ${item.label} page`);

                    // Set justToggled to prevent immediate reopening
                    setJustToggled(true);

                    // Force close the sidebar
                    if (!sidebarCollapsed) {
                      toggleSidebar();
                    }

                    // Navigate with a delay
                    setTimeout(() => {
                      console.log(`Navigating to ${item.page} after delay`);
                      navigate(`/${item.page}`);
                    }, 200);
                  } else {
                    // On desktop, just navigate
                    navigate(`/${item.page}`);
                  }
                } else {
                  // Normal handling for other pages
                  handleMenuClick(item.page);
                }
              }}
            >
              <div className="mr-4">
                <item.icon />
              </div>

              {!isCollapsed() || isMobile ? (
                <>
                  <span className="menu-text">{item.label}</span>
                  {activeMenu === item.label && <span className="ml-auto text-indigo-600 menu-text">›</span>}
                </>
              ) : null}
            </div>
          ))}


        </div>

        {/* Help Icon with Tooltip */}
        <div className="p-4 border-t flex justify-center cursor-pointer hover:text-indigo-600 transition-colors duration-200 relative group">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="9" stroke="#8B93CB" strokeWidth="2" />
            <path d="M12 7V13" stroke="#8B93CB" strokeWidth="2" strokeLinecap="round" />
            <circle cx="12" cy="17" r="1" fill="#8B93CB" />
          </svg>
          {/* Enhanced Tooltip */}
          <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-white border border-gray-200 shadow-lg text-gray-800 text-xs rounded-md py-3 px-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none w-64 z-50">
            <h4 className="font-bold text-indigo-700 border-b pb-1 mb-2">Support Contacts</h4>

            <div className="mb-2">
              <div className="font-semibold text-indigo-600 mb-1 flex items-center">
                <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 4L4 8L12 12L20 8L12 4Z" fill="#6366F1" />
                  <path d="M4 12L12 16L20 12" stroke="#6366F1" strokeWidth="2" />
                  <path d="M4 16L12 20L20 16" stroke="#6366F1" strokeWidth="2" />
                </svg>
                Provisioning:
              </div>
              <div className="pl-4">
                {supportStaff.provisioning.length > 0 ? (
                  supportStaff.provisioning.map((admin) => (
                    <div key={admin.id} className="mb-1 last:mb-0">
                      {admin.first_name} {admin.last_name}
                    </div>
                  ))
                ) : (
                  <div>Michael Martin</div>
                )}
              </div>
            </div>

            <div className="mb-2">
              <div className="font-semibold text-indigo-600 mb-1 flex items-center">
                <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="9" stroke="#6366F1" strokeWidth="2" />
                  <path d="M12 8V16" stroke="#6366F1" strokeWidth="2" strokeLinecap="round" />
                  <path d="M8 12H16" stroke="#6366F1" strokeWidth="2" strokeLinecap="round" />
                </svg>
                L2 Technical Support:
              </div>
              <div className="pl-4">
                {supportStaff.l2Support.length > 0 ? (
                  supportStaff.l2Support.map((admin) => (
                    <div key={admin.id} className="mb-1 last:mb-0">
                      {admin.first_name} {admin.last_name}
                    </div>
                  ))
                ) : (
                  <div>Mario Marin and Sorin</div>
                )}
              </div>
            </div>

            <div>
              <div className="font-semibold text-indigo-600 mb-1 flex items-center">
                <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 4L19 18H5L12 4Z" fill="#6366F1" />
                </svg>
                L3 Advanced Issues:
              </div>
              <div className="pl-4">
                {supportStaff.l3Support.length > 0 ? (
                  supportStaff.l3Support.map((admin) => (
                    <div key={admin.id} className="mb-1 last:mb-0">
                      {admin.first_name} {admin.last_name}
                    </div>
                  ))
                ) : (
                  <div>Alexandru Tolgyi and Shakib</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;