import React, { useState, useEffect } from 'react';
import {
  AreaChart,
  Area,
  ResponsiveContainer
} from 'recharts';
import {
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import { API_URL } from '../config';
// Ultra compact sparkline-style graph with NO interactions - just 25px total height
const MiniPortTrafficGraph = ({ port_id }) => {
  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [trafficData, setTrafficData] = useState([]);
  
  // Colors
  const colors = {
    inbound: '#4f46e5', // indigo-600
    outbound: '#ef4444', // red-500
  };

  // Fetch traffic data when component mounts
  useEffect(() => {
    fetchTrafficData();
  }, [port_id]);
  
  // Fetch traffic data from the API
  const fetchTrafficData = async () => {
    if (!port_id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('admin_token');
      
      // For mini graph, we just want the last 24 hours
      const params = {
        token,
        port_id: port_id,
        days: 1,
        interval: 5, // 5-minute intervals
        timestamp: new Date().getTime() // Prevent caching
      };
      
      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_port_traffic`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Process and set the traffic data
        if (result.data && result.data.length > 0) {
          const processedData = result.data.map(point => ({
            ...point,
            timestamp: new Date(point.timestamp).getTime(),
            in_bps: Number(point.in_bps) || 0,
            out_bps: Number(point.out_bps) || 0,
          }));
          
          setTrafficData(processedData);
        } else {
          // Generate empty dataset
          setTrafficData(generateEmptyTimeSeries());
        }
      } else {
        throw new Error(result.error || 'Unknown error occurred');
      }
    } catch (error) {
      console.error('Error fetching traffic data:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };
  
  // Generate an empty time series for the last 24 hours
  const generateEmptyTimeSeries = () => {
    const now = new Date();
    const endTime = now.getTime();
    const startTime = new Date(now);
    startTime.setHours(startTime.getHours() - 24);
    
    const timeSeries = [];
    const interval = 5 * 60 * 1000; // 5 minutes in milliseconds
    
    for (let time = startTime.getTime(); time <= endTime; time += interval) {
      timeSeries.push({
        timestamp: time,
        in_bps: 0,
        out_bps: 0
      });
    }
    
    return timeSeries;
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-[25px] w-32">
        <RefreshCw className="w-3 h-3 animate-spin text-indigo-700" />
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="flex items-center justify-center h-[25px] w-32">
        <AlertTriangle className="w-3 h-3 text-red-600" />
      </div>
    );
  }
  
  if (!trafficData || trafficData.length === 0) {
    return (
      <div className="flex items-center justify-center h-[25px] w-32">
        <div className="text-gray-400 text-[9px]">No data</div>
      </div>
    );
  }
  
  return (
    <div className="h-[25px] w-32 bg-white rounded border border-gray-200 overflow-hidden pointer-events-none">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={trafficData}
          margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
        >
          <defs>
            <linearGradient id={`inboundGradient-${port_id}`} x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={colors.inbound} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={colors.inbound} stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id={`outboundGradient-${port_id}`} x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={colors.outbound} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={colors.outbound} stopOpacity={0.1}/>
            </linearGradient>
          </defs>
          {/* Removed Tooltip component completely */}
          <Area 
            type="monotone" 
            dataKey="in_bps" 
            name="In" 
            stroke={colors.inbound} 
            fillOpacity={1} 
            fill={`url(#inboundGradient-${port_id})`} 
            strokeWidth={0.8}
            isAnimationActive={false}
          />
          <Area 
            type="monotone" 
            dataKey="out_bps" 
            name="Out" 
            stroke={colors.outbound} 
            fillOpacity={1} 
            fill={`url(#outboundGradient-${port_id})`} 
            strokeWidth={0.8}
            isAnimationActive={false}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

export default MiniPortTrafficGraph;