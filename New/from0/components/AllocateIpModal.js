import React, { useState, useEffect } from 'react';
import { XCircle, AlertCircle, Tag, Check, Server, Cpu, AlertTriangle } from 'lucide-react';
import axios from 'axios';
import { API_URL } from '../config';

const AllocateIpModal = ({ subnet, ipAddress, onClose, onAllocateIp }) => {
  const [formData, setFormData] = useState({
    subnet_id: subnet.id,
    ip_address: ipAddress,
    description: '',
    notes: '',
    allocationType: 'custom', // 'custom' or 'server_ipmi'
    serverId: '',
    serverType: 'dedicated' // 'dedicated' or 'blade'
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [servers, setServers] = useState([]);
  const [loadingServers, setLoadingServers] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [serverWithIpmi, setServerWithIpmi] = useState(null);

  // Fetch available servers
  useEffect(() => {
    const fetchServers = async () => {
      setLoadingServers(true);
      try {
        // Fetch dedicated servers
        const dedicatedResponse = await axios.post(
          `${API_URL}/api_admin_inventory.php?f=get_inventory_dedicated`,
          { token: localStorage.getItem('admin_token') }
        );

        // Fetch blade servers
        const bladeResponse = await axios.post(
          `${API_URL}/api_admin_inventory.php?f=get_blade_server_inventory`,
          { token: localStorage.getItem('admin_token') }
        );

        // Process and log the raw server data
        console.log('Raw dedicated server data:', dedicatedResponse.data);
        console.log('Raw blade server data:', bladeResponse.data);

        // Combine and format servers, ensuring consistent ID types (as strings)
        const dedicatedServers = dedicatedResponse.data.map(server => ({
          ...server,
          id: String(server.id), // Ensure ID is a string
          type: 'dedicated',
          displayName: `${server.label || server.hostname || `Server #${server.id}`} (Dedicated)`
        }));

        const bladeServers = bladeResponse.data.map(server => ({
          ...server,
          id: String(server.id), // Ensure ID is a string
          type: 'blade',
          displayName: `${server.label || server.hostname || `Server #${server.id}`} (Blade)`
        }));

        const allServers = [...dedicatedServers, ...bladeServers];
        console.log('Processed server data:', allServers);

        setServers(allServers);
      } catch (err) {
        console.error('Error fetching servers:', err);
        setError('Failed to load servers. Please try again.');
      } finally {
        setLoadingServers(false);
      }
    };

    fetchServers();
  }, []);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === 'allocationType') {
      // Reset server-related fields if switching to custom
      if (value === 'custom') {
        setFormData({
          ...formData,
          allocationType: value,
          serverId: '',
          serverType: 'dedicated',
          description: formData.description || ''
        });
      } else {
        setFormData({
          ...formData,
          allocationType: value,
          description: 'IPMI Address - Select a server below'
        });
      }
    } else if (name === 'serverId') {
      // Convert value to both string and number for comparison
      const valueAsString = String(value);
      const valueAsNumber = parseInt(value, 10);

      // Log server IDs for debugging
      console.log('Selected server ID:', value);
      console.log('Server IDs in list:', servers.map(s => ({ id: s.id, type: typeof s.id })));

      // Find the selected server, comparing both as string and number
      const selectedServer = servers.find(server =>
        server.id === value ||
        server.id === valueAsString ||
        server.id === valueAsNumber
      );

      console.log('Selected server:', selectedServer);

      // Update server type and description
      let serverLabel = 'Unknown Server';
      let serverType = 'dedicated';

      if (selectedServer) {
        // Use the most specific name available
        serverLabel = selectedServer.label ||
                     selectedServer.hostname ||
                     selectedServer.displayName ||
                     `Server #${selectedServer.id}`;

        serverType = selectedServer.type || 'dedicated';

        console.log('Using server label:', serverLabel);
      } else {
        console.warn('No server found with ID:', value);
      }

      setFormData({
        ...formData,
        serverId: value,
        serverType: serverType,
        description: `IPMI for ${serverLabel} (${serverType.toUpperCase()})`
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Check if server already has an IPMI IP
  const checkServerIpmi = async (serverId, serverType) => {
    try {
      const serverTable = serverType === 'dedicated' ? 'inventory_dedicated_servers' : 'blade_server_inventory';
      const response = await axios.post(
        `${API_URL}/api_admin_inventory.php?f=get_server_details`,
        {
          server_id: serverId,
          server_type: serverType,
          token: localStorage.getItem('admin_token')
        }
      );

      if (response.data && response.data.ipmi) {
        console.log(`Server already has IPMI IP: ${response.data.ipmi}`);
        return response.data;
      }

      return null;
    } catch (err) {
      console.error('Error checking server IPMI:', err);
      return null;
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!formData.description.trim()) {
      setError('Please enter a description');
      return;
    }

    if (formData.allocationType === 'server_ipmi' && !formData.serverId) {
      setError('Please select a server');
      return;
    }

    // If this is an IPMI allocation, check if the server already has an IPMI IP
    if (formData.allocationType === 'server_ipmi' && !showConfirmation) {
      setLoading(true);
      const serverDetails = await checkServerIpmi(formData.serverId, formData.serverType);
      setLoading(false);

      if (serverDetails && serverDetails.ipmi) {
        // Server already has an IPMI IP, show confirmation dialog
        setServerWithIpmi(serverDetails);
        setShowConfirmation(true);
        return;
      }
    }

    setLoading(true);
    setError('');

    try {
      // Prepare data for allocation
      const allocateData = {
        ...formData,
        subnet_id: typeof subnet.id === 'string'
          ? parseInt(subnet.id.replace(/\D/g, ''), 10)
          : subnet.id,
      };

      // For server IPMI allocation, add server-specific data
      if (formData.allocationType === 'server_ipmi') {
        allocateData.for_server_ipmi = true;
        allocateData.server_id = formData.serverId;
        allocateData.server_type = formData.serverType;
      }

      console.log('Allocating IP with data:', allocateData);

      await onAllocateIp(allocateData);
      console.log('IP allocated successfully');
      onClose();
    } catch (err) {
      console.error('Full error object:', err);
      setError(
        err.response?.data?.error ||
        err.message ||
        'An error occurred while allocating the IP address'
      );
    } finally {
      setLoading(false);
      setShowConfirmation(false);
    }
  };

  // Cancel confirmation and reset state
  const cancelConfirmation = () => {
    setShowConfirmation(false);
    setServerWithIpmi(null);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full">
        <div className="p-6 border-b flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-800">
            {showConfirmation ? 'Confirm IPMI IP Change' : 'Allocate IP Address'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>

        {showConfirmation && serverWithIpmi ? (
          <div className="p-6 space-y-4">
            <div className="p-3 bg-amber-50 text-amber-700 rounded-md flex items-start">
              <AlertTriangle className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Server already has an IPMI IP address</p>
                <p className="text-sm mt-1">
                  The selected server already has an IPMI IP address ({serverWithIpmi.ipmi}).
                  If you continue, the old IP will be deallocated and the new IP ({ipAddress}) will be assigned.
                </p>
              </div>
            </div>

            <div className="pt-4 border-t flex justify-end">
              <button
                type="button"
                onClick={cancelConfirmation}
                className="mr-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSubmit}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-700 hover:bg-indigo-800 rounded-md flex items-center"
              >
                <Tag className="w-4 h-4 mr-1" />
                {loading ? 'Processing...' : 'Confirm & Allocate'}
              </button>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            {error && (
              <div className="p-3 bg-red-50 text-red-700 rounded-md flex items-center">
                <AlertCircle className="w-5 h-5 mr-2" />
                {error}
              </div>
            )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Subnet
            </label>
            <div className="p-2 bg-gray-100 rounded-md font-mono">
              {subnet.cidr}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              IP Address
            </label>
            <div className="p-2 bg-gray-100 rounded-md font-mono">
              {ipAddress}
            </div>
          </div>

          {/* Allocation Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Allocation Type
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="allocationType"
                  value="custom"
                  checked={formData.allocationType === 'custom'}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                <span className="text-sm">Custom Allocation</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="allocationType"
                  value="server_ipmi"
                  checked={formData.allocationType === 'server_ipmi'}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                <span className="text-sm">Server IPMI</span>
              </label>
            </div>
          </div>

          {/* Server Selection (only shown for server_ipmi allocation type) */}
          {formData.allocationType === 'server_ipmi' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Server *
              </label>
              {loadingServers ? (
                <div className="text-sm text-gray-500">Loading servers...</div>
              ) : (
                <select
                  name="serverId"
                  value={formData.serverId}
                  onChange={handleInputChange}
                  className="w-full p-2 border rounded-md"
                  required
                >
                  <option value="">-- Select a server --</option>

                  {/* Group servers by type */}
                  <optgroup label="Dedicated Servers">
                    {servers
                      .filter(server => server.type === 'dedicated')
                      .map(server => (
                        <option key={`dedicated-${server.id}`} value={server.id}>
                          {server.label || server.hostname || `Server #${server.id}`}
                        </option>
                      ))
                    }
                  </optgroup>

                  <optgroup label="Blade Servers">
                    {servers
                      .filter(server => server.type === 'blade')
                      .map(server => (
                        <option key={`blade-${server.id}`} value={server.id}>
                          {server.label || server.hostname || `Server #${server.id}`}
                        </option>
                      ))
                    }
                  </optgroup>
                </select>
              )}
              <p className="mt-1 text-xs text-gray-500">
                This IP will be assigned as the IPMI address for the selected server
              </p>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description *
            </label>
            <input
              type="text"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="e.g., Reserved for VPN server"
              className="w-full p-2 border rounded-md"
              required
              disabled={formData.allocationType === 'server_ipmi'}
            />
            <p className="mt-1 text-xs text-gray-500">
              {formData.allocationType === 'server_ipmi'
                ? 'Description is auto-generated for server IPMI allocations'
                : 'Enter a description for this IP allocation'}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              placeholder="Additional notes about this allocation"
              className="w-full p-2 border rounded-md h-24"
            />
          </div>

          <div className="pt-4 border-t flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="mr-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`px-4 py-2 text-sm font-medium text-white rounded-md flex items-center ${
                formData.description.trim() && !loading
                  ? 'bg-indigo-700 hover:bg-indigo-800'
                  : 'bg-indigo-400 cursor-not-allowed'
              }`}
              disabled={!formData.description.trim() || loading}
            >
              <Tag className="w-4 h-4 mr-1" />
              {loading ? 'Allocating...' : 'Allocate IP'}
            </button>
          </div>
        </form>
        )}
      </div>
    </div>
  );
};

export default AllocateIpModal;
