import React from 'react';

// Component to display upgrade configuration details
const UpgradeConfigurationDisplay = ({ upgradeDetails }) => {
  if (!upgradeDetails) {
    return <div className="text-gray-500 italic">No upgrade configuration available</div>;
  }

  // Function to determine which fields to display based on upgrade type
  const getDisplayFields = (upgradeType) => {
    const fields = {
      plan: false,
      memory: false,
      storage: false,
      bandwidth: false
    };

    switch (upgradeType) {
      case 'VPS Plan Upgrade':
        // Show all fields for VPS plan upgrades
        fields.plan = true;
        fields.memory = true;
        fields.storage = true;
        fields.bandwidth = true;
        break;
      
      case 'Storage Upgrade':
        // Only show storage for storage upgrades
        fields.storage = true;
        break;
      
      case 'Bandwidth Upgrade':
        // Only show bandwidth for bandwidth upgrades
        fields.bandwidth = true;
        break;
      
      case 'Subnet Upgrade':
        // Show as network/subnet info
        fields.bandwidth = true; // Using bandwidth field to show subnet info
        break;
      
      case 'Additional IPs Upgrade':
        // Show as IP package info
        fields.bandwidth = true; // Using bandwidth field to show IP info
        break;
      
      default:
        // For unknown types, show what we have
        fields.plan = true;
        fields.storage = true;
        fields.bandwidth = true;
    }

    return fields;
  };

  const displayFields = getDisplayFields(upgradeDetails.upgrade_type);
  const oldConfig = upgradeDetails.old_configuration;
  const newConfig = upgradeDetails.new_configuration;

  // Helper to render only non-empty and relevant fields
  const renderConfigField = (label, oldValue, newValue, shouldDisplay) => {
    // Don't render if field shouldn't be displayed
    if (!shouldDisplay) return null;
    
    // Don't render if both values are "Unknown" or contain "No Change"
    if ((oldValue === 'Unknown' && newValue === 'Unknown') || 
        oldValue?.includes('No Change') || 
        newValue?.includes('No Change')) {
      return null;
    }

    return (
      <div className="grid grid-cols-2 gap-4">
        <div>
          <span className="text-gray-500">{label}:</span> {oldValue}
        </div>
        <div>
          <span className="text-gray-500">{label}:</span>{' '}
          <span className="text-green-600 font-medium">{newValue}</span>
        </div>
      </div>
    );
  };

  // Custom label for subnet and IP upgrades
  const getFieldLabel = (field, upgradeType) => {
    if (field === 'bandwidth' && upgradeType === 'Subnet Upgrade') return 'Subnet';
    if (field === 'bandwidth' && upgradeType === 'Additional IPs Upgrade') return 'IP Package';
    return field.charAt(0).toUpperCase() + field.slice(1);
  };

  return (
    <div className="space-y-4">
      <div>
        <div className="text-sm text-gray-500">Service Hostname</div>
        <div className="font-medium">{upgradeDetails.hostname || 'Not specified'}</div>
      </div>
      <div>
        <div className="text-sm text-gray-500">Upgrade Type</div>
        <div className="font-medium">{upgradeDetails.upgrade_type}</div>
      </div>
      
      {/* Configuration Comparison */}
      <div className="mt-4">
        <div className="grid grid-cols-2 gap-4 mb-2">
          <div className="text-xs text-gray-400 uppercase">Current Config</div>
          <div className="text-xs text-green-600 uppercase font-medium">New Config</div>
        </div>
        
        <div className="space-y-2">
          {displayFields.plan && renderConfigField(
            getFieldLabel('plan', upgradeDetails.upgrade_type),
            oldConfig.cpu,
            newConfig.cpu,
            true
          )}
          
          {displayFields.memory && oldConfig.memory && newConfig.memory && renderConfigField(
            'Memory',
            oldConfig.memory,
            newConfig.memory,
            true
          )}
          
          {displayFields.storage && renderConfigField(
            'Storage',
            oldConfig.storage,
            newConfig.storage,
            true
          )}
          
          {displayFields.bandwidth && renderConfigField(
            getFieldLabel('bandwidth', upgradeDetails.upgrade_type),
            oldConfig.bandwidth,
            newConfig.bandwidth,
            true
          )}
        </div>
      </div>
    </div>
  );
};

export default UpgradeConfigurationDisplay;