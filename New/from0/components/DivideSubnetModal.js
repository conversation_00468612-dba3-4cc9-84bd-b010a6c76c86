import React, { useState, useEffect } from 'react';
import { XCircle, AlertCircle, Info } from 'lucide-react';

const DivideSubnetModal = ({ subnet, onClose, onDivideSubnet }) => {
  // Extract subnet size from the CIDR notation
  const [currentSubnetSize, setCurrentSubnetSize] = useState(0);
  const [error, setError] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [generateIps, setGenerateIps] = useState(false);

  useEffect(() => {
    // Extract subnet size from CIDR notation (e.g., "***********/24" => 24)
    if (subnet && subnet.cidr) {
      console.log('Subnet data for division:', subnet);
      const cidrParts = subnet.cidr.split('/');
      if (cidrParts.length === 2) {
        const size = parseInt(cidrParts[1], 10);
        console.log('Extracted subnet size:', size);

        // Validate the size before setting it
        if (isNaN(size) || size <= 0 || size >= 32) {
          setError(`Invalid subnet size: /${size}`);
          return;
        }

        setCurrentSubnetSize(size);
      } else {
        setError('Invalid CIDR format');
      }
    } else {
      setError('Missing subnet CIDR information');
    }
  }, [subnet]);

  // Validate subnet
  const validateSubnet = () => {
    setError('');

    // Make sure we have a valid subnet ID
    const subnetId = subnet.id;
    if (!subnetId) {
      setError('Invalid subnet ID');
      return false;
    }

    // Make sure subnetId is a number or can be converted to one
    const numericId = typeof subnetId === 'string' ?
      parseInt(subnetId.replace(/\D/g, ''), 10) : // Strip non-numeric characters
      parseInt(subnetId, 10);

    if (isNaN(numericId)) {
      setError('Subnet ID must be a number');
      return false;
    }

    // Validate current subnet size
    if (currentSubnetSize <= 0 || currentSubnetSize >= 31) {
      setError(`Invalid current subnet size: /${currentSubnetSize}. Cannot divide this subnet.`);
      return false;
    }

    // Calculate new subnet size (always current size + 1 for dividing in 2)
    const newSize = currentSubnetSize + 1;
    console.log('Calculated new subnet size:', newSize);

    if (newSize <= currentSubnetSize) {
      setError(`New subnet size (/${newSize}) must be larger than current size (/${currentSubnetSize})`);
      return false;
    }

    if (newSize > 31) {
      setError('Cannot divide subnet: new subnet size would be larger than /31');
      return false;
    }

    return true;
  };

  // Handle division
  const handleDivide = () => {
    if (isProcessing) return;

    if (!validateSubnet()) {
      return;
    }

    setIsProcessing(true);

    // Extract numeric ID from subnet.id (in case it's a formatted string like 'SUB-0001')
    let numericId = subnet.id;
    if (typeof subnet.id === 'string' && subnet.id.includes('-')) {
      // Try to extract the numeric part
      const matches = subnet.id.match(/\d+/);
      if (matches && matches.length > 0) {
        numericId = parseInt(matches[0], 10);
      }
    }

    // Always use current size + 1 to divide in 2 equal parts
    const newSize = currentSubnetSize + 1;

    // Final validation before submission
    if (newSize <= currentSubnetSize || newSize > 31 || currentSubnetSize <= 0) {
      const errorMsg = `Cannot divide subnet: current size=${currentSubnetSize}, new size=${newSize}`;
      console.error(errorMsg);
      setError(errorMsg);
      setIsProcessing(false);
      return;
    }

    // Log what we're about to submit
    console.log('Submitting division request with data:', {
      subnet_id: numericId,
      new_subnet_size: newSize,
      generate_ips: generateIps,
      current_subnet_size: currentSubnetSize
    });

    // Call the parent handler with well-formed data
    onDivideSubnet({
      subnet_id: numericId,
      new_subnet_size: newSize,
      generate_ips: generateIps
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg max-w-lg w-full">
        <div className="p-6 border-b flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-800">Dividing Subnet</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
            disabled={isProcessing}
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6">
          <div className="mb-6">
            <div className="text-sm text-gray-500">Current Subnet</div>
            <div className="font-medium font-mono">{subnet.cidr}</div>
            <div className="mt-1 text-xs text-gray-500">
              Current subnet size: /{currentSubnetSize}
            </div>
            <div className="mt-1 text-xs text-gray-500">
              ID: {subnet.id}, Status: {subnet.status || 'Unknown'}
            </div>
          </div>

          {error ? (
            <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md flex items-start">
              <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Error</p>
                <p className="text-sm mt-1">{error}</p>
                <p className="text-sm mt-1">
                  Please close this dialog and try again with a valid subnet.
                </p>
              </div>
            </div>
          ) : currentSubnetSize > 0 ? (
            <div className="mb-4 p-3 bg-blue-50 text-blue-700 rounded-md flex items-start">
              <Info className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Divide subnet into 2 equal parts?</p>
                <p className="text-sm mt-1">
                  Current subnet size: /{currentSubnetSize}
                </p>
                <p className="text-sm mt-1">
                  New subnet size will be /{currentSubnetSize + 1}
                </p>
      
              </div>
            </div>
          ) : (
            <div className="mb-4 p-3 bg-yellow-50 text-yellow-700 rounded-md flex items-start">
              <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Waiting for subnet information...</p>
              </div>
            </div>
          )}

   

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700"
              disabled={isProcessing}
            >
              Cancel
            </button>
            {!isProcessing && (
              <button
                type="button"
                onClick={handleDivide}
                className="px-4 py-2 bg-indigo-700 text-white rounded-md hover:bg-indigo-800"
                disabled={!currentSubnetSize || currentSubnetSize <= 0}
              >
                Divide Subnet
              </button>
            )}
            {isProcessing && (
              <button
                type="button"
                className="px-4 py-2 bg-indigo-700 text-white rounded-md opacity-70 cursor-not-allowed flex items-center"
                disabled
              >
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DivideSubnetModal;