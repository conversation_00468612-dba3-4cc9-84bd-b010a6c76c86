import React, { useState, useEffect } from 'react';
import {
  RefreshCw,
  <PERSON>Up,
  ArrowDown,
  Download,
  Eye,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  FileText,
  Filter,
  Search,
  XCircle,
  Clock,
  AlertCircle,
  Edit
} from 'lucide-react';
import InvoiceView from './InvoiceView';


const InvoicesTab = ({ userId, token }) => {
  // State for invoices data
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Sorting and filtering state
  const [sortField, setSortField] = useState('payment_date');
  const [sortDirection, setSortDirection] = useState('desc');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');

  // Invoice view modal state
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [invoiceModalOpen, setInvoiceModalOpen] = useState(false);

  // Stats
  const [stats, setStats] = useState({
    totalInvoices: 0,
    paidInvoices: 0,
    pendingInvoices: 0,
    totalAmount: 0
  });

  // Fetch invoices on component mount or when userId changes
  useEffect(() => {
    if (userId) {
      fetchInvoices();
    }
  }, [userId]);

  // Fetch invoices data
  const fetchInvoices = async () => {
    setLoading(true);
    setError(null);

    try {
      // First try the admin endpoint to see if it works
      console.log('Trying admin endpoint first...');
      let adminResponse;
      try {
        adminResponse = await fetch(`/api_admin_invoices.php?f=get_invoices`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: localStorage.getItem('admin_token'),
            user_id: userId // Add user_id to filter by specific user
          })
        });

        if (adminResponse.ok) {
          console.log('Admin endpoint worked!');
        } else {
          console.log('Admin endpoint failed, falling back to user endpoint');
          adminResponse = null;
        }
      } catch (adminError) {
        console.error('Error with admin endpoint:', adminError);
        adminResponse = null;
      }

      // If admin endpoint worked, use that response
      const response = adminResponse || await fetch(`/api_accounts.php?f=get_user_invoices`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          user_id: userId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      let responseText = await response.text();
      console.log('API Response (raw):', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        console.error('Error parsing response:', e);
        throw new Error('Invalid response format from server');
      }

      // Process invoices data
      console.log('Invoices data (parsed):', data);

      // Log specific fields we're interested in
      if (Array.isArray(data) && data.length > 0) {
        console.log('Sample invoice fields:', {
          invoice_number: data[0].invoice_number,
          proforma_number: data[0].proforma_number,
          invoice_type: data[0].invoice_type,
          id: data[0].id,
          raw_data: JSON.stringify(data[0]),
          all_keys: Object.keys(data[0])
        });
      }

      // Normalize the data to ensure consistent structure
      const processedInvoices = Array.isArray(data) ? data.map(invoice => {
        // Log each invoice for debugging
        console.log('Processing invoice:', invoice);

        // Check if it's a proforma invoice based on type field
        const isProforma = invoice.type === 'proforma' || invoice.invoice_type === 'proforma' || invoice.isProforma === true;

        console.log(`Invoice #${invoice.id} type check:`, {
          type: invoice.type,
          invoice_type: invoice.invoice_type,
          isProforma: invoice.isProforma,
          calculated: isProforma
        });

        // Create the processed invoice object - use the same structure as InvoicesPage
        const processedInvoice = {
          id: invoice.id || 0,
          isProforma: isProforma,
          user_id: invoice.user_id || userId,
          invoice_type: invoice.invoice_type || invoice.type || 'invoice',
          amount: invoice.amount || '€0.00',
          payment_date: invoice.payment_date || new Date().toISOString(),
          due_date: invoice.due_date || calculateDueDate(invoice.payment_date || new Date().toISOString()),
          payment_method: invoice.payment_method || 'Unknown',
          status: invoice.status || 'Pending',
          order_id: invoice.order_id || null,
          description: invoice.description || '',
          full_description: invoice.full_description || invoice.description || '',
          // Client information
          client: invoice.client || 'Client #' + userId,
          clientId: userId,
          clientEmail: invoice.clientEmail || '',
          // Additional fields needed for InvoiceView
          date: invoice.date || invoice.payment_date || new Date().toISOString(),
          dueDate: invoice.dueDate || invoice.due_date || calculateDueDate(invoice.payment_date || new Date().toISOString()),
          paymentDate: invoice.paymentDate || invoice.paid_date || '',
          items: invoice.items || [{
            description: invoice.description || 'Invoice item',
            quantity: 1,
            unitPrice: '€' + parseFloat(invoice.amount).toFixed(2),
            total: '€' + parseFloat(invoice.amount).toFixed(2)
          }],
          subtotal: invoice.subtotal || '€' + parseFloat(invoice.amount || 0).toFixed(2),
          tax: invoice.tax || '€0.00',
          total: invoice.total || '€' + parseFloat(invoice.amount || 0).toFixed(2),
          notes: invoice.notes || invoice.description || ''
        };

        // Log the final processed invoice
        console.log(`Final processed invoice #${processedInvoice.id}:`, {
          id: processedInvoice.id,
          isProforma: processedInvoice.isProforma,
          type: processedInvoice.invoice_type,
          display_value: `${processedInvoice.isProforma ? 'Proforma ' : ''}${processedInvoice.id}`,
          client: processedInvoice.client,
          amount: processedInvoice.amount,
          status: processedInvoice.status
        });

        return processedInvoice;
      }) : [];

      console.log('Processed invoices:', processedInvoices);

      // Log a sample processed invoice
      if (processedInvoices.length > 0) {
        console.log('Sample processed invoice:', {
          invoice_number: processedInvoices[0].invoice_number,
          proforma_number: processedInvoices[0].proforma_number,
          isProforma: processedInvoices[0].isProforma,
          id: processedInvoices[0].id
        });
      }

      setInvoices(processedInvoices);

      // Calculate stats
      const totalAmount = processedInvoices.reduce((sum, invoice) => {
        // Extract numeric value from amount string (remove currency symbol)
        const numericAmount = parseFloat(String(invoice.amount || '0').replace(/[^0-9.]/g, '') || 0);
        return sum + numericAmount;
      }, 0);
      const paidInvoices = processedInvoices.filter(invoice => invoice.status === 'Paid').length;
      const pendingInvoices = processedInvoices.filter(invoice => invoice.status === 'Pending').length;

      setStats({
        totalInvoices: processedInvoices.length,
        paidInvoices,
        pendingInvoices,
        totalAmount: totalAmount.toFixed(2)
      });

      setLoading(false);
    } catch (err) {
      console.error("Error fetching invoices:", err);
      setError("Failed to load invoice data: " + err.message);
      setLoading(false);

      // Set sample data for preview
      const sampleData = generateSampleInvoices(userId);
      setInvoices(sampleData);

      // Calculate sample stats
      const totalAmount = sampleData.reduce((sum, invoice) => {
        // Extract numeric value from amount string (remove currency symbol)
        const numericAmount = parseFloat(String(invoice.amount || '0').replace(/[^0-9.]/g, '') || 0);
        return sum + numericAmount;
      }, 0);
      const paidInvoices = sampleData.filter(invoice => invoice.status === 'Paid').length;
      const pendingInvoices = sampleData.filter(invoice => invoice.status === 'Pending').length;

      setStats({
        totalInvoices: sampleData.length,
        paidInvoices,
        pendingInvoices,
        totalAmount: totalAmount.toFixed(2)
      });
    }
  };

  // Calculate due date (30 days from issue date)
  const calculateDueDate = (dateString) => {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    date.setDate(date.getDate() + 30);
    return date.toISOString();
  };

  // Generate sample invoice data for preview
  const generateSampleInvoices = (userId) => {
          const statuses = ['Paid', 'Pending', 'Processing', 'Overdue'];
    const paymentMethods = ['Credit Card', 'Bank Transfer', 'PayPal', 'Crypto'];
    const descriptions = [
      'Monthly subscription payment',
      'Service fee',
      'Product purchase',
      'Premium plan renewal'
    ];

    return Array.from({ length: 8 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i * 30);
      const dueDate = new Date(date);
      dueDate.setDate(dueDate.getDate() + 30);

      const amount = (99.99 + i * 25).toFixed(2);
      const description = descriptions[i % descriptions.length];

      // Make some invoices proforma for demonstration
      const isProforma = i % 3 === 2;

      // Create a sample invoice with consistent structure
      const sampleInvoice = {
        id: 1000 + i,
        isProforma: isProforma,
        invoice_type: isProforma ? 'proforma' : 'invoice',
        user_id: userId,
        amount: '€' + amount,
        payment_date: date.toISOString(),
        due_date: dueDate.toISOString(),
        payment_method: paymentMethods[i % paymentMethods.length],
        status: statuses[i % statuses.length],
        description: description,
        // Client information
        client: 'Client #' + userId,
        clientId: userId,
        clientEmail: 'client' + userId + '@example.com',
        // Additional fields needed for InvoiceView
        date: date.toISOString(),
        dueDate: dueDate.toISOString(),
        paymentDate: statuses[i % statuses.length] === 'Paid' ? date.toISOString() : '',
        items: [{
          description: description,
          quantity: 1,
          unitPrice: '€' + amount,
          total: '€' + amount
        }],
        subtotal: '€' + amount,
        tax: '€0.00',
        total: '€' + amount,
        notes: 'Thank you for your business.'
      };

      // Log the sample invoice for debugging
      console.log('Sample invoice:', {
        ...sampleInvoice,
        display_value: `${sampleInvoice.isProforma ? 'Proforma ' : ''}${sampleInvoice.id}`
      });

      return sampleInvoice;
    });
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle status filter
  const handleStatusFilter = (e) => {
    setStatusFilter(e.target.value);
  };

  // Handle sort
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get sort icon
  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <ArrowUp className="w-4 h-4 inline ml-1" /> :
      <ArrowDown className="w-4 h-4 inline ml-1" />;
  };

  // Render status badge
  const renderStatusBadge = (status, isProforma = false) => {
    // If this is a proforma invoice, show a special badge
    if (isProforma) {
      return (
        <span className="px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit bg-purple-100 text-purple-800">
          <FileText className="w-4 h-4 mr-1" />
          Proforma
        </span>
      );
    }

    const badgeClasses = {
      'Paid': 'bg-green-100 text-green-800',
      'Pending': 'bg-yellow-100 text-yellow-800',
      'Processing': 'bg-blue-100 text-blue-800',
      'Cancelled': 'bg-gray-100 text-gray-800',
      'Refunded': 'bg-red-100 text-red-800',
      'Overdue': 'bg-red-100 text-red-800'
    };

    const icons = {
      'Paid': <CheckCircle className="w-4 h-4 mr-1" />,
      'Pending': <Clock className="w-4 h-4 mr-1" />,
      'Processing': <RefreshCw className="w-4 h-4 mr-1" />,
      'Cancelled': <AlertTriangle className="w-4 h-4 mr-1" />,
      'Refunded': <DollarSign className="w-4 h-4 mr-1" />,
      'Overdue': <AlertCircle className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertTriangle className="w-4 h-4 mr-1" />}
        {status}
      </span>
    );
  };

  // Format date safely in DD/MM/YYYY format
  const formatDate = (dateString) => {
    try {
      if (!dateString) return 'N/A';
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid date';

      // Format as DD/MM/YYYY
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Month is 0-indexed
      const year = date.getFullYear();

      return `${day}/${month}/${year}`;
    } catch (e) {
      console.error('Date formatting error:', e, dateString);
      return 'Invalid date';
    }
  };

  // Calculate due date status
  const getDueDateStatus = (dueDate, status) => {
    if (status === 'Paid') return null;

    const today = new Date();
    const due = new Date(dueDate);
    const diffDays = Math.ceil((due - today) / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return <span className="text-xs text-red-600">{Math.abs(diffDays)} days overdue</span>;
    } else if (diffDays <= 7) {
      return <span className="text-xs text-yellow-600">Due in {diffDays} days</span>;
    }

    return null;
  };

  // Filter invoices
  const filteredInvoices = invoices.filter(invoice => {
    // Convert everything to strings for safer comparison
    const invoiceIdStr = String(invoice.invoice_id || '').toLowerCase();
    const clientStr = String(invoice.client || '').toLowerCase();
    const paymentMethodStr = String(invoice.payment_method || '').toLowerCase();
    const descriptionStr = String(invoice.description || '').toLowerCase();
    const searchQueryLower = searchQuery.toLowerCase();

    const matchesSearch =
      invoiceIdStr.includes(searchQueryLower) ||
      clientStr.includes(searchQueryLower) ||
      paymentMethodStr.includes(searchQueryLower) ||
      descriptionStr.includes(searchQueryLower);

    const matchesStatus = statusFilter === 'All' || invoice.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Sort invoices
  const sortedInvoices = [...filteredInvoices].sort((a, b) => {
    let comparison = 0;

    if (sortField === 'invoice_id' || sortField === 'id') {
      comparison = parseInt(a.id || 0) - parseInt(b.id || 0);
    } else if (sortField === 'amount') {
      comparison = parseFloat(a.amount || 0) - parseFloat(b.amount || 0);
    } else if (sortField === 'payment_date') {
      const dateA = new Date(a.payment_date || 0);
      const dateB = new Date(b.payment_date || 0);
      comparison = dateA - dateB;
    } else if (sortField === 'due_date') {
      const dateA = new Date(a.due_date || 0);
      const dateB = new Date(b.due_date || 0);
      comparison = dateA - dateB;
    } else if (sortField === 'client') {
      comparison = (a.client || '').localeCompare(b.client || '');
    } else if (sortField === 'status') {
      comparison = (a.status || '').localeCompare(b.status || '');
    } else {
      comparison = parseInt(a.id || 0) - parseInt(b.id || 0);
    }

    return sortDirection === 'asc' ? comparison : -comparison;
  });

  // Handle view invoice
  const handleViewInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setInvoiceModalOpen(true);
  };

  // Handle close invoice modal
  const handleCloseInvoiceModal = () => {
    setInvoiceModalOpen(false);
    setSelectedInvoice(null);
  };

  // Handle invoice actions
  const handleDownloadInvoice = (invoice) => {
    console.log('Download invoice:', invoice.id);
    // Implement download functionality
    alert(`Download functionality for invoice #${invoice.id} would be implemented here.`);
  };

  // Generate invoice stats cards
  const generateInvoiceStatsCards = () => {
    const statsCards = [
      {
        title: 'Total Invoices',
        value: stats.totalInvoices,
        icon: <FileText className="text-indigo-700" size={36} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-info'
      },
      {
        title: 'Paid Invoices',
        value: stats.paidInvoices,
        icon: <CheckCircle className="text-success" size={36} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-success'
      },
      {
        title: 'Pending Invoices',
        value: stats.pendingInvoices,
        icon: <AlertTriangle className="text-warning" size={36} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-warning'
      },
      {
        title: 'Total Amount',
        value: `$${stats.totalAmount}`,
        icon: <DollarSign className="text-emerald-600" size={36} strokeWidth={2} />,
        iconClass: 'icon-dropshadow-success'
      }
    ];

    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
        {statsCards.map((stat, index) => (
          <div
            key={index}
            className="bg-white border rounded p-4 flex items-center justify-between"
          >
            <div>
              <div className="text-xs text-gray-600">{stat.title}</div>
              <div className="text-lg font-bold mt-1">{stat.value}</div>
            </div>
            <div className={`card-custom-icon ${stat.iconClass}`}>
              {stat.icon}
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Invoice Stats Cards */}
      {generateInvoiceStatsCards()}

      {/* Invoice Table */}
      <div className="bg-white border rounded-md">
        <div className="p-3 border-b flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
          <div className="flex gap-2">
            <div className="relative">
              <select
                className="pl-3 pr-8 py-1.5 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                value={statusFilter}
                onChange={handleStatusFilter}
              >
                <option value="All">All Status</option>
                <option value="Paid">Paid</option>
                <option value="Pending">Pending</option>
                <option value="Processing">Processing</option>
                <option value="Overdue">Overdue</option>
                <option value="Cancelled">Cancelled</option>
                <option value="Refunded">Refunded</option>
              </select>
              <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
            </div>
            <button
              onClick={fetchInvoices}
              className="p-2 border rounded-md text-gray-500 hover:bg-gray-50"
              title="Refresh invoices"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </button>
          </div>
          <div className="relative w-full sm:w-64">
            <input
              type="text"
              placeholder="Search invoices..."
              className="pl-8 pr-3 py-1.5 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200"
              value={searchQuery}
              onChange={handleSearch}
            />
            <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-gray-500 text-xs border-b">
                <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('id')}>
                  INVOICE # {getSortIcon('id')}
                </th>
                <th className="p-4 text-left font-medium cursor-pointer hide-xs" onClick={() => handleSort('client')}>
                  CLIENT {getSortIcon('client')}
                </th>
                <th className="p-4 text-left font-medium cursor-pointer hide-sm" onClick={() => handleSort('payment_date')}>
                  ISSUE DATE {getSortIcon('payment_date')}
                </th>
                <th className="p-4 text-left font-medium cursor-pointer hide-sm" onClick={() => handleSort('due_date')}>
                  DUE DATE {getSortIcon('due_date')}
                </th>
                <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('amount')}>
                  AMOUNT {getSortIcon('amount')}
                </th>
                <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('status')}>
                  STATUS {getSortIcon('status')}
                </th>
                <th className="p-4 text-left font-medium">ACTIONS</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                Array(5).fill(0).map((_, index) => (
                  <tr key={index} className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                    <td className="p-4"><div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div></td>
                    <td className="p-4 hide-xs"><div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div></td>
                    <td className="p-4 hide-sm"><div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div></td>
                    <td className="p-4 hide-sm"><div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div></td>
                    <td className="p-4"><div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div></td>
                    <td className="p-4"><div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div></td>
                    <td className="p-4"><div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div></td>
                  </tr>
                ))
              ) : error ? (
                <tr>
                  <td colSpan="7" className="p-4 text-center text-red-600">
                    {error}
                  </td>
                </tr>
              ) : sortedInvoices.length > 0 ? (
                sortedInvoices.map((invoice, index) => (
                  <tr
                    key={invoice.id}
                    className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-gray-50 cursor-pointer ${invoice.isProforma ? 'bg-purple-50' : ''}`}
                    onClick={() => handleViewInvoice(invoice)}
                  >
                    <td className="p-4 font-medium text-indigo-700">
                      {invoice.isProforma ? 'Proforma ' : ''}{invoice.id}
                    </td>
                    <td className="p-4 text-gray-700 hide-xs">{invoice.client}</td>
                    <td className="p-4 text-gray-700 hide-sm">
                      {formatDate(invoice.payment_date)}
                    </td>
                    <td className="p-4 hide-sm">
                      <div className="flex flex-col">
                        <span className="text-gray-700">{formatDate(invoice.due_date)}</span>
                        {getDueDateStatus(invoice.due_date, invoice.status)}
                      </div>
                    </td>
                    <td className="p-4 font-medium">{invoice.amount}</td>
                    <td className="p-4">
                      {invoice.isProforma ? (
                        <span className="px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit bg-purple-100 text-purple-800">
                          <FileText className="w-4 h-4 mr-1" />
                          Proforma
                        </span>
                      ) : (
                        renderStatusBadge(invoice.status || 'Pending', false)
                      )}
                    </td>
                    <td className="p-4" onClick={(e) => e.stopPropagation()}>
                      <div className="flex space-x-2">
                        <button
                          className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
                          title={invoice.full_description || invoice.description || 'View invoice details'}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewInvoice(invoice);
                          }}
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        {invoice.status === 'Paid' && (
                          <button
                            className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
                            title="Download Invoice"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDownloadInvoice(invoice);
                            }}
                          >
                            <Download className="w-4 h-4" />
                          </button>
                        )}
                        <button
                          className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
                          title="Edit Invoice"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Implement edit functionality
                            console.log('Edit invoice:', invoice.id);
                          }}
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="7" className="p-4 text-center text-gray-500">
                    No invoices found matching your criteria
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Table Footer */}
        <div className="p-4 border-t flex justify-between items-center">
          <div className="text-sm text-gray-500">
            Showing {sortedInvoices.length} of {invoices.length} invoices
          </div>
          {sortedInvoices.length > 10 && (
            <div className="flex space-x-1">
              <button className="px-3 py-1 border rounded text-sm bg-indigo-700 text-white">1</button>
              <button className="px-3 py-1 border rounded text-sm bg-white text-gray-700 hover:bg-gray-50">2</button>
              <button className="px-3 py-1 border rounded text-sm bg-white text-gray-700 hover:bg-gray-50">3</button>
            </div>
          )}
        </div>
      </div>

      {/* Invoice View Modal */}
      {invoiceModalOpen && selectedInvoice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {/* Custom header with close button */}
            <div className="p-6 border-b flex justify-between items-center">
              <div>
                <h2 className="text-xl font-bold text-gray-800 mb-2">
                  {selectedInvoice.isProforma ? 'Proforma Invoice' : 'Invoice'} #{selectedInvoice.id}
                </h2>
                <div className="flex flex-wrap gap-2">
                  {/* For proforma, only show one badge */}
                  {selectedInvoice.isProforma ? (
                    <>
                      {/* Type badge for proforma */}
                      <span className="px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit bg-purple-100 text-purple-800">
                        <FileText className="w-4 h-4 mr-1" />
                        Proforma
                      </span>

                      {/* Invoice number badge */}
                      <span className="px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit bg-gray-100 text-gray-800">
                        <FileText className="w-4 h-4 mr-1" />
                        #{selectedInvoice.id}
                      </span>
                    </>
                  ) : (
                    <>
                      {/* Type badge for regular invoice */}
                      <span className="px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit bg-blue-100 text-blue-800">
                        <FileText className="w-4 h-4 mr-1" />
                        Invoice
                      </span>

                      {/* Invoice number badge */}
                      <span className="px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit bg-gray-100 text-gray-800">
                        <FileText className="w-4 h-4 mr-1" />
                        #{selectedInvoice.id}
                      </span>

                      {/* Status badge for regular invoice */}
                      {renderStatusBadge(selectedInvoice.status, false)}
                    </>
                  )}
                </div>
              </div>
              <button
                onClick={handleCloseInvoiceModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            {/* Modified InvoiceView with hideHeader to avoid duplicate headers */}
            <InvoiceView
              invoice={selectedInvoice}
              onEdit={() => {
                // Implement the edit functionality for InvoicesTab
                console.log('Edit invoice:', selectedInvoice.id);
              }}
              onUpdateStatus={(id, status) => {
                console.log(`Status updated for invoice #${id} to ${status}`);
                fetchInvoices(); // Refresh invoices
                handleCloseInvoiceModal();
              }}
              onSend={(invoice) => console.log('Send invoice:', invoice.id)}
              onPrint={(invoice) => console.log('Print invoice:', invoice.id)}
              onDownload={(invoice) => handleDownloadInvoice(invoice)}
              className="border-t-0 rounded-t-none"
              hideHeader={true}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default InvoicesTab;