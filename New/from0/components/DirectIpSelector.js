import React, { useState, useEffect } from 'react';
import { RefreshCw, Search, ChevronDown } from 'lucide-react';
import { API_URL } from '../config';

// SearchableIpDropdown component for IP address selection with search functionality
const SearchableIpDropdown = ({ 
  value, 
  onChange, 
  options = [], 
  placeholder = "Select an IP address",
  disabled = false,
  loading = false,
  excludeIps = []
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredOptions, setFilteredOptions] = useState(options);

  // Update filtered options when search term or options change
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredOptions(options);
    } else {
      const filtered = options.filter(option => {
        const searchLower = searchTerm.toLowerCase();
        return (
          option.ip_address.toLowerCase().includes(searchLower) ||
          (option.subnet_name && option.subnet_name.toLowerCase().includes(searchLower)) ||
          (option.subnet_cidr && option.subnet_cidr.toLowerCase().includes(searchLower)) ||
          (option.city && option.city.toLowerCase().includes(searchLower)) ||
          (option.country && option.country.toLowerCase().includes(searchLower))
        );
      });
      setFilteredOptions(filtered);
    }
  }, [searchTerm, options]);

  // Find selected option for display
  const selectedOption = options.find(option => option.ip_address === value);

  const handleSelect = (option) => {
    onChange(option.ip_address);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleInputChange = (e) => {
    setSearchTerm(e.target.value);
    if (!isOpen) {
      setIsOpen(true);
    }
  };

  const handleInputFocus = () => {
    setIsOpen(true);
  };

  const handleInputBlur = (e) => {
    // Delay closing to allow clicks on dropdown items
    setTimeout(() => {
      setIsOpen(false);
      setSearchTerm('');
    }, 200);
  };

  const handleDropdownToggle = () => {
    if (disabled || loading) return;
    setIsOpen(!isOpen);
    if (!isOpen) {
      setSearchTerm('');
    }
  };

  return (
    <div className="relative w-full">
      <div className="relative">
        <div 
          className={`w-full p-1.5 border ${
            disabled ? 'bg-gray-100 border-gray-200' : 'border-gray-300 hover:border-gray-400'
          } rounded-md text-sm cursor-pointer flex items-center`}
          onClick={handleDropdownToggle}
        >
          <div className="truncate">
            {selectedOption ? (
              <span className="block truncate">
                {selectedOption.ip_address}
                {selectedOption.subnet_name && ` - ${selectedOption.subnet_name}`}
                {selectedOption.city && ` (${selectedOption.city}${selectedOption.country ? `, ${selectedOption.country}` : ''})`}
                {parseInt(String(selectedOption.is_used), 10) === 1 && ' [USED]'}
                {selectedOption.ip_address === value && ' [CURRENT]'}
              </span>
            ) : (
              <span className="text-gray-500">{placeholder}</span>
            )}
          </div>
          {loading ? (
            <RefreshCw className="w-4 h-4 flex-shrink-0 text-gray-400 animate-spin ml-2" />
          ) : (
            <ChevronDown className={`w-4 h-4 flex-shrink-0 text-gray-400 ml-2 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          )}
        </div>

        {/* Dropdown */}
        {isOpen && !disabled && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-hidden">
            {/* Search input */}
            <div className="p-2 border-b border-gray-200">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={handleInputChange}
                  onFocus={handleInputFocus}
                  onBlur={handleInputBlur}
                  placeholder="Search IP addresses..."
                  className="w-full pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                  autoFocus
                />
              </div>
            </div>

            {/* Options list */}
            <div className="max-h-40 overflow-y-auto">
              {/* Clear/Empty option - always show at top */}
              <div
                className={`px-3 py-2 text-sm cursor-pointer border-b border-gray-100 hover:bg-red-50 hover:text-red-700 ${
                  !value ? 'bg-red-100 text-red-700' : 'text-gray-600'
                }`}
                onMouseDown={(e) => {
                  e.preventDefault(); // Prevent blur from firing before click
                  onChange(''); // Set to empty string to clear the field
                  setIsOpen(false);
                  setSearchTerm('');
                }}
              >
                <div className="flex flex-col">
                  <div className="font-medium flex items-center">
                    <span className="text-red-500 mr-2">✕</span>
                    Clear IP Address
                    {!value && ' [CURRENT]'}
                  </div>
                  <div className="text-xs text-gray-500">
                    Remove the current IP assignment
                  </div>
                </div>
              </div>

              {filteredOptions.length === 0 ? (
                <div className="px-3 py-2 text-sm text-gray-500">
                  {searchTerm ? 'No IP addresses found matching your search' : 'No IP addresses available'}
                </div>
              ) : (
                filteredOptions
                  .filter(option => {
                    const isCurrentSelection = option.ip_address === value;
                    const isExcluded = excludeIps && excludeIps.includes(option.ip_address) && !isCurrentSelection;
                    return !isExcluded;
                  })
                  .map((option, idx) => {
                    const isUsed = parseInt(String(option.is_used), 10) === 1;
                    const isCurrentSelection = option.ip_address === value;
                    
                    return (
                      <div
                        key={`${option.ip_address}-${idx}`}
                        className={`px-3 py-2 text-sm cursor-pointer hover:bg-indigo-50 hover:text-indigo-700 ${
                          option.ip_address === value ? 'bg-indigo-100 text-indigo-700' : 'text-gray-900'
                        }`}
                        onMouseDown={(e) => {
                          e.preventDefault(); // Prevent blur from firing before click
                          handleSelect(option);
                        }}
                      >
                        <div className="flex flex-col">
                          <div className={`font-medium ${isUsed ? 'text-red-600' : isCurrentSelection ? 'text-green-600' : ''}`}>
                            {option.ip_address}
                            {isUsed && ' [USED]'}
                            {isCurrentSelection && ' [CURRENT]'}
                          </div>
                          <div className="text-xs text-gray-500">
                            {option.subnet_name || option.subnet_cidr || 'Unknown subnet'}
                            {option.city && ` • ${option.city}${option.country ? `, ${option.country}` : ''}`}
                          </div>
                        </div>
                      </div>
                    );
                  })
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// DirectIpSelector component for selecting IPs directly from the database
const DirectIpSelector = ({ index, value, onChange, cityId, deviceType, label, placeholder, cities, excludeIps = [] }) => {
  const [availableIps, setAvailableIps] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showAllIpsButton, setShowAllIpsButton] = useState(false);
  const [showingAllIps, setShowingAllIps] = useState(false);
  const [showDirectQueryButton, setShowDirectQueryButton] = useState(false);
  const [directQueryAttempted, setDirectQueryAttempted] = useState(false);

  // Fetch available IPs when the component mounts or when cityId changes
  useEffect(() => {
    if (cityId) {
      fetchAvailableIps();
    }
  }, [cityId]);

  // Function to directly query the IP table without going through subnets
  const fetchAllIpsDirectly = async () => {
    if (!cityId) return;

    console.log(`Directly querying all IPs in the database`);
    setLoading(true);
    setError('');
    setDirectQueryAttempted(true);

    try {
      const token = localStorage.getItem('admin_token');

      // Get all subnets first to map subnet_id to subnet info
      const subnetsResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnets`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      const allSubnets = await subnetsResponse.json();

      if (!Array.isArray(allSubnets)) {
        console.error('Failed to fetch subnets');
        setError('Failed to fetch subnets');
        setLoading(false);
        return;
      }

      // Create a map of subnet_id to subnet info
      const subnetMap = {};
      allSubnets.forEach(subnet => {
        subnetMap[subnet.id] = subnet;
      });

      // Now get all IPs for all subnets
      const allIps = [];

      for (const subnet of allSubnets) {
        try {
          // Extract subnet ID
          const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
            ? subnet.id.replace('SUB-', '')
            : subnet.id;

          console.log(`Fetching IPs for subnet ${subnet.subnet || subnet.cidr || 'Unnamed'} (ID: ${subnetId})`);

          const ipResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnet_ips`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              subnet_id: subnetId,
              token: token,
              include_used: true // Include used IPs
            })
          });

          const ipData = await ipResponse.json();

          if (ipData && ipData.success && ipData.ips && ipData.ips.length > 0) {
            // Add subnet info to each IP
            const ipsWithSubnetInfo = ipData.ips.map(ip => ({
              ...ip,
              subnet_cidr: ipData.subnet_cidr || subnet.cidr,
              subnet_id: subnet.id,
              subnet_name: subnet.subnet,
              city: subnet.city,
              country: subnet.country
            }));

            allIps.push(...ipsWithSubnetInfo);
          }
        } catch (err) {
          console.error(`Error fetching IPs for subnet ${subnet.id}:`, err);
        }
      }

      console.log(`Found ${allIps.length} total IPs in the database`);

      // Filter for free IPs unless we're including used IPs
      const freeIps = allIps.filter(ip => parseInt(String(ip.is_used), 10) === 0);
      const usedIps = allIps.filter(ip => parseInt(String(ip.is_used), 10) === 1);

      console.log(`Found ${freeIps.length} free IPs and ${usedIps.length} used IPs`);

      // If we have a current value but it's not in the IPs, add it as a special option
      if (value && !allIps.some(ip => ip.ip_address === value)) {
        console.log(`Current IP ${value} not found in IPs, adding it as a special option`);

        // Add the current IP as a special option
        allIps.push({
          ip_address: value,
          is_used: '1', // Mark as used
          subnet_cidr: "Current IP",
          subnet_id: 'current', // Special marker
          is_current: true, // Flag to identify this as the current IP
          city: '',
          country: ''
        });
      }

      if (freeIps.length === 0 && usedIps.length === 0) {
        setError('No IPs found in the database at all. Please contact an administrator to generate IPs.');
      } else if (freeIps.length === 0) {
        setError(`No free IPs found in the database. Found ${usedIps.length} used IPs. All IPs are currently in use.`);
        setShowAllIpsButton(true);
      }

      // Set all IPs or just free IPs based on showingAllIps flag
      setAvailableIps(showingAllIps ? allIps : freeIps);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching all IPs:', err);
      setError('Failed to load IPs: ' + (err.message || 'Unknown error'));
      setLoading(false);
    }
  };

  // Function to fetch available IPs directly from the database
  const fetchAvailableIps = async (includeUsed = false) => {
    // If we're showing all IPs, set the flag
    if (includeUsed) {
      setShowingAllIps(true);
    }
    if (!cityId) return;

    console.log(`Fetching available IPs for city ID: ${cityId}`);
    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('admin_token');

      // First, get city and country names for display
      let cityName = '';
      let countryName = '';

      console.log('Cities array:', cities);

      if (Array.isArray(cities) && cities.length > 0) {
        const cityInfo = cities.find(city =>
          city.id === parseInt(cityId, 10) ||
          city.id === cityId ||
          String(city.id) === String(cityId)
        );

        if (cityInfo) {
          cityName = cityInfo.name;
          countryName = cityInfo.country;
          console.log(`City: ${cityName}, Country: ${countryName} (ID: ${cityId})`);
        } else {
          console.warn(`City with ID ${cityId} not found in cities array`);

          // Try to get city info from the first city that matches the country
          const cityInCountry = cities.find(city => city.country);
          if (cityInCountry) {
            countryName = cityInCountry.country;
            console.log(`Using country from first city: ${countryName}`);
          }
        }
      } else {
        console.warn('Cities array is empty or not an array');
      }

      // Get all subnets and their IPs
      console.log(`Fetching all subnets and their IPs for city ID: ${cityId}`);

      // First get all subnets in the city
      const subnetsResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnets`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      const allSubnets = await subnetsResponse.json();

      if (!Array.isArray(allSubnets)) {
        console.error('Failed to fetch subnets');
        setError('Failed to fetch subnets');
        setLoading(false);
        return;
      }

      // Get all subnets - don't filter by status
      console.log('Getting all subnets regardless of status');
      const citySubnets = allSubnets;

      console.log(`Found ${citySubnets.length} subnets in city ${cityName}`);

      // Get IPs for each subnet
      const allIps = [];

      for (const subnet of citySubnets) {
        try {
          // Extract subnet ID
          const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
            ? subnet.id.replace('SUB-', '')
            : subnet.id;

          console.log(`Fetching IPs for subnet ${subnet.subnet || subnet.cidr || 'Unnamed'} (ID: ${subnetId})`);

          // Continue fetching IPs for each subnet
          const subnetIpResponse = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnet_ips`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              subnet_id: subnetId,
              token: token,
              include_used: true // Include used IPs to check if the current value is already used
            })
          });

          const subnetIpData = await subnetIpResponse.json();

          if (subnetIpData && subnetIpData.success && subnetIpData.ips) {
            // Count total IPs and used IPs
            const totalIps = subnetIpData.ips.length;
            const usedIps = subnetIpData.ips.filter(ip => parseInt(String(ip.is_used), 10) === 1);
            const availableSubnetIps = subnetIpData.ips.filter(ip => parseInt(String(ip.is_used), 10) === 0);

            console.log(`Subnet ${subnet.subnet || subnet.cidr || 'Unnamed'}: ${availableSubnetIps.length} available IPs, ${usedIps.length} used IPs, ${totalIps} total IPs`);

            // If we're showing all IPs or there are no available IPs but there are used IPs, include all IPs
            let ipsToInclude = includeUsed || showingAllIps
              ? subnetIpData.ips // Include all IPs
              : availableSubnetIps; // Only include available IPs

            // Filter out IPs that are in the excludeIps array (already selected by other items)
            if (excludeIps && excludeIps.length > 0) {
              ipsToInclude = ipsToInclude.filter(ip => {
                // Always include the current value (the IP selected for this specific item)
                if (ip.ip_address === value) {
                  return true;
                }
                // Exclude IPs that are already selected by other items
                return !excludeIps.includes(ip.ip_address);
              });
            }

            // Add subnet info to each IP
            const ipsWithSubnetInfo = ipsToInclude.map(ip => ({
              ...ip,
              subnet_cidr: subnetIpData.subnet_cidr,
              subnet_id: subnet.id,
              subnet_name: subnet.subnet,
              city: subnet.city,
              country: subnet.country
            }));

            allIps.push(...ipsWithSubnetInfo);

            // Store information about used IPs for later
            if (usedIps.length > 0) {
              subnet.usedIps = usedIps.length;
              subnet.totalIps = totalIps;
            }
          }
        } catch (err) {
          console.error(`Error fetching IPs for subnet ${subnet.id}:`, err);
        }
      }

      console.log(`Found ${allIps.length} total available IPs in city ${cityName}`);

      if (allIps.length === 0) {
        // Check if there are used IPs in the subnets
        const subnetsWithUsedIps = citySubnets.filter(subnet => subnet.usedIps > 0);
        const totalUsedIps = subnetsWithUsedIps.reduce((total, subnet) => total + (subnet.usedIps || 0), 0);
        const totalIpsInSubnets = subnetsWithUsedIps.reduce((total, subnet) => total + (subnet.totalIps || 0), 0);

        const cityText = cityName ? ` for ${cityName}` : '';
        let message = '';

        if (totalUsedIps > 0) {
          message = `No available IPs found in the database${cityText}. Found ${totalUsedIps} used IPs out of ${totalIpsInSubnets} total IPs in ${subnetsWithUsedIps.length} subnets. All IPs are currently in use. Please contact an administrator to deallocate some IPs.`;

          // Add a button to show all IPs, including used ones
          setShowAllIpsButton(true);
        } else {
          message = `No IPs found in the visible subnets${cityText}. Processed ${citySubnets.length} subnets but found no IPs. Try checking all subnets in the database.`;

          // Add a button to check all subnets
          setShowDirectQueryButton(true);
        }

        console.warn(message);
        setError(message);
        setLoading(false);
        return;
      }

      let freeIps = allIps;

      // Filter out IPs that are in the excludeIps array (already selected by other items)
      if (excludeIps && excludeIps.length > 0) {
        freeIps = freeIps.filter(ip => {
          // Always include the current value (the IP selected for this specific item)
          if (ip.ip_address === value) {
            return true;
          }
          // Exclude IPs that are already selected by other items
          return !excludeIps.includes(ip.ip_address);
        });
      }

      // If we have a current value but it's not in the free IPs, add it as a special option
      if (value && !freeIps.some(ip => ip.ip_address === value)) {
        console.log(`Current IP ${value} not found in free IPs, adding it as a special option`);

        // Add the current IP as a special option
        freeIps.push({
          ip_address: value,
          is_used: '1', // Mark as used
          subnet_cidr: "Current IP",
          subnet_id: 'current', // Special marker
          is_current: true, // Flag to identify this as the current IP
          city: cityName,
          country: countryName
        });
      }

      if (freeIps.length === 0) {
        setError('No available IPs found in the database for this city. Please contact an administrator.');
      }

      setAvailableIps(freeIps);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching available IPs:', err);
      setError('Failed to load available IPs: ' + (err.message || 'Unknown error'));
      setLoading(false);
    }
  };

  // Handle IP selection change
  const handleIpChange = (newValue) => {
    // Call the parent's onChange handler
    onChange(index, deviceType === 'server' ? 'ipmi' : 'switch_ip', newValue);
  };

  return (
    <div className="flex flex-col bulk-ip-selector">
      <div className="flex">
        <SearchableIpDropdown
          value={value || ''}
          onChange={handleIpChange}
          options={availableIps}
          placeholder={placeholder || 'Select an IP address'}
          disabled={loading || !cityId}
          loading={loading}
          excludeIps={excludeIps}
        />
        <button
          type="button"
          onClick={fetchAvailableIps}
          className={`ml-1 p-1 ${loading ? 'bg-gray-100 text-gray-400' : 'bg-indigo-100 text-indigo-700 hover:bg-indigo-200'} rounded-md flex items-center justify-center`}
          title="Refresh available IPs"
          disabled={loading || !cityId}
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {error && (
        <div className="text-xs mt-1">
          <div className="text-red-500">{error}</div>

          {showAllIpsButton && (
            <div className="mt-2">
              <button
                type="button"
                onClick={() => fetchAvailableIps(true)}
                className="px-2 py-1 text-xs bg-amber-600 hover:bg-amber-700 text-white rounded-md"
              >
                Show All IPs (Including Used)
              </button>
              <div className="text-amber-600 text-xs mt-1">
                Warning: Selecting a used IP may cause conflicts. Use only if you know the IP is actually free.
              </div>
            </div>
          )}

          {showDirectQueryButton && !directQueryAttempted && (
            <div className="mt-2">
              <button
                type="button"
                onClick={fetchAllIpsDirectly}
                className="px-2 py-1 text-xs bg-blue-600 hover:bg-blue-700 text-white rounded-md"
              >
                Check All Subnets
              </button>
              <div className="text-blue-600 text-xs mt-1">
                This will check all subnets in the database, not just the ones marked as 'Available'.
              </div>
            </div>
          )}
        </div>
      )}

      {!cityId && (
        <div className="text-xs text-amber-500 mt-1">
          Select a city first
        </div>
      )}

      {cityId && !loading && !error && availableIps.length === 0 && (
        <div className="text-xs text-amber-500 mt-1">
          No available IPs found. Please contact an administrator to check the database.
        </div>
      )}

    </div>
  );
};

export default DirectIpSelector;
