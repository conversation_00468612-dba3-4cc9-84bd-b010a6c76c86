<?php
require_once 'auth_functions.php';

// Handle API requests
if (isset($_GET['f'])) {
  // Get request data
  $requestBody = file_get_contents('php://input');
  $data = json_decode($requestBody, true) ?: [];

  // Handle endpoints
  switch ($_GET['f']) {
    case 'global_search':
      try {
        // Authenticate admin
        $admin_id = auth_admin();

        // Get search query
        $query = isset($_GET['q']) ? $_GET['q'] : '';
        error_log("Global search query: $query");

        if (empty($query) || strlen($query) < 2) {
          // Return empty results if query is too short
          header('Content-Type: application/json');
          echo json_encode([
            'success' => true,
            'results' => [
              'invoices' => [],
              'clients' => [],
              'servers' => [],
              'switches' => [],
              'blades' => [],
              'chassis' => []
            ]
          ]);
          return;
        }

        // Log tables existence for debugging
        $tablesExist = [
          'invoices' => $pdo->query("SHOW TABLES LIKE 'invoices'")->rowCount() > 0,
          'users' => $pdo->query("SHOW TABLES LIKE 'users'")->rowCount() > 0,
          'inventory_dedicated_servers' => $pdo->query("SHOW TABLES LIKE 'inventory_dedicated_servers'")->rowCount() > 0,
          'inventory_switches' => $pdo->query("SHOW TABLES LIKE 'inventory_switches'")->rowCount() > 0,
          'blade_server_inventory' => $pdo->query("SHOW TABLES LIKE 'blade_server_inventory'")->rowCount() > 0,
          'inventory_chassis' => $pdo->query("SHOW TABLES LIKE 'inventory_chassis'")->rowCount() > 0
        ];

        error_log("Tables existence check: " . json_encode($tablesExist));

        // Initialize results array
        $results = [
          'invoices' => [],
          'clients' => [],
          'servers' => [],
          'switches' => [],
          'blades' => [],
          'chassis' => []
        ];

        // Search for invoices and proformas
        $searchInvoices = searchInvoices($pdo, $query);
        if ($searchInvoices) {
          $results['invoices'] = $searchInvoices;
        }

        // Search for clients
        $searchClients = searchClients($pdo, $query);
        if ($searchClients) {
          $results['clients'] = $searchClients;
        }

        // Search for servers
        $searchServers = searchServers($pdo, $query);
        if ($searchServers) {
          $results['servers'] = $searchServers;
        }

        // Search for switches
        $searchSwitches = searchSwitches($pdo, $query);
        if ($searchSwitches) {
          $results['switches'] = $searchSwitches;
        }

        // Search for blade servers
        $searchBlades = searchBlades($pdo, $query);
        if ($searchBlades) {
          $results['blades'] = $searchBlades;
        }

        // Search for chassis
        $searchChassis = searchChassis($pdo, $query);
        if ($searchChassis) {
          $results['chassis'] = $searchChassis;
        }

        // Return results
        header('Content-Type: application/json');
        echo json_encode([
          'success' => true,
          'results' => $results
        ]);

      } catch (Exception $e) {
        error_log("Error in global_search: " . $e->getMessage());
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
          'success' => false,
          'error' => 'Failed to perform global search: ' . $e->getMessage()
        ]);
      }
      break;

    default:
      // Unknown endpoint
      header('Content-Type: application/json');
      http_response_code(400);
      echo json_encode([
        'success' => false,
        'error' => 'Unknown endpoint'
      ]);
  }
} else {
  // No endpoint specified
  header('Content-Type: application/json');
  http_response_code(400);
  echo json_encode([
    'success' => false,
    'error' => 'No endpoint specified'
  ]);
}

/**
 * Search for invoices and proformas
 *
 * @param PDO $pdo Database connection
 * @param string $query Search query
 * @return array Array of matching invoices and proformas
 */
function searchInvoices($pdo, $query) {
  try {
    // Check if invoices table exists
    $tablesResult = $pdo->query("SHOW TABLES LIKE 'invoices'");
    if ($tablesResult->rowCount() === 0) {
      return [];
    }

    // Search for invoices and proformas
    $sql = "SELECT
              i.id,
              i.type,
              i.value as total,
              i.date,
              i.due_date,
              i.paid,
              i.proforma_number,
              i.invoice_number,
              i.subtotal,
              i.tax,
              i.payment_method,
              i.description,
              CASE
                WHEN i.paid = 1 THEN 'Paid'
                WHEN i.due_date < NOW() THEN 'Overdue'
                ELSE 'Pending'
              END as status,
              u.first_name,
              u.last_name,
              u.company_name,
              u.email,
              u.id as user_id
            FROM invoices i
            LEFT JOIN users u ON i.user_id = u.id
            WHERE
              CAST(i.id AS CHAR) LIKE :query OR
              i.type LIKE :query OR
              i.proforma_number LIKE :query OR
              i.invoice_number LIKE :query OR
              u.first_name LIKE :query OR
              u.last_name LIKE :query OR
              u.company_name LIKE :query OR
              u.email LIKE :query
            ORDER BY i.date DESC
            LIMIT 5";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':query', '%' . $query . '%');
    $stmt->execute();

    $invoices = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
      $clientName = '';
      if (!empty($row['company_name'])) {
        $clientName = $row['company_name'];
      } else {
        $clientName = trim($row['first_name'] . ' ' . $row['last_name']);
      }

      // Determine if this is a proforma
      $isProforma = strtolower($row['type']) === 'proforma';

      // Determine the display ID - use invoice_number if available, otherwise use proforma_number
      $displayId = !empty($row['invoice_number']) ? $row['invoice_number'] : $row['proforma_number'];
      // If both are empty, fall back to the database ID
      if (empty($displayId)) {
        $displayId = $row['id'];
      }

      // Get invoice items
      $items = [];
      try {
        $itemsStmt = $pdo->prepare("SELECT * FROM invoice_items WHERE invoice_id = :invoice_id");
        $itemsStmt->bindValue(':invoice_id', $row['id']);
        $itemsStmt->execute();

        while ($item = $itemsStmt->fetch(PDO::FETCH_ASSOC)) {
          $items[] = [
            'description' => $item['description'],
            'quantity' => intval($item['quantity']),
            'unitPrice' => '€' . number_format(floatval($item['price']), 2, '.', ','),
            'total' => '€' . number_format(floatval($item['total']), 2, '.', ','),
            'period' => $item['period']
          ];
        }
      } catch (Exception $e) {
        error_log("Error fetching invoice items: " . $e->getMessage());
        // Continue even if items can't be fetched
      }

      // If no items were found, create a default item
      if (empty($items)) {
        $items = [
          [
            'description' => ($isProforma ? 'Proforma' : 'Invoice') . ' #' . $displayId,
            'quantity' => 1,
            'unitPrice' => '€' . number_format(floatval($row['total']), 2, '.', ','),
            'total' => '€' . number_format(floatval($row['total']), 2, '.', ','),
            'period' => null
          ]
        ];
      }

      $invoices[] = [
        'id' => $displayId, // Use the display ID for navigation
        'original_id' => $row['id'], // Keep the original ID for reference
        'type' => ucfirst($row['type']),
        'total' => $row['total'],
        'date' => $row['date'],
        'due_date' => $row['due_date'],
        'status' => $row['status'],
        'client_name' => $clientName,
        'client_email' => $row['email'],
        'client_id' => $row['user_id'],
        'isProforma' => $isProforma,
        'proforma_number' => $row['proforma_number'],
        'invoice_number' => $row['invoice_number'],
        'subtotal' => $row['subtotal'],
        'tax' => $row['tax'],
        'payment_method' => $row['payment_method'],
        'description' => $row['description'],
        'items' => $items
      ];
    }

    return $invoices;
  } catch (Exception $e) {
    error_log("Error searching invoices: " . $e->getMessage());
    return [];
  }
}

/**
 * Search for clients
 *
 * @param PDO $pdo Database connection
 * @param string $query Search query
 * @return array Array of matching clients
 */
function searchClients($pdo, $query) {
  try {
    // Check if users table exists
    $tablesResult = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($tablesResult->rowCount() === 0) {
      return [];
    }

    // Search for clients
    $sql = "SELECT
              id,
              first_name,
              last_name,
              company_name,
              email,
              city,
              country
            FROM users
            WHERE
              first_name LIKE :query OR
              last_name LIKE :query OR
              company_name LIKE :query OR
              email LIKE :query
            ORDER BY id DESC
            LIMIT 5";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':query', '%' . $query . '%');
    $stmt->execute();

    $clients = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
      $fullName = trim($row['first_name'] . ' ' . $row['last_name']);

      $clients[] = [
        'id' => $row['id'],
        'name' => !empty($row['company_name']) ? $row['company_name'] : $fullName,
        'email' => $row['email'],
        'location' => trim($row['city'] . ', ' . $row['country'])
      ];
    }

    return $clients;
  } catch (Exception $e) {
    error_log("Error searching clients: " . $e->getMessage());
    return [];
  }
}

/**
 * Search for dedicated servers
 *
 * @param PDO $pdo Database connection
 * @param string $query Search query
 * @return array Array of matching servers
 */
function searchServers($pdo, $query) {
  try {
    // Check if inventory_dedicated_servers table exists
    $tablesResult = $pdo->query("SHOW TABLES LIKE 'inventory_dedicated_servers'");
    if ($tablesResult->rowCount() === 0) {
      return [];
    }

    // Search for servers
    $sql = "SELECT
              ds.id,
              ds.label,
              ds.status,
              ds.main_ip,
              ds.ipmi,
              c.city,
              co.country
            FROM inventory_dedicated_servers ds
            LEFT JOIN cities c ON ds.city_id = c.id
            LEFT JOIN countries co ON c.country_id = co.id
            WHERE
              ds.label LIKE :query OR
              ds.main_ip LIKE :query OR
              ds.ipmi LIKE :query OR
              ds.mac LIKE :query OR
              ds.notes LIKE :query
            ORDER BY ds.id DESC
            LIMIT 5";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':query', '%' . $query . '%');
    $stmt->execute();

    $servers = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
      $location = '';
      if (!empty($row['city']) && !empty($row['country'])) {
        $location = $row['city'] . ', ' . $row['country'];
      }

      $servers[] = [
        'id' => $row['id'],
        'label' => $row['label'],
        'hostname' => $row['ipmi'] ?: 'N/A', // Use IPMI as hostname if available
        'status' => $row['status'],
        'ip' => $row['main_ip'] ?: $row['ipmi'] ?: 'N/A', // Use main_ip or IPMI as IP
        'location' => $location,
        'type' => 'dedicated'
      ];
    }

    return $servers;
  } catch (Exception $e) {
    error_log("Error searching servers: " . $e->getMessage());
    return [];
  }
}

/**
 * Search for switches
 *
 * @param PDO $pdo Database connection
 * @param string $query Search query
 * @return array Array of matching switches
 */
function searchSwitches($pdo, $query) {
  try {
    // Check if inventory_switches table exists
    $tablesResult = $pdo->query("SHOW TABLES LIKE 'inventory_switches'");
    if ($tablesResult->rowCount() === 0) {
      return [];
    }

    // Search for switches
    $sql = "SELECT
              s.id,
              s.label,
              s.status,
              s.switch_ip,
              c.city,
              co.country
            FROM inventory_switches s
            LEFT JOIN cities c ON s.city_id = c.id
            LEFT JOIN countries co ON c.country_id = co.id
            WHERE
              s.label LIKE :query OR
              s.switch_ip LIKE :query OR
              s.notes LIKE :query
            ORDER BY s.id DESC
            LIMIT 5";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':query', '%' . $query . '%');
    $stmt->execute();

    $switches = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
      $location = '';
      if (!empty($row['city']) && !empty($row['country'])) {
        $location = $row['city'] . ', ' . $row['country'];
      }

      $switches[] = [
        'id' => $row['id'],
        'label' => $row['label'],
        'hostname' => $row['switch_ip'] ?: 'N/A', // Use switch_ip as hostname
        'status' => $row['status'],
        'ip' => $row['switch_ip'] ?: 'N/A',
        'location' => $location,
        'type' => 'switch'
      ];
    }

    return $switches;
  } catch (Exception $e) {
    error_log("Error searching switches: " . $e->getMessage());
    return [];
  }
}

/**
 * Search for blade servers
 *
 * @param PDO $pdo Database connection
 * @param string $query Search query
 * @return array Array of matching blade servers
 */
function searchBlades($pdo, $query) {
  try {
    // Check if blade_server_inventory table exists
    $tablesResult = $pdo->query("SHOW TABLES LIKE 'blade_server_inventory'");
    if ($tablesResult->rowCount() === 0) {
      return [];
    }

    // Search for blade servers
    $sql = "SELECT
              b.id,
              b.label,
              b.status,
              b.main_ip,
              b.ipmi,
              c.city,
              co.country,
              ch.label as chassis_label
            FROM blade_server_inventory b
            LEFT JOIN inventory_chassis ch ON b.chassis_id = ch.id
            LEFT JOIN cities c ON ch.city_id = c.id
            LEFT JOIN countries co ON c.country_id = co.id
            WHERE
              b.label LIKE :query OR
              b.main_ip LIKE :query OR
              b.ipmi LIKE :query OR
              b.mac LIKE :query OR
              b.notes LIKE :query OR
              ch.label LIKE :query
            ORDER BY b.id DESC
            LIMIT 5";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':query', '%' . $query . '%');
    $stmt->execute();

    $blades = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
      $location = '';
      if (!empty($row['city']) && !empty($row['country'])) {
        $location = $row['city'] . ', ' . $row['country'];
      }

      $blades[] = [
        'id' => $row['id'],
        'label' => $row['label'],
        'hostname' => $row['ipmi'] ?: 'N/A', // Use IPMI as hostname if available
        'status' => $row['status'],
        'ip' => $row['main_ip'] ?: $row['ipmi'] ?: 'N/A', // Use main_ip or IPMI as IP
        'location' => $location,
        'chassis' => $row['chassis_label'],
        'type' => 'blade'
      ];
    }

    return $blades;
  } catch (Exception $e) {
    error_log("Error searching blade servers: " . $e->getMessage());
    return [];
  }
}

/**
 * Search for chassis
 *
 * @param PDO $pdo Database connection
 * @param string $query Search query
 * @return array Array of matching chassis
 */
function searchChassis($pdo, $query) {
  try {
    // Check if inventory_chassis table exists
    $tablesResult = $pdo->query("SHOW TABLES LIKE 'inventory_chassis'");
    if ($tablesResult->rowCount() === 0) {
      return [];
    }

    // Search for chassis
    $sql = "SELECT
              ch.id,
              ch.label,
              ch.status,
              ch.position,
              r.rack_name,
              c.city,
              co.country,
              ch.notes
            FROM inventory_chassis ch
            LEFT JOIN racks r ON ch.rack_id = r.id
            LEFT JOIN cities c ON ch.city_id = c.id
            LEFT JOIN countries co ON c.country_id = co.id
            WHERE
              ch.label LIKE :query OR
              ch.notes LIKE :query OR
              r.rack_name LIKE :query
            ORDER BY ch.id DESC
            LIMIT 5";

    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':query', '%' . $query . '%');
    $stmt->execute();

    $chassis = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
      $location = '';
      if (!empty($row['city']) && !empty($row['country'])) {
        $location = $row['city'] . ', ' . $row['country'];
      }

      $chassis[] = [
        'id' => $row['id'],
        'label' => $row['label'],
        'status' => $row['status'],
        'position' => $row['position'],
        'rack' => $row['rack_name'] ?: 'N/A',
        'location' => $location,
        'type' => 'chassis'
      ];
    }

    return $chassis;
  } catch (Exception $e) {
    error_log("Error searching chassis: " . $e->getMessage());
    return [];
  }
}
