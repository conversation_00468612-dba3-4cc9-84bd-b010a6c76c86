<?php
require_once("auth_functions.php");



// Get all devices inside a rack
if($_GET['f'] == 'get_rack_devices'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['rack_id']) || empty($data['rack_id'])) {
      throw new Exception('Rack ID is required');
    }

    $rack_id = intval($data['rack_id']);

    // Initialize results array
    $devices = [];

    // Get all dedicated servers in this rack
    $dedicatedSql = "SELECT
                      id,
                      label,
                      'dedicated' as type,
                      position,
                      size,
                      status,
                      notes,
                      cpu,
                      ram,
                      (SELECT cpu FROM dedicated_cpu WHERE id = inventory_dedicated_servers.cpu) as cpu_name,
                      (SELECT description FROM ram_configurations WHERE id = inventory_dedicated_servers.ram) as ram_description
                    FROM inventory_dedicated_servers
                    WHERE rack_id = :rack_id";
    $dedicatedSth = $pdo->prepare($dedicatedSql);
    $dedicatedSth->bindValue(':rack_id', $rack_id);
    $dedicatedSth->execute();

    $dedicatedServers = $dedicatedSth->fetchAll(PDO::FETCH_ASSOC);
    $devices = array_merge($devices, $dedicatedServers);

    // Get all switches in this rack
    $switchesSql = "SELECT
                      id,
                      label,
                      'switch' as type,
                      rack_position as position,
                      COALESCE(size_ru, 1) as size,
                      status,
                      notes,
                      (SELECT name FROM inventory_switch_model WHERE id = inventory_switches.model_id) as model
                    FROM inventory_switches
                    WHERE rack_id = :rack_id";
    $switchesSth = $pdo->prepare($switchesSql);
    $switchesSth->bindValue(':rack_id', $rack_id);
    $switchesSth->execute();

    $switches = $switchesSth->fetchAll(PDO::FETCH_ASSOC);
    $devices = array_merge($devices, $switches);

    // Get all chassis in this rack
    $chassisSql = "SELECT
                     c.id,
                     c.label,
                     'chassis' as type,
                     c.position,
                     COALESCE(m.size, 2) as size,
                     c.status,
                     c.notes,
                     m.name as model
                   FROM inventory_chassis c
                   LEFT JOIN inventory_chassis_model m ON c.model_id = m.id
                   WHERE c.rack_id = :rack_id";
    $chassisSth = $pdo->prepare($chassisSql);
    $chassisSth->bindValue(':rack_id', $rack_id);
    $chassisSth->execute();

    $chassis = $chassisSth->fetchAll(PDO::FETCH_ASSOC);
    $devices = array_merge($devices, $chassis);

    // Return the devices
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'devices' => $devices
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Get all devices not assigned to a rack
elseif($_GET['f'] == 'get_unassigned_devices'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Initialize results array
    $devices = [];

    // Get unassigned dedicated servers
    $dedicatedSql = "SELECT
                       id,
                       label,
                       'dedicated' as type,
                       COALESCE(size, 1) as size,
                       status,
                       (SELECT cpu FROM dedicated_cpu WHERE id = inventory_dedicated_servers.cpu) as model
                     FROM inventory_dedicated_servers
                     WHERE (rack_id IS NULL OR rack_id = 0)
                     AND status = 'Available'
                     LIMIT 100";
    $dedicatedSth = $pdo->prepare($dedicatedSql);
    $dedicatedSth->execute();

    $dedicatedServers = $dedicatedSth->fetchAll(PDO::FETCH_ASSOC);
    $devices = array_merge($devices, $dedicatedServers);

    // Get unassigned switches
 // Get unassigned switches
 $switchesSql = "SELECT
 id,
 label,
 'switch' as type,
 COALESCE(size_ru, 1) as size,
 status,
 (SELECT name FROM inventory_switch_model WHERE id = inventory_switches.model_id) as model
FROM inventory_switches
WHERE (rack_id IS NULL OR rack_id = 0)
LIMIT 100";
    $switchesSth = $pdo->prepare($switchesSql);
    $switchesSth->execute();

    $switches = $switchesSth->fetchAll(PDO::FETCH_ASSOC);
    $devices = array_merge($devices, $switches);

    // Get unassigned chassis - REMOVED status filter to show all unassigned chassis
    $chassisSql = "SELECT
                     c.id,
                     c.label,
                     'chassis' as type,
                     COALESCE(m.size, 2) as size,
                     c.status,
                     m.name as model
                   FROM inventory_chassis c
                   LEFT JOIN inventory_chassis_model m ON c.model_id = m.id
                   WHERE (c.rack_id IS NULL OR c.rack_id = 0)
                   LIMIT 100";
    $chassisSth = $pdo->prepare($chassisSql);
    $chassisSth->execute();

    $chassis = $chassisSth->fetchAll(PDO::FETCH_ASSOC);
    $devices = array_merge($devices, $chassis);

    // Return the devices
    header('Content-Type: application/json');
    echo json_encode($devices);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Update a rack's information
elseif($_GET['f'] == 'update_rack'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('Rack ID is required');
    }

    if (!isset($data['rack_name']) || empty($data['rack_name'])) {
      throw new Exception('Rack name is required');
    }

    $rack_id = intval($data['id']);

    // Prepare update fields
    $updates = [];
    $params = [':id' => $rack_id];

    // Process fields
    if (isset($data['rack_name'])) {
      $updates[] = "rack_name = :rack_name";
      $params[':rack_name'] = $data['rack_name'];
    }

    if (isset($data['size'])) {
      $updates[] = "size = :size";
      $params[':size'] = intval($data['size']) ?: 42; // Default to 42U if invalid
    }

    // Handle field name alignment - frontend uses country_id but DB uses country
    if (isset($data['country_id'])) {
      $updates[] = "country = :country";
      $params[':country'] = intval($data['country_id']);
    }

    // Handle field name alignment - frontend uses city_id but DB uses city
    if (isset($data['city_id'])) {
      $updates[] = "city = :city";
      $params[':city'] = intval($data['city_id']);
    }

    // If notes column exists
    if ($pdo->query("SHOW COLUMNS FROM racks LIKE 'notes'")->rowCount() > 0) {
      if (isset($data['notes'])) {
        $updates[] = "notes = :notes";
        $params[':notes'] = $data['notes'];
      }
    }

    // If there's nothing to update
    if (empty($updates)) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'No changes to update'
      ]);
      return;
    }

    // Build and execute the query
    $sql = "UPDATE racks SET " . implode(", ", $updates) . " WHERE id = :id";
    $sth = $pdo->prepare($sql);

    foreach ($params as $key => $value) {
      $sth->bindValue($key, $value);
    }

    $sth->execute();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Rack updated successfully'
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Assign a device to a rack
elseif($_GET['f'] == 'assign_device_to_rack'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['device_id']) || empty($data['device_id'])) {
      throw new Exception('Device ID is required');
    }

    if (!isset($data['device_type']) || empty($data['device_type'])) {
      throw new Exception('Device type is required');
    }

    if (!isset($data['rack_id']) || empty($data['rack_id'])) {
      throw new Exception('Rack ID is required');
    }

    if (!isset($data['position'])) {
      throw new Exception('Position is required');
    }

    $device_id = intval($data['device_id']);
    $device_type = $data['device_type'];
    $rack_id = intval($data['rack_id']);
    $position = intval($data['position']);

    // Determine which table to update based on device type
    $table = '';
    $positionColumn = '';

    switch ($device_type) {
      case 'dedicated':
        $table = 'inventory_dedicated_servers';
        $positionColumn = 'position';
        break;
      case 'switch':
        $table = 'inventory_switches';
        $positionColumn = 'rack_position';
        break;
      case 'chassis':
        $table = 'inventory_chassis';
        $positionColumn = 'position';
        break;
      default:
        throw new Exception('Unsupported device type');
    }

    // Update the device's rack assignment and position
    $sql = "UPDATE `$table` SET rack_id = :rack_id, `$positionColumn` = :position WHERE id = :id";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':rack_id', $rack_id);
    $sth->bindValue(':position', $position);
    $sth->bindValue(':id', $device_id);
    $sth->execute();

    // Check if update was successful
    if ($sth->rowCount() === 0) {
      throw new Exception("Failed to update device. Device may not exist.");
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Device assigned to rack successfully'
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Remove a device from a rack
elseif($_GET['f'] == 'remove_device_from_rack'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['device_id']) || empty($data['device_id'])) {
      throw new Exception('Device ID is required');
    }

    if (!isset($data['device_type']) || empty($data['device_type'])) {
      throw new Exception('Device type is required');
    }

    $device_id = intval($data['device_id']);
    $device_type = $data['device_type'];

    // Determine which table to update based on device type
    $table = '';
    $positionColumn = '';

    switch ($device_type) {
      case 'dedicated':
        $table = 'inventory_dedicated_servers';
        $positionColumn = 'position';
        break;
      case 'switch':
        $table = 'inventory_switches';
        $positionColumn = 'rack_position';
        break;
      case 'chassis':
        $table = 'inventory_chassis';
        $positionColumn = 'position';
        break;
      default:
        throw new Exception('Unsupported device type');
    }

    // Update the device to remove rack assignment
    $sql = "UPDATE `$table` SET rack_id = NULL, `$positionColumn` = NULL WHERE id = :id";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':id', $device_id);
    $sth->execute();

    // Check if update was successful
    if ($sth->rowCount() === 0) {
      throw new Exception("Failed to update device. Device may not exist.");
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Device removed from rack successfully'
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Update a device's position in a rack
elseif($_GET['f'] == 'update_device_position'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['device_id']) || empty($data['device_id'])) {
      throw new Exception('Device ID is required');
    }

    if (!isset($data['device_type']) || empty($data['device_type'])) {
      throw new Exception('Device type is required');
    }

    if (!isset($data['position'])) {
      throw new Exception('Position is required');
    }

    $device_id = intval($data['device_id']);
    $device_type = $data['device_type'];
    $position = intval($data['position']);

    // Determine which table to update based on device type
    $table = '';
    $positionColumn = '';

    switch ($device_type) {
      case 'dedicated':
        $table = 'inventory_dedicated_servers';
        $positionColumn = 'position';
        break;
      case 'switch':
        $table = 'inventory_switches';
        $positionColumn = 'rack_position';
        break;
      case 'chassis':
        $table = 'inventory_chassis';
        $positionColumn = 'position';
        break;
      default:
        throw new Exception('Unsupported device type');
    }

    // Update the device's position
    $sql = "UPDATE `$table` SET `$positionColumn` = :position WHERE id = :id";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':position', $position);
    $sth->bindValue(':id', $device_id);
    $sth->execute();

    // Check if update was successful
    if ($sth->rowCount() === 0) {
      throw new Exception("Failed to update device position. Device may not exist.");
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Device position updated successfully'
    ]);

  } catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}


elseif($_GET['f'] == 'add_country'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['country']) || empty($data['country'])) {
      throw new Exception('Country name is required');
    }

    if (!isset($data['flag_code']) || empty($data['flag_code'])) {
      throw new Exception('Flag code is required');
    }

    // Insert the country
    $sql = "INSERT INTO countries (country, flag_code) VALUES (:country, :flag_code)";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':country', $data['country']);
    $sth->bindValue(':flag_code', $data['flag_code']);
    $sth->execute();

    // Get the new ID
    $country_id = $pdo->lastInsertId();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $country_id,
      'message' => 'Country added successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_country: " . $e->getMessage());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Add a new city
elseif($_GET['f'] == 'add_city'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['city']) || empty($data['city'])) {
      throw new Exception('City name is required');
    }

    if (!isset($data['country_id']) || empty($data['country_id'])) {
      throw new Exception('Country ID is required');
    }

    // Optional fields with defaults
    $datacenter = isset($data['datacenter']) ? $data['datacenter'] : '';
    $address = isset($data['address']) ? $data['address'] : '';
    $cod_postal = isset($data['cod_postal']) ? $data['cod_postal'] : '';

    // Insert the city
    $sql = "INSERT INTO cities (city, country_id, datacenter, address, cod_postal)
            VALUES (:city, :country_id, :datacenter, :address, :cod_postal)";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':city', $data['city']);
    $sth->bindValue(':country_id', $data['country_id']);
    $sth->bindValue(':datacenter', $datacenter);
    $sth->bindValue(':address', $address);
    $sth->bindValue(':cod_postal', $cod_postal);
    $sth->execute();

    // Get the new ID
    $city_id = $pdo->lastInsertId();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $city_id,
      'message' => 'City added successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_city: " . $e->getMessage());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'update_city_datacenter'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['city_id']) || empty($data['city_id'])) {
      throw new Exception('City ID is required');
    }

    if (!isset($data['datacenter'])) {
      throw new Exception('Datacenter name is required');
    }

    // Update the city's datacenter field
    $sql = "UPDATE cities SET datacenter = :datacenter WHERE id = :city_id";
    $sth = $pdo->prepare($sql);
    $sth->bindValue(':datacenter', $data['datacenter']);
    $sth->bindValue(':city_id', $data['city_id']);
    $sth->execute();

    // Check if any rows were affected
    if ($sth->rowCount() === 0) {
      throw new Exception('City not found');
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Datacenter updated successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in update_city_datacenter: " . $e->getMessage());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Add a new rack
elseif($_GET['f'] == 'add_rack'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['rack_name']) || empty($data['rack_name'])) {
      throw new Exception('Rack name is required');
    }

    if (!isset($data['country']) || empty($data['country'])) {
      throw new Exception('Country is required');
    }

    if (!isset($data['city']) || empty($data['city'])) {
      throw new Exception('City is required');
    }

    // Default size if not provided
    $size = isset($data['size']) && !empty($data['size']) ? intval($data['size']) : 42;

    // Check if the table has a notes column
    $hasNotesColumn = $pdo->query("SHOW COLUMNS FROM racks LIKE 'notes'")->rowCount() > 0;

    // Build the SQL query based on whether notes column exists
    if ($hasNotesColumn) {
      $sql = "INSERT INTO racks (rack_name, size, country, city, notes)
              VALUES (:rack_name, :size, :country, :city, :notes)";
    } else {
      $sql = "INSERT INTO racks (rack_name, size, country, city)
              VALUES (:rack_name, :size, :country, :city)";
    }

    $sth = $pdo->prepare($sql);
    $sth->bindValue(':rack_name', $data['rack_name']);
    $sth->bindValue(':size', $size);
    $sth->bindValue(':country', $data['country']);
    $sth->bindValue(':city', $data['city']);

    // Bind notes parameter if the column exists
    if ($hasNotesColumn) {
      $sth->bindValue(':notes', isset($data['notes']) ? $data['notes'] : '');
    }

    $sth->execute();

    // Get the new ID
    $rack_id = $pdo->lastInsertId();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'id' => $rack_id,
      'message' => 'Rack added successfully'
    ]);

  } catch (Exception $e) {
    error_log("Error in add_rack: " . $e->getMessage());

    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Delete a country - with validation to prevent deleting countries with cities
elseif($_GET['f'] == 'delete_country'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('Country ID is required');
    }

    $country_id = intval($data['id']);

    // Start transaction
    $pdo->beginTransaction();

    // Check if this country has any cities
    $checkCitiesSql = "SELECT COUNT(*) FROM cities WHERE country_id = :country_id";
    $checkCitiesSth = $pdo->prepare($checkCitiesSql);
    $checkCitiesSth->bindValue(':country_id', $country_id);
    $checkCitiesSth->execute();

    $citiesCount = $checkCitiesSth->fetchColumn();

    if ($citiesCount > 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Cannot delete country - it has {$citiesCount} cities. Delete all cities first.");
    }

    // Delete the country
    $deleteSql = "DELETE FROM countries WHERE id = :id";
    $deleteSth = $pdo->prepare($deleteSql);
    $deleteSth->bindValue(':id', $country_id);
    $deleteSth->execute();

    // Check if deletion was successful
    if ($deleteSth->rowCount() === 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Country with ID {$country_id} not found.");
    }

    // Commit transaction
    $pdo->commit();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Country deleted successfully'
    ]);

  } catch (Exception $e) {
    // Rollback if transaction is active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    // Return error
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Delete a city - with validation to prevent deleting cities with racks
elseif($_GET['f'] == 'delete_city'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('City ID is required');
    }

    $city_id = intval($data['id']);

    // Start transaction
    $pdo->beginTransaction();

    // Check if this city has any racks
    $checkRacksSql = "SELECT COUNT(*) FROM racks WHERE city = :city_id";
    $checkRacksSth = $pdo->prepare($checkRacksSql);
    $checkRacksSth->bindValue(':city_id', $city_id);
    $checkRacksSth->execute();

    $racksCount = $checkRacksSth->fetchColumn();

    if ($racksCount > 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Cannot delete city - it has {$racksCount} racks. Delete all racks first.");
    }

    // Delete the city
    $deleteSql = "DELETE FROM cities WHERE id = :id";
    $deleteSth = $pdo->prepare($deleteSql);
    $deleteSth->bindValue(':id', $city_id);
    $deleteSth->execute();

    // Check if deletion was successful
    if ($deleteSth->rowCount() === 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("City with ID {$city_id} not found.");
    }

    // Commit transaction
    $pdo->commit();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'City deleted successfully'
    ]);

  } catch (Exception $e) {
    // Rollback if transaction is active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    // Return error
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Delete a rack - with validation to prevent deleting racks with devices
elseif($_GET['f'] == 'delete_rack'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('Rack ID is required');
    }

    $rack_id = intval($data['id']);

    // Start transaction
    $pdo->beginTransaction();

    // Check if this rack has any dedicated servers
    $checkDedicatedSql = "SELECT COUNT(*) FROM inventory_dedicated_servers WHERE rack_id = :rack_id";
    $checkDedicatedSth = $pdo->prepare($checkDedicatedSql);
    $checkDedicatedSth->bindValue(':rack_id', $rack_id);
    $checkDedicatedSth->execute();

    $dedicatedCount = $checkDedicatedSth->fetchColumn();

    if ($dedicatedCount > 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Cannot delete rack - it has {$dedicatedCount} dedicated servers. Remove all servers first.");
    }

    // Check if this rack has any chassis
    $checkChassisSql = "SELECT COUNT(*) FROM inventory_chassis WHERE rack_id = :rack_id";
    $checkChassisSth = $pdo->prepare($checkChassisSql);
    $checkChassisSth->bindValue(':rack_id', $rack_id);
    $checkChassisSth->execute();

    $chassisCount = $checkChassisSth->fetchColumn();

    if ($chassisCount > 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Cannot delete rack - it has {$chassisCount} chassis. Remove all chassis first.");
    }

    // Check if this rack has any switches
    $checkSwitchesSql = "SELECT COUNT(*) FROM inventory_switches WHERE rack_id = :rack_id";
    $checkSwitchesSth = $pdo->prepare($checkSwitchesSql);
    $checkSwitchesSth->bindValue(':rack_id', $rack_id);
    $checkSwitchesSth->execute();

    $switchesCount = $checkSwitchesSth->fetchColumn();

    if ($switchesCount > 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Cannot delete rack - it has {$switchesCount} switches. Remove all switches first.");
    }

    // Delete the rack
    $deleteSql = "DELETE FROM racks WHERE id = :id";
    $deleteSth = $pdo->prepare($deleteSql);
    $deleteSth->bindValue(':id', $rack_id);
    $deleteSth->execute();

    // Check if deletion was successful
    if ($deleteSth->rowCount() === 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Rack with ID {$rack_id} not found.");
    }

    // Commit transaction
    $pdo->commit();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Rack deleted successfully'
    ]);

  } catch (Exception $e) {
    // Rollback if transaction is active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    // Return error
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Update a country
elseif($_GET['f'] == 'update_country'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('Country ID is required');
    }

    if (!isset($data['country']) || empty($data['country'])) {
      throw new Exception('Country name is required');
    }

    $country_id = intval($data['id']);
    $country_name = trim($data['country']);

    // Check for duplicate country name
    $checkDuplicateSql = "SELECT COUNT(*) FROM countries WHERE country = :country AND id != :id";
    $checkDuplicateSth = $pdo->prepare($checkDuplicateSql);
    $checkDuplicateSth->bindValue(':country', $country_name);
    $checkDuplicateSth->bindValue(':id', $country_id);
    $checkDuplicateSth->execute();

    if ($checkDuplicateSth->fetchColumn() > 0) {
      throw new Exception("A country with this name already exists");
    }

    // Update the country
    $updateSql = "UPDATE countries SET country = :country WHERE id = :id";
    $updateSth = $pdo->prepare($updateSql);
    $updateSth->bindValue(':country', $country_name);
    $updateSth->bindValue(':id', $country_id);
    $updateSth->execute();

    // Check if update was successful
    if ($updateSth->rowCount() === 0) {
      throw new Exception("Country with ID {$country_id} not found or no changes were made");
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Country updated successfully'
    ]);

  } catch (Exception $e) {
    // Return error
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

// Update a city
elseif($_GET['f'] == 'update_city'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
      throw new Exception('City ID is required');
    }

    $city_id = intval($data['id']);

    // Build update fields
    $updateFields = [];
    $params = [':id' => $city_id];

    // Check for city name update
    if (isset($data['city'])) {
      $updateFields[] = "city = :city";
      $params[':city'] = trim($data['city']);

      // Validate city name
      if (empty($params[':city'])) {
        throw new Exception('City name cannot be empty');
      }
    }

    // Check for datacenter update
    if (isset($data['datacenter'])) {
      $updateFields[] = "datacenter = :datacenter";
      $params[':datacenter'] = trim($data['datacenter']);
    }

    // Check for country update
    if (isset($data['country_id'])) {
      $updateFields[] = "country_id = :country_id";
      $params[':country_id'] = intval($data['country_id']);

      // Validate country exists
      $checkCountrySql = "SELECT COUNT(*) FROM countries WHERE id = :country_id";
      $checkCountrySth = $pdo->prepare($checkCountrySql);
      $checkCountrySth->bindValue(':country_id', $params[':country_id']);
      $checkCountrySth->execute();

      if ($checkCountrySth->fetchColumn() === 0) {
        throw new Exception("Country with ID {$params[':country_id']} not found");
      }
    }

    // If no update fields, no need to proceed
    if (empty($updateFields)) {
      header('Content-Type: application/json');
      echo json_encode([
        'success' => true,
        'message' => 'No changes were made'
      ]);
      return;
    }

    // Build and execute the update query
    $updateSql = "UPDATE cities SET " . implode(", ", $updateFields) . " WHERE id = :id";
    $updateSth = $pdo->prepare($updateSql);

    foreach ($params as $key => $value) {
      $updateSth->bindValue($key, $value);
    }

    $updateSth->execute();

    // Check if update was successful
    if ($updateSth->rowCount() === 0) {
      throw new Exception("City with ID {$city_id} not found or no changes were made");
    }

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'City updated successfully'
    ]);

  } catch (Exception $e) {
    // Return error
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

elseif($_GET['f'] == 'delete_datacenter'){
  try {
    // Authenticate admin
    $admin_id = auth_admin();

    // Get data
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['city_id']) || empty($data['city_id'])) {
      throw new Exception('City ID is required');
    }

    $city_id = intval($data['city_id']);

    // Start transaction
    $pdo->beginTransaction();

    // Check if this city has any racks
    $checkRacksSql = "SELECT COUNT(*) FROM racks WHERE city = :city_id";
    $checkRacksSth = $pdo->prepare($checkRacksSql);
    $checkRacksSth->bindValue(':city_id', $city_id);
    $checkRacksSth->execute();

    $racksCount = $checkRacksSth->fetchColumn();

    if ($racksCount > 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("Cannot delete datacenter - it has {$racksCount} racks. Delete all racks first.");
    }

    // Get the current datacenter name for logging
    $getDcSql = "SELECT datacenter FROM cities WHERE id = :id";
    $getDcSth = $pdo->prepare($getDcSql);
    $getDcSth->bindValue(':id', $city_id);
    $getDcSth->execute();
    $datacenterName = $getDcSth->fetchColumn();

    if (empty($datacenterName)) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("City with ID {$city_id} doesn't have a datacenter.");
    }

    // Update the city to clear datacenter field
    $updateSql = "UPDATE cities SET datacenter = :empty_dc WHERE id = :id";
    $updateSth = $pdo->prepare($updateSql);
    $updateSth->bindValue(':empty_dc', '');
    $updateSth->bindValue(':id', $city_id);
    $updateSth->execute();

    // Check if update was successful
    if ($updateSth->rowCount() === 0) {
      // Rollback and return error
      $pdo->rollBack();
      throw new Exception("City with ID {$city_id} not found or datacenter could not be cleared.");
    }

    // Commit transaction
    $pdo->commit();

    // Return success
    header('Content-Type: application/json');
    echo json_encode([
      'success' => true,
      'message' => 'Datacenter deleted successfully',
      'datacenter_name' => $datacenterName
    ]);

  } catch (Exception $e) {
    // Rollback if transaction is active
    if ($pdo->inTransaction()) {
      $pdo->rollBack();
    }

    // Return error
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
      'success' => false,
      'error' => $e->getMessage()
    ]);
  }
}

?>