#!/bin/bash

# Directory to search (default is current directory)
SEARCH_DIR="."

# Set to false by default for safety (dry run mode)
PERFORM_CHANGES=false

# Function to display usage information
function show_usage {
  echo "Usage: $0 [options]"
  echo "Options:"
  echo "  -d, --directory DIR   Directory to search (default: current directory)"
  echo "  -a, --apply           Actually apply changes (default: dry run)"
  echo "  -h, --help            Show this help message"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    -d|--directory)
      SEARCH_DIR="$2"
      shift 2
      ;;
    -a|--apply)
      PERFORM_CHANGES=true
      shift
      ;;
    -h|--help)
      show_usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      show_usage
      exit 1
      ;;
  esac
done

echo "Searching for files in $SEARCH_DIR..."

# Find files containing API_URL with single quotes
FILES=$(grep -l "{API_URL}/api_.*\.php?f=.*'," --include="*.js" --include="*.jsx" --include="*.ts" --include="*.tsx" -r "$SEARCH_DIR" 2>/dev/null)

if [ -z "$FILES" ]; then
  echo "No matching files found."
  exit 0
fi

# Count for statistics
FILE_COUNT=0
REPLACEMENT_COUNT=0

# Process each file
for file in $FILES; do
  if [ ! -f "$file" ]; then
    continue
  fi
  
  # Count occurrences for reporting
  OCCURRENCES=$(grep -c "{API_URL}/api_.*\.php?f=.*'," "$file")
  
  if [ $OCCURRENCES -gt 0 ]; then
    FILE_COUNT=$((FILE_COUNT+1))
    REPLACEMENT_COUNT=$((REPLACEMENT_COUNT+OCCURRENCES))
    
    echo "Processing $file... ($OCCURRENCES occurrences)"
    
    if [ "$PERFORM_CHANGES" = true ]; then
      # Create backup
      cp "$file" "${file}.bak"
      echo "  Created backup: ${file}.bak"
      
      # Make the replacement - find API_URL patterns with single quotes and replace with backticks
      # This uses perl regex to handle the complex pattern more reliably than sed
      perl -i -pe 's/({API_URL}\/api_[^,]*?)'\''(,)/\1`\2/g' "$file"
      echo "  Applied changes to $file"
    else
      # Show what would be changed in dry run mode
      echo "  Would replace in $file (dry run):"
      grep -n "{API_URL}/api_.*\.php?f=.*'," "$file" | head -3 | sed 's/^/    /'
      if [ $OCCURRENCES -gt 3 ]; then
        echo "    ... and $((OCCURRENCES-3)) more occurrences"
      fi
    fi
  fi
done

echo ""
if [ "$PERFORM_CHANGES" = true ]; then
  echo "Changes applied to $FILE_COUNT files with a total of $REPLACEMENT_COUNT replacements."
  echo "Backup files were created with .bak extension."
else
  echo "Dry run completed. $FILE_COUNT files would be modified with $REPLACEMENT_COUNT replacements."
  echo "Run with --apply option to actually make changes."
fi
