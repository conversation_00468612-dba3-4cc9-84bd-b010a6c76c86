<?php
/**
 * Test script for PXE configuration generation
 * This script tests the generation of PXE configuration files for server xr-bl1s4
 */

require_once 'mysql.php';
require_once 'pxe_api_integration.php';

// Test configuration for server xr-bl1s4
$test_server_data = [
    'id' => 'test-123',
    'mac' => '00:8C:FA:FB:DB:18',
    'label' => 'xr-bl41s4.zetservers.com'
];

$test_network_config = [
    'hostname' => 'xr-bl41s4.zetservers.com',
    'ip_address' => '************',
    'subnet_mask' => '***************',
    'gateway' => '************',
    'dns_primary' => '*******',
    'dns_secondary' => '*******'
];

$test_os_template = 'ubuntu-22.04';
$test_custom_password = 'SecurePassword123!';

echo "Testing PXE configuration generation for server xr-bl1s4\n";
echo "========================================================\n\n";

try {
    // Create PXE manager instance
    $pxe_manager = new PXENetworkManager($pdo);
    
    echo "Server data:\n";
    echo "- MAC: {$test_server_data['mac']}\n";
    echo "- Hostname: {$test_network_config['hostname']}\n";
    echo "- IP: {$test_network_config['ip_address']}\n";
    echo "- Subnet: {$test_network_config['subnet_mask']}\n";
    echo "- Gateway: {$test_network_config['gateway']}\n";
    echo "- DNS: {$test_network_config['dns_primary']}\n";
    echo "- OS Template: $test_os_template\n\n";
    
    // Test the complete PXE setup
    $result = $pxe_manager->setupCompleteReinstall(
        $test_server_data['id'],
        $test_server_data,
        $test_network_config,
        $test_os_template,
        $test_custom_password
    );
    
    if ($result['success']) {
        echo "✅ PXE setup completed successfully!\n\n";
        echo "Generated files:\n";
        foreach ($result['details']['files'] as $type => $path) {
            echo "- $type: $path\n";
        }
        echo "\n";
        
        // Display generated content
        $servername = $pxe_manager->extractServerName($test_network_config['hostname']);
        $server_dir = "/var/www/html/autoinstall/servers/$servername";
        
        echo "Generated content preview:\n";
        echo "==========================\n\n";
        
        // Show boot.ipxe content
        if (file_exists("$server_dir/boot.ipxe")) {
            echo "📄 boot.ipxe:\n";
            echo "-------------\n";
            echo file_get_contents("$server_dir/boot.ipxe");
            echo "\n";
        }
        
        // Show meta-data content
        if (file_exists("$server_dir/meta-data")) {
            echo "📄 meta-data:\n";
            echo "-------------\n";
            echo file_get_contents("$server_dir/meta-data");
            echo "\n";
        }
        
        // Show user-data content (first 20 lines)
        if (file_exists("$server_dir/user-data")) {
            echo "📄 user-data (first 20 lines):\n";
            echo "--------------------------------\n";
            $user_data_lines = explode("\n", file_get_contents("$server_dir/user-data"));
            for ($i = 0; $i < min(20, count($user_data_lines)); $i++) {
                echo $user_data_lines[$i] . "\n";
            }
            if (count($user_data_lines) > 20) {
                echo "... (" . (count($user_data_lines) - 20) . " more lines)\n";
            }
            echo "\n";
        }
        
        // Check DHCP configuration
        $dhcp_file = '/etc/dhcp/dhcpd.conf';
        if (file_exists($dhcp_file)) {
            echo "📄 DHCP configuration update:\n";
            echo "-----------------------------\n";
            $dhcp_content = file_get_contents($dhcp_file);
            
            // Look for the server entry
            if (strpos($dhcp_content, $servername) !== false) {
                echo "✅ DHCP entry for $servername found in configuration\n";
                
                // Extract the host block for this server
                $pattern = '/host\s+h-[^{]*\{[^}]*filename\s+"[^"]*\/servers\/' . preg_quote($servername, '/') . '\/[^"]*"[^}]*\}/s';
                if (preg_match($pattern, $dhcp_content, $matches)) {
                    echo "DHCP entry content:\n";
                    echo $matches[0] . "\n";
                }
            } else {
                echo "❌ DHCP entry for $servername not found in configuration\n";
            }
        } else {
            echo "⚠️  DHCP configuration file not found at $dhcp_file\n";
        }
        
    } else {
        echo "❌ PXE setup failed: " . $result['message'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during test: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nTest completed.\n";
?> 