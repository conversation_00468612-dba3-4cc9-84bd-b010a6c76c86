<?php
// Include database connection
require_once 'db_connect.php';

// Test the searchInvoices function from api_admin_search.php
require_once 'api_admin_search.php';

// Test search query
$query = 'test'; // Replace with your test query

// Call the searchInvoices function
$results = searchInvoices($pdo, $query);

// Display the results
echo "Search results for query: '$query'\n\n";
echo "Found " . count($results) . " invoices\n\n";

// Display each result
foreach ($results as $index => $invoice) {
    echo "Result #" . ($index + 1) . ":\n";
    echo "ID: " . $invoice['id'] . "\n";
    echo "Original ID: " . $invoice['original_id'] . "\n";
    echo "Type: " . $invoice['type'] . "\n";
    echo "Is Proforma: " . ($invoice['isProforma'] ? 'Yes' : 'No') . "\n";
    echo "Proforma Number: " . ($invoice['proforma_number'] ?? 'N/A') . "\n";
    echo "Invoice Number: " . ($invoice['invoice_number'] ?? 'N/A') . "\n";
    echo "Total: " . $invoice['total'] . "\n";
    echo "Status: " . $invoice['status'] . "\n";
    echo "Client: " . $invoice['client_name'] . "\n";
    echo "Email: " . $invoice['client_email'] . "\n";
    echo "\n";
}

echo "Test completed.\n";
