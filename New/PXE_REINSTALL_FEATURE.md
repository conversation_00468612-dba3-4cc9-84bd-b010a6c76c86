# PXE Reinstall Feature

This document describes the PXE (Preboot eXecution Environment) reinstall functionality that has been integrated into the server management system.

## Overview

The PXE reinstall feature allows administrators to remotely reinstall servers with fresh operating systems using network boot. This is particularly useful for:
- Deploying new OS installations without physical access
- Recovering from system failures
- Standardizing server configurations
- Automated server provisioning

## Components

### 1. Frontend Components

#### PXEReinstallModal.js
- React modal component for managing the reinstall process
- Multi-step wizard interface:
  - **Step 1**: Operating system selection and custom password option
  - **Step 2**: Network configuration setup
  - **Step 3**: Final confirmation with detailed warning
  - **Step 4**: Progress display and completion status

#### PXENetworkConfigModal.js
- Network infrastructure configuration modal
- Multi-step network setup wizard:
  - **Step 1**: IP address, subnet, gateway, and DNS configuration
  - **Step 2**: DHCP server settings and PXE boot options
  - **Step 3**: PXE server configuration and boot menu setup
  - **Step 4**: Configuration completion confirmation
- Fetches available operating systems from the API
- Validates server IPMI credentials before proceeding

#### ServerView.js Integration
- PXE Reinstall button added to the server details page
- Located in the power management section
- Disabled when IPMI credentials are not available
- Opens the PXE reinstall modal when clicked

### 2. Backend API

#### API Endpoint: `api_admin_inventory.php?f=pxe_reinstall`
- Handles PXE reinstall requests
- Validates server and OS parameters
- Configures server for PXE boot via IPMI/iDRAC
- Powers cycles the server to initiate network boot
- Logs all reinstall activities for audit purposes

#### API Endpoint: `api_admin_inventory.php?f=setup_pxe_dhcp`
- Configures DHCP server reservations for PXE boot
- Creates MAC address to IP address mappings
- Sets up PXE boot options (TFTP server, boot filename)
- Validates network configuration parameters
- Logs DHCP configuration for audit purposes

#### API Endpoint: `api_admin_inventory.php?f=setup_pxe_server`
- Configures PXE server boot menus and files
- Generates kickstart/preseed files for automated installation
- Creates MAC-specific boot menu entries
- Sets up installation parameters and network configuration
- Manages PXE boot file organization

### 3. Database Schema

#### server_reinstall_log Table
- Tracks all PXE reinstall operations
- Stores server details, OS information, and admin who initiated
- Includes status tracking and error logging
- Provides audit trail for compliance

## How It Works

### Process Flow

1. **Initiation**
   - Admin opens server details page
   - Clicks "PXE Reinstall" button
   - Modal opens with OS selection

2. **Configuration**
   - Admin selects desired operating system
   - Optionally sets custom root password
   - Reviews confirmation details

3. **Execution**
   - API validates IPMI credentials
   - Configures server boot order to PXE first
   - Power cycles the server
   - Server boots from network

4. **Installation**
   - Server downloads OS image via PXE
   - Automated installation begins
   - Server reboots when complete

### IPMI Commands Used

The system uses RACADM commands via SSH to configure the server:

```bash
# Set boot device to PXE
racadm config -g cfgServerInfo -o cfgServerBootOnce 1
racadm config -g cfgServerInfo -o cfgServerFirstBootDevice PXE

# Alternative for newer iDRAC versions
racadm set iDRAC.ServerBoot.FirstBootDevice PXE

# Power cycle the server
racadm serveraction powercycle
```

## Requirements

### Server Requirements
- Dell servers with iDRAC (integrated Dell Remote Access Controller)
- IPMI enabled and configured
- Network boot (PXE) capability
- Access to PXE server on the network

### Network Requirements
- **DHCP Server**: Must support PXE boot options and MAC address reservations
- **TFTP Server**: Serves initial boot files (pxelinux.0, boot menus)
- **PXE Server**: Hosts OS images and installation files
- **HTTP/FTP Server**: Provides kickstart/preseed files and packages
- **DNS Server**: Optional but recommended for hostname resolution
- **Network Connectivity**: Servers must be able to reach all network services during boot

### Software Requirements
- SSH access to iDRAC
- `sshpass` utility for automated SSH
- Valid IPMI credentials stored in server database

## Security Considerations

### Access Control
- Only authenticated admin users can initiate reinstalls
- All reinstall actions are logged with admin user ID
- IPMI passwords are handled securely (temporary files, proper cleanup)

### Audit Trail
- Complete logging of all reinstall operations
- Tracks server ID, OS selected, timestamp, and initiating admin
- Error logging for failed operations
- Status tracking throughout the process

### Network Security
- IPMI/iDRAC access should be on isolated management network
- PXE boot should be restricted to authorized network segments
- OS images should be verified for integrity

## Configuration

### 1. Database Setup
Run the SQL schema to create required tables:
```sql
mysql -u username -p database_name < pxe_reinstall_schema.sql
```

### 2. Operating Systems
Add operating systems to the `dedicated_os` table:
```sql
INSERT INTO dedicated_os (os_name, os_template, logo_url) VALUES 
('Ubuntu 22.04 LTS', 'ubuntu-22.04', 'ubuntu.png'),
('CentOS 8', 'centos-8', 'centos.png'),
('Windows Server 2022', 'windows-2022', 'windows.png');
```

### 3. PXE Server Configuration
Configure your PXE server with the corresponding OS templates and installation files.

## Usage

### From Server Details Page

1. Navigate to any server details page
2. Ensure server has IPMI credentials configured
3. Click "PXE Reinstall" in the power management section
4. Select desired operating system
5. Optionally set custom root password
6. Confirm the reinstall operation
7. Monitor progress through IPMI console if needed

### Status Tracking

- Server status changes to "Installing" during reinstall
- Check `server_reinstall_log` table for detailed logs
- Monitor server through IPMI console for real-time progress

## Troubleshooting

### Common Issues

1. **"IPMI credentials required"**
   - Ensure server has `ipmi` and `ipmi_root_pass` fields populated
   - Verify IPMI connectivity and credentials

2. **"Failed to configure PXE boot"**
   - Check iDRAC version compatibility
   - Verify SSH access to iDRAC
   - Try alternative RACADM commands

3. **Server doesn't boot from network**
   - Verify PXE server configuration
   - Check network connectivity
   - Ensure DHCP provides PXE boot options

4. **Installation fails**
   - Verify OS image integrity on PXE server
   - Check installation scripts and kickstart files
   - Monitor installation logs

### Log Files

- Server reinstall operations: `server_reinstall_log` table
- PHP error logs: Check server error logs for API failures
- IPMI command output: Logged in PHP error logs

## Future Enhancements

### Potential Improvements

1. **Progress Monitoring**
   - Real-time installation progress tracking
   - Integration with installation status APIs
   - Estimated completion times

2. **Custom Installation Scripts**
   - Support for custom kickstart/preseed files
   - Post-installation configuration scripts
   - Software package pre-selection

3. **Batch Operations**
   - Multi-server reinstall capabilities
   - Scheduled reinstall operations
   - Bulk OS deployment

4. **Enhanced Logging**
   - Installation completion detection
   - Performance metrics tracking
   - Success/failure notifications

## Support

For issues or questions regarding the PXE reinstall feature:

1. Check the troubleshooting section above
2. Review server and API logs
3. Verify PXE server configuration
4. Contact system administrators for network-related issues

---

**Note**: Always test PXE reinstall functionality in a development environment before using in production. Server reinstallation is a destructive operation that will erase all data on the target server. 