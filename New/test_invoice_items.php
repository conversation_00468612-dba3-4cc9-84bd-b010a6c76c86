<?php
// Include database connection
require_once 'db_connect.php';

// Include the search function
require_once 'api_admin_search.php';

// Test search query
$query = isset($argv[1]) ? $argv[1] : 'test';

// Call the searchInvoices function
$results = searchInvoices($pdo, $query);

// Display the results
echo "Search results for query: '$query'\n\n";
echo "Found " . count($results) . " invoices\n\n";

// Display each result
foreach ($results as $index => $invoice) {
    echo "Result #" . ($index + 1) . ":\n";
    echo "Display ID: " . $invoice['id'] . "\n";
    echo "Original ID: " . $invoice['original_id'] . "\n";
    echo "Type: " . $invoice['type'] . "\n";
    echo "Is Proforma: " . ($invoice['isProforma'] ? 'Yes' : 'No') . "\n";
    echo "Proforma Number: " . ($invoice['proforma_number'] ?? 'N/A') . "\n";
    echo "Invoice Number: " . ($invoice['invoice_number'] ?? 'N/A') . "\n";
    echo "Total: " . $invoice['total'] . "\n";
    echo "Status: " . $invoice['status'] . "\n";
    echo "Client: " . $invoice['client_name'] . "\n";
    echo "Email: " . $invoice['client_email'] . "\n";
    echo "Description: " . ($invoice['description'] ?? 'N/A') . "\n";
    
    // Display invoice items
    echo "Items (" . count($invoice['items']) . "):\n";
    foreach ($invoice['items'] as $itemIndex => $item) {
        echo "  Item #" . ($itemIndex + 1) . ":\n";
        echo "    Description: " . $item['description'] . "\n";
        echo "    Quantity: " . $item['quantity'] . "\n";
        echo "    Unit Price: " . $item['unitPrice'] . "\n";
        echo "    Total: " . $item['total'] . "\n";
        if (isset($item['period']) && !empty($item['period'])) {
            echo "    Period: " . $item['period'] . "\n";
        }
        echo "\n";
    }
    
    echo "\n";
}

echo "Test completed.\n";
