<?php
/**
 * Debug script to simulate the exact API call from ServerView.js
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

echo "Debug: Simulating actual PXE reinstall API call\n";
echo "===============================================\n\n";

// Simulate the exact API call that ServerView.js makes
$test_request_data = [
    'token' => 'test_token', // This will fail auth, but we can see where it fails
    'server_id' => 'test-123',
    'server_type' => 'dedicated',
    'os_id' => '1',
    'ipmi_address' => '*************',
    'ipmi_username' => 'root',
    'ipmi_password' => 'password123',
    'network_config' => [
        'hostname' => 'xr-bl41s4.zetservers.com',
        'ip_address' => '************',
        'subnet_mask' => '***************',
        'gateway' => '************',
        'dns_primary' => '*******'
    ],
    'mac_address' => '00:8C:FA:FB:DB:18'
];

// Set up the environment to simulate a POST request
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['CONTENT_TYPE'] = 'application/json';
$_GET['f'] = 'pxe_reinstall';

// Capture the JSON input
$json_input = json_encode($test_request_data);

echo "Simulating API call with data:\n";
echo json_encode($test_request_data, JSON_PRETTY_PRINT) . "\n\n";

// Temporarily replace php://input for our test
file_put_contents('php://temp/maxmemory:1048576', $json_input);

try {
    // Start output buffering to catch the API response
    ob_start();
    
    // Capture any output
    echo "Starting API inclusion...\n";
    
    // Create a temporary input stream
    $temp_input = fopen('data://text/plain,' . $json_input, 'r');
    
    // Mock the php://input
    stream_wrapper_unregister('php');
    stream_wrapper_register('php', 'MockPhpInputStream');
    MockPhpInputStream::$data = $json_input;
    
    // Include the API file
    include 'api_admin_inventory.php';
    
    $api_output = ob_get_contents();
    ob_end_clean();
    
    echo "API Output:\n";
    echo $api_output . "\n";
    
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Exception caught: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    ob_end_clean();
    echo "❌ Fatal error caught: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Throwable $e) {
    ob_end_clean();
    echo "❌ Throwable caught: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Restore php stream wrapper
stream_wrapper_restore('php');

echo "\nDebug completed.\n";

/**
 * Mock class to simulate php://input
 */
class MockPhpInputStream {
    public static $data = '';
    private $position = 0;
    
    public function stream_open($path, $mode, $options, &$opened_path) {
        $this->position = 0;
        return true;
    }
    
    public function stream_read($count) {
        $ret = substr(self::$data, $this->position, $count);
        $this->position += strlen($ret);
        return $ret;
    }
    
    public function stream_eof() {
        return $this->position >= strlen(self::$data);
    }
    
    public function stream_tell() {
        return $this->position;
    }
    
    public function stream_seek($offset, $whence = SEEK_SET) {
        switch ($whence) {
            case SEEK_SET:
                $this->position = $offset;
                break;
            case SEEK_CUR:
                $this->position += $offset;
                break;
            case SEEK_END:
                $this->position = strlen(self::$data) + $offset;
                break;
        }
        return $this->position >= 0;
    }
    
    public function stream_stat() {
        return [];
    }
}
?> 