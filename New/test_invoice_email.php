<?php
/**
 * Test script for invoice email sending
 */

// Include necessary files
require_once("mysql.php");
require_once("invoice_email_functions.php");

// Get the latest invoice ID from the database
try {
    $stmt = $pdo->query("SELECT id, user_id, proforma_number FROM invoices ORDER BY id DESC LIMIT 1");
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "No invoices found in the database.\n";
        exit;
    }
    
    $invoice_id = $invoice['id'];
    $user_id = $invoice['user_id'];
    $proforma_number = $invoice['proforma_number'];
    
    echo "Testing email sending for invoice #$invoice_id (Proforma #$proforma_number) for user ID: $user_id\n";
    
    // Get user email
    $userStmt = $pdo->prepare("SELECT email FROM users WHERE id = :user_id");
    $userStmt->bindValue(':user_id', $user_id);
    $userStmt->execute();
    $userEmail = $userStmt->fetchColumn();
    
    echo "User email: " . ($userEmail ?: "Not set") . "\n";
    
    // Check email settings
    $settingsStmt = $pdo->query("SELECT * FROM email_settings LIMIT 1");
    if ($settingsStmt->rowCount() == 0) {
        echo "Email settings not configured in database.\n";
        exit;
    }
    
    $settings = $settingsStmt->fetch(PDO::FETCH_ASSOC);
    echo "Email settings:\n";
    echo "- Enabled: " . ($settings['enabled'] ? 'Yes' : 'No') . "\n";
    echo "- Provider: " . $settings['provider'] . "\n";
    echo "- SMTP Host: " . ($settings['smtp_host'] ?? 'Not set') . "\n";
    echo "- SMTP Username: " . ($settings['smtp_username'] ?? 'Not set') . "\n";
    echo "- From Email: " . ($settings['from_email'] ?? 'Not set') . "\n";
    
    // Check email template
    $templateStmt = $pdo->prepare("SELECT * FROM email_templates WHERE type = 'invoice_generated'");
    $templateStmt->execute();
    if ($templateStmt->rowCount() == 0) {
        echo "'invoice_generated' email template not found in database.\n";
        exit;
    }
    
    echo "'invoice_generated' email template found in database.\n";
    
    // Try to send the email
    echo "Attempting to send email...\n";
    $result = sendInvoiceGeneratedEmail($invoice_id);
    
    if ($result) {
        echo "Email sent successfully!\n";
    } else {
        echo "Failed to send email. Check the error logs for details.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
