#!/bin/bash

# ESXi PXE Setup Script
# Downloads and extracts ESXi 8.0 and 9.0 for PXE installation

set -e

# Configuration
BASE_DIR="/var/www/html/New/os"
TEMP_DIR="/tmp/esxi_downloads"
WEB_USER="www-data"

echo "=== ESXi PXE Setup Script ==="
echo "Setting up ESXi 8.0 and 9.0 for PXE installation"
echo

# Create directories
echo "Creating directories..."
sudo mkdir -p "$BASE_DIR/esxi-8.0"
sudo mkdir -p "$BASE_DIR/esxi-9.0"
mkdir -p "$TEMP_DIR"

# Function to download with retry
download_with_retry() {
    local url="$1"
    local output="$2"
    local max_attempts=3
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        echo "Attempt $attempt/$max_attempts: Downloading $(basename "$output")..."
        if wget --timeout=30 --tries=3 -O "$output" "$url"; then
            echo "Download successful!"
            return 0
        else
            echo "Download failed, retrying..."
            ((attempt++))
            sleep 5
        fi
    done
    
    echo "Failed to download after $max_attempts attempts: $url"
    return 1
}

# ESXi download URLs (you'll need to get these from VMware)
# Note: VMware requires registration and acceptance of terms
echo "=== IMPORTANT: ESXi Download Information ==="
echo
echo "ESXi ISOs must be downloaded from VMware's official website:"
echo "https://customerconnect.vmware.com/downloads/details?downloadGroup=ESXI80U3&productId=974"
echo "https://customerconnect.vmware.com/downloads/details?downloadGroup=ESXI90U1&productId=1345"
echo
echo "You need a VMware account and must accept the license terms."
echo "Download the following files manually:"
echo "- VMware-VMvisor-Installer-8.0.3-********.x86_64.iso (ESXi 8.0U3)"
echo "- VMware-VMvisor-Installer-9.0.0-********.x86_64.iso (ESXi 9.0)"
echo

# Check if user has downloaded the ISOs
read -p "Have you downloaded the ESXi ISO files to $TEMP_DIR? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Please download the ESXi ISOs first and place them in $TEMP_DIR"
    echo "Then run this script again."
    exit 1
fi

# Look for ESXi ISO files
ESXI8_ISO=$(find "$TEMP_DIR" -name "*VMware-VMvisor-Installer-8.*" -o -name "*esxi-8.*" -o -name "*ESXi-8.*" | head -1)
ESXI9_ISO=$(find "$TEMP_DIR" -name "*VMware-VMvisor-Installer-9.*" -o -name "*esxi-9.*" -o -name "*ESXi-9.*" | head -1)

if [ -z "$ESXI8_ISO" ]; then
    echo "ESXi 8.0 ISO not found in $TEMP_DIR"
    echo "Please ensure the file is named with 'VMware-VMvisor-Installer-8' or 'esxi-8'"
    exit 1
fi

if [ -z "$ESXI9_ISO" ]; then
    echo "ESXi 9.0 ISO not found in $TEMP_DIR"
    echo "Please ensure the file is named with 'VMware-VMvisor-Installer-9' or 'esxi-9'"
    exit 1
fi

echo "Found ESXi 8.0 ISO: $ESXI8_ISO"
echo "Found ESXi 9.0 ISO: $ESXI9_ISO"
echo

# Function to extract ESXi ISO
extract_esxi() {
    local iso_file="$1"
    local dest_dir="$2"
    local version="$3"
    
    echo "=== Extracting ESXi $version ==="
    
    # Create mount point
    local mount_point="/mnt/esxi_$version"
    sudo mkdir -p "$mount_point"
    
    # Mount ISO
    echo "Mounting $iso_file..."
    sudo mount -o loop "$iso_file" "$mount_point"
    
    # Copy files
    echo "Copying files to $dest_dir..."
    sudo cp -r "$mount_point"/* "$dest_dir/"
    
    # Unmount
    echo "Unmounting..."
    sudo umount "$mount_point"
    sudo rmdir "$mount_point"
    
    # Set permissions
    echo "Setting permissions..."
    sudo chown -R "$WEB_USER:$WEB_USER" "$dest_dir"
    sudo chmod -R 755 "$dest_dir"
    
    echo "ESXi $version extraction complete!"
    echo
}

# Extract ESXi 8.0
extract_esxi "$ESXI8_ISO" "$BASE_DIR/esxi-8.0" "8.0"

# Extract ESXi 9.0
extract_esxi "$ESXI9_ISO" "$BASE_DIR/esxi-9.0" "9.0"

# Verify extracted files
echo "=== Verification ==="
echo "Checking critical files..."

check_file() {
    local file="$1"
    if [ -f "$file" ]; then
        echo "✓ $file exists"
        return 0
    else
        echo "✗ $file missing"
        return 1
    fi
}

# Check ESXi 8.0 files
echo "ESXi 8.0 files:"
check_file "$BASE_DIR/esxi-8.0/mboot.c32"
check_file "$BASE_DIR/esxi-8.0/boot.cfg"
check_file "$BASE_DIR/esxi-8.0/efi/boot/bootx64.efi"

echo

# Check ESXi 9.0 files
echo "ESXi 9.0 files:"
check_file "$BASE_DIR/esxi-9.0/mboot.c32"
check_file "$BASE_DIR/esxi-9.0/boot.cfg"
check_file "$BASE_DIR/esxi-9.0/efi/boot/bootx64.efi"

echo

# Test web accessibility
echo "=== Web Accessibility Test ==="
echo "Testing if files are accessible via web server..."

test_url() {
    local url="$1"
    local description="$2"
    
    if curl -s -I "$url" | grep -q "200 OK"; then
        echo "✓ $description accessible"
    else
        echo "✗ $description not accessible - check web server configuration"
    fi
}

# Test URLs (adjust domain as needed)
test_url "http://localhost/New/os/esxi-8.0/mboot.c32" "ESXi 8.0 mboot.c32"
test_url "http://localhost/New/os/esxi-9.0/mboot.c32" "ESXi 9.0 mboot.c32"

echo

# Display directory structure
echo "=== Directory Structure ==="
echo "Created structure:"
tree "$BASE_DIR" 2>/dev/null || find "$BASE_DIR" -type d | head -20

echo

# Final instructions
echo "=== Setup Complete ==="
echo "ESXi PXE files have been set up successfully!"
echo
echo "Directory locations:"
echo "- ESXi 8.0: $BASE_DIR/esxi-8.0/"
echo "- ESXi 9.0: $BASE_DIR/esxi-9.0/"
echo
echo "Next steps:"
echo "1. Verify web server can serve these files"
echo "2. Test PXE installation with a server"
echo "3. Check auto.logs for any issues"
echo
echo "Available ESXi templates in the system:"
echo "- esxi-8.0, esxi-8, vmware-esxi-8"
echo "- esxi-9.0, esxi-9, vmware-esxi-9"

# Cleanup
echo "Cleaning up temporary files..."
rm -rf "$TEMP_DIR"

echo "Setup script completed successfully!" 