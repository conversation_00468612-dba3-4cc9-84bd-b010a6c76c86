<?php
// Simple test file to debug the admin profile issue
error_log("test_admin_profile.php started");

// Set error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

try {
    error_log("About to require auth_functions.php");
    require_once("auth_functions.php");
    error_log("auth_functions.php loaded successfully");
    
    error_log("PDO connection status: " . ($pdo ? 'available' : 'not available'));
    
    if (isset($_GET['f']) && $_GET['f'] == 'test') {
        error_log("Test function called");
        
        // Get request data
        $input = file_get_contents('php://input');
        error_log("Raw input: " . $input);
        
        $data = json_decode($input, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("JSON decode error: " . json_last_error_msg());
            $data = $_POST;
        }
        
        $token = $data['token'] ?? '';
        error_log("Token: " . ($token ? 'present' : 'missing'));
        
        // Simple database test
        if ($pdo) {
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM admins");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            error_log("Admin count: " . $result['count']);
        }
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Test successful',
            'token_present' => !empty($token),
            'pdo_available' => $pdo ? true : false
        ]);
    } else {
        // Default response if no 'f' parameter or wrong value
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'No test function specified',
            'f_param' => $_GET['f'] ?? 'missing'
        ]);
    }

} catch (Exception $e) {
    error_log("Exception in test file: " . $e->getMessage());
    error_log("Exception trace: " . $e->getTraceAsString());
    
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
